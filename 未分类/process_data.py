#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
from collections import defaultdict

def main():
    # 读取CSV文件
    csv_file = '/Users/<USER>/Desktop/工作文档/03-代码和脚本/excel-data-analysis-tools/大附件数据完整明细_1325条记录_20250725_173653.csv'
    
    data = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data_id = row['数据ID']
            object_type = row['对象API名称']
            data.append((data_id, object_type))
    
    print(f"总共读取了 {len(data)} 条记录")
    
    # 按对象类型分组
    grouped_data = defaultdict(list)
    for data_id, object_type in data:
        grouped_data[object_type].append(data_id)
    
    # 显示统计信息
    print("统计结果:")
    for obj_type, ids in grouped_data.items():
        print(f"{obj_type}: {len(ids)} 条记录")
    
    # 生成完整的TPMActivityProofObj SQL语句
    tpm_proof_ids = grouped_data.get('TPMActivityProofObj', [])
    if tpm_proof_ids:
        print(f"\n生成TPMActivityProofObj的完整SQL语句 ({len(tpm_proof_ids)} 条记录):")
        
        # 将ID列表分成多个批次，每批150个
        batch_size = 150
        batches = [tpm_proof_ids[i:i + batch_size] for i in range(0, len(tpm_proof_ids), batch_size)]
        
        sql_content = []
        for i, batch in enumerate(batches, 1):
            sql_content.append(f"-- Batch {i} of TPMActivityProofObj IDs ({len(batch)} records)")
            sql_content.append("UPDATE mt_data")
            sql_content.append("SET value5 = '[]'")
            sql_content.append("WHERE tenant_id = '759383'")
            sql_content.append("  AND object_describe_api_name = 'TPMActivityProofObj'")
            sql_content.append("  AND id IN (")
            
            # 添加ID列表
            for j, id_val in enumerate(batch):
                if j == len(batch) - 1:  # 最后一个ID
                    sql_content.append(f"    '{id_val}'")
                else:
                    sql_content.append(f"    '{id_val}',")
            
            sql_content.append("  );")
            sql_content.append("")  # 空行分隔
        
        # 写入到新的SQL文件
        output_file = '/Users/<USER>/Desktop/工作文档/未分类/complete_tpm_activity_proof_sql.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- Complete SQL UPDATE statements for TPMActivityProofObj\n")
            f.write(f"-- Generated from CSV data: {len(tpm_proof_ids)} records\n")
            f.write("-- All statements set value5 = '[]' for tenant_id = '759383'\n\n")
            f.write("\n".join(sql_content))
        
        print(f"完整的TPMActivityProofObj SQL语句已保存到: {output_file}")

if __name__ == "__main__":
    main()