#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
from collections import defaultdict

def main():
    # 读取CSV文件
    csv_file = '/Users/<USER>/Desktop/工作文档/03-代码和脚本/excel-data-analysis-tools/大附件数据完整明细_1325条记录_20250725_173653.csv'
    
    data = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data_id = row['数据ID']
            object_type = row['对象API名称']
            data.append((data_id, object_type))
    
    print(f"CSV文件总记录数: {len(data)}")
    
    # 按对象类型分组并去重 - 使用set确保唯一性
    grouped_data = defaultdict(set)
    for data_id, object_type in data:
        grouped_data[object_type].add(data_id)
    
    # 转换为列表并显示统计信息
    final_grouped = {}
    total_unique = 0
    print("\n去重后的正确统计:")
    for obj_type, id_set in grouped_data.items():
        id_list = sorted(list(id_set))  # 转换为排序的列表
        final_grouped[obj_type] = id_list
        print(f"{obj_type}: {len(id_list)} 条唯一记录")
        total_unique += len(id_list)
    
    print(f"\n总唯一记录数: {total_unique}")
    
    # 生成正确的TPMActivityProofObj SQL语句
    tpm_proof_ids = final_grouped.get('TPMActivityProofObj', [])
    if tpm_proof_ids:
        print(f"\n生成TPMActivityProofObj的正确SQL语句 ({len(tpm_proof_ids)} 条唯一记录):")
        
        # 将ID列表分成多个批次，每批150个
        batch_size = 150
        batches = [tpm_proof_ids[i:i + batch_size] for i in range(0, len(tpm_proof_ids), batch_size)]
        
        sql_content = []
        for i, batch in enumerate(batches, 1):
            sql_content.append(f"-- Batch {i} of TPMActivityProofObj IDs ({len(batch)} unique records)")
            sql_content.append("UPDATE mt_data")
            sql_content.append("SET value5 = '[]'")
            sql_content.append("WHERE tenant_id = '759383'")
            sql_content.append("  AND object_describe_api_name = 'TPMActivityProofObj'")
            sql_content.append("  AND id IN (")
            
            # 添加ID列表
            for j, id_val in enumerate(batch):
                if j == len(batch) - 1:  # 最后一个ID
                    sql_content.append(f"    '{id_val}'")
                else:
                    sql_content.append(f"    '{id_val}',")
            
            sql_content.append("  );")
            sql_content.append("")  # 空行分隔
        
        # 写入到修正后的SQL文件
        output_file = '/Users/<USER>/Desktop/工作文档/未分类/corrected_tpm_activity_proof_sql.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- CORRECTED SQL UPDATE statements for TPMActivityProofObj\n")
            f.write(f"-- Generated from CSV data: {len(tpm_proof_ids)} UNIQUE records (removed duplicates)\n")
            f.write("-- All statements set value5 = '[]' for tenant_id = '759383'\n\n")
            f.write("\n".join(sql_content))
        
        print(f"修正后的TPMActivityProofObj SQL语句已保存到: {output_file}")
        print(f"批次数量: {len(batches)}")

if __name__ == "__main__":
    main()