#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
from collections import defaultdict

def main():
    print("=== 分析TPMActivityProofObj数据完整性 ===\n")
    
    # 读取CSV文件
    csv_file = '/Users/<USER>/Desktop/工作文档/03-代码和脚本/excel-data-analysis-tools/大附件数据完整明细_1325条记录_20250725_173653.csv'
    
    print(f"正在读取CSV文件: {csv_file}")
    
    all_data = []
    tpm_proof_data = []
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data_id = row['数据ID']
            object_type = row['对象API名称']
            all_data.append((data_id, object_type))
            
            if object_type == 'TPMActivityProofObj':
                tpm_proof_data.append(data_id)
    
    print(f"CSV文件总记录数: {len(all_data)}")
    print(f"TPMActivityProofObj记录数: {len(tpm_proof_data)}")
    
    # 去重
    unique_tpm_proof = sorted(list(set(tpm_proof_data)))
    print(f"TPMActivityProofObj唯一记录数: {len(unique_tpm_proof)}")
    
    # 生成标准的data.text格式文件
    data_text_file = '/Users/<USER>/Desktop/工作文档/未分类/generated_data.text'
    with open(data_text_file, 'w', encoding='utf-8') as f:
        f.write("id\tobject_describe_api_name\n")  # 标题行
        
        # 按对象类型分组
        grouped_data = defaultdict(set)
        for data_id, object_type in all_data:
            grouped_data[object_type].add(data_id)
        
        # 写入所有数据
        for object_type, id_set in grouped_data.items():
            for data_id in sorted(id_set):
                f.write(f"{data_id}\t{object_type}\n")
    
    print(f"\n已生成标准data.text文件: {data_text_file}")
    
    # 读取我们之前生成的SQL文件，检查是否有遗漏
    sql_file = '/Users/<USER>/Desktop/工作文档/未分类/corrected_tpm_activity_proof_sql.sql'
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 从SQL文件中提取ID
        sql_ids = []
        lines = sql_content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith("'") and line.endswith("',") or line.endswith("'"):
                # 提取ID
                id_val = line.strip("',")
                if len(id_val) == 24:  # 假设ID长度为24
                    sql_ids.append(id_val)
        
        unique_sql_ids = sorted(list(set(sql_ids)))
        print(f"\nSQL文件中的TPMActivityProofObj ID数量: {len(unique_sql_ids)}")
        
        # 对比检查
        csv_set = set(unique_tpm_proof)
        sql_set = set(unique_sql_ids)
        
        missing_in_sql = csv_set - sql_set
        extra_in_sql = sql_set - csv_set
        
        print(f"\n=== 数据对比结果 ===")
        print(f"CSV中的唯一TPMActivityProofObj ID: {len(csv_set)}")
        print(f"SQL中的唯一TPMActivityProofObj ID: {len(sql_set)}")
        print(f"CSV中有但SQL中缺失的ID: {len(missing_in_sql)}")
        print(f"SQL中有但CSV中没有的ID: {len(extra_in_sql)}")
        
        if missing_in_sql:
            print(f"\n❌ 缺失的ID (前10个):")
            for i, missing_id in enumerate(sorted(missing_in_sql)[:10]):
                print(f"  {i+1}. {missing_id}")
            if len(missing_in_sql) > 10:
                print(f"  ... 还有 {len(missing_in_sql) - 10} 个")
        
        if extra_in_sql:
            print(f"\n⚠️ SQL中多余的ID (前10个):")
            for i, extra_id in enumerate(sorted(extra_in_sql)[:10]):
                print(f"  {i+1}. {extra_id}")
            if len(extra_in_sql) > 10:
                print(f"  ... 还有 {len(extra_in_sql) - 10} 个")
        
        if not missing_in_sql and not extra_in_sql:
            print("✅ 数据完全匹配！")
            
    except FileNotFoundError:
        print(f"⚠️ 找不到SQL文件: {sql_file}")
    
    print(f"\n=== 总结 ===")
    print(f"标准data.text文件已生成: {data_text_file}")
    print(f"包含 {len(all_data)} 条记录 (去重后 {sum(len(ids) for ids in grouped_data.values())} 条)")

if __name__ == "__main__":
    main()