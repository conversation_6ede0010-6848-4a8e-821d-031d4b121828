#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def main():
    # 读取现有的部分SQL文件
    with open('COMPLETE_ALL_SQL_UPDATES.sql', 'r', encoding='utf-8') as f:
        existing_content = f.read()
    
    # 读取完整的TPMActivityProofObj SQL
    with open('corrected_tpm_activity_proof_sql.sql', 'r', encoding='utf-8') as f:
        tpm_content = f.read()
    
    # 提取TPMActivityProofObj的SQL语句（跳过前3行注释）
    tmp_lines = tpm_content.split('\n')[3:]  # 跳过前3行注释
    tpm_sql = '\n'.join(tmp_lines)
    
    # 创建完整的SQL文件
    complete_sql = existing_content + '\n' + tpm_sql
    
    # 添加最终总结
    complete_sql += '''

-- ========================================
-- FINAL SUMMARY - COMPLETE SQL FILE
-- ========================================
-- Total UPDATE statements: 3 object types, 1058 unique records
-- - object_H3T1c__c: 23 unique records ✅
-- - TPMActivityObj: 72 unique records ✅  
-- - TPMActivityProofObj: 963 unique records (7 batches) ✅
-- 
-- All statements set value5 = '[]' for tenant_id = '759383'
-- 
-- EXECUTION RECOMMENDATION:
-- 1. Execute object_H3T1c__c UPDATE (23 records)
-- 2. Execute TPMActivityObj UPDATE (72 records)  
-- 3. Execute TPMActivityProofObj UPDATEs in sequence (7 batches, 963 total records)
--    - Batch 1: 150 records
--    - Batch 2: 150 records
--    - Batch 3: 150 records
--    - Batch 4: 150 records
--    - Batch 5: 150 records
--    - Batch 6: 150 records
--    - Batch 7: 63 records
-- 
-- Total records to be updated: 1058 unique records
-- Data source: 大附件数据完整明细_1325条记录_20250725_173653.csv (deduplicated)
'''
    
    # 写入最终完整文件
    with open('FINAL_COMPLETE_ALL_SQL_UPDATES.sql', 'w', encoding='utf-8') as f:
        f.write(complete_sql)
    
    print("✅ 已生成完整的SQL文件: FINAL_COMPLETE_ALL_SQL_UPDATES.sql")
    
    # 统计信息
    lines = complete_sql.split('\n')
    update_count = sum(1 for line in lines if line.strip().startswith('UPDATE mt_data'))
    
    print(f"📊 文件统计:")
    print(f"   - 总行数: {len(lines)}")
    print(f"   - UPDATE语句数量: {update_count}")
    print(f"   - 包含所有1058条唯一记录的SQL语句")

if __name__ == "__main__":
    main()