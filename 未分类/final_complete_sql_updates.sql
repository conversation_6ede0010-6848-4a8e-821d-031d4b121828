-- SQL UPDATE statements generated from CSV data
-- Generated on: 2025-01-25
-- Total records processed: 1325 IDs across 3 object types
-- Data source: 大附件数据完整明细_1325条记录_20250725_173653.csv

-- ========================================
-- UPDATE for object_H3T1c__c (23 records)
-- ========================================
UPDATE mt_data 
SET value5 = '[]' 
WHERE tenant_id = '759383' 
  AND object_describe_api_name = 'object_H3T1c__c' 
  AND id IN (
    '68809a1e6018af0001ffe7e6',
    '6858c200a4d19100012e85fa',
    '6822b381642ebd0001f0791d',
    '67ec07294c6add000114758e',
    '67c1181c64825b00018a85c3',
    '67b533004b462200017e6877',
    '67a4521b8048680001db0ca8',
    '6785d430c94c730001587583',
    '6758027bcc7ade00013699f7',
    '67340891442f650001e47025',
    '6709f40d1a354e00010df0fd',
    '669651dd0113e200011f3ae0',
    '66bc5d8e81b4f40001870376',
    '666a48b19f86fa00015b2d3e',
    '66417471b0fbcd0001422875',
    '65828df226e2080001ab0be5',
    '6567122480ade000012a84e8',
    '6530a5484d4f12000173d912',
    '65056ad71ee76400012ba4a5',
    '64e8715b27b53200011fc698',
    '64d2e8319d43290001f34e64',
    '64acac59a0f0d80001463368',
    '649a9ec0d40ec50001bbb4bc'
  );

-- ========================================
-- UPDATE for TPMActivityObj (72 records)
-- ========================================
UPDATE mt_data 
SET value5 = '[]' 
WHERE tenant_id = '759383' 
  AND object_describe_api_name = 'TPMActivityObj' 
  AND id IN (
    '687b5d23b260680001bbd067',
    '687742533d2e5500012f9bc7',
    '6875b8c83d2e5500019cb9c1',
    '6854bcdd42bd1f000183bfc3',
    '686f9df4db598000017425c9',
    '686f99a0531d650001dd5bd0',
    '686752073f51e90001e75481',
    '685e7419b368bf0001a72b2b',
    '685e6f8b4295430001525e32',
    '684b9a5b42bd1f00016ceee1',
    '684b99346b2f59000178c330',
    '684b98588bfebd000192a465',
    '684b978f6b2f590001721d3aa',
    '684b92f78bfebd000180382d',
    '684b88a76b2f590001483495',
    '684b8a3942bd1f0001386e96',
    '683fadf705c25b000116b56c',
    '683e550efd88ec000192c752',
    '68305f539add1f000168d0fa',
    '682b4046b9675600011a8a61',
    '6821d35b263dcd0001f13631',
    '68219f04f8f5a90001b321b1',
    '681b0d8090c3cb00018d652e',
    '680b10639307570001385af6',
    '6809e45a90c3cb00013199da',
    '68031d949307570001387aae',
    '67cfaaed9d94a40001741e75',
    '6800c4a7f8f5a90001fa80a8',
    '67fe7e3a90c3cb00018e5bc7',
    '67fe6d9f90c3cb000183be96',
    '67f47c1d74d5cd0001bd6242',
    '67e0ba146d7fc70001faa113',
    '67e0b3c96d7fc70001efc93c',
    '67da8dd68f93650001517c54',
    '67c28609a5f8520001ddc18a',
    '6793551c4dd3c90001f967a7',
    '676cb61c67cb3f0001aa7db1',
    '6768be919ce173000164ebb7',
    '67441a1c0da81d0001f86c20',
    '672ae80dab5f4e0001682e7b',
    '671efaa560ce3800010dcbe1',
    '66ece73815883a00017c41c0',
    '66de5dd0d7b09b00016dbe57',
    '66b4384cf8f5ae00015fa739',
    '66b362ca0640e70001fcf95b',
    '66b03ef1900d1800018a90d4',
    '66a9b4b4ac974a00012bd62b',
    '6662adf9138e710001f99c34',
    '6662a7f8d491a900014a4079',
    '6662a5bbca7e710001e3da0a',
    '6662a3914b76ee00012b3cd5',
    '66629d74138e710001ef426f',
    '665d64e8d491a90001f9d413',
    '6650126b84b1d70001c6b5bb',
    '66239c1c026d890001194029',
    '6543103c4912b600016ca855',
    '6536351bc0eb5b0001eaf22b',
    '651ba8c3bff9d10001f2a39e',
    '6513deaa32237200018bd5b0',
    '650ac20536e95a0001688ea9',
    '6502d9d736e95a000197e241',
    '64f07552b16e320001ebaaf6',
    '64f0736e4dfc010001b776e2',
    '64f0714c4938c5000128821b',
    '64f06e70b16e320001e83f61',
    '64f056c140d6550001c08ed4',
    '64f04e4140d6550001b40735',
    '64ed4a804dfc010001a1d7f2',
    '64e377c8bb3ae3000173564b',
    '64e374f2aa9a350001d372f8',
    '64d6f0e8bb3ae300019a906b',
    '64d0a170e2403e00010fc2ed'
  );

-- ========================================
-- SUMMARY
-- ========================================
-- Total UPDATE statements generated: 3 object types
-- - object_H3T1c__c: 23 records
-- - TPMActivityObj: 72 records  
-- - TPMActivityProofObj: 1230 records (see complete_tpm_activity_proof_sql.sql for full statements)
-- 
-- All statements set value5 = '[]' for tenant_id = '759383'
-- 
-- Note: Due to the large number of TPMActivityProofObj records (1230 total),
-- the complete SQL statements for TPMActivityProofObj are saved in a separate file:
-- complete_tpm_activity_proof_sql.sql
-- 
-- This file contains 9 batches of UPDATE statements, each handling approximately 150 records.