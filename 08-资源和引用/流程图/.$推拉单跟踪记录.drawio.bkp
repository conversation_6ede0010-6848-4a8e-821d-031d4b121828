<mxfile host="Electron" modified="2024-05-29T04:04:31.563Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="A1d_N5RS4BNejJozOf2T" version="24.2.5" type="device">
  <diagram name="第 1 页" id="jzg9EmJsZ-hEQknDo3J6">
    <mxGraphModel dx="1434" dy="854" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=none;strokeColor=#09555B;fontColor=default;" edge="1" parent="1" source="uEsc1DGgTx4L-yIOAjiA-1" target="uEsc1DGgTx4L-yIOAjiA-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-1" value="提交数据（推单/拉单）" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#FAD9D5;strokeColor=#D0CEE2;fontColor=#1A1A1A;" vertex="1" parent="1">
          <mxGeometry x="350" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=none;strokeColor=#09555B;fontColor=default;" edge="1" parent="1" source="uEsc1DGgTx4L-yIOAjiA-2" target="uEsc1DGgTx4L-yIOAjiA-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-2" value="保存数据" style="whiteSpace=wrap;html=1;rounded=1;labelBackgroundColor=none;fillColor=#FAD9D5;strokeColor=#D0CEE2;fontColor=#1A1A1A;" vertex="1" parent="1">
          <mxGeometry x="350" y="510" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=none;strokeColor=#09555B;fontColor=default;" edge="1" parent="1" source="uEsc1DGgTx4L-yIOAjiA-4" target="uEsc1DGgTx4L-yIOAjiA-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-4" value="关闭源单" style="whiteSpace=wrap;html=1;rounded=1;labelBackgroundColor=none;fillColor=#FAD9D5;strokeColor=#D0CEE2;fontColor=#1A1A1A;" vertex="1" parent="1">
          <mxGeometry x="350" y="610" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=none;strokeColor=#09555B;fontColor=default;" edge="1" parent="1" source="uEsc1DGgTx4L-yIOAjiA-6" target="uEsc1DGgTx4L-yIOAjiA-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-6" value="记录源单和目标单id对应关系" style="whiteSpace=wrap;html=1;rounded=1;labelBackgroundColor=none;fillColor=#FAD9D5;strokeColor=#D0CEE2;fontColor=#1A1A1A;" vertex="1" parent="1">
          <mxGeometry x="350" y="720" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uEsc1DGgTx4L-yIOAjiA-8" value="修改记录增加源单快照" style="whiteSpace=wrap;html=1;rounded=1;labelBackgroundColor=none;fillColor=#FAD9D5;strokeColor=#D0CEE2;fontColor=#1A1A1A;" vertex="1" parent="1">
          <mxGeometry x="350" y="830" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
