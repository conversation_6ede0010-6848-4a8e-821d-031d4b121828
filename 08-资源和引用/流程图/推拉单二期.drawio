<mxfile host="Electron" modified="2024-04-20T00:12:34.134Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="XFLdHcsfuHR2OjtfKaW1" version="24.2.5" type="device">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel dx="1026" dy="772" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-53" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-54" target="Xcwr8iL6onqwJBvyUMFV-56" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-54" value="选择多条" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="40" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-55" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-56" target="Xcwr8iL6onqwJBvyUMFV-75" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-56" value="点击『转换』按钮" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="210" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-57" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-60" target="Xcwr8iL6onqwJBvyUMFV-62" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-58" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=#EEEEEE;fontColor=#1A1A1A;" parent="Xcwr8iL6onqwJBvyUMFV-57" vertex="1" connectable="0">
          <mxGeometry x="-0.2333" y="1" relative="1" as="geometry">
            <mxPoint x="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-59" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-60" target="Xcwr8iL6onqwJBvyUMFV-65" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-60" value="是否超过20条" style="rhombus;whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;rounded=1;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="530" y="90" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-61" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-62" target="Xcwr8iL6onqwJBvyUMFV-63" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="740" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-62" value="报错提示超过20条" style="whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;rounded=1;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="510" y="220" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-63" value="结束" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.terminator;whiteSpace=wrap;fillColor=#F5AB50;strokeColor=#909090;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="520" y="310" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-64" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-65" target="Xcwr8iL6onqwJBvyUMFV-67" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-65" value="批量查询数据" style="whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;rounded=1;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="670" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-66" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-67" target="Xcwr8iL6onqwJBvyUMFV-69" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-67" value="查询所有源对象为当前对象的所有规则" style="whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;rounded=1;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="830" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-68" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-69" target="Xcwr8iL6onqwJBvyUMFV-71" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-69" value="通过数据业务类型过滤转换规则" style="whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;rounded=1;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="990" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-70" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-71" target="Xcwr8iL6onqwJBvyUMFV-73" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-71" value="再通过规则中字段条件范围过滤转换规则（函数条件忽略同现状）" style="whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;rounded=1;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="990" y="210" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-72" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=#EEEEEE;strokeColor=#182E3E;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-73" target="Xcwr8iL6onqwJBvyUMFV-63" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-73" value="将符合条件的转换规则返回" style="whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;rounded=1;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="990" y="310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-74" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-75" target="Xcwr8iL6onqwJBvyUMFV-60" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-75" value="查询转换规则接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5AB50;strokeColor=#909090;fillStyle=auto;fontColor=#1A1A1A;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="370" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-86" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-77" target="Xcwr8iL6onqwJBvyUMFV-84" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-77" value="执行转换接口（Convert）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-81" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-78" target="Xcwr8iL6onqwJBvyUMFV-80" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-78" value="初始化主对象数据" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-83" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-80" target="Xcwr8iL6onqwJBvyUMFV-82" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-80" value="初始化主对象描述" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="930" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-90" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-82" target="Xcwr8iL6onqwJBvyUMFV-89" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-82" value="初始化从对象描述" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="1020" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-85" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-84" target="Xcwr8iL6onqwJBvyUMFV-78" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-84" value="初始化转换规则" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="740" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-92" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-89" target="Xcwr8iL6onqwJBvyUMFV-91" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-95" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Xcwr8iL6onqwJBvyUMFV-92" vertex="1" connectable="0">
          <mxGeometry x="-0.2667" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-94" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-89" target="Xcwr8iL6onqwJBvyUMFV-99" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="1260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-96" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Xcwr8iL6onqwJBvyUMFV-94" vertex="1" connectable="0">
          <mxGeometry x="-0.4889" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-89" value="检验规则中字段是否正常" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="760" y="1130" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-91" value="报错提示字段无效或不存在" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="900" y="1140" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-121" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-98" target="Xcwr8iL6onqwJBvyUMFV-120" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-122" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Xcwr8iL6onqwJBvyUMFV-121" vertex="1" connectable="0">
          <mxGeometry x="-0.2667" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-124" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-98" target="Xcwr8iL6onqwJBvyUMFV-123" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-125" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Xcwr8iL6onqwJBvyUMFV-124" vertex="1" connectable="0">
          <mxGeometry x="-0.2333" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-98" value="校验数据是否满足规则范围" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="760" y="1580" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-101" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-99" target="Xcwr8iL6onqwJBvyUMFV-100" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-99" value="转换规则范围处理" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="1260" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-107" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-100" target="Xcwr8iL6onqwJBvyUMFV-106" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-109" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Xcwr8iL6onqwJBvyUMFV-107" vertex="1" connectable="0">
          <mxGeometry x="-0.24" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-111" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-100" target="Xcwr8iL6onqwJBvyUMFV-110" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-100" value="是否为字段范围规则" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="760" y="1350" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-108" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-106" target="Xcwr8iL6onqwJBvyUMFV-98" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-106" value="通过规则范围过滤数据" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="1480" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-112" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-113" target="Xcwr8iL6onqwJBvyUMFV-106" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1100" y="1510" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-115" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Xcwr8iL6onqwJBvyUMFV-112" vertex="1" connectable="0">
          <mxGeometry x="0.1313" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-114" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-110" target="Xcwr8iL6onqwJBvyUMFV-113" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-110" value="调用函数服务获取范围规则" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="900" y="1360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-117" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-113" target="Xcwr8iL6onqwJBvyUMFV-116" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-113" value="是否正确返回" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1060" y="1350" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-119" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-116" target="Xcwr8iL6onqwJBvyUMFV-118" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1360" y="1390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-116" value="报错提示函数执行失败" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1200" y="1360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-118" value="结束" style="html=1;dashed=0;whiteSpace=wrap;shape=mxgraph.dfd.start;fillColor=#ffcd28;gradientColor=#ffa500;strokeColor=#d79b00;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1360" y="1375" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-120" target="zW4v-0BvRmP45jW7PdTI-0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-120" value="初始化从对象数据" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="1720" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-126" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0.5;entryDx=0;entryDy=15;entryPerimeter=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="Xcwr8iL6onqwJBvyUMFV-123" target="Xcwr8iL6onqwJBvyUMFV-118" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1400" y="1410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Xcwr8iL6onqwJBvyUMFV-123" value="报错提示不满足范围" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="900" y="1590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="zW4v-0BvRmP45jW7PdTI-0" target="zW4v-0BvRmP45jW7PdTI-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-0" value="根据从对象规则过滤从对象数据" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="1820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="zW4v-0BvRmP45jW7PdTI-2" target="zW4v-0BvRmP45jW7PdTI-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-2" value="支持转换" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="1920" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="zW4v-0BvRmP45jW7PdTI-4" target="zW4v-0BvRmP45jW7PdTI-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-4" value="补充相关字段" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="2020" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="zW4v-0BvRmP45jW7PdTI-6" target="zW4v-0BvRmP45jW7PdTI-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-6" value="转换结果返回" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;gradientColor=#ffa500;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="740" y="2130" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-9" value="结束" style="html=1;dashed=0;whiteSpace=wrap;shape=mxgraph.dfd.start;fillColor=#ffcd28;gradientColor=#ffa500;strokeColor=#d79b00;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="760" y="2230" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zW4v-0BvRmP45jW7PdTI-11" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;gradientColor=#ea6b66;strokeColor=#b85450;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1110" y="770" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
