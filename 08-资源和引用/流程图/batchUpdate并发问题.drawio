<mxfile host="Electron" modified="2024-03-28T11:42:32.158Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.1.0 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="l53KEg9FSwQT1H3wRZdH" version="24.1.0" type="device">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel dx="1026" dy="743" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="miJhAh5twrzY70EU5ENz-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="110" y="115" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-33" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;labelBackgroundColor=none;fontColor=default;gradientColor=#7ea6e0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="miJhAh5twrzY70EU5ENz-34" target="miJhAh5twrzY70EU5ENz-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-34" value="用户需要更新的数据（字段1=2）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;labelBackgroundColor=none;gradientColor=#7ea6e0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="150" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-35" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;labelBackgroundColor=none;fontColor=default;gradientColor=#7ea6e0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="miJhAh5twrzY70EU5ENz-36" target="miJhAh5twrzY70EU5ENz-38" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-36" value="查询库中数据&lt;br&gt;（字段2=空）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;labelBackgroundColor=none;gradientColor=#7ea6e0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="313.5" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-37" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;labelBackgroundColor=none;fontColor=default;gradientColor=#7ea6e0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="miJhAh5twrzY70EU5ENz-38" target="miJhAh5twrzY70EU5ENz-39" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-38" value="需要更新的数据合并到查询的数据&lt;br&gt;（字段1=2，字段2=空）" style="whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;rounded=1;labelBackgroundColor=none;gradientColor=#7ea6e0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="480" y="280" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-39" value="更新数据&lt;br&gt;（字段1=2，字段2=空）" style="whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;rounded=1;labelBackgroundColor=none;gradientColor=#7ea6e0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="660" y="280" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-40" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="miJhAh5twrzY70EU5ENz-41" target="miJhAh5twrzY70EU5ENz-46" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-41" value="字段2=测试" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;labelBackgroundColor=none;gradientColor=#97d077;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="433.5" y="390" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-42" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="miJhAh5twrzY70EU5ENz-43" target="miJhAh5twrzY70EU5ENz-41" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-43" value="其他线程" style="shape=singleArrow;whiteSpace=wrap;html=1;rounded=1;fillColor=#d5e8d4;gradientColor=#97d077;strokeColor=#82b366;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="300" y="390" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="miJhAh5twrzY70EU5ENz-45" target="miJhAh5twrzY70EU5ENz-34" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-45" value="用户线程" style="shape=singleArrow;whiteSpace=wrap;html=1;rounded=1;fillColor=#dae8fc;gradientColor=#7ea6e0;strokeColor=#6c8ebf;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="20" y="280" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-46" value="更新数据&lt;br&gt;（字段2=测试）" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;labelBackgroundColor=none;gradientColor=#97d077;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="580" y="390" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-47" value="" style="endArrow=classic;html=1;rounded=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="30" y="370" as="sourcePoint" />
            <mxPoint x="790" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="miJhAh5twrzY70EU5ENz-48" value="&lt;b&gt;&lt;font color=&quot;#df2020&quot; style=&quot;font-size: 14px;&quot;&gt;时间线&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="20" y="340" width="70" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
