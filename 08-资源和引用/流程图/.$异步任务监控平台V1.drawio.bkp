<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.17 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36" version="24.7.17">
  <diagram name="第 1 页" id="3uPgWUlhoZX_GZlRCyV2">
    <mxGraphModel dx="1434" dy="854" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="pa_L5QfHDGo31jyiKVJK-2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#666666;fontColor=#333333;fillStyle=auto;" parent="1" vertex="1">
          <mxGeometry x="40" y="190" width="170" height="480" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-4" value="计划任务" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" parent="1" vertex="1">
          <mxGeometry x="65" y="260" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-5" value="统计字段" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" parent="1" vertex="1">
          <mxGeometry x="65" y="355" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-8" value="计算字段" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;" parent="1" vertex="1">
          <mxGeometry x="65" y="450" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-9" value="...." style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;" parent="1" vertex="1">
          <mxGeometry x="65" y="552.5" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-10" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F5F5F5;" parent="1" vertex="1">
          <mxGeometry x="329" y="190" width="381" height="480" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-11" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;各业务系统&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="80" y="200" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-12" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;异步任务监控&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="460" y="200" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-15" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;strokeWidth=2;shape=mxgraph.arrows2.arrow;dy=0.6;dx=40;notch=0;fillColor=#dae8fc;gradientColor=#7ea6e0;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="210" y="395" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-17" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DDEBFF;" parent="1" vertex="1">
          <mxGeometry x="370" y="240" width="310" height="120" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-18" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DDEBFF;" parent="1" vertex="1">
          <mxGeometry x="364.5" y="600" width="310" height="55" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-19" value="基础设施" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="370" y="612.5" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-20" value="Mongo" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#f5f5f5;gradientColor=#b3b3b3;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="440" y="600" width="100" height="52.5" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-21" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F5F5F5;" parent="1" vertex="1">
          <mxGeometry x="840" y="190" width="160" height="480" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-22" value="管&lt;div&gt;理&lt;/div&gt;&lt;div&gt;后&lt;/div&gt;&lt;div&gt;台&lt;/div&gt;&lt;div&gt;接&lt;/div&gt;&lt;div&gt;口&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" parent="1" vertex="1">
          <mxGeometry x="700" y="330" width="30" height="170" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-24" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;管理后台&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="855" y="200" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-25" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;strokeWidth=2;shape=mxgraph.arrows2.arrow;dy=0.6;dx=40;notch=0;direction=west;fillColor=#dae8fc;gradientColor=#7ea6e0;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="740" y="380" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-26" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DDEBFF;gradientColor=none;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="860" y="275" width="120" height="365" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-27" value="计划任务" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;gradientColor=#7ea6e0;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="875" y="310" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-28" value="日志任务监控" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="880" y="275" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-29" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DDEBFF;" parent="1" vertex="1">
          <mxGeometry x="875" y="390" width="90" height="230" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-30" value="后台任务" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="880" y="395" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-32" value="统计字段" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;gradientColor=#7ea6e0;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="885" y="430" width="70" height="35" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-34" value="计算字段" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;gradientColor=#ea6b66;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="885" y="480" width="70" height="35" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-35" value="引用字段" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;gradientColor=#ea6b66;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="885" y="530" width="70" height="35" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-36" value="..." style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;gradientColor=#ea6b66;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="885" y="577.5" width="70" height="35" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-37" value="查询" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="780" y="400" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-38" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DDEBFF;" parent="1" vertex="1">
          <mxGeometry x="370" y="467.5" width="310" height="110" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-39" value="数据访问模块" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="380" y="510" width="20" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pa_L5QfHDGo31jyiKVJK-40" value="接口提供模块" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="370" y="285" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="YGtEuUjV7xo_cSaFW38U-8" value="rocketMq" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#f5f5f5;gradientColor=#b3b3b3;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="560" y="601.25" width="100" height="52.5" as="geometry" />
        </mxCell>
        <mxCell id="YGtEuUjV7xo_cSaFW38U-9" value="消&lt;div&gt;费&lt;/div&gt;&lt;div&gt;模&lt;/div&gt;&lt;div&gt;块&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" parent="1" vertex="1">
          <mxGeometry x="310" y="340" width="30" height="170" as="geometry" />
        </mxCell>
        <mxCell id="YGtEuUjV7xo_cSaFW38U-10" value="service层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" parent="1" vertex="1">
          <mxGeometry x="414" y="497.5" width="95" height="50" as="geometry" />
        </mxCell>
        <mxCell id="YGtEuUjV7xo_cSaFW38U-11" value="上报&lt;br&gt;更新" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="220" y="416" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Tm2Kq8P5E_qkx0xTpMP4-1" value="...." style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;" vertex="1" parent="1">
          <mxGeometry x="590" y="268.13" width="80" height="55" as="geometry" />
        </mxCell>
        <mxCell id="Tm2Kq8P5E_qkx0xTpMP4-2" value="列表表头接口" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" vertex="1" parent="1">
          <mxGeometry x="410" y="267.5" width="80" height="55" as="geometry" />
        </mxCell>
        <mxCell id="Tm2Kq8P5E_qkx0xTpMP4-4" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DDEBFF;" vertex="1" parent="1">
          <mxGeometry x="370" y="370" width="310" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Tm2Kq8P5E_qkx0xTpMP4-3" value="查询列表数据接口" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" vertex="1" parent="1">
          <mxGeometry x="500" y="268.13" width="80" height="53.75" as="geometry" />
        </mxCell>
        <mxCell id="Tm2Kq8P5E_qkx0xTpMP4-5" value="消费上报日志" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" vertex="1" parent="1">
          <mxGeometry x="420" y="385" width="95" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Tm2Kq8P5E_qkx0xTpMP4-6" value="消息处理模块" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="375" y="380" width="30" height="65" as="geometry" />
        </mxCell>
        <mxCell id="Tm2Kq8P5E_qkx0xTpMP4-7" value="dao层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;" vertex="1" parent="1">
          <mxGeometry x="540" y="497.5" width="95" height="50" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
