---
description: 
globs: 
alwaysApply: false
---
# 代码和脚本指南

[03-代码和脚本](mdc:03-代码和脚本)目录包含各种实用程序和代码文件：

## Python脚本
- [generateCurl.py](mdc:03-代码和脚本/generateCurl.py) - 生成curl命令的工具
- [brushValidate.py](mdc:03-代码和脚本/brushValidate.py) - 数据验证脚本
- [requestLog.py](mdc:03-代码和脚本/requestLog.py) - 请求日志处理工具
- [extract_api_names.py](mdc:03-代码和脚本/extract_api_names.py) - 提取API名称的工具
- [tally_sum.py](mdc:03-代码和脚本/tally_sum.py) - 数据统计计算工具

## Shell脚本
- [dns_script.sh](mdc:03-代码和脚本/dns_script.sh) - DNS配置脚本

## Groovy脚本
- [test.groovy](mdc:03-代码和脚本/test.groovy) - 测试用Groovy脚本

## 专项目录
- [cursor/](mdc:03-代码和脚本/cursor) - Cursor相关工具和配置
- [clickhouse-time-helper/](mdc:03-代码和脚本/clickhouse-time-helper) - ClickHouse时间助手工具
- [流水线/](mdc:03-代码和脚本/流水线) - CI/CD流水线相关脚本
