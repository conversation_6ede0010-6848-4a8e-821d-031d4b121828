---
description: 
globs: 
alwaysApply: false
---
# iLangScan 多语言扫描工具

iLangScan是一款i18n扫描器，用于扫描项目中包含的中文文本，并将结果输出到文件中。这个工具可以帮助开发人员识别和管理需要国际化的文本。

## 工具目录结构
- [iLangScan-Released-3-5/](mdc:03-代码和脚本/iLangScan-Released-3-5) - 多语言扫描工具根目录
  - [bin/](mdc:03-代码和脚本/iLangScan-Released-3-5/bin) - 执行脚本目录
    - [iLangScan.py](mdc:03-代码和脚本/iLangScan-Released-3-5/bin/iLangScan.py) - 主程序入口
  - [conf/](mdc:03-代码和脚本/iLangScan-Released-3-5/conf) - 配置文件目录
    - [base_settings.yaml](mdc:03-代码和脚本/iLangScan-Released-3-5/conf/base_settings.yaml) - 基础配置（不可覆盖）
    - [default_settings.yaml](mdc:03-代码和脚本/iLangScan-Released-3-5/conf/default_settings.yaml) - 默认配置
    - [setting_mapping.yaml](mdc:03-代码和脚本/iLangScan-Released-3-5/conf/setting_mapping.yaml) - 工程与配置映射
  - [output/](mdc:03-代码和脚本/iLangScan-Released-3-5/output) - 扫描结果输出目录
  - [logs/](mdc:03-代码和脚本/iLangScan-Released-3-5/logs) - 日志文件目录
  - [readme.md](mdc:03-代码和脚本/iLangScan-Released-3-5/readme.md) - 使用文档

## 辅助工具
- [I18nCommentAdderFromFile.java](mdc:03-代码和脚本/iLangScan-Released-3-5/I18nCommentAdderFromFile.java) - 自动添加ignoreI18n注释的工具

## 使用方法

### 基本使用

在bin目录下执行：
```bash
python iLangScan.py -ip {你的项目路径}
```

### 常用参数选项
- `-ip` - 被扫描的项目路径，支持多个
- `-op` - 扫描结果输出路径
- `-ext` - 扫描文件类型，支持多个
- `-ignoreLine` - 忽略的行，正则表达式匹配
- `-files` - 指定需要扫描的特定文件

### 使用示例
```bash
# 将项目扫描结果输出成.xlsx和.txt
python iLangScan.py -ip ./your-project -ext .xlsx .txt

# 输出到指定目录
python iLangScan.py -ip ./your-project -ext .xlsx -op user/output

# 忽略包含ignoreI18n字符串的行
python iLangScan.py -ip ./your-project -ignoreLine ".*ignoreI18n.*"
```

## 标记忽略规则

### 忽略单行
```java
String text = "中文文本"; // ignoreI18n
```

### 忽略整个文件（选择其一即可）
```java
/**
 * @IgnoreI18n 或 IgnoreI18nFile 或 @IgnoreI18nFile
 */
public class YourClass {}
```

## 配置文件优先级
基础配置 > 控制台参数 > (自定义配置文件|默认配置)

## 更多信息
更多详细信息请参考[iLangScan更新文档](mdc:https:/wiki.firstshare.cn/pages/viewpage.action?pageId=456952112)
