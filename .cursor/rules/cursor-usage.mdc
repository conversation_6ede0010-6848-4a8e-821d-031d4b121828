---
description: 
globs: 
alwaysApply: false
---
# Cursor使用指南

本规则提供有关Cursor代码编辑器的使用建议和最佳实践。

## Cursor安装
- [cursor_install.sh](mdc:01-技术文档/cursor_install.sh) - Cursor安装脚本

## Java开发配置
- [extensions_java.txt](mdc:01-技术文档/extensions_java.txt) - Java开发所需扩展列表

## Maven服务调试
- [后端战士如何通过Cursor Run及Debug现有的Maven Web服务(包含Junit测试)/](mdc:01-技术文档/后端战士如何通过Cursor Run及Debug现有的Maven Web服务(包含Junit测试)/) - Maven服务调试指南

## 常用快捷键
- `Cmd+Shift+P` - 命令面板
- `Cmd+P` - 快速打开文件
- `Cmd+Shift+L` - 选择所有当前选择的匹配项
- `Cmd+D` - 选择下一个匹配项
- `Cmd+/` - 注释/取消注释行
- `Alt+Up/Down` - 移动行上下
- `Cmd+Shift+Enter` - 在当前行之前插入行
- `Cmd+Enter` - 在当前行之后插入行

## AI辅助编码
- `/` - 触发Cursor AI命令
- `/fix` - 修复当前代码中的问题
- `/explain` - 解释选中的代码
- `/doc` - 为选中的代码生成文档
- `/test` - 为选中的代码生成测试
