---
description: 
globs: 
alwaysApply: false
---
# 配置合并工具指南

配置合并工具用于合并不同环境的配置数据，将指定环境的数据作为基础数据，将其他环境中不同的配置项合并到基础数据中。

## 核心文件
- [merge_config.py](mdc:03-代码和脚本/mergConfig/merge_config.py) - 配置合并工具主程序
- [test_merge_config.py](mdc:03-代码和脚本/mergConfig/test_merge_config.py) - 配置合并工具测试程序
- [config.json](mdc:03-代码和脚本/mergConfig/config.json) - 配置合并工具JSON配置
- [config.yaml](mdc:03-代码和脚本/mergConfig/config.yaml) - 配置合并工具YAML配置

## 文档
- [README.md](mdc:03-代码和脚本/mergConfig/README.md) - 配置合并工具使用指南
- [开发案例分享.md](mdc:03-代码和脚本/mergConfig/开发案例分享.md) - 配置合并工具开发案例

## 使用方法

```bash
python merge_config.py --base <基础环境> --profiles <其他环境,用逗号分隔> --name <配置文件名> --token <API令牌>
```

### 参数说明
- `--base`: 基础环境名称，如 fstest
- `--profiles`: 其他环境名称，用逗号分隔，如 dev,test,prod
- `--name`: 配置文件名称，如 gray-rel-webPage
- `--token`: API访问令牌
