---
description: Generate code for API calls based on user-provided API information
globs: *
alwaysApply: false
---
<generate-api-all-code>
# Instructions
Use the following commands to get AI assistant:

# Action
Generate code for API calls based on user-provided API information.

API calls code includes 3 essential code components:
1. **Interface Definition**: must following Code style eg: [RuleEngineProxy.java](mdc:fs-crm-common/src/main/java/com/facishare/crm/sfa/predefine/service/pricepolicy/rest/proxy/RuleEngineProxy.java)
2. **Request Parameters**: of body  just like [AdvancedCompute.java](mdc:fs-crm-common/src/main/java/com/facishare/crm/sfa/predefine/service/pricepolicy/rest/dto/AdvancedCompute.java) inner class Arg
3. **Response Model**: Result of API calls like [AdvancedCompute.java](mdc:fs-crm-common/src/main/java/com/facishare/crm/sfa/predefine/service/pricepolicy/rest/dto/AdvancedCompute.java) inner class Result


## Code Style Guidelines
- Use standard Java naming conventions
- Include proper JavaDoc documentation
- Follow clean code principles
- Implement necessary validation annotations
- Maintain consistent formatting
</generate-api-all-code>

#指令
使用以下命令获取 AI 助手：
#行动
根据用户提供的 API 信息为 API 调用生成代码。
API 调用代码包括 3 个基本代码组件：
1.接口定义：必须遵循代码样式，例如：@AiRestServiceProxy.java
2.请求参数：入参 body 参考： @ChatComplete.java 接口类中 Arg
3.响应模型：API 调用的结果，如 @ChatComplete.java 口类中 Result
## 代码风格指南
- 使用标准 Java 命名约定
- 包括适当的 JavaDoc 留档
- 遵循干净的代码原则