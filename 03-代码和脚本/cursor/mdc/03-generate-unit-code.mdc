---
description: Generate junit code for user provided file
globs: *Test.java
---
<generate-unit-test-code>
# 生成单元测试

## Purpose
Generate comprehensive unit tests for the user provided Java code, ensuring proper test coverage and following testing best practices.

## Output Requirements
The assistant will generate unit test code including:

1. **Test Class Structure**
   - Proper test class naming (ending with "Test")
   - Required test annotations (@Test, @Before, etc.)
   - Necessary mock configurations

2. **Test Cases**
   - Happy path scenarios
   - Edge cases and error conditions
   - Input validation tests
   - Mock behavior definitions

3. **Test Data**
   - Appropriate test data setup
   - Mock object initialization
   - Test constants and utilities

## Testing Guidelines
- Follow AAA (Arrange-Act-Assert) pattern
- Use meaningful test method names
- Include proper test documentation
- Implement proper assertion statements
- Handle exceptions appropriately
- Use @DisplayName for test case descriptions

## Required Dependencies
- JUnit 5
- Mockito
- AssertJ (optional)

## Action 
After generation Code, Use the following commands to get AI assistant:
`mvn test -o -am -pl <module-name> -DfailIfNoTests=false -Dtest=<testfile_path>`

</generate-unit-test-code>