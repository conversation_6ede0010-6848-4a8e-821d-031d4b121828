---
description: generate code structure from Confluence wiki prd
globs: 
---
<generate-prd-structure>
# Instructions
Use the following commands to get AI assistant:

# extract content from user input Confluence wiki address
Generate the following command execution, and generate relevant requirements descriptions and understanding from the command
eg: `curl -H "Authorization: Bearer NDg2NzE2MDY4NDg1OpCqukW8VdzeL7lzoTEyZ//dLTty" "https://wiki.firstshare.cn/rest/api/content/<user_input_wiki_url_pageid>?expand=body.storage"`

</generate-prd-structure>