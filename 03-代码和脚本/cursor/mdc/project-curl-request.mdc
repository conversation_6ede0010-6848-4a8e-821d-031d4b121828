---
description: extract curl request for debug
globs: *
---

<rewrite-curl-request>
# Instructions
Use the following commands to get <PERSON> assistant:

# extract local request for debug
rewrite user input curl to local request for debugging and run it

eg: `curl --location --request POST 'localhost:8080/API/v1/object/<url_path>' \
--header 'x-fs-Enterprise-Id: "<tenant_id>"' \
--header 'x-fs-Employee-Id: <employee_id>' \
--header 'Content-Type: application/json' \
--data-raw '<full_body>'`

</rewrite-curl-request>