# 使用cursor优化代码及编写单元测试
## 使用案例
1. 使用cursor优化代码*
![image.png](../assets/image_1739683239597_0.png)
![image.png](../assets/image_1739699925111_0.png)
2. 让cursor编写单元测试，但是第一版提供的构造描述和字段的mock并不正确，需要增加规则让cursor生成的单测符合要求。eg：ObjectDescribe 构造和IFieldDescribe 构造示例。
   使用Spock框架编写单元测试规则参考：
   ```text
   # 单元测试规范

   ## 基本要求
   - 测试类位置：统一放在 groovy 包下
   - 命名规范：类名 + Test（如已存在则在原文件中修改）
   - 测试框架：使用 Spock 框架
   - 不使用 SpringBoot

   ## 测试覆盖要求
   - 确保所有方法调用都有对应测试用例
   - 覆盖所有代码分支
   - 测试用例之间保持独立，避免相互依赖
   - 使用 where 语句覆盖所有逻辑分支

   ## 边界值测试要求
   1. 数值类型：
      - 负数、0、正数
      - 最小值、最大值
      - NaN（非数字）
      - 无穷大

   2. 字符串类型：
      - 空字符串
      - 单字符
      - 非 ASCII 字符串
      - 多字节字符串

   3. 集合类型：
      - 空集合
      - 单元素集合
      - 首元素、末元素测试

   4. 日期类型：
      - 1月1日
      - 2月29日（闰年）
      - 12月31日

   ## Mock 相关配置

   ### 1. 基础 Mock 设置
   必需导入：
   import com.fxiaoke.i18n.client.I18nClient
   import com.fxiaoke.i18n.client.impl.I18nServiceImpl
   import org.powermock.reflect.Whitebox

   Mock 示例：
   def setupSpec() {
      def i18nClient = Mock(I18nClient)
      def i18nServiceImpl = Mock(I18nServiceImpl)
      Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
      Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
      i18nClient.getAllLanguage() >> []
   }

   ### 2. UdobjGrayConfig Mock 设置
   必需导入：
   import com.fxiaoke.release.FsGrayReleaseBiz
   import com.facishare.paas.appframework.core.util.UdobjGrayConfig

   Mock 示例：
   def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
   Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
   fsGrayReleaseBiz.isAllow(*_) >> trueOrFalse

   ### 3. ObjectDescribe 构造
   必需导入：
   import com.facishare.paas.metadata.api.describe.IObjectDescribe
   import com.facishare.paas.metadata.impl.describe.ObjectDescribe
   import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
   import com.facishare.paas.metadata.impl.describe.FieldDescribe

   构造示例：
   IObjectDescribe describe = new ObjectDescribe()
   describe.setApiName("AccountObj")
   def fieldDescribe = FieldDescribeFactory.newInstance([
      "api_name": "employeeField", 
      "type": "employee"
   ])
   describe.setFieldDescribes([fieldDescribe])

   ### 4. IFieldDescribe 构造
   必需导入：
   import com.facishare.paas.metadata.api.describe.IFieldDescribe
   import com.facishare.paas.metadata.api.describe.IFieldType
   import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory

   构造示例：
   IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance([
      "api_name": "employeeField", 
      "type": IFieldType.EMPLOYEE
   ])

   注：字段类型请参考 com.facishare.paas.metadata.api.describe.IFieldType 中的定义
   ```

3 优化单元测试后规则后重新生成单元测试

image.png
image.png
4. 执行单元测试，将报错信息提供给cursor比你让其修复
image.png
image.png
image.png
# 总结
软件开发中，优质单元测试是保障代码质量的关键。若借助 cursor 生成预期单元测试案例，团队共同维护常用测试 demo 必不可少。
成员开发经验各异，维护 demo 可整合经验，覆盖多元业务与技术场景，促进技术交流，助 cursor 理解项目技术架构。
业务层面，不同成员负责不同模块，维护过程也是知识共享，详细注释让 cursor 把握业务需求，为新功能输出适配测试。
同时，这利于知识传承，新成员借此融入团队。总之，团队维护 demo 可让 cursor 契合项目，提升协作与开发效率。