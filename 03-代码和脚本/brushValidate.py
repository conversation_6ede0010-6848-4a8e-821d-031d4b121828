import time
import requests
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

def get_execution(login_url):
    try:
        response = requests.get(login_url)
        response.raise_for_status()  # 检查请求是否成功
        soup = BeautifulSoup(response.text, 'html.parser')
        # 假设 execution 参数在一个 class 为 'cas-field' 的元素中
        elements = soup.find_all(class_='cas-field')
        if elements and len(elements) > 3:
            execution = elements[3].find('input', {'name': 'execution'})['value']
            if not execution:
                logger.error("Failed to find execution parameter")
                raise Exception("Failed to find execution parameter")
            return execution
        else:
            logger.error("Failed to find execution parameter")
            raise Exception("Failed to find execution parameter")
    except requests.RequestException as e:
        logger.error(f"Request failed: {e}")
        raise Exception("Failed to get execution parameter")

def login_and_get_cookie(login_url, username, password):
    try:
        session = requests.Session()
        execution = get_execution(login_url)
        payload = {
            'username': username,
            'password': password,
            'execution': execution,
            '_eventId': 'submit',
            'geolocation': ''
        }
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'User-Agent': 'Mozilla/5.0'  # 添加 User-Agent
        }
        response = session.post(login_url, data=payload, headers=headers)
        # print(f"Response Status Code: {response.status_code}")
        # print(f"Response Headers: {response.headers}")
        # print(f"Response Text: {response.text}")

        # 假设响应体中包含了会话标识符
        # 这里需要根据实际的响应格式解析会话标识符
        # 例如，假设会话标识符在响应体的某个 JSON 字段中
        # session_id = response.json().get('session_id')

        # 检查并提取 JSESSIONID
        cookies = session.cookies.get_dict()
        # print(f"Cookies: {cookies}")
        if not cookies:
            logger.error("Failed to obtain cookies")
            raise Exception("Failed to obtain cookies")
        return cookies
    except requests.RequestException as e:
        logger.error(f"Login request failed: {e}")
        raise Exception("Failed to login and obtain JSESSIONID")

def extract_cas_service_prefix(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    cas_service_prefixes = []
    for line in lines:
        if 'casServicePrefix=' in line:
            parts = line.split()
            for part in parts:
                if part.startswith('casServicePrefix='):
                    prefix = part.split('=')[1]
                    cas_service_prefixes.append(prefix)
                    break
    
    return cas_service_prefixes

def login_and_request(cas_service_prefixes, username, password):
    for prefix in cas_service_prefixes:
        login_url = f"https://oss.foneshare.cn/cas/login?service={prefix}/paas-console/shiro-cas"
        try:
            session = requests.Session()
            execution = get_execution(login_url)
            payload = {
                'username': username,
                'password': password,
                'execution': execution,
                '_eventId': 'submit',
                'geolocation': ''
            }
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'User-Agent': 'Mozilla/5.0'  # 添加 User-Agent
            }
            response = session.post(login_url, data=payload, headers=headers)
            print(f"Response Status Code: {response.status_code}")
            print(f"Response Headers: {response.headers}")
            print(f"Response Text: {response.text}")

            # 提取并格式化 Cookies
            cookies = session.cookies.get_dict()
            if not cookies:
                logger.error(f"Failed to obtain cookies for URL: {login_url}")
                continue

            cookies_dict = {key: value for key, value in cookies.items()}
            print(f"Formatted Cookies: {cookies_dict}")

            # Use the cookies to make requests
            url = f"{prefix}/paas-console/metadata/describe/list?tenantId=-100&apiName=AsyncTaskMonitorObj&displayName=&_={int(time.time() * 1000)}"
            response = requests.get(url, cookies=cookies_dict)
            print(f"Response from {url}: {response.text}")

        except requests.RequestException as e:
            logger.error(f"Request failed for URL {login_url}: {e}")

# Example usage
file_path = '杂/data.txt'
cas_service_prefixes = extract_cas_service_prefix(file_path)

try:
    login_and_request(cas_service_prefixes, '17611547898', '192634726Pwd..')
except Exception as e:
    print(f"Error: {e}")