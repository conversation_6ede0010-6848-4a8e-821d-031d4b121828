/**
 * <AUTHOR>
 * @codeName 【客户】回填【人员规模】
 * @description 【客户】回填【人员规模】
 * @createTime 2023-12-05
 */
UIEvent event
 String qymc = context.data.name as String //企业名称
 log.info("企业名称:" + qymc)
 def(Boolean error, List data, String errorMessage) = Fx.crm.getEnterpriseByName(qymc)
 log.info("查询结果：" + Fx.json.toJson(data))
 if(data != null){
   data.each{d->
    if((d["name"] as String) == qymc){
      String keyNo = d["keyNo"] as String
      log.info(qymc + "  对应的 keyNo:" + keyNo)
      
      def(Boolean error1, Map data1, String errorMessage1) = Fx.crm.getEnterpriseDetailById(keyNo)
      log.info("查询到的结果：" + Fx.json.toJson(data1))
      String employNumber = data1["employNumber"] as String //人员规模
      log.info("人员规模：" + employNumber)
      event = UIEvent.build(context){
        editMaster("field_Y3ugZ__c":employNumber)
      }
      return event
    }else{
      event = UIEvent.build(context){
        
      }
    }
   }
 }else{
   event = UIEvent.build(context){
      
    }
 }
 return event