#!/bin/bash
# 异步任务批量更新脚本
# 生成时间: 2025-07-25 21:42:12

BASE_URL="http://localhost"
API_ENDPOINT="$BASE_URL/API/v1/rest/object/AsyncTaskMonitorObj/action/IncrementUpdate"

# 批量更新函数
update_task() {
    local task_id=$1
    local status=$2
    local tenant_id=$3
    
    curl --request POST \
      --url "$API_ENDPOINT" \
      --header 'Accept: */*' \
      --header 'Content-Type: application/json' \
      --header "X-fs-Employee-Id: -10000" \
      --header "X-fs-Enterprise-Id: $tenant_id" \
      --header "x-fs-ei: $tenant_id" \
      --header "x-fs-userInfo: -10000" \
      --data "{
        \"data\": {
          \"task_status\": \"$status\",
          \"_id\": \"$task_id\"
        },
        \"optionInfo\": {
          \"isDuplicateSearch\": true,
          \"skipFuncValidate\": false
        }
      }"
    
    echo "Updated task $task_id to $status"
    sleep 0.1  # 避免API限流
}

# 示例调用
# update_task "6840e50865b3bc3f0292bcdf" "completed" "736684"
# update_task "6865dd19aa00d91c0a104695" "completed" "736684"
# update_task "68698b4865b3bc3f02a5da33" "completed" "736684"
# update_task "683fd9d965b3bc3f02923340" "completed" "736684"
# update_task "684a71cc65b3bc3f02974941" "completed" "736684"

echo "批量更新完成"
