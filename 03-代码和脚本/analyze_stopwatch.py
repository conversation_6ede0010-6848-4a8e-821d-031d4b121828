#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
StopWatch日志分析工具
用于从命令行直接分析StopWatch日志
"""

import sys
import os
import re
import argparse

try:
    from stopwatch_sorter import parse_stopwatch_log, sort_tasks_by_percent, print_sorted_tasks, save_to_csv
except ImportError as e:
    print(f"导入stopwatch_sorter模块时出错: {e}")
    print("请确保stopwatch_sorter.py文件与本脚本位于同一目录")
    sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='从命令行直接分析StopWatch日志')
    parser.add_argument('log_text', nargs='?', help='StopWatch日志文本')
    parser.add_argument('-c', '--csv', help='将结果保存为CSV文件')
    args = parser.parse_args()
    
    # 获取日志内容
    if args.log_text:
        log_text = args.log_text
    else:
        # 如果没有提供文本，则从标准输入读取
        print("请输入StopWatch日志内容 (输入完成后按Ctrl+D或Ctrl+Z):")
        log_text = sys.stdin.read()
    
    if not log_text.strip():
        parser.print_help()
        return
    
    try:
        # 解析日志
        watch_name, total_time, tasks = parse_stopwatch_log(log_text)
        
        # 排序任务
        sorted_tasks = sort_tasks_by_percent(tasks)
        
        # 打印结果
        print_sorted_tasks(watch_name, total_time, sorted_tasks)
        
        # 保存CSV
        if args.csv:
            save_to_csv(watch_name, total_time, sorted_tasks, args.csv)
            
    except Exception as e:
        print(f"处理日志时出错: {e}")

if __name__ == "__main__":
    main()
