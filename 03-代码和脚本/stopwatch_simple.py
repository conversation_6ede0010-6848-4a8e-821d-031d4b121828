#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
StopWatch日志分析工具 - 简化版
用于分析Java StopWatch日志，按执行时间百分比排序
"""

import re
import csv
from typing import List, <PERSON><PERSON>

def parse_stopwatch(log_text: str) -> Tuple[str, int, List[Tuple[str, int, float]]]:
    """解析StopWatch日志，提取任务名称、耗时和百分比"""
    # 提取StopWatch名称和总运行时间
    watch_name = "StopWatch"
    total_time = 0

    header_match = re.search(r"StopWatch '([^']+)': running time = (\d+) ms", log_text)
    if header_match:
        watch_name = header_match.group(1)
        total_time = int(header_match.group(2))

    # 提取任务信息
    tasks = []
    task_pattern = re.compile(r'\[([^\]]+)\] (\d+) ms = (\d+)%')

    for match in task_pattern.finditer(log_text):
        task_name = match.group(1)
        task_time = int(match.group(2))
        task_percent = float(match.group(3))
        tasks.append((task_name, task_time, task_percent))

    # 如果没有找到总时间，计算所有任务时间的总和
    if total_time == 0 and tasks:
        total_time = sum(task[1] for task in tasks)

    # 按百分比降序排序
    sorted_tasks = sorted(tasks, key=lambda x: x[2], reverse=True)

    return watch_name, total_time, sorted_tasks

def print_sorted_tasks(watch_name: str, total_time: int, tasks: List[Tuple[str, int, float]]):
    """打印排序后的任务列表"""
    print(f"StopWatch '{watch_name}': 总运行时间 = {total_time} ms\n")
    print("按百分比排序的任务:")
    print("-" * 80)
    print(f"{'任务名称':<40} {'耗时(ms)':<10} {'百分比':<10}")
    print("-" * 80)

    for task_name, task_time, task_percent in tasks:
        print(f"{task_name:<40} {task_time:<10} {task_percent:<10.2f}%")

def save_to_csv(watch_name: str, total_time: int, tasks: List[Tuple[str, int, float]], filename: str):
    """将分析结果保存为CSV文件"""
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['StopWatch', watch_name])
        writer.writerow(['总运行时间(ms)', total_time])
        writer.writerow([])
        writer.writerow(['任务名称', '耗时(ms)', '百分比(%)'])

        for task_name, task_time, task_percent in tasks:
            writer.writerow([task_name, task_time, f"{task_percent:.2f}"])

    print(f"分析结果已保存至CSV文件: {filename}")

# 在此处粘贴StopWatch日志数据
STOPWATCH_LOG = """StopWatch 'StandardRelatedListController': running time = 12905 ms; [doControllerBeforeListener] 2857 ms = 22%; [doFunPrivilegeCheck] 2026 ms = 16%; [findObject] 537 ms = 4%; [findCustomButton] 721 ms = 6%; [queryAndCacheFuncPrivilege] 26 ms = 0%; [runPlugin_before] 24 ms = 0%; [findDomainPluginDescribes] 827 ms = 6%; [runDomainPlugin_before] 66 ms = 1%; [buildSearchTemplateQuery] 402 ms = 3%; [findData] 3632 ms = 28%; [asyncFillFieldInfo] 630 ms = 5%; [findMobileLayouts] 63 ms = 0%; [findLayout] 851 ms = 7%; [getButtonInfo] 224 ms = 2%; [doControllerAfterListener] 6 ms = 0%; [runPlugin_after] 3 ms = 0%; [filterUnauthorizedFieldsByDataList] 1 ms = 0%"""

def main():
    try:
        # 解析并排序任务
        watch_name, total_time, sorted_tasks = parse_stopwatch(STOPWATCH_LOG)

        if not sorted_tasks:
            print("警告: 未找到任何任务信息，请检查日志格式")
            return

        # 打印结果
        print_sorted_tasks(watch_name, total_time, sorted_tasks)

        # 保存CSV (可选，取消注释以启用)
        # save_to_csv(watch_name, total_time, sorted_tasks, "stopwatch_analysis.csv")

    except Exception as e:
        print(f"处理日志时出错: {e}")

if __name__ == "__main__":
    main()
