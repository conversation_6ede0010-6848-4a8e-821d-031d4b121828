import json
import re
from typing import List

def validate_api_format(api_names: List[str]) -> None:
    """验证API名称格式是否符合Obj结尾规范"""
    invalid_apis = [api for api in api_names if not re.match(r'^[A-Za-z0-9_]+Obj$', api)]
    if invalid_apis:
        print(f"⚠️ 格式验证失败：发现 {len(invalid_apis)} 个不符合规范的API名称（应以Obj结尾且不含特殊字符）")
        print("问题API列表：", invalid_apis)
        raise ValueError("API名称格式验证失败")

def extract_json_section(content: str, key: str) -> dict:
    """使用正则表达式更健壮地提取JSON配置段"""
    pattern = re.compile(rf'{key}=(\{{.*?\}})', re.DOTALL)
    match = pattern.search(content)
    if not match:
        raise ValueError(f"未找到 {key} 配置段")
    
    try:
        return json.loads(match.group(1).replace('\n', '').replace('\t', ''))
    except json.JSONDecodeError as e:
        print(f"JSON解析失败：{key} 配置段格式错误")
        raise

def process_print_ini(file_path: str) -> str:
    """
    处理print.ini文件的主要流程：
    1. 提取两个配置段的API名称
    2. 合并去重并排序
    3. 格式验证
    4. 生成结果文件
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取配置段
        main_config = extract_json_section(content, 'gray_many_abstract_layout_filter_config')

        # 合并去重
        all_apis = list({*main_config.keys()})
        all_apis.sort()

        # 格式验证
        validate_api_format(all_apis)

        # 生成结果
        result = ','.join(all_apis)
        with open('api_names.txt', 'w', encoding='utf-8') as f:
            f.write(result)

        print(f"✅ 成功提取 {len(all_apis)} 个API名称")
        print(f"📄 结果文件路径：{file_path}/../api_names.txt")
        return result

    except FileNotFoundError:
        raise FileNotFoundError("❌ 文件未找到，请确认路径：print.ini")
    except Exception as e:
        print(f"❌ 处理过程中发生错误：{str(e)}")
        raise

if __name__ == '__main__':
    try:
        print("🔍 开始提取API名称...")
        result = process_print_ini('../dataDir/print.ini')
        print("\n验证完成！提取结果：\n" + result[:100] + "...")  # 打印前100字符预览
    except Exception as e:
        print(f"❌ 程序执行失败：{str(e)}") 