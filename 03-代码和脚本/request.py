import time
import requests
from bs4 import BeautifulSoup
import logging
import json

# 配置日志
logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

class BizException(Exception):
    pass

class AbsRequestService:
    def __init__(self):
        self.cache_cookie = None
        self.session = requests.Session()  # 使用Session来保持会话

    def okhttp_post(self, url, request_body, login_url):
        return self.do_post(url, request_body, login_url)

    def do_post(self, url, request_body, login_url):
        return self.do_post_location_consumer(url, request_body, login_url, self.reset_cookie)

    def do_post_location_consumer(self, url, request_body, login_url, consumer):
        if not self.get_cache_cookie():
            self.reset_cookie(login_url)

        # 将cookie字典转换为字符串
        cookie_dict = self.get_cache_cookie()
        cookie_str = '; '.join([f"{key}={value}" for key, value in cookie_dict.items()])
        # print(f"Cookie: {cookie_str}")
        headers = {
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'cookie': cookie_str  # 使用转换后的字符串
        }

        try:
            response = self.session.post(url, data=request_body, headers=headers, allow_redirects=False)
            # print(f"Response: {response.text}")
            response_body = response.text
            code = response.status_code
            # print(f"Response code: {code}")
            location = response.headers.get('location')

            if code == 302 and location and location.endswith("shiro-cas"):
                new_cookie = {"JSESSIONID": response.cookies.get_dict().get('JSESSIONID', '')}
                if not CONFIG.get_first_login():
                    # print(f"new_cookie: {new_cookie}")
                    self.set_cache_cookie(new_cookie)
                consumer(login_url)
                return self.do_second_post(url, request_body, login_url)
            elif code in [200, 500]:
                return response_body, location
            elif code == 502:
                logger.error(f"POST请求发出成功，服务器内部发生错误地址为{url}，参数为{request_body}")
                raise BizException("服务器内部发生错误")

            return response_body, location
        except requests.RequestException as e:
            logger.error(f"POST请求发出失败，请求的地址为{url}，参数为{request_body}，错误信息为{e}")
            raise BizException("POST请求发出失败")

    def do_second_post(self, url, request_body, login_url):
        return self.do_post_location_consumer(url, request_body, login_url, lambda msg: self.handle_login_failure(url, request_body))

    def handle_login_failure(self, url, request_body):
        logger.error(f"POST请求发出失败，请求的地址为{url}，参数为{request_body}")
        raise BizException("登录失败，请依次检查用户名密码是否正确！")

    def get_timeout(self):
        return 10

    def reset_cookie(self, login_url):
        cookie = self.login(CONFIG.get_user_name(), CONFIG.get_password(), login_url)
        if self.get_cache_cookie() is None:
            self.set_cache_cookie(cookie)
            CONFIG.set_first_login(False)
    def set_cache_cookie(self, cookie):
        self.cache_cookie = cookie

    def get_cache_cookie(self):
        return self.cache_cookie

    def login(self, username, password, login_url):
        try:
            payload = {
                'username': username,
                'password': password,
                'execution': self.get_execution(login_url),
                '_eventId': 'submit',
                'geolocation': ''
            }
            headers = {
                'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
            # 打印调试信息
            # print(f"Login URL: {login_url}")
            
            response = self.session.post(login_url, data=payload, headers=headers)
            # print(f"Response: {response.text}")
            
            cookies = self.session.cookies.get_dict()
            # print(f"Cookies: {cookies}")
            jsessionid = cookies.get('JSESSIONID', '')
            TGC = cookies.get('TGC', '')
            if not jsessionid:
                raise BizException("CAS获取cookie失败，返回值为空,请检查用户名或者密码")
            return { "JSESSIONID": jsessionid}
        except requests.RequestException as e:
            logger.error(f"CAS获取cookie失败，错误信息为{e}")
            raise BizException("CAS获取cookie失败")

    @staticmethod
    def get_execution(login_url):
        try:
            response = requests.get(login_url)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            elements = soup.find_all(class_='cas-field')
            if len(elements) > 3:
                value = elements[3].find('input')['value']
                # print(f"Execution: {value}")
                if not value:
                    logger.error("CAS获取execution失败")
                    raise BizException("CAS获取execution失败")
                return value
            else:
                logger.error("CAS获取execution失败")
                raise BizException("CAS获取execution失败")
        except requests.RequestException as e:
            logger.error(f"CAS获取execution失败，错误信息为{e}")
            raise BizException("CAS获取execution失败")

class QueryPaasRequestService(AbsRequestService):
    FONESHARE_PAAS = "https://oss.foneshare.cn/paas-console"
    LOGIN_URL = "https://oss.foneshare.cn/cas/login?service=%s/shiro-cas"
    QUERY_URL = "%s/metadata/sql/query-result"

    def request_query(self, sql, tenant_id):
        form_body = self.get_form_body(sql, tenant_id)
        url = CONFIG.get_url()
        response, _ = self.okhttp_post(f"{url}/metadata/sql/query-result", form_body, f"https://oss.foneshare.cn/cas/login?service={url}/shiro-cas")
        return response

    def get_form_body(self, sql, tenant_id):
        form_body = self.get_common_form_body(sql, tenant_id)
        form_body['readOnly'] = 'true'
        return form_body

    def get_common_form_body(self, sql, tenant_id):
        return {
            'sql': sql,
            'module': CONFIG.get_business_type(),
            'enableNestloop': str(CONFIG.get_enable_nest_loop()),
            'resourceType': CONFIG.get_resource_type(),
            'tenantId': tenant_id
        }

    def set_cache_cookie(self, cookie):
        CONFIG.set_cookie_paas(cookie)

    def get_cache_cookie(self):
        return CONFIG.get_cookie_paas()

    def get_timeout(self):
        return CONFIG.get_timeout()

    def get_location(self):
        url = CONFIG.get_url()
        return f"https://oss.foneshare.cn/cas/login?service={url}/shiro-cas" 

class Config:
    def __init__(self):
        # 初始化配置参数
        self.url = "https://oss.foneshare.cn/paas-console"
        self.business_type = "CRM"
        self.enable_nest_loop = True
        self.resource_type = "postgresql"
        self.timeout = 10
        self.cookie_paas = None
        self.first_login = False
        self.username = "***********"
        self.password = "192634726Pwd.."

    def get_url(self):
        return self.url
    
    def set_url(self, url):
        self.url = url

    def get_business_type(self):
        return self.business_type

    def get_enable_nest_loop(self):
        return self.enable_nest_loop

    def get_resource_type(self):
        return self.resource_type

    def get_timeout(self):
        return self.timeout

    def get_cookie_paas(self):
        return self.cookie_paas

    def set_cookie_paas(self, cookie):
        self.cookie_paas = cookie

    def get_user_name(self):
        return self.username

    def get_password(self):
        return self.password

    def set_first_login(self, first_login):
        self.first_login = first_login

    def get_first_login(self):
        return self.first_login

# 创建一个全局的 CONFIG 实例
CONFIG = Config() 

def extract_cas_service_prefix(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    cas_service_prefixes = []
    for line in lines:
        if 'casServicePrefix=' in line:
            parts = line.split()
            for part in parts:
                if part.startswith('casServicePrefix='):
                    prefix = part.split('=')[1]
                    cas_service_prefixes.append(prefix)
                    break
    
    return cas_service_prefixes

def main():
    file_path = 'data.txt'
    cas_service_prefixes = extract_cas_service_prefix(file_path)
    # 创建 QueryPaasRequestService 的实例
    query_service = QueryPaasRequestService()


    # select enable_multi_lang from mt_field
    #             where describe_api_name = 'MtOptionObj'
    #             and api_name in ('description', 'label')
    #             and tenant_id in ('-100','-101') and status= 'released' and enable_multi_lang = true ;
    # 定义 SQL 查询和租户 ID
    # select i18n_info_list from mt_ui_component limit 1
    # select * from mt_describe where describe_api_name = 'MtOptionObj' and tenant_id in ('-100','-101')
    # select * from mt_ui_component where tenant_id in ('-100','-101') and api_name = 'layout_SettlementObj_mobile'
    sql_query = '''
                select * from mt_describe where describe_api_name = 'AsyncTaskMonitorObj' and tenant_id in ('-100','-101')
                '''
    print("验证sql：", sql_query)
    tenant_id = "-100"
    for prefix in cas_service_prefixes:
        CONFIG.set_url(prefix + "/paas-console")
        try:
            response = query_service.request_query(sql_query, tenant_id)
            # print("Response:", response)
            try:
                response_data = json.loads(response)
            except json.JSONDecodeError as e:
                print(f"JSON解析失败，错误信息：{e} prefix: {prefix}")
            # 休眠10毫秒
            time.sleep(0.02)
            if 'info' in response_data:
                data_list = json.loads(response_data['info'])
                
                # 根据不同的 prefix 进行判断
                # print(CONFIG.get_url())
                if prefix == "https://oss.foneshare.cn":
                    # print(data_list)
                    if len(data_list) != 2:
                        print("Query Response:", response)
                        print(f"❌ Error: Data count must be exactly 2 for prefix: {prefix}")
                    else:
                        print(f"✅ Success: Correct data count for prefix: {prefix}")
                else:
                    if len(data_list) == 0:
                        print("Query Response:", response)
                        print(f"❌ Error: No data found for prefix: {prefix}")
                    elif len(data_list) > 2:
                        print("Query Response:", response)
                        print(f"❌ Error: Data count exceeds 2, actual count is {len(data_list)} for prefix: {prefix}")
                    else:
                        print(f"✅ Success: Correct data count for prefix: {prefix}")
            else:
                print(f"❌ Error: No 'info' field in response for prefix: {prefix}")
                
        except BizException as e:
            print(f"❌ An error occurred at URL {CONFIG.get_url()}: {e}")

if __name__ == "__main__":
    main() 