# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

variables:
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end"
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=ERROR -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"

default:
  interruptible: true
  tags:
    - jdk11
    - sonar
    - k8s

cache:
  paths:
    - .m2/repository/
    
stages: 
  - i18nScan         # List of stages for jobs, and their order of execution
  - test
  
i18nScan:
  stage: i18nScan
  timeout: 30m
  allow_failure: true
  script:
    - 'python3 /scripts/i18n_scan.py $CI_PROJECT_DIR'

unit-test-job:   # This job runs in the test stage.
  stage: test    # It only starts when the job in the build stage completes successfully.
  script:
    - mvn clean compile -pl fs-paas-function-local-provider,fs-paas-function-service-background-provider -am test -B org.jacoco:jacoco-maven-plugin:0.8.8:report sonar:sonar -Dsonar.core.codeCoveragePlugin=jacoco -Dsonar.dynamicAnalysis=reuseReports -Dconfig.mode=localNoUpdate -Dmaven.test.failure.ignore=true
#    - mvn clean compile -pl fs-paas-function-local-provider,fs-paas-function-service-background-provider,fs-paas-function-runtime-pod -am test -B org.jacoco:jacoco-maven-plugin:0.8.8:report sonar:sonar -Dsonar.core.codeCoveragePlugin=jacoco -Dsonar.dynamicAnalysis=reuseReports -Dconfig.mode=localNoUpdate
    - echo "start upload jacoco"
    - cp /scripts/jacoco-report-upload.py .
    - 'python3 jacoco-report-upload.py $CI_REPOSITORY_URL $CI_COMMIT_SHA $CI_COMMIT_REF_NAME'