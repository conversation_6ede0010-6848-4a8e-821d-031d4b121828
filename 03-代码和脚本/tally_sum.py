import re
text = """
12月31号拍照到2周岁花费￥11000 
1月4号曼哈顿手抓球 ￥35.3 
1月7号 纸尿裤 m码4包 ￥275 
1月20号疫苗十三价肺炎 五价轮状口服  ￥1028 
1月21号 布书 6本￥23 看图说话 10本  ￥15.8  365夜故事书  4本 ￥17 
1月27号 纸尿裤 m码4包 ￥270 
1月29号 皇宠安抚牙胶  ￥39.9 
2月7号儿科挂号费 ￥50   
D2盒 和钙3盒共￥328.23 
2月6号两套连体衣 ￥69 
2月17号  AD 两盒￥64  毛绒连体衣 ￥121 
2月20号 m码 l码纸尿裤 ￥270  松达面霜￥60 
布书￥14.6 
2月22号 安慕斯抽纸2箱￥68.6 
2月24号五价  十三价  ￥1028 
2月28号 半袖包屁衣 ￥62.1   2件 
3.2号餐椅 ￥145  浦利顿辅食机￥100 
3月13号 l码纸尿裤 ￥270 
3月18号小皮米粉￥43  注水碗 ￥59 
3月29号奶瓶清洁剂￥43 
3月30号拉拉裤￥9.9  纱布浴巾￥35 
4月1号拉拉裤 4包￥270 
4月8号辅食刀 ￥16  钓鱼玩具￥25.8  画板 ￥11 转转乐 ￥11 
4月9号辅食油￥72  咬咬乐￥27 
4月13号唇周膏￥79 
4月18号可心柔1箱￥64 
4月20号拉拉裤四包  ￥270 洗护二合一￥51 辅食碗￥11 
4月26号辅食锅  ￥148  两件衣服￥95 
4月27号 辅食碗两个￥45  小熊很忙第二季 ￥60 
"""
total_cost = 0
summary_procedure = ''
for line in text.strip().split('\n'):
    match_list = re.findall(r'￥(\d+(\.\d+)?)', line)
    for match in match_list:
        if match != '':
            cost = float(match[0])
            print(f"{line}￥{cost:.2f}")
            total_cost += cost
            summary_procedure += str(cost) + '+'
print(summary_procedure.strip('+'))
print(f"合计花费：{total_cost:.2f}元")
# total_price = 0
#  # 去除换行符，并将金额和其它文字分开
# text_list = text.strip().split('\n')
# sum_text = ""
# for item in text_list:
#     content = item.split('￥')
#     if len(content) > 1:
#         for price in content[1:]:
#             try:
#                 total_price += float(price)
#                 sum_text += price + '+'
#             except ValueError:
#                 pass
# # 剔除文本中的空格
# sum_text = sum_text.strip('+')
# print(sum_text.replace(' ', ''))
# print("合计为：￥", round(total_price, 2))

