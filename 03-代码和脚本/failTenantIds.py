import sys;

tenantIds = 69205,69206,69207,69208,69813,69814,69815,69816,70073,70078,70077,70080,70138,71566,74410,74825,74951,74954,74955,75120,76685,78527,78703,78765,78783,78896,78914,79058,79231,79232,79235,79236,79245,79335,79461,79613,79521,79668,79669,79672,79673,79676,79677,79693,79694,79732,79733,79746,79747,79748,79754,76517,79531,80344,78698,80815,80942,81081,78702,81141,81162,81165,81166,81140,79687,78057,82379,81043,71554,82958,74745,78586,84602,84603,84604,84605,84606,84607,84608,84609,84612,84613,84614,84615,84616,84698,84699,84700,84701,84702,84703,84706,84710,84715,84717,84722,84723,84724,84725,84726,84727,84728,84729,84748,84751,84752,84753,84754,84756,84757,84759,84760,84761,84764,84765,85102,85104,85106,85107,85109,85110,85111,85112,85113,85116,85117,85118,85119,85120,85121,85123,85124,85125,85126,85127,85128,85129,85130,85131,85132,85133,85135,85136,85137,85138,85139,85140,85141,85146,85147,85148,85149,85150,85152,85156,85157,85158,85159,85161,85162,85163,85164,85165,85166,85167,85168,85169,85170,85172,82857,85173,85174,85175,85176,85177,85178,85179,85180,85181,85183,85185,85186,85187,85189,85190,85191,85193,85194,85195,85184,85196,85197,85198,85199,85200,85201,85202,85203,85155,85204,85205,85206,85207,85209,85210,85211,85212,85213,85214,85215,85217,85219,85221,85222,85223,85160,85224,85225,85227,85230,85231,85232,85233,85234,85235,85236,85237,85238,85239,85240,85241,85242,85243,85244,85245,85246,85247,85248,85249,85250,85251,85252,85253,85254,85255,85256,85257,80941,85260,85261,85263,85265,85267,85268,85228,85269,85270,72946,72958,72972,72973,72978,72979,72995,72999,73006,73007,73008,73009,74326,74329,74345,74356,74404,75147,75148,75149,75150,75151,76169,76170,76171,76172,76179,76190,76193,79081,79366,85271,85272,85273,85274,85275,85276,85277,85278,85279,85282,85283,85285,85286,85289,85290,85291,85293,85294,85296,85299,85134,85301,85302,85303,82666,83362,85304,85305,85307,85308,85309,85310,85311,85312,85313,85315,85317,85318,85331,85333,85334,85335,85336,85337,85339,85346,85347,85349,85351,85353,85355,85356,85359,85360,84905,85362,85363,85365,85366,85368,85369,85370,85373,85374,85375,85376,85372,85378,85379,85381,85382,85383,85384,85385,85386,85387,85388,85389,85391,85392,85393,85394,85395,85396,85397,85399,85400,85401,80981,85402,85403,85404,85405,85406,85407,85408,85410,85411,85412,85413,85414,85416,85417,85419,85420,85421,85345,85422,85423,85424,85425,84105,85426,85427,85428,85429,85430,85431,85432,85433,85434,84104,85435,85436,85437,85438,85439,85440,85441,85442,85443,85444,85445,85446,85447,85448,85449,85450,85451,85452,85453,85454,85455,85456,85457,85458,85459,85460,85461,85462,85463,85466,85467,85469,85470,85471,85472,85358,85357,85473,85475,85476,85478,85479,85480,85481,85482,85483,85485,85487,85488,85489,85490,85492,85493,85495,85484,85496,85497,85498,85499,85500,85501,85502,85503,85504,85505,85506,85507,85508,85509,85510,85511,85512,85513,85220,85514,85515,85516,85517,85520,85521,85522,85523,72943,72944,72945,85524,71696,71700,71701,71703,71704,71729,71731,71837,71838,71847,71855,71856,71869,71873,71877,71536,71682,71685,71686,71689,71694,71695,85340,85526,71534,71535,85030,85527,85528,85529,85530,85216,85264,71517,71520,71527,71529,71533,71262,85532,85533,85534,85535,71263,71268,71466,71481,71488,71490,71492,85536,85537,85538,85545,85544,85546,85547,85549,85551,85553,85555,85556,85557,85558,85559,84446,85561,85562,85563,85564,85567,85568,85571,85574,85575,85579,85582,85583,85584,85585,85586,85587,85588,85590,85591,85592,85593,85594,85595,85596,85597,85599,85600,85601,85602,85603,85604,85605,85606,85607,85608,85609,85610,85611,85612,85613,85614,85464,85229,85615,85616,85619,85618,85620,85621,85624,85625,85626,85627,85628,85629,85630,85631,85632,85633,85634,85635,85636,85637,85638,85639,85640,85641,85642,85643,85644,85645,85646,85647,85648,85649,85653,85657,85658,85659,85660,85661,85662,85663,85664,85665,85666,85667,85668,85669,85670,85671,85672,85673,85674,85675,85676,85677,85678,85680,85682,85683,85684,85685,85686,85687,85688,85689,85681,85690,85691,85695,85696,85697,85698,85699,85700,85701,85702,85705,85706,85707,85708,85709,85710,85711,85712,85713,85714,85715,85717,85718,85719,85720,85721,85722,85723,85724,85726,78762,71590,71557,77740,81193,82677,85728,85729,85730,85731,85732,84672,85733,85734,85735,85736,85737,85738,85739,85740,85741,85742,85743,85745,85746,85747,85748,85749,85750,85751,85752,85753,85754,85755,85756,85757,85758,85764,85765,85767,85768,85769,85770,85773,85778,85779,85781,85782,85783,85784,85785,85786,85776,85775,84979,85788,85789,85790,85791,85792,85794,85795,85796,85797,85598,85799,85800,85801,85803,85804,85805,85806,85807,85812,85813,85814,85818,85819,85820,85821,85822,85823,85824,85825,85826,84800,85827,85828,85829,85830,85834,85836,85837,85589,85839,85840,85841,85842,85843,85844,85845,85846,85847,85848,85849,85858,85859,85862,85864,85866,85867,85868,85870,85873,85875,85877,85878,85880,85881,85883,85885,85890,85891,85895,85896,85897,85898,85899,85900,85902,85903,85904,85905,85906,85907,85908,85909,85911,85912,85913,85914,85915,85916,85917,85919,85920,85921,85922,85923,85924,85925,85926,85927,85928,85930,85932,85933,85934,85935,85936,85937,85938,85939,85940,85941,85918,85943,85944,85945,85946,85947,85948,85949,85951,85952,85953,85954,85963,85964,85965,85966,85967,85968,85969,85970,85971,85972,85973,85974,85975,85976,85977,85978,85979,85980,85981,85982,85983,85984,85985,85986,85987,85988,85990,85991,85992,85993,85994,85995,85996,85997,85998,85999,86001,86003,86004,86005,86006,86007,86009,86012,86010,86013,86014,86015,86016,86017,86018,86019,86020,86023,86026,86027,86029,86033,86035,82829,86036,86038,86039,86040,86041,86042,86044,86045,86046,86047,86048,86049,86050,86051,86052,86053,86054,86056,77789,86059,86060,86061,86063,86065,86067,86068,86070,86071,86072,86073,86074,86076,86077,86088,85879,74255,86090,86091,86092,86093,86094,86095,86096,86098,86099,86100,86102,86103,86104,86105,86106,86107,86108,86109,86110,86111,86112,86114,86115,86127,86132,86133,86134,86135,86136,86138,78989,86140,86142,86143,86144,86145,86148,86149,86150,86151,86152,86154,86155,81217,86157,86158,82793,86164,86166,86167,86168,86173,86174,86175,86176,86177,86179,86180,86181,86182,86183,86187,86186,86188,86189,86190,86191,86193,86194,86195,86196,86197,86198,86199,86200,86201,86202,86203,86204,86206,86207,86208,86209,86210,86211,86212,86213,86214,86215,86216,86217,86218,86219,86220,86223,86224,86227,86228,86229,86230,86231,86232,86233,86234,86237,86238,86239,86240,86241,86243,86244,86245,86246,86247,85491,86248,86249,86250,86251,86252,86254,86255,86256,86257,86260,86261,86263,86264,86265,86266,86267,86268,86272,86273,86274,86275,86276,86277,86280,86281,86282,86283,86008,86284,86285,86287,86288,86289,86290,86291,86292,86293,86294,86295,86296,86298,86258,86299,86300,86301,86302,86303,86304,86305,86306,86307,86308,86309,86310,86311,86317,86318,86319,86320,86321,86322,86324,86326,86327,86328,86329,86330,86331,86332,86333,86334,86335,86336,86337,86338,86339,86340,86344,86345,86348,86350,82514,86351,86352,86353,86354,86360,86361,86362,86363,86364,86365,86366,86367,86368,81146,86395,86396,86397,86398,86399,86400,86401,86402,86403,86404,86405,86406,86407,86408,86409,86410,86411,86412,86413,86414,86415,86416,86417,86418,86419,86420,86421,86422,86423,86424,86425,86426,86441,86442,86443,86444,86445,86446,86447,86448,86449,86450,86451,86452,86453,86454,86455,86458,86459,86460,86461,86462,86463,86464,86465,86466,86606,88151,88246,88324
print(len(tenantIds))
print(sys.getsizeof(tenantIds))