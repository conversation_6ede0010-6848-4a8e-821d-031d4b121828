#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据处理和Groovy脚本生成工具

功能：
1. 从temp.text文件中提取extra_info字段数据
2. 将JSON格式转换为Groovy Map格式
3. 提取text字段作为collaborative_rich_text__c__o的值
4. 根据tempId.text中的ID生成对应的Groovy更新脚本

作者：AI Assistant
创建时间：2025-01-15
"""

import json
import os
import re
from typing import List, Dict, Tuple, Optional


class GroovyScriptGenerator:
    """Groovy脚本生成器"""
    
    def __init__(self, temp_file_path: str, temp_id_file_path: str, output_dir: str):
        """
        初始化生成器
        
        Args:
            temp_file_path: temp.text文件路径
            temp_id_file_path: tempId.text文件路径
            output_dir: 输出目录路径
        """
        self.temp_file_path = temp_file_path
        self.temp_id_file_path = temp_id_file_path
        self.output_dir = output_dir
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
    
    def read_temp_ids(self) -> List[str]:
        """
        读取tempId.text文件中的ID列表
        
        Returns:
            ID列表
        """
        try:
            with open(self.temp_id_file_path, 'r', encoding='utf-8') as f:
                ids = [line.strip() for line in f if line.strip()]
            print(f"成功读取 {len(ids)} 个ID")
            return ids
        except FileNotFoundError:
            print(f"错误：找不到文件 {self.temp_id_file_path}")
            return []
        except Exception as e:
            print(f"读取ID文件时出错: {e}")
            return []
    
    def read_temp_data(self) -> List[Dict]:
        """
        读取temp.text文件中的数据
        
        Returns:
            数据列表
        """
        data_list = []
        try:
            with open(self.temp_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        if line.strip():
                            data = json.loads(line.strip())
                            data_list.append(data)
                    except json.JSONDecodeError as e:
                        print(f"第 {line_num} 行JSON解析错误: {e}")
                        continue
            print(f"成功读取 {len(data_list)} 条数据")
            return data_list
        except FileNotFoundError:
            print(f"错误：找不到文件 {self.temp_file_path}")
            return []
        except Exception as e:
            print(f"读取数据文件时出错: {e}")
            return []
    
    def extract_and_convert_data(self, data: Dict) -> Tuple[Optional[str], Optional[str]]:
        """
        提取并转换数据
        
        Args:
            data: 原始数据字典
            
        Returns:
            (collaborative_rich_text__c__o值, 转换后的collaborative_rich_text__c值)
        """
        try:
            # 提取extra_info字段
            extra_info_str = data.get('extra_info', '')
            if not extra_info_str:
                print("警告：未找到extra_info字段")
                return None, None
            
            # 解析extra_info为JSON对象
            extra_info_json = json.loads(extra_info_str)
            
            # 提取text字段作为collaborative_rich_text__c__o的值
            text_value = extra_info_json.get('text', '无')
            
            # 将extra_info转换为Groovy格式
            # 1. 将双引号替换为单引号
            groovy_format = extra_info_str.replace('"', "'")
            
            # 2. 将花括号替换为方括号
            groovy_format = groovy_format.replace('{', '[')
            groovy_format = groovy_format.replace('}', ']')
            
            return text_value, groovy_format
            
        except json.JSONDecodeError as e:
            print(f"解析extra_info JSON时出错: {e}")
            return None, None
        except Exception as e:
            print(f"数据转换时出错: {e}")
            return None, None
    
    def generate_groovy_script(self, data_id: str, text_value: str, groovy_data: str) -> str:
        """
        生成Groovy更新脚本

        Args:
            data_id: 数据ID
            text_value: collaborative_rich_text__c__o的值
            groovy_data: 转换后的Groovy格式数据

        Returns:
            Groovy脚本内容
        """
        # 转义单引号和换行符以避免Groovy语法错误
        text_value_escaped = text_value.replace("'", "\\'").replace("\n", "\\n").replace("\r", "\\r")
        groovy_data_escaped = groovy_data.replace("'", "\\'")

        script_template = """// Groovy更新脚本
// 数据ID: {}
// 生成时间: {}

String objectAPIName = 'LeadsObj'
String dataId = '{}'
Map updateData = [
    "collaborative_rich_text__c__o": "{}",
    "collaborative_rich_text__c": {}
]

def (Boolean error, Map data, String errorMessage) = Fx.object.update(objectAPIName, dataId, updateData, UpdateAttribute.builder().triggerWorkflow(false).build())

if (error) {{
    log.error("获取对象异常: " + errorMessage)
}} else {{
    log.info("更新成功: " + data)
}}
""".format(data_id, self._get_current_time(), data_id, text_value_escaped, groovy_data_escaped)
        return script_template
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def process_and_generate(self):
        """
        主处理函数：处理数据并生成Groovy脚本
        """
        print("开始处理数据和生成Groovy脚本...")
        
        # 读取ID列表和数据列表
        ids = self.read_temp_ids()
        data_list = self.read_temp_data()
        
        if not ids:
            print("错误：没有读取到任何ID")
            return
        
        if not data_list:
            print("错误：没有读取到任何数据")
            return
        
        # 处理每个ID对应的数据
        generated_count = 0
        for i, data_id in enumerate(ids):
            if i >= len(data_list):
                print(f"警告：ID {data_id} 没有对应的数据（数据不足）")
                continue
            
            print(f"\n处理ID: {data_id}")
            
            # 提取和转换数据
            text_value, groovy_data = self.extract_and_convert_data(data_list[i])
            
            if text_value is None or groovy_data is None:
                print(f"跳过ID {data_id}：数据提取失败")
                continue
            
            # 生成Groovy脚本
            script_content = self.generate_groovy_script(data_id, text_value, groovy_data)
            
            # 保存脚本文件
            script_filename = f"update_script_{data_id}.groovy"
            script_filepath = os.path.join(self.output_dir, script_filename)
            
            try:
                with open(script_filepath, 'w', encoding='utf-8') as f:
                    f.write(script_content)
                print(f"成功生成脚本: {script_filename}")
                generated_count += 1
            except Exception as e:
                print(f"保存脚本文件时出错: {e}")
        
        print(f"\n处理完成！共生成 {generated_count} 个Groovy脚本文件")


def main():
    """主函数"""
    # 文件路径配置
    temp_file_path = "/Users/<USER>/Desktop/工作文档/03-代码和脚本/data-processing-groovy-scripts/rich_text.text"
    temp_id_file_path = "/Users/<USER>/Desktop/工作文档/03-代码和脚本/data-processing-groovy-scripts/rich_text_id.text"
    output_dir = "/Users/<USER>/Desktop/工作文档/03-代码和脚本/data-processing-groovy-scripts"
    
    # 创建生成器并执行处理
    generator = GroovyScriptGenerator(temp_file_path, temp_id_file_path, output_dir)
    generator.process_and_generate()


if __name__ == "__main__":
    main()
