# 数据处理和Groovy脚本生成工具

## 项目描述
本项目用于处理富文本编辑器数据，并生成对应的Groovy更新脚本。主要功能是将复杂的富文本JSON数据转换为符合Groovy语法的Map格式，并生成可直接执行的更新脚本。

## 功能特性
1. **数据提取**：从temp.text文件中提取每行的extra_info字段数据
2. **格式转换**：将JSON格式转换为Groovy Map格式（双引号->单引号，{}->[]）
3. **文本提取**：提取text字段作为collaborative_rich_text__c__o的值，并正确处理换行符
4. **脚本生成**：根据tempId.text中的ID生成对应的Groovy更新脚本
5. **错误处理**：包含完善的异常处理和日志记录

## 文件说明
- `generate_groovy_scripts.py` - 主要的数据处理和脚本生成工具
- `update_script_*.groovy` - 生成的Groovy更新脚本文件（18个）
- `README.md` - 项目说明文档

## 使用方法
```bash
# 进入项目目录
cd /Users/<USER>/Desktop/工作文档/03-代码和脚本/data-processing-groovy-scripts

# 运行脚本生成工具
python3 generate_groovy_scripts.py

cd "/Users/<USER>/Desktop/工作文档/03-代码和脚本/data-processing-groovy-scripts" && rm -f update_script_*.groovy && python3 generate_groovy_scripts.py
```

## 输入文件
- `/Users/<USER>/Desktop/工作文档/temp.text` - 包含富文本数据的JSON文件（18行数据）
- `/Users/<USER>/Desktop/工作文档/tempId.text` - 包含数据ID的文件（18个ID）

## 输出文件
- `update_script_{dataId}.groovy` - 为每个ID生成的Groovy更新脚本
- 示例：`update_script_67aab823f8ec930001adb2a2.groovy`

## 生成的脚本格式
每个生成的Groovy脚本包含：
```groovy
String objectAPIName = 'LeadsObj'
String dataId = '{具体的ID}'
Map updateData = [
    "collaborative_rich_text__c__o": "{提取的纯文本内容}",
    "collaborative_rich_text__c": {转换后的Groovy格式富文本数据}
]

def (Boolean error, Map data, String errorMessage) = Fx.object.update(objectAPIName, dataId, updateData, UpdateAttribute.builder().triggerWorkflow(false).build())

if (error) {
    log.error("获取对象异常: " + errorMessage)
} else {
    log.info("更新成功: " + data)
}
```

## 技术要求
- Python 3.6+
- JSON处理能力
- 文件读写权限

## 执行结果
✅ 成功处理18个数据ID
✅ 生成18个Groovy更新脚本
✅ 正确转换JSON格式为Groovy Map格式
✅ 正确处理文本换行符转义
✅ 包含完整的错误处理机制
