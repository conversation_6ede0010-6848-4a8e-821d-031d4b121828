# StopWatch日志分析工具

这是一组用于分析Java StopWatch日志的Python脚本，可以按执行时间百分比排序并可视化展示。

## 脚本说明

### 1. stopwatch_sorter.py

通用的StopWatch日志分析工具，支持多种格式的StopWatch日志输入。

**功能**：
- 解析StopWatch日志
- 按百分比排序任务
- 生成可视化图表（需要matplotlib）
- 保存分析结果为CSV文件

**用法**：
```bash
# 从文件读取日志
./stopwatch_sorter.py -f stopwatch_log.txt

# 生成图表
./stopwatch_sorter.py -f stopwatch_log.txt -o chart.png

# 只显示前N个任务
./stopwatch_sorter.py -f stopwatch_log.txt -t 10

# 选择图表类型（饼图或条形图）
./stopwatch_sorter.py -f stopwatch_log.txt -c bar

# 保存为CSV文件
./stopwatch_sorter.py -f stopwatch_log.txt --csv result.csv

# 从标准输入读取日志
cat stopwatch_log.txt | ./stopwatch_sorter.py
```

### 2. analyze_standard_describe_layout.py

专门用于分析StandardDescribeLayoutController的StopWatch日志。

**用法**：
```bash
# 使用示例日志
./analyze_standard_describe_layout.py --example

# 从文件读取日志
./analyze_standard_describe_layout.py -f layout_log.txt

# 生成图表
./analyze_standard_describe_layout.py -f layout_log.txt -o chart.png

# 保存为CSV文件
./analyze_standard_describe_layout.py -f layout_log.txt --csv result.csv
```

### 3. analyze_stopwatch.py

简单的命令行工具，用于直接分析StopWatch日志文本。

**用法**：
```bash
# 直接分析日志文本
./analyze_stopwatch.py "StopWatch 'TestController': running time = 1000 ms; [task1] 500 ms = 50%; [task2] 300 ms = 30%; [task3] 200 ms = 20%;"

# 保存为CSV文件
./analyze_stopwatch.py "StopWatch 'TestController': running time = 1000 ms; [task1] 500 ms = 50%;" --csv result.csv

# 从标准输入读取日志
cat stopwatch_log.txt | ./analyze_stopwatch.py
```

## 依赖

- Python 3.6+
- matplotlib (可选，用于生成图表)

## 安装依赖

```bash
pip install matplotlib
```

## 示例日志格式

这些脚本支持以下格式的StopWatch日志：

```
StopWatch 'ControllerName': running time = 15007 ms; [task1] 3049 ms = 20%; [task2] 8 ms = 0%; ...
```

## 注意事项

1. 如果没有安装matplotlib，脚本仍然可以运行，但无法生成图表。
2. 可以使用CSV选项保存分析结果，即使没有安装matplotlib。
3. 对于大型日志，建议使用`-t`选项只显示前N个任务。
