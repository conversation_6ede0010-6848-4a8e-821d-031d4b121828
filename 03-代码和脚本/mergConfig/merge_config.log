2025-03-13 16:43:08,606 - __main__ - INFO - 处理配置文件: gray-rel-webPage, 目标环境: cloud, 源环境: ['ucd-public-test', 'cloudmodel-public-prod', 'kehua-public-test', 'allink8s-public-prod']
2025-03-13 16:43:08,606 - __main__ - INFO - 获取目标环境 cloud 的配置...
2025-03-13 16:43:08,675 - __main__ - INFO - 已保存原始目标环境配置文件: output/gray-rel-webPage-cloud-original
2025-03-13 16:43:08,675 - __main__ - INFO - 获取源环境 ucd-public-test 的配置...
2025-03-13 16:43:08,711 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-ucd-public-test-original
2025-03-13 16:43:08,712 - __main__ - INFO - 处理键 'default__c-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;707988;'
2025-03-13 16:43:08,712 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list};', 源环境 ucd-public-test 值 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-13 16:43:08,712 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list};' 和 'allow;${variables_journalObj_blackList.ei_comma_list}' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-13 16:43:08,712 - __main__ - INFO - 处理键 'IndexHome-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};'
2025-03-13 16:43:08,712 - __main__ - INFO - 处理键 'CrmToDo-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};'
2025-03-13 16:43:08,712 - __main__ - INFO - 处理键 'CrmRemind-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};'
2025-03-13 16:43:08,712 - __main__ - INFO - 环境 ucd-public-test 变动的配置项: default__c-enterprise, JournalObj-enterprise, IndexHome-enterprise, CrmToDo-enterprise, CrmRemind-enterprise
2025-03-13 16:43:08,712 - __main__ - INFO - 获取源环境 cloudmodel-public-prod 的配置...
2025-03-13 16:43:08,744 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-cloudmodel-public-prod-original
2025-03-13 16:43:08,744 - __main__ - INFO - 处理键 'NewOpportunityObj-url-enterprise': 当前合并配置值 'deny;40010021;', 源环境 cloudmodel-public-prod 值 'deny;40010021;688092;'
2025-03-13 16:43:08,744 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并值列表 'deny;40010021;' 和 'deny;40010021;688092;' 为 'deny;40010021;688092'
2025-03-13 16:43:08,744 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list}', 源环境 cloudmodel-public-prod 值 'allow;${variables_journalObj_blackList.ei_comma_list};'
2025-03-13 16:43:08,744 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list}' 和 'allow;${variables_journalObj_blackList.ei_comma_list};' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-13 16:43:08,744 - __main__ - INFO - 键 'JournalObj-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-13 16:43:08,744 - __main__ - INFO - 环境 cloudmodel-public-prod 变动的配置项: NewOpportunityObj-url-enterprise
2025-03-13 16:43:08,744 - __main__ - INFO - 获取源环境 kehua-public-test 的配置...
2025-03-13 16:43:08,777 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-kehua-public-test-original
2025-03-13 16:43:08,777 - __main__ - INFO - 处理键 'NewOpportunityObj-url-enterprise': 当前合并配置值 'deny;40010021;688092', 源环境 kehua-public-test 值 'deny;40010021;688092;'
2025-03-13 16:43:08,777 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并值列表 'deny;40010021;688092' 和 'deny;40010021;688092;' 为 'deny;40010021;688092'
2025-03-13 16:43:08,777 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-13 16:43:08,778 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list}', 源环境 kehua-public-test 值 'allow;${variables_journalObj_blackList.ei_comma_list};'
2025-03-13 16:43:08,778 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list}' 和 'allow;${variables_journalObj_blackList.ei_comma_list};' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-13 16:43:08,778 - __main__ - INFO - 键 'JournalObj-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-13 16:43:08,778 - __main__ - INFO - 获取源环境 allink8s-public-prod 的配置...
2025-03-13 16:43:08,815 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-allink8s-public-prod-original
2025-03-13 16:43:08,815 - __main__ - INFO - 处理键 'NewOpportunityObj-url-enterprise': 当前合并配置值 'deny;40010021;688092', 源环境 allink8s-public-prod 值 'deny;40010021;688092;'
2025-03-13 16:43:08,815 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并值列表 'deny;40010021;688092' 和 'deny;40010021;688092;' 为 'deny;40010021;688092'
2025-03-13 16:43:08,815 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-13 16:43:08,815 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list}', 源环境 allink8s-public-prod 值 'allow;${variables_journalObj_blackList.ei_comma_list};'
2025-03-13 16:43:08,815 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list}' 和 'allow;${variables_journalObj_blackList.ei_comma_list};' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-13 16:43:08,815 - __main__ - INFO - 键 'JournalObj-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-13 16:43:08,815 - __main__ - INFO - 所有环境变动的配置项汇总:
2025-03-13 16:43:08,815 - __main__ - INFO - 环境 ucd-public-test: default__c-enterprise, JournalObj-enterprise, IndexHome-enterprise, CrmToDo-enterprise, CrmRemind-enterprise
2025-03-13 16:43:08,815 - __main__ - INFO - 环境 cloudmodel-public-prod: NewOpportunityObj-url-enterprise
2025-03-13 16:43:08,815 - __main__ - INFO - 写入配置项: switch=allow;
2025-03-13 16:43:08,815 - __main__ - INFO - 写入配置项: enterprise=allow;
2025-03-13 16:43:08,815 - __main__ - INFO - 写入配置项: CrmInfo-enterprise=deny;
2025-03-13 16:43:08,815 - __main__ - INFO - 写入配置项: rfm_analysis-component-enterprise=deny;590115;729908;
2025-03-13 16:43:08,815 - __main__ - INFO - 写入配置项: contact_member_relationship-component-enterprise=allow;
2025-03-13 16:43:08,815 - __main__ - INFO - 写入配置项: OpportunityObj-enterprise=deny;
2025-03-13 16:43:08,815 - __main__ - INFO - 写入配置项: ParamReview-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: Fmcg_store__c-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ClosedStoreReview-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: shopkeeper-enterprise=deny;684898;590056;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: dealer_yonfer-enterprise=deny;684898;590056;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: shop-enterprise=deny;670624;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: dealer_jinmailangmianpin-enterprise=deny;670624;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ApprovalMonitor-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: CarSaleGuide-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: VisitGuide-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: DealerGuide-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: FirmGuide-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ServiceKnowledgeObj-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: NewOpportunityObj-url-enterprise=deny;40010021;688092
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: filterAll-enterprise=deny;688092;40010021;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: FSAID_11491084-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: JournalObj-enterprise=allow;${variables_journalObj_blackList.ei_comma_list}
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: SalesOrderObjDefault-enterprise=deny;707365;581014;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: record_arbE9__c-enterprise=deny;707365;581014;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: qixin-mainChannel-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: Todo-mainChannel-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: APP_CENTRE-close-menu-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: tool-component-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-sidebar_transfer-dropList-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-sidebar_header-dropList-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: dht_manufacturer-enterprise=deny;540053;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: dht_eiapp-enterprise=deny;540053;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: dht_setting-enterprise=deny;540053;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: QualityDocumentQuery-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: FSAID_11490c84_dht_shopmall_recentorder-component-enterprise=deny;590001;590000;679491;679493;715682;679488;581014;679427;679428;719556;714439;747123;747124;735273;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: PromotionActivity-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: NewWebMainChannel-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: fieldworkInvestigatesetting-enterprise= deny;%1;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: fieldworkDataScreen-enterprise= deny;739657;663116;683722;735454;735463;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: fieldworkCarexamine-enterprise= deny;608158;670624;663085;747438
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: fieldworkExpensecheck-enterprise= deny;608158;670624;fktest1159;747438
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: fieldworkAreaManage-enterprise= deny;751566;754534;748356;755259;748471;749938;755688;752878;752119
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: unityConsultBench-mainChannel-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-eservice_cases_dynamic-dropList-enterprise=deny;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ObjectEditPage-web-form_table-dropList-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ObjectEditPage-web-text_component-dropList-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-form_table-dropList-enterprise=allow;
2025-03-13 16:43:08,816 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-text_component-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectEditPage-web-related_list_form-dropList-enterprise=deny;${variables_fs_gray_product_comma.related_objects_simultaneous_create_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectEditPage-app-related_list_form-dropList-enterprise=deny;${variables_fs_gray_product_comma.related_objects_simultaneous_create_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-approval_component-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-stage_component-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-bpm_component-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-approval_component-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-stage_component-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-bpm_component-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: work_circle-component-enterprise=allow;${variables_journalObj_blackList.ei_comma_list};
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectEditPage-web-order_settlement-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectEditPage-app-order_settlement-dropList-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: CrmServiceManager-enterprise=deny;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: richTextWidget-component-enterprise=deny;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: CreateEnterpriseSwitch-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: crossSchedule-component-enterprise=deny;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_win_first_values_clear-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_business_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_financial_report_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_risk_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_public_opinion_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: crm_assistant_component-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_leads_product_intention-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_leads_feed_summary-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_leads_marketing_summary-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_leads_bant_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_deal_account_case-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_knowledge_recommend-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: sfa_basic_sell_product-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-model_prediction-dropList-enterprise=deny;${variables_func.gray_model_menu};
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-model_prediction-dropList-enterprise=deny;${variables_func.gray_model_menu};
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ReportDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: DataBoardDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: StatThemeMgrDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ReportPermissionMgrDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: contact_path-component-enterprise=allow;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-suspendedComponent-dropList-enterprise=deny;${variables_gray_conf_930.ei_list_format_semicolon};${variables_fs_gray_product_semicolon.suspended_component_gray_ei}
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: default__c-enterprise=deny;707988;
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: IndexHome-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: CrmToDo-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-13 16:43:08,817 - __main__ - INFO - 写入配置项: CrmRemind-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-13 16:43:08,818 - __main__ - INFO - 已保存配置文件: output/gray-rel-webPage-merged
2025-03-13 16:43:08,818 - __main__ - INFO - 已保存变更信息到CSV文件: output/gray-rel-webPage-changes.csv
2025-03-13 16:43:08,900 - __main__ - INFO - 已保存变更信息到Excel文件: output/gray-rel-webPage-changes.xlsx
2025-03-22 00:19:10,074 - __main__ - INFO - 处理配置文件: gray-rel-webPage, 目标环境: cloud, 源环境: ['ucd-public-test', 'cloudmodel-public-prod', 'kehua-public-test', 'allink8s-public-prod']
2025-03-22 00:19:10,074 - __main__ - INFO - 获取目标环境 cloud 的配置...
2025-03-22 00:19:21,358 - __main__ - INFO - 已保存原始目标环境配置文件: output/gray-rel-webPage-cloud-original
2025-03-22 00:19:21,359 - __main__ - INFO - 获取源环境 ucd-public-test 的配置...
2025-03-22 00:19:21,543 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-ucd-public-test-original
2025-03-22 00:19:21,543 - __main__ - INFO - 处理键 'invoice_app_sales_order_multi_related_list-component-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;${variables_gray_conf_950.ei_list_format_semicolon}'
2025-03-22 00:19:21,543 - __main__ - INFO - 处理键 'default__c-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;707988;'
2025-03-22 00:19:21,543 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list};', 源环境 ucd-public-test 值 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-22 00:19:21,543 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list};' 和 'allow;${variables_journalObj_blackList.ei_comma_list}' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-22 00:19:21,543 - __main__ - INFO - 处理键 'IndexHome-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};'
2025-03-22 00:19:21,544 - __main__ - INFO - 处理键 'CrmToDo-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};'
2025-03-22 00:19:21,544 - __main__ - INFO - 处理键 'CrmRemind-enterprise': 当前合并配置中不存在，添加源环境 ucd-public-test 的值 'deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};'
2025-03-22 00:19:21,544 - __main__ - INFO - 环境 ucd-public-test 变动的配置项: invoice_app_sales_order_multi_related_list-component-enterprise, default__c-enterprise, JournalObj-enterprise, IndexHome-enterprise, CrmToDo-enterprise, CrmRemind-enterprise
2025-03-22 00:19:21,544 - __main__ - INFO - 获取源环境 cloudmodel-public-prod 的配置...
2025-03-22 00:19:21,720 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-cloudmodel-public-prod-original
2025-03-22 00:19:21,720 - __main__ - INFO - 处理键 'NewOpportunityObj-url-enterprise': 当前合并配置值 'deny;40010021;', 源环境 cloudmodel-public-prod 值 'deny;40010021;688092;'
2025-03-22 00:19:21,720 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并值列表 'deny;40010021;' 和 'deny;40010021;688092;' 为 'deny;40010021;688092'
2025-03-22 00:19:21,720 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list}', 源环境 cloudmodel-public-prod 值 'allow;${variables_journalObj_blackList.ei_comma_list};'
2025-03-22 00:19:21,720 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list}' 和 'allow;${variables_journalObj_blackList.ei_comma_list};' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-22 00:19:21,720 - __main__ - INFO - 键 'JournalObj-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-22 00:19:21,720 - __main__ - INFO - 环境 cloudmodel-public-prod 变动的配置项: NewOpportunityObj-url-enterprise
2025-03-22 00:19:21,720 - __main__ - INFO - 获取源环境 kehua-public-test 的配置...
2025-03-22 00:19:21,899 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-kehua-public-test-original
2025-03-22 00:19:21,899 - __main__ - INFO - 处理键 'NewOpportunityObj-url-enterprise': 当前合并配置值 'deny;40010021;688092', 源环境 kehua-public-test 值 'deny;40010021;688092;'
2025-03-22 00:19:21,899 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并值列表 'deny;40010021;688092' 和 'deny;40010021;688092;' 为 'deny;40010021;688092'
2025-03-22 00:19:21,899 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-22 00:19:21,899 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list}', 源环境 kehua-public-test 值 'allow;${variables_journalObj_blackList.ei_comma_list};'
2025-03-22 00:19:21,899 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list}' 和 'allow;${variables_journalObj_blackList.ei_comma_list};' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-22 00:19:21,899 - __main__ - INFO - 键 'JournalObj-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-22 00:19:21,899 - __main__ - INFO - 获取源环境 allink8s-public-prod 的配置...
2025-03-22 00:19:22,074 - __main__ - INFO - 已保存原始源环境配置文件: output/gray-rel-webPage-allink8s-public-prod-original
2025-03-22 00:19:22,074 - __main__ - INFO - 处理键 'NewOpportunityObj-url-enterprise': 当前合并配置值 'deny;40010021;688092', 源环境 allink8s-public-prod 值 'deny;40010021;688092;'
2025-03-22 00:19:22,075 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并值列表 'deny;40010021;688092' 和 'deny;40010021;688092;' 为 'deny;40010021;688092'
2025-03-22 00:19:22,075 - __main__ - INFO - 键 'NewOpportunityObj-url-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-22 00:19:22,075 - __main__ - INFO - 处理键 'JournalObj-enterprise': 当前合并配置值 'allow;${variables_journalObj_blackList.ei_comma_list}', 源环境 allink8s-public-prod 值 'allow;${variables_journalObj_blackList.ei_comma_list};'
2025-03-22 00:19:22,075 - __main__ - INFO - 键 'JournalObj-enterprise': 合并值列表 'allow;${variables_journalObj_blackList.ei_comma_list}' 和 'allow;${variables_journalObj_blackList.ei_comma_list};' 为 'allow;${variables_journalObj_blackList.ei_comma_list}'
2025-03-22 00:19:22,075 - __main__ - INFO - 键 'JournalObj-enterprise': 合并后的值与原值相同，不记录为变更
2025-03-22 00:19:22,075 - __main__ - INFO - 所有环境变动的配置项汇总:
2025-03-22 00:19:22,075 - __main__ - INFO - 环境 ucd-public-test: invoice_app_sales_order_multi_related_list-component-enterprise, default__c-enterprise, JournalObj-enterprise, IndexHome-enterprise, CrmToDo-enterprise, CrmRemind-enterprise
2025-03-22 00:19:22,075 - __main__ - INFO - 环境 cloudmodel-public-prod: NewOpportunityObj-url-enterprise
2025-03-22 00:19:22,075 - __main__ - INFO - 写入配置项: switch=allow;
2025-03-22 00:19:22,075 - __main__ - INFO - 写入配置项: enterprise=allow;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: CrmInfo-enterprise=deny;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: rfm_analysis-component-enterprise=deny;590115;729908;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: contact_member_relationship-component-enterprise=allow;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: OpportunityObj-enterprise=deny;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: ParamReview-enterprise=deny;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: Fmcg_store__c-enterprise=deny;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: ClosedStoreReview-enterprise=deny;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: shopkeeper-enterprise=deny;684898;590056;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: dealer_yonfer-enterprise=deny;684898;590056;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: shop-enterprise=deny;670624;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: dealer_jinmailangmianpin-enterprise=deny;670624;
2025-03-22 00:19:22,076 - __main__ - INFO - 写入配置项: ApprovalMonitor-enterprise=deny;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: CarSaleGuide-enterprise=deny;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: VisitGuide-enterprise=deny;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: DealerGuide-enterprise=deny;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: FirmGuide-enterprise=deny;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: ServiceKnowledgeObj-enterprise=deny;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: NewOpportunityObj-url-enterprise=deny;40010021;688092
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: filterAll-enterprise=deny;688092;40010021;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: FSAID_11491084-enterprise=allow;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: JournalObj-enterprise=allow;${variables_journalObj_blackList.ei_comma_list}
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: SalesOrderObjDefault-enterprise=deny;707365;581014;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: record_arbE9__c-enterprise=deny;707365;581014;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: qixin-mainChannel-enterprise=allow;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: Todo-mainChannel-enterprise=allow;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: APP_CENTRE-close-menu-enterprise=allow;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: tool-component-enterprise=allow;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-sidebar_transfer-dropList-enterprise=allow;
2025-03-22 00:19:22,077 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-sidebar_header-dropList-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: dht_manufacturer-enterprise=deny;540053;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: dht_eiapp-enterprise=deny;540053;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: dht_setting-enterprise=deny;540053;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: QualityDocumentQuery-enterprise=deny;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: FSAID_11490c84_dht_shopmall_recentorder-component-enterprise=deny;590001;590000;679491;679493;715682;679488;581014;679427;679428;719556;714439;747123;747124;735273;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: PromotionActivity-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: NewWebMainChannel-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: fieldworkInvestigatesetting-enterprise= deny;%1;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: fieldworkDataScreen-enterprise= deny;739657;663116;683722;735454;735463;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: fieldworkCarexamine-enterprise= deny;608158;670624;663085;747438
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: fieldworkExpensecheck-enterprise= deny;608158;670624;fktest1159;747438
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: fieldworkAreaManage-enterprise= deny;751566;754534;748356;755259;748471;749938;755688;752878;752119
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: unityConsultBench-mainChannel-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-eservice_cases_dynamic-dropList-enterprise=deny;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: ObjectEditPage-web-form_table-dropList-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: ObjectEditPage-web-text_component-dropList-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-form_table-dropList-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-text_component-dropList-enterprise=allow;
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: ObjectEditPage-web-related_list_form-dropList-enterprise=deny;${variables_fs_gray_product_comma.related_objects_simultaneous_create_ei}
2025-03-22 00:19:22,078 - __main__ - INFO - 写入配置项: ObjectEditPage-app-related_list_form-dropList-enterprise=deny;${variables_fs_gray_product_comma.related_objects_simultaneous_create_ei}
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-approval_component-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-stage_component-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-bpm_component-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-approval_component-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-stage_component-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-bpm_component-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: work_circle-component-enterprise=allow;${variables_journalObj_blackList.ei_comma_list};
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectEditPage-web-order_settlement-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: ObjectEditPage-app-order_settlement-dropList-enterprise=allow;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: CrmServiceManager-enterprise=deny;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: richTextWidget-component-enterprise=deny;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: CreateEnterpriseSwitch-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: crossSchedule-component-enterprise=deny;
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: sfa_win_first_values_clear-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: sfa_basic_business_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: sfa_financial_report_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,079 - __main__ - INFO - 写入配置项: sfa_risk_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,080 - __main__ - INFO - 写入配置项: sfa_public_opinion_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,080 - __main__ - INFO - 写入配置项: crm_assistant_component-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,080 - __main__ - INFO - 写入配置项: sfa_basic_leads_product_intention-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,080 - __main__ - INFO - 写入配置项: sfa_basic_leads_feed_summary-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,080 - __main__ - INFO - 写入配置项: sfa_basic_leads_marketing_summary-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,080 - __main__ - INFO - 写入配置项: sfa_basic_leads_bant_insights-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: sfa_basic_deal_account_case-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: sfa_basic_knowledge_recommend-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: sfa_basic_sell_product-component-enterprise=deny;${variables_fs_gray_product_semicolon.gray_fs_share_ai_ei}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: ObjectDetailPage-web-model_prediction-dropList-enterprise=deny;${variables_func.gray_model_menu};
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-model_prediction-dropList-enterprise=deny;${variables_func.gray_model_menu};
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: ReportDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: DataBoardDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: StatThemeMgrDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: ReportPermissionMgrDownStream-enterprise=deny;${variables_gray_config_bi.down_stream_support_bi_eilist_format_semicolon}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: contact_path-component-enterprise=allow;
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: ObjectDetailPage-app-suspendedComponent-dropList-enterprise=deny;${variables_gray_conf_930.ei_list_format_semicolon};${variables_fs_gray_product_semicolon.suspended_component_gray_ei}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: invoice_app_sales_order_multi_related_list-component-enterprise=deny;${variables_gray_conf_950.ei_list_format_semicolon}
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: default__c-enterprise=deny;707988;
2025-03-22 00:19:22,081 - __main__ - INFO - 写入配置项: IndexHome-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-22 00:19:22,082 - __main__ - INFO - 写入配置项: CrmToDo-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-22 00:19:22,082 - __main__ - INFO - 写入配置项: CrmRemind-enterprise=deny;${variables_crm_migrate_tenants.goNewCrmEnterpriseIds};
2025-03-22 00:19:22,082 - __main__ - INFO - 已保存配置文件: output/gray-rel-webPage-merged
2025-03-22 00:19:22,082 - __main__ - INFO - 已保存变更信息到CSV文件: output/gray-rel-webPage-changes.csv
2025-03-22 00:19:22,268 - __main__ - INFO - 已保存变更信息到Excel文件: output/gray-rel-webPage-changes.xlsx
