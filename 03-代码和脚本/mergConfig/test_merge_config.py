#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import unittest
import os
import tempfile
import json
import yaml
from unittest.mock import patch, MagicMock
from merge_config import parse_config, merge_configs, save_configs, merge_value_lists, load_config, process_batch

class TestMergeConfig(unittest.TestCase):
    """测试合并配置工具"""

    def test_parse_config(self):
        """测试解析配置文件"""
        config_text = """# 注释1
key1=value1
# 注释2
key2=value2
key3=value3
"""
        config_dict, comments = parse_config(config_text)
        
        # 测试配置字典
        self.assertEqual(config_dict, {
            'key1': 'value1',
            'key2': 'value2',
            'key3': 'value3'
        })
        
        # 测试注释
        self.assertEqual(comments, {
            'key1': ['# 注释1'],
            'key2': ['# 注释2']
        })

    def test_merge_value_lists(self):
        """测试合并值列表功能"""
        # 测试逗号分隔的列表
        self.assertEqual(merge_value_lists("value1,value2", "value1,value3"), "value1,value2,value3")
        
        # 测试分号分隔的列表
        self.assertEqual(merge_value_lists("value1;value2", "value1;value3"), "value1;value2;value3")
        
        # 测试空格分隔的列表
        self.assertEqual(merge_value_lists("value1 value2", "value1 value3"), "value1 value2 value3")
        
        # 测试竖线分隔的列表
        self.assertEqual(merge_value_lists("value1|value2", "value1|value3"), "value1|value2|value3")
        
        # 测试不同分隔符的列表
        self.assertEqual(merge_value_lists("value1,value2", "value3"), "value1,value2,value3")
        
        # 测试重复值
        self.assertEqual(merge_value_lists("value1,value2", "value2,value3"), "value1,value2,value3")
        
        # 测试没有分隔符的情况
        self.assertEqual(merge_value_lists("value1", "value2"), "value2")
        
        # 测试特殊情况：目标环境值为allow;
        self.assertEqual(merge_value_lists("allow;", "value1;value2"), "allow;")
        
        # 测试特殊情况：目标环境值为deny;
        self.assertEqual(merge_value_lists("deny;", "value1;value2"), "deny;")
        
        # 测试特殊情况：源环境值为allow;
        self.assertEqual(merge_value_lists("value1;value2", "allow;"), "allow;")
        
        # 测试特殊情况：源环境值为deny;
        self.assertEqual(merge_value_lists("value1;value2", "deny;"), "deny;")

    @patch('merge_config.fetch_config')
    def test_merge_configs(self, mock_fetch_config):
        """测试合并配置"""
        # 模拟目标环境配置
        base_config = """# 注释1
key1=value1
# 注释2
key2=value2
key3=value3
key4=value1,value2
"""
        # 模拟源环境配置
        other_config = """# 注释1
key1=value1
# 注释2修改版
key2=newvalue2
key3=value3
key4=value1,value3
key5=value5
"""
        
        # 设置模拟返回值
        mock_fetch_config.side_effect = lambda profile, name, token, api_url=None: {
            'fstest': base_config,
            'fktest': other_config
        }[profile]
        
        # 调用合并配置函数
        merged_configs = merge_configs('fstest', ['fktest'], 'gray-rel-webPage', 'token')
        
        # 验证结果
        self.assertIn('gray-rel-webPage-fktest', merged_configs)
        merged_config, merged_comments = merged_configs['gray-rel-webPage-fktest']
        
        # 验证合并后的配置
        self.assertEqual(merged_config['key1'], 'value1')
        self.assertEqual(merged_config['key2'], 'newvalue2')
        self.assertEqual(merged_config['key3'], 'value3')
        self.assertEqual(merged_config['key4'], 'value1,value2,value3')  # 测试值列表合并
        self.assertEqual(merged_config['key5'], 'value5')
        
        # 验证合并后的注释
        self.assertEqual(merged_comments['key1'], ['# 注释1'])
        self.assertEqual(merged_comments['key2'], ['# 注释2修改版'])

    def test_save_configs(self):
        """测试保存配置文件"""
        # 准备测试数据
        merged_configs = {
            'test-file': ({
                'key1': 'value1',
                'key2': 'value2',
                'key3': 'value3',
                'key4': 'value1,value2,value3'
            }, {
                'key1': ['# 注释1'],
                'key2': ['# 注释2']
            })
        }
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 切换到临时目录
            original_dir = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # 保存配置
                save_configs(merged_configs)
                
                # 验证文件是否创建
                self.assertTrue(os.path.exists('test-file'))
                
                # 读取文件内容
                with open('test-file', 'r') as f:
                    content = f.read()
                
                # 验证文件内容
                self.assertIn('# 注释1', content)
                self.assertIn('key1=value1', content)
                self.assertIn('# 注释2', content)
                self.assertIn('key2=value2', content)
                self.assertIn('key3=value3', content)
                self.assertIn('key4=value1,value2,value3', content)
            finally:
                # 切回原目录
                os.chdir(original_dir)

    def test_load_config_json(self):
        """测试加载JSON配置文件"""
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试配置文件
            config_file = os.path.join(temp_dir, 'config.json')
            config_data = {
                "api_url": "http://test.example.com/api",
                "token": "test_token",
                "output_dir": "test_output",
                "tasks": [
                    {
                        "base_profile": "test_base",
                        "profiles": ["test_profile1", "test_profile2"],
                        "names": ["test_name1", "test_name2"]
                    }
                ]
            }
            
            with open(config_file, 'w') as f:
                json.dump(config_data, f)
            
            # 加载配置文件
            config = load_config(config_file)
            
            # 验证配置
            self.assertEqual(config['api_url'], "http://test.example.com/api")
            self.assertEqual(config['token'], "test_token")
            self.assertEqual(config['output_dir'], "test_output")
            self.assertEqual(len(config['tasks']), 1)
            self.assertEqual(config['tasks'][0]['base_profile'], "test_base")
            self.assertEqual(config['tasks'][0]['profiles'], ["test_profile1", "test_profile2"])
            self.assertEqual(config['tasks'][0]['names'], ["test_name1", "test_name2"])

    def test_load_config_yaml(self):
        """测试加载YAML配置文件"""
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试配置文件
            config_file = os.path.join(temp_dir, 'config.yaml')
            config_data = """
api_url: http://test.example.com/api
token: test_token
output_dir: test_output
tasks:
  - base_profile: test_base
    profiles:
      - test_profile1
      - test_profile2
    names:
      - test_name1
      - test_name2
"""
            
            with open(config_file, 'w') as f:
                f.write(config_data)
            
            # 加载配置文件
            config = load_config(config_file)
            
            # 验证配置
            self.assertEqual(config['api_url'], "http://test.example.com/api")
            self.assertEqual(config['token'], "test_token")
            self.assertEqual(config['output_dir'], "test_output")
            self.assertEqual(len(config['tasks']), 1)
            self.assertEqual(config['tasks'][0]['base_profile'], "test_base")
            self.assertEqual(config['tasks'][0]['profiles'], ["test_profile1", "test_profile2"])
            self.assertEqual(config['tasks'][0]['names'], ["test_name1", "test_name2"])

    @patch('merge_config.merge_configs')
    @patch('merge_config.save_configs')
    def test_process_batch(self, mock_save_configs, mock_merge_configs):
        """测试批量处理配置文件"""
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试配置文件
            config_file = os.path.join(temp_dir, 'config.json')
            config_data = {
                "api_url": "http://test.example.com/api",
                "token": "test_token",
                "output_dir": "test_output",
                "tasks": [
                    {
                        "base_profile": "test_base",
                        "profiles": ["test_profile1", "test_profile2"],
                        "names": ["test_name1", "test_name2"]
                    }
                ]
            }
            
            with open(config_file, 'w') as f:
                json.dump(config_data, f)
            
            # 设置模拟返回值
            mock_merge_configs.return_value = {"test_result": ({}, {})}
            
            # 调用批量处理函数
            process_batch(config_file)
            
            # 验证调用次数
            self.assertEqual(mock_merge_configs.call_count, 2)  # 2个名称
            self.assertEqual(mock_save_configs.call_count, 2)  # 2个名称
            
            # 验证调用参数
            mock_merge_configs.assert_any_call(
                "test_base", ["test_profile1", "test_profile2"], 
                "test_name1", "test_token", "http://test.example.com/api", "test_output"
            )
            mock_merge_configs.assert_any_call(
                "test_base", ["test_profile1", "test_profile2"], 
                "test_name2", "test_token", "http://test.example.com/api", "test_output"
            )

if __name__ == '__main__':
    unittest.main() 