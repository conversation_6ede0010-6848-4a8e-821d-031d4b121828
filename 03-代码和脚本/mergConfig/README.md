# 配置合并工具

这个工具用于合并不同环境的配置数据，将指定环境的数据作为目标数据，将源环境中不同的配置项合并到目标数据中，生成新的配置文件。

## 功能特点

- 获取指定环境的配置数据作为目标数据
- 获取源环境的配置数据
- 比较并合并不同环境中value不同的配置项
- 合并源环境中有而目标环境中没有的配置项
- 智能合并以分隔符分隔的值列表（支持逗号、分号、竖线、空格等分隔符）
- 特殊处理 `allow;` 和 `deny;` 值，不进行合并
- 保留配置项的注释信息
- 生成新的配置文件，命名为 `${文件名}-${环境}`
- 支持通过配置文件批量处理多个环境和多个配置文件
- 支持自定义API地址，适应不同环境
- 支持自定义输出目录
- 提供详细的日志记录
- **输出每个环境变动的配置项**，清晰展示合并过程中的变化

## 合并规则

1. 以指定的目标环境配置作为基础数据
2. 对于源环境中与目标环境相同key但value不同的配置项：
   - 如果值是 `allow;` 或 `deny;`，不进行合并，保留原值
   - 如果值是以分隔符分隔的列表（如逗号、分号、竖线、空格），会智能合并两个环境中的值列表，去除重复项
   - 如果值不是列表，则使用源环境的value
3. 对于源环境中有而目标环境中没有的配置项，添加到合并后的配置中
4. 保留所有配置项的注释信息

## 使用方法

### 安装依赖

```bash
pip install requests pyyaml
```

### 命令行方式

```bash
python merge_config.py --base <目标环境> --profiles <源环境,用逗号分隔> --name <配置文件名> --token <API令牌> [--api-url <API地址>] [--output-dir <输出目录>]
```

#### 参数说明

- `--base`: 目标环境名称，即要被合并到的环境，如 firstshare
- `--profiles`: 源环境名称，即要从这些环境合并配置的环境，用逗号分隔，如 fstest,dev,test
- `--name`: 配置文件名称，如 gray-rel-webPage
- `--token`: API访问令牌
- `--api-url`: API地址，默认为 http://oss.firstshare.cn/cs/api/get/
- `--output-dir`: 输出目录，默认为当前目录

#### 示例

```bash
# 将fstest环境的配置合并到firstshare环境
python merge_config.py --base firstshare --profiles fstest --name gray-rel-webPage --token 2E983C21F7E2212E4181E5E2A28EC54DA83C1AE084D901B0B5131DC1FF86F70C --output-dir output
```

### 配置文件方式

```bash
python merge_config.py --config <配置文件路径>
```

#### 配置文件格式

支持YAML和JSON格式的配置文件。

YAML格式示例：

```yaml
# API地址，可以根据环境切换
api_url: http://oss.firstshare.cn/cs/api/get/

# API访问令牌
token: 2E983C21F7E2212E4181E5E2A28EC54DA83C1AE084D901B0B5131DC1FF86F70C

# 输出目录
output_dir: output

# 任务列表
tasks:
  # 任务1：将firstshare环境的配置合并到fstest环境
  - base_profile: firstshare
    profiles:
      - fstest
    names:
      - gray-rel-webPage
      - gray-rel-appPage

  # 任务2：将fstest环境的配置合并到firstshare环境
  - base_profile: firstshare
    profiles:
      - fstest
    names:
      - gray-rel-webPage
```

JSON格式示例：

```json
{
  "api_url": "http://oss.firstshare.cn/cs/api/get/",
  "token": "2E983C21F7E2212E4181E5E2A28EC54DA83C1AE084D901B0B5131DC1FF86F70C",
  "output_dir": "output",
  "tasks": [
    {
      "base_profile": "firstshare",
      "profiles": ["fstest"],
      "names": ["gray-rel-webPage", "gray-rel-appPage"]
    },
    {
      "base_profile": "firstshare",
      "profiles": ["fstest"],
      "names": ["gray-rel-webPage"]
    }
  ]
}
```

#### 示例

```bash
python merge_config.py --config config.yaml
```

或

```bash
python merge_config.py --config config.json
```

## 输出示例

### 变动配置项输出

在合并过程中，工具会输出每个环境变动的配置项，包括修改的值和新增的配置项：

```
环境 fstest 变动的配置项:
  - key1: 'value1' -> 'value1,value3'
  - key2: 'value2' -> 'newvalue2'
  - key4: 新增 'value4'

所有环境变动的配置项汇总:
环境 fstest: key1, key2, key4
```

### 基本合并

假设目标环境 firstshare 的配置为：

```
# 这是注释1
key1=value1
# 这是注释2
key2=value2
key3=value3
```

源环境 fstest 的配置为：

```
# 这是注释1
key1=value1
# 这是注释2修改版
key2=newvalue2
key3=value3
key4=value4
```

合并后生成的 `gray-rel-webPage-fstest-merged` 文件内容为：

```
# 这是注释1
key1=value1
# 这是注释2修改版
key2=newvalue2
key3=value3
key4=value4
```

### 值列表合并

假设目标环境 firstshare 的配置为：

```
# 这是注释1
key1=value1,value2
# 这是注释2
key2=value2
```

源环境 fstest 的配置为：

```
# 这是注释1
key1=value1,value3
# 这是注释2
key2=newvalue2
```

合并后生成的 `gray-rel-webPage-fstest-merged` 文件内容为：

```
# 这是注释1
key1=value1,value2,value3
# 这是注释2
key2=newvalue2
```

### 特殊值处理

假设目标环境 firstshare 的配置为：

```
# 这是注释1
key1=allow;
# 这是注释2
key2=value2
```

源环境 fstest 的配置为：

```
# 这是注释1
key1=value1;value3
# 这是注释2
key2=deny;
```

合并后生成的 `gray-rel-webPage-fstest-merged` 文件内容为：

```
# 这是注释1
key1=allow;
# 这是注释2
key2=deny;
```

## 注意事项

- 脚本会保留配置项的注释信息
- 只有当源环境中的配置项与目标环境不同时，才会生成新的合并配置文件
- 如果源环境中没有与目标环境不同的配置项，则不会生成对应的合并配置文件
- 脚本会处理源环境中有而目标环境中没有的配置项
- 脚本会智能识别并合并以分隔符分隔的值列表，支持的分隔符包括逗号、分号、竖线和空格
- 对于值为 `allow;` 或 `deny;` 的特殊情况，脚本不会进行合并，而是保留原值
- 脚本会生成详细的日志文件 `merge_config.log`，方便排查问题 