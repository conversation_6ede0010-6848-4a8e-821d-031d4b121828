#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import argparse
import os
import json
import yaml
import logging
import csv
from collections import defaultdict

# 添加pandas库用于生成Excel文件
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logging.warning("pandas库未安装，无法生成Excel文件。请使用pip install pandas安装。")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("merge_config.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config(config_file):
    """加载配置文件"""
    if not os.path.exists(config_file):
        logger.error(f"配置文件 {config_file} 不存在")
        return None
    
    try:
        if config_file.endswith('.json'):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif config_file.endswith('.yaml') or config_file.endswith('.yml'):
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        else:
            logger.error(f"不支持的配置文件格式: {config_file}")
            return None
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return None

def fetch_config(profile, name, token, api_url=None):
    """获取指定环境的配置数据"""
    if api_url is None:
        api_url = 'http://oss.firstshare.cn/cs/api/get/'
    
    url = f'{api_url}?token={token}&profile={profile}&name={name}'
    try:
        response = requests.post(url)
        if response.status_code != 200:
            raise Exception(f"获取配置失败: {response.status_code} - {response.text}")
        return response.text
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise

def parse_config(config_text):
    """解析配置文本为字典"""
    config_dict = {}
    comments = {}
    current_comment = []
    
    for line in config_text.splitlines():
        line = line.strip()
        if not line:
            continue
        
        if line.startswith('#'):
            current_comment.append(line)
        elif '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            config_dict[key] = value
            
            # 保存该键对应的注释
            if current_comment:
                comments[key] = current_comment
                current_comment = []
    
    return config_dict, comments

def merge_value_lists(base_value, other_value, key=None):
    """合并以特定分隔符分隔的值列表"""
    # 特殊情况处理：如果值为allow;或deny;，不进行合并
    if base_value in ["allow;", "deny;"]:
        logger.info(f"键 '{key}': 检测到特殊值 '{base_value}'，不进行合并，保留目标环境的值")
        return base_value, True  # 返回值和一个标志，表示这是特殊值处理
    if other_value in ["allow;", "deny;"]:
        logger.info(f"键 '{key}': 检测到特殊值 '{other_value}'，不进行合并，使用源环境的值")
        return other_value, True  # 返回值和一个标志，表示这是特殊值处理
    
    # 检查是否包含分隔符
    for sep in [',', ';', '|', ' ']:
        if sep in base_value:
            # 找到分隔符
            base_items = [item.strip() for item in base_value.split(sep)]
            other_items = [item.strip() for item in other_value.split(sep)] if sep in other_value else [other_value]
            
            # 合并列表，保留顺序，去除重复项
            merged_items = []
            for item in base_items + other_items:
                if item and item not in merged_items:
                    merged_items.append(item)
            
            # 使用相同的分隔符连接
            result = sep.join(merged_items)
            if key:
                logger.info(f"键 '{key}': 合并值列表 '{base_value}' 和 '{other_value}' 为 '{result}'")
            return result, False  # 返回值和一个标志，表示这不是特殊值处理
    
    # 如果没有找到分隔符，直接返回源环境的值
    if key:
        logger.info(f"键 '{key}': 没有找到分隔符，使用源环境的值 '{other_value}'")
    return other_value, False  # 返回值和一个标志，表示这不是特殊值处理

def merge_configs(base_profile, profiles, name, token, api_url=None, output_dir=None):
    """合并不同环境的配置"""
    # 获取目标环境配置
    logger.info(f"获取目标环境 {base_profile} 的配置...")
    base_config_text = fetch_config(base_profile, name, token, api_url)
    base_config, base_comments = parse_config(base_config_text)
    
    # 保存原始目标环境配置
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存原始目标环境配置文件
    base_file_path = os.path.join(output_dir, f"{name}-{base_profile}-original") if output_dir else f"{name}-{base_profile}-original"
    with open(base_file_path, 'w', encoding='utf-8') as f:
        f.write(base_config_text)
    logger.info(f"已保存原始目标环境配置文件: {base_file_path}")
    
    # 记录所有环境变动的配置项
    changed_keys = {}
    
    # 记录详细的变更信息，用于生成CSV
    changes_data = []
    
    # 当前合并后的配置，初始为目标环境配置
    current_config = base_config.copy()
    current_comments = base_comments.copy()
    
    # 按顺序依次合并每个源环境的配置
    for profile in profiles:
        if profile == base_profile:
            continue
        logger.info(f"获取源环境 {profile} 的配置...")
        try:
            config_text = fetch_config(profile, name, token, api_url)
            config, comments = parse_config(config_text)
            
            # 保存原始环境配置文件
            profile_file_path = os.path.join(output_dir, f"{name}-{profile}-original") if output_dir else f"{name}-{profile}-original"
            with open(profile_file_path, 'w', encoding='utf-8') as f:
                f.write(config_text)
            logger.info(f"已保存原始源环境配置文件: {profile_file_path}")
            
            # 记录当前环境变动的配置项
            profile_changed_keys = []
            
            # 处理源环境中与当前合并配置相同key但value不同的情况
            for key, value in config.items():
                if key in current_config:
                    if current_config[key] != value:
                        # 如果当前合并配置中有此key但value不同，尝试合并值列表
                        logger.info(f"处理键 '{key}': 当前合并配置值 '{current_config[key]}', 源环境 {profile} 值 '{value}'")
                        old_value = current_config[key]
                        merged_value, is_special_value = merge_value_lists(current_config[key], value, key)
                        
                        # 如果是特殊值处理，记录为"特殊值保留"
                        if is_special_value:
                            # 记录变动的配置项
                            profile_changed_keys.append(key)
                            # 记录详细变更信息
                            changes_data.append({
                                'name': name,
                                'profile': profile,
                                'key': key,
                                'change_type': '特殊值保留',
                                'old_value': old_value,
                                'new_value': merged_value,
                                'source_value': value  # 添加源环境的值
                            })
                            # 如果有注释，也一并更新
                            if key in comments:
                                current_comments[key] = comments[key]
                            current_config[key] = merged_value
                        # 只有当合并后的值与原值不同时，才记录为变更
                        elif merged_value != old_value:
                            current_config[key] = merged_value
                            # 记录变动的配置项
                            profile_changed_keys.append(key)
                            # 记录详细变更信息
                            changes_data.append({
                                'name': name,
                                'profile': profile,
                                'key': key,
                                'change_type': '改动',
                                'old_value': old_value,
                                'new_value': merged_value,
                                'source_value': value  # 添加源环境的值
                            })
                            # 如果有注释，也一并更新
                            if key in comments:
                                current_comments[key] = comments[key]
                        else:
                            logger.info(f"键 '{key}': 合并后的值与原值相同，不记录为变更")
                else:
                    # 处理源环境中有而当前合并配置中没有的key的情况
                    logger.info(f"处理键 '{key}': 当前合并配置中不存在，添加源环境 {profile} 的值 '{value}'")
                    current_config[key] = value
                    # 记录变动的配置项
                    profile_changed_keys.append(key)
                    # 记录详细变更信息
                    changes_data.append({
                        'name': name,
                        'profile': profile,
                        'key': key,
                        'change_type': '新增',
                        'old_value': '',
                        'new_value': value,
                        'source_value': value  # 添加源环境的值
                    })
                    # 如果有注释，也一并添加
                    if key in comments:
                        current_comments[key] = comments[key]
            
            # 保存变动的配置项
            if profile_changed_keys:
                changed_keys[profile] = profile_changed_keys
                # 输出变动的配置项汇总
                logger.info(f"环境 {profile} 变动的配置项: {', '.join(profile_changed_keys)}")
                print(f"\n环境 {profile} 变动的配置项:")
                for key in profile_changed_keys:
                    if key in base_config:
                        print(f"  - {key}: '{base_config[key]}' -> '{current_config[key]}'")
                    else:
                        print(f"  - {key}: 新增 '{current_config[key]}'")
            
        except Exception as e:
            logger.error(f"获取源环境 {profile} 的配置失败: {e}")
            continue
    
    # 输出所有环境变动的配置项汇总
    if changed_keys:
        logger.info("所有环境变动的配置项汇总:")
        print("\n所有环境变动的配置项汇总:")
        for profile, keys in changed_keys.items():
            logger.info(f"环境 {profile}: {', '.join(keys)}")
            print(f"环境 {profile}: {', '.join(keys)}")
    else:
        logger.info("没有发现任何环境的配置项变动")
        print("\n没有发现任何环境的配置项变动")
    
    # 返回最终合并后的配置，使用一个统一的文件名
    merged_configs = {f"{name}-merged": (current_config, current_comments)}
    return merged_configs, changes_data

def save_changes_to_csv_and_excel(changes_data, name, output_dir=None):
    """将变更信息保存到CSV和Excel文件中"""
    if not changes_data:
        logger.info("没有变更信息需要保存")
        return
    
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    csv_file_path = os.path.join(output_dir, f"{name}-changes.csv") if output_dir else f"{name}-changes.csv"
    excel_file_path = os.path.join(output_dir, f"{name}-changes.xlsx") if output_dir else f"{name}-changes.xlsx"
    
    # 定义列名映射（英文列名 -> 带中文描述的列名）
    column_mapping = {
        'name': '配置名称（name）',
        'profile': '源环境名称（profile）',
        'key': '配置项（key）',
        'change_type': '变更类型（change_type）',
        'old_value': '目标环境原值（old_value）',
        'new_value': '合并后的值（new_value）',
        'source_value': '源环境的值（source_value）'
    }
    
    try:
        # 保存为CSV文件
        with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
            # 使用带中文描述的列名
            fieldnames = list(column_mapping.values())
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for change in changes_data:
                # 创建一个新的字典，使用带中文描述的列名
                row = {}
                for eng_key, cn_key in column_mapping.items():
                    row[cn_key] = change[eng_key]
                writer.writerow(row)
        
        logger.info(f"已保存变更信息到CSV文件: {csv_file_path}")
        
        # 保存为Excel文件
        if PANDAS_AVAILABLE:
            # 使用pandas创建DataFrame
            df = pd.DataFrame(changes_data)
            # 重命名列，添加中文描述
            df = df.rename(columns=column_mapping)
            # 设置列顺序
            df = df[list(column_mapping.values())]
            # 保存为Excel文件
            df.to_excel(excel_file_path, index=False, engine='openpyxl')
            logger.info(f"已保存变更信息到Excel文件: {excel_file_path}")
        else:
            logger.warning("未安装pandas库，无法生成Excel文件。请使用pip install pandas安装。")
    except Exception as e:
        logger.error(f"保存变更信息到文件失败: {e}")

def save_configs(merged_configs, output_dir=None):
    """保存合并后的配置到文件"""
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    for filename, (config, comments) in merged_configs.items():
        file_path = os.path.join(output_dir, filename) if output_dir else filename
        with open(file_path, 'w', encoding='utf-8') as f:
            # 按照原始配置的顺序写入
            for key, value in config.items():
                # 先写入注释（如果有）
                if key in comments:
                    for comment_line in comments[key]:
                        f.write(f"{comment_line}\n")
                # 再写入配置项
                f.write(f"{key}={value}\n")
                logger.info(f"写入配置项: {key}={value}")
        logger.info(f"已保存配置文件: {file_path}")

def process_batch(config_file):
    """批量处理配置文件"""
    config = load_config(config_file)
    if not config:
        return
    
    api_url = config.get('api_url', 'http://oss.firstshare.cn/cs/api/get/')
    token = config.get('token')
    output_dir = config.get('output_dir', 'output')
    
    if not token:
        logger.error("配置文件中缺少token")
        return
    
    # 处理每个任务
    tasks = config.get('tasks', [])
    for task in tasks:
        base_profile = task.get('base_profile')
        profiles = task.get('profiles', [])
        names = task.get('names', [])
        
        if not base_profile or not profiles or not names:
            logger.error(f"任务配置不完整: {task}")
            continue
        
        for name in names:
            try:
                logger.info(f"处理配置文件: {name}, 目标环境: {base_profile}, 源环境: {profiles}")
                # 链式合并多个环境的配置
                merged_configs, changes_data = merge_configs(base_profile, profiles, name, token, api_url, output_dir)
                save_configs(merged_configs, output_dir)
                # 保存变更信息到CSV和Excel
                save_changes_to_csv_and_excel(changes_data, name, output_dir)
            except Exception as e:
                logger.error(f"处理配置文件 {name} 失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='合并不同环境的配置数据')
    parser.add_argument('--base', help='目标环境名称')
    parser.add_argument('--profiles', help='源环境名称，用逗号分隔')
    parser.add_argument('--name', help='配置文件名称')
    parser.add_argument('--token', help='API访问令牌')
    parser.add_argument('--api-url', help='API地址')
    parser.add_argument('--output-dir', help='输出目录')
    parser.add_argument('--config', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 如果提供了配置文件，则使用配置文件
    if args.config:
        process_batch(args.config)
        return
    
    # 否则使用命令行参数
    if not args.base or not args.profiles or not args.name or not args.token:
        parser.print_help()
        return
    
    profiles = args.profiles.split(',')
    merged_configs, changes_data = merge_configs(args.base, profiles, args.name, args.token, args.api_url, args.output_dir)
    save_configs(merged_configs, args.output_dir)
    # 保存变更信息到CSV和Excel
    save_changes_to_csv_and_excel(changes_data, args.name, args.output_dir)

if __name__ == "__main__":
    main() 