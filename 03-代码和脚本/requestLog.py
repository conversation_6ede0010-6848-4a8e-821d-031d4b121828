import requests
import time
from datetime import datetime, timedel<PERSON>



def format_curl_request(st, et, table_id, query_conditions):
    url = f'https://log.foneshare.cn/api/v1/tables/{table_id}/logs'
    
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'priority': 'u=1, i',
        'referer': 'https://log.foneshare.cn/query/',
        'sec-ch-ua': '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0'
    }
    
    params = {
        'st': st,
        'et': et,
        'query': query_conditions,
        'pageSize': 100,
        'page': 1,
        'isQueryCount': 0
    }
    
    return url, headers, params

def convert_to_timestamp(date_str):
    """
    将日期字符串转换为时间戳
    支持格式：'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS'
    """
    try:
        # 尝试解析完整的日期时间格式
        dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        # 如果失败，尝试仅解析日期
        dt = datetime.strptime(date_str, '%Y-%m-%d')
    return int(dt.timestamp())

def parse_timestamp(time_str):
    """
    解析时间戳字符串，支持多种格式
    """
    try:
        # 如果是数字类型的时间戳
        if isinstance(time_str, (int, float)):
            return datetime.fromtimestamp(time_str)
        # 如果是ISO格式的时间字符串
        elif isinstance(time_str, str):
            # 移除时区信息并解析
            time_str = time_str.split('+')[0]
            return datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%S.%f')
    except Exception as e:
        print(f"解析时间戳出错: {e}")
        return datetime.now()  # 返回当前时间作为后备选项

def search_logs(query_conditions, start_date, days_to_search=7, by_hour=False, table_id=278, interval_count=1):
    """
    从指定日期开始，向前查询指定天数的日志
    :param query_conditions: 查询条件字符串
    :param start_date: 开始日期，格式：'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS'
    :param days_to_search: 需要向前查询的天数
    :param by_hour: 是否按小时查询，默认为 False（按天查询）
    :param table_id: 日志表格 ID
    :param interval_count: 查询间隔的天数或小时数，默认为 1
    """
    current_st = convert_to_timestamp(start_date)
    day_seconds = 24 * 60 * 60
    hour_seconds = 60 * 60
    
    interval = (hour_seconds if by_hour else day_seconds) * interval_count
    total_intervals = days_to_search * (24 if by_hour else 1) // interval_count
    
    for i in range(total_intervals):
        query_st = current_st - (i * interval)
        query_et = query_st + interval
        
        url, headers, params = format_curl_request(query_st, query_et, table_id, query_conditions)
        
        try:
            response = requests.get(url, headers=headers, params=params)
            data = response.json()
            
            st_time = datetime.fromtimestamp(query_st)
            et_time = datetime.fromtimestamp(query_et)
            
            current_unit = i + 1
            if by_hour:
                current_unit = f"第 {(i // (24 // interval_count)) + 1} 天的第 {(i % (24 // interval_count)) + 1} 小时"
            else:
                current_unit = f"第 {current_unit} 次"
            
            print(f"\n正在查询{current_unit}:")
            print(f"时间范围：{st_time} 到 {et_time}")
            
            if isinstance(data, list) and len(data) > 0:
                print(f"✅ 找到日志！")
                print(f"日志数量：{len(data)}")
                print(data)
            elif isinstance(data, dict) and data.get('data', {}).get('logs'):
                print(f"✅ 找到日志！")
                print(f"日志数量：{len(data['data']['logs'])}")
                for log in data['data']['logs']:
                    print(log)
                    if '17199' in log['msg']:
                        log_time = parse_timestamp(log.get('_time_second_'))
                        print(f"时间: {log_time}")
                        print(f"消息: {log['msg']}\n")
            else:
                print(f"❌ 未找到日志")
                
        except Exception as e:
            print(f"请求出错: {e}")
            print(f"响应内容: {response.text}")
            continue
        
        # 添加等待时间，避免接口掉挂
        time.sleep(1)  # 等待 1 秒

        # 创建日志类型与表格 ID 的映射
log_table_mapping = {
    'app_log': 278,
    'op_log': 124,
    'paas_auth':218,
    'tomcat_log':279,
    'metadata_log':96
}

if __name__ == "__main__":
    # 示例使用：指定开始日期、查询天数和查询模式
    query_conditions=" id = '66de995d9e74af000147859b'"
    search_logs(query_conditions, '2024-12-02 17:59:14', 60, by_hour=False, table_id=log_table_mapping['op_log'], interval_count=15)  # 按小时查询
    # search_logs('2025-01-03 03:00:24', 3, table_id=log_table_mapping['op_log'])  # 按天查询
    


