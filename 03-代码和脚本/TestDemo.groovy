/**
 * <AUTHOR>
 * @codeName 【报价单明细】价格计算
 * @description 【报价单明细】价格计算
 * @createTime 2024-09-10
 * @函数需求编号
 */
String field_trade_mode__c = context.data.field_trade_mode__c as String // 贸易方式
String field_trade_termination_1__c = context.data.field_trade_termination_1__c as String // 终端市场
String account_id = context.data.account_id as String // 客户名称

List QuoteLinesObjList = context.details.QuoteLinesObj as List 
log.info("当前页面所有的报价明细：" + QuoteLinesObjList.size() + QuoteLinesObjList['product_id'])

List price_book_product_idList = QuoteLinesObjList.grep{it['product_id'] != null}
log.info("产品：" + price_book_product_idList['product_id'])


def ProductObjList = Fx.object.findByIds("ProductObj", //
             price_book_product_idList['product_id'] as List,
             FQLAttribute.builder()
                .columns(["_id","name",
                'is_principal__c',
                'product_category_id',
                'product_primary_category__c', 
                'product_second_category__c',
                 'is_package',
                ]) //返回的数据id
                .build(),
             SelectAttribute.builder()
                .build()).result() as List;
// 按产品一级分类  
Map productMap = [:]
ProductObjList.each { product ->
  String product_primary_category__c = product['product_primary_category__c']

  if( productMap.containsKey(product_primary_category__c) ){
    List primaryList  = productMap.get(product_primary_category__c) as List
    primaryList.add(product)
    productMap.put(product_primary_category__c, primaryList)
  }else{
    productMap.put(product_primary_category__c, [product])
  }
}
log.info("产品分类汇总：" + productMap)

// 主件不超过4件
List delIds = []
boolean tips = false
productMap.each{Object key, entry ->
  List productList = entry as List
  List masterProduct = productList.grep { x -> x['is_principal__c'] == '0' }
  if( masterProduct.size() > 99 ){
    delIds.addAll(productList['_id'])
    tips = true
  }
}
log.info("tips==${tips}");
UIEvent event = UIEvent.build(context) {
  if( tips ){
    remind Remind.Alert("同型号产品超出99套报价数量、请重新选择")
    removeDetail "QuoteLinesObj" where { x -> x["product_id"] in delIds }
  } 
  // 计算价格
  else {
    boolean is_continue = true
    boolean is_blocked = false
    log.info("ProductObjList-size=" + ProductObjList.size());
    ProductObjList.each { product ->
      log.info("产品信息：" + product)
      String is_principal__c = product['is_principal__c']
      BigDecimal priceRes = 0
      String product_id = product['_id']
      // 主件产品计算 基准价格
      if( is_principal__c != null && is_principal__c == '0') {
        if( is_continue ){
          
           // 基准价格
          String category__c = product['product_second_category__c']
          def product_base_price__cMap = Fx.object.findOne("product_base_price__c", //
                       FQLAttribute.builder()
                          .columns(["_id", "name",'field_We2f1__c']) //返回的数据id
                          .queryTemplate( QueryTemplate.AND(
                            ["find_association_category__c": QueryOperator.EQ(category__c)],
                          ))
                          .build(),
                       SelectAttribute.builder()
                          .build()).result() as Map;
          log.info("查询到基准定价：" + product_base_price__cMap)
          if( product_base_price__cMap == null ){
            remind Remind.Alert("无基准款定价，请维护基准定价后再报价")
            is_continue = false
            is_blocked = true
            return
          }
          priceRes += product_base_price__cMap['field_We2f1__c'] as BigDecimal
          
          // 属性增量价格
          String product_primary_category__c = product['product_primary_category__c'] 
          String product_second_category__c = product['product_second_category__c']
          // 获取报价明细
          Map lineMap = price_book_product_idList.find{line -> (line['product_id'] as String ) == product_id}
          
          String attribute = lineMap['attribute'] 
          log.info("当前产品属性：" + attribute)
          if( attribute == null || attribute == '' ){
            remind Remind.Alert("当前产品属性为空，请维护后再报价")
            is_continue = false
            return
          }
          // if( attribute == null || attribute == '' ){
          //           //赋值产品物料编码
          //   editMaster("search_association_product__c": product_id)
           
          // }
          List attrs = attribute.split(';') as List
          log.info("attrs==" + Fx.json.toJson(attrs));
          List attrsNameList = [];
          List attrsValueList = [];
          attrs.each{ attr ->
            log.info("属性：" + attr)
            String[] fields = (attr as String).split(":")
            String attrName = fields[0]
            String attrValue = fields[1]
            attrsNameList.add(attrName);
            attrsValueList.add(attrValue);
          }
          log.info("attrsNameList=${attrsNameList},attrsValueList=${attrsValueList}");
          
          //查询属性对象
          List attributeList = [];
          def attributeSql = "select _id, name from AttributeObj where name in ('${attrsNameList.join('\',\'')}')";
          log.info("attributeSql=${attributeSql}");
          Fx.object.select(attributeSql, SelectAttribute.builder().build(), { list->
            attributeList.addAll(list);
          }).result();
          log.info("属性列表=" + Fx.json.toJson(attributeList));
          
          List attrsNameIdList = attributeList["_id"] as List;
          //查询属性值对象
          List attributeValueList = [];
          def attributeValueSql = "select _id, name,attribute_id from AttributeValueObj where name in ('${attrsValueList.join('\',\'')}') and attribute_id in ('${attrsNameIdList.join('\',\'')}')";
          log.info("attributeValueSql=${attributeValueSql}");
          Fx.object.select(attributeValueSql, SelectAttribute.builder().build(), { list->
            attributeValueList.addAll(list);
          }).result();
          log.info("属性值列表" + Fx.json.toJson(attributeValueList));
          //把attributeList对应id的name放到attributeValueList中，命名为attributeName
          // 创建ID映射字典（提升查询效率）
          def attributeMap = attributeList.collectEntries { [((it as Map)._id): (it as Map).name] }
          
          // 添加attributeName字段（精确匹配）
          attributeValueList.each { item ->
              (item as Map).attributeName = attributeMap[(item as Map).attribute_id];
          }
          log.info("attributeValueList=" + Fx.json.toJson(attributeValueList));
          
          attributeValueList.each{ attributeValueItem ->
            Map attributeValueItemMap = attributeValueItem as Map;
            //等价于
            //AttributeObjMap['_id']   == attribute_id
            //AttributeValueObjMap['_id'] == _id
            //attrName == attributeName
                      
            QueryTemplate que = QueryTemplate.AND(["field_property__c": QueryOperator.EQ(attributeValueItemMap['attribute_id'] as String)],
              ["field_UcsEH__c": QueryOperator.EQ(attributeValueItemMap['_id'] as String)],
              ["field_product_category__c": QueryOperator.EQ(product_primary_category__c)],
            )
            log.info("que==" + que);
            QueryTemplate second = QueryTemplate.AND(["field_product_second_category__c": QueryOperator.EQ(product_second_category__c)])
            QueryTemplate third = QueryTemplate.AND(["field_product_second_category__c": QueryOperator.EXISTS(false)])
            
            def object_demo_incremental_pricing__cMap = Fx.object.findOne("object_demo_incremental_pricing__c", //
                        FQLAttribute.builder()
                            .columns(["_id", "name",'field_54ciY__c']) //返回的数据id
                            .queryTemplate(QueryTemplate.AND(que, second) )
                            .build(),
                        SelectAttribute.builder()
                            .build()).result() as Map;
            if( object_demo_incremental_pricing__cMap == null ){
              log.info("二级分类没有找到增量定价， 修改条件：" + que)
              object_demo_incremental_pricing__cMap = Fx.object.findOne("object_demo_incremental_pricing__c", //
                        FQLAttribute.builder()
                            .columns(["_id", "name",'field_54ciY__c']) //返回的数据id
                            .queryTemplate(QueryTemplate.AND(que, third) )
                            .build(),
                        SelectAttribute.builder()
                            .build()).result() as Map;
              log.info("属性增量定价: " + object_demo_incremental_pricing__cMap)
              if( object_demo_incremental_pricing__cMap == null ){
                String attrNameValue = attributeValueItemMap["attributeName"] as String;
                remind Remind.Alert("${attrNameValue}-无属性定价，请维护属性定价后再报价")
                is_continue = false
                is_blocked = true
                return 
              }
              
              priceRes += object_demo_incremental_pricing__cMap['field_54ciY__c'] as BigDecimal
            } else {
              log.info("属性增量定价: " + object_demo_incremental_pricing__cMap)
              priceRes += object_demo_incremental_pricing__cMap['field_54ciY__c'] as BigDecimal
            }
          }
          
          // 按条件设置价格
          if( is_continue == false ){
            editDetail "QuoteLinesObj" set("field_nIF2w__c": null) where { x -> (x["product_id"] as String) == product_id }
          } else {
            editDetail "QuoteLinesObj" set("field_nIF2w__c": priceRes) where { x -> (x["product_id"] as String) == product_id }
          }
        }
      }
      
      if( is_principal__c == null || is_principal__c == '1') {
        Map QuoteLinesObjMap =  QuoteLinesObjList.find{item ->item['product_id'] == product_id}
        // log.debug("明细：" + QuoteLinesObjMap['name'])
        // String price_book_product_id = QuoteLinesObjMap['price_book_product_id'] as String
        // if( price_book_product_id == null ){
        //   log.info("明细的价目表为空")
        //   return 
        // }
        // def PriceBookProductObjMap = Fx.object.findById("PriceBookProductObj", 
        //             price_book_product_id,
        //             FQLAttribute.builder()
        //                 .columns(["_id","name",'field_W5m19__c','pricebook_sellingprice']) //返回的数据id
        //                 .build(),
        //             SelectAttribute.builder()
        //                 .build()).result() as Map;
        // log.debug("配件价目表：" + PriceBookProductObjMap)
        // BigDecimal field_W5m19__c = PriceBookProductObjMap['field_W5m19__c'] as BigDecimal
        BigDecimal price = QuoteLinesObjMap['price'] as BigDecimal
        priceRes = price == null ? 0: price
        editDetail "QuoteLinesObj" set("field_nIF2w__c": priceRes) where { x -> (x["product_id"] as String) == product_id }
      }
      // 战略价
      log.debug("开始查询战略价格")
      QueryTemplate scustomer__cQuery = QueryTemplate.AND(['customer__c': QueryOperator.EQ(account_id)])
      QueryTemplate trade_mode__cQuery = QueryTemplate.AND(['trade_mode__c': QueryOperator.EQ(field_trade_mode__c)])
      QueryTemplate terminal_market__cQuery = QueryTemplate.AND(['terminal_market__c': QueryOperator.EQ(field_trade_termination_1__c)])
      
      List standard_strategic_price__cList = []
      BigDecimal field_X2YY3__c = 0
      // 全条件查询
      QueryTemplate all = QueryTemplate.AND(scustomer__cQuery,trade_mode__cQuery,terminal_market__cQuery)
      def standard_strategic_price__cMap1 = Fx.object.findOne("standard_strategic_price__c", //查询战略价
                   FQLAttribute.builder()
                      .columns(["_id", "name",'strategic_discount__c']) //返回的数据id
                      .queryTemplate( all )
                      .orderBy(["strategic_discount__c": 1])
                      .build(),
                   SelectAttribute.builder()
                      .build()).result() as Map;
      if( standard_strategic_price__cMap1 != null ){
        log.info("全条件查询到的战略折扣：" + standard_strategic_price__cMap1)
        BigDecimal strategic_discount__c = (standard_strategic_price__cMap1['strategic_discount__c'] as BigDecimal) / 100
        field_X2YY3__c = priceRes * strategic_discount__c
      } 
      else { //战略价暂时不使用
        field_X2YY3__c = priceRes 
        /*
        // 条件两两匹配
        QueryTemplate sub1 = QueryTemplate.AND(scustomer__cQuery,trade_mode__cQuery,QueryTemplate.AND(['terminal_market__c': QueryOperator.EXISTS(false)]))
        def standard_strategic_price__cMap2 = Fx.object.findOne("standard_strategic_price__c", //查询战略价
                     FQLAttribute.builder()
                        .columns(["_id", "name",'strategic_discount__c']) //返回的数据id
                        .queryTemplate( sub1  )
                        .orderBy(["strategic_discount__c": 1])
                        .build(),
                     SelectAttribute.builder()
                        .build()).result() as Map;
        if( standard_strategic_price__cMap2 != null ){
          standard_strategic_price__cList.add(standard_strategic_price__cMap2)
        }
        QueryTemplate sub2 = QueryTemplate.AND(scustomer__cQuery,terminal_market__cQuery,QueryTemplate.AND(['trade_mode__c': QueryOperator.EXISTS(false)]))
        def standard_strategic_price__cMap3 = Fx.object.findOne("standard_strategic_price__c", //查询战略价
                     FQLAttribute.builder()
                        .columns(["_id", "name",'strategic_discount__c']) //返回的数据id
                        .queryTemplate( sub2 )
                        .orderBy(["strategic_discount__c": 1])
                        .build(),
                     SelectAttribute.builder()
                        .build()).result() as Map;
        if( standard_strategic_price__cMap3 != null ){
          standard_strategic_price__cList.add(standard_strategic_price__cMap3)
        }
        QueryTemplate sub3 = QueryTemplate.AND(trade_mode__cQuery,terminal_market__cQuery,QueryTemplate.AND(['customer__c': QueryOperator.EXISTS(false)]))
        def standard_strategic_price__cMap4 = Fx.object.findOne("standard_strategic_price__c", //查询战略价
                     FQLAttribute.builder()
                        .columns(["_id", "name",'strategic_discount__c']) //返回的数据id
                        .queryTemplate( sub3 )
                        .orderBy(["strategic_discount__c": 1])
                        .build(),
                     SelectAttribute.builder()
                        .build()).result() as Map;
        if( standard_strategic_price__cMap4 != null ){
          standard_strategic_price__cList.add(standard_strategic_price__cMap4)
        }
        
        if( standard_strategic_price__cList.size() > 0 ){
          log.info("条件两两匹配查询到的战略折扣：" + standard_strategic_price__cList)
          standard_strategic_price__cList.sort(new java.util.Comparator() {
              @Override
              public int compare(Object obj1, Object obj2) {
                BigDecimal field_l314m1 = obj1['strategic_discount__c'] as BigDecimal 
                BigDecimal field_l314m2 = obj2['strategic_discount__c'] as BigDecimal 
                  return (field_l314m1 - field_l314m2) as Integer;
              }
          });
          Map standard_strategic_price__cMap = standard_strategic_price__cList[0] as Map
          log.info("=========" + standard_strategic_price__cMap)
          BigDecimal strategic_discount__c = (standard_strategic_price__cMap['strategic_discount__c'] as BigDecimal) / 100
          priceRes = priceRes == null ? 0 : priceRes
          field_X2YY3__c = priceRes * strategic_discount__c
        }
        else {
          // 单个条件匹配
          scustomer__cQuery = QueryTemplate.AND(['customer__c': QueryOperator.EQ(account_id)],
            ['trade_mode__c': QueryOperator.EXISTS(false)],
            ['terminal_market__c': QueryOperator.EXISTS(false)]
          )
          def standard_strategic_price__cMap5 = Fx.object.findOne("standard_strategic_price__c", //查询战略价
                       FQLAttribute.builder()
                          .columns(["_id", "name",'strategic_discount__c']) //返回的数据id
                          .queryTemplate( scustomer__cQuery )
                          .orderBy(["strategic_discount__c": 1])
                          .build(),
                       SelectAttribute.builder()
                          .build()).result() as Map;
          if( standard_strategic_price__cMap5 != null ){
            standard_strategic_price__cList.add(standard_strategic_price__cMap5)
          }
          trade_mode__cQuery = QueryTemplate.AND(['trade_mode__c': QueryOperator.EQ(field_trade_mode__c)],
            ['customer__c': QueryOperator.EXISTS(false)],
            ['terminal_market__c': QueryOperator.EXISTS(false)]
          )
          def standard_strategic_price__cMap6 = Fx.object.findOne("standard_strategic_price__c", //查询战略价
                       FQLAttribute.builder()
                          .columns(["_id", "name",'strategic_discount__c']) //返回的数据id
                          .queryTemplate( trade_mode__cQuery )
                          .orderBy(["strategic_discount__c": 1])
                          .build(),
                       SelectAttribute.builder()
                          .build()).result() as Map;
          log.debug("---------------"+ trade_mode__cQuery)
          log.debug("---------------"+ standard_strategic_price__cMap6)
          if( standard_strategic_price__cMap6 != null ){
            standard_strategic_price__cList.add(standard_strategic_price__cMap6)
          }
          terminal_market__cQuery = QueryTemplate.AND(['terminal_market__c': QueryOperator.EQ(field_trade_termination_1__c)],
            ['trade_mode__c': QueryOperator.EXISTS(false)],
            ['customer__c': QueryOperator.EXISTS(false)]
          )
          def standard_strategic_price__cMap7 = Fx.object.findOne("standard_strategic_price__c", //查询战略价
                       FQLAttribute.builder()
                          .columns(["_id", "name",'strategic_discount__c']) //返回的数据id
                          .queryTemplate( terminal_market__cQuery )
                          .orderBy(["strategic_discount__c": 1])
                          .build(),
                       SelectAttribute.builder()
                          .build()).result() as Map;
          if( standard_strategic_price__cMap7 != null ){
            standard_strategic_price__cList.add(standard_strategic_price__cMap7)
          }
          
          if( standard_strategic_price__cList.size() > 0 ){
            log.info("单个条件匹配查询到的战略折扣：" + standard_strategic_price__cList)
            standard_strategic_price__cList.sort(new java.util.Comparator() {
                @Override
                public int compare(Object obj1, Object obj2) {
                  BigDecimal field_l314m1 = obj1['strategic_discount__c'] as BigDecimal 
                  BigDecimal field_l314m2 = obj2['strategic_discount__c'] as BigDecimal 
                    return (field_l314m1 - field_l314m2) as Integer;
                }
            });
            Map standard_strategic_price__cMap = standard_strategic_price__cList[0] as Map
            log.info("=========" + standard_strategic_price__cMap)
            BigDecimal strategic_discount__c = (standard_strategic_price__cMap['strategic_discount__c'] as BigDecimal) / 100
            priceRes = priceRes == null ? 0 : priceRes
            field_X2YY3__c = priceRes * strategic_discount__c
          } else {
            
             if( priceRes ==null ){
              field_X2YY3__c = 0
             }else {
               field_X2YY3__c = priceRes * 1
             }
           }
        }*/
      }
      
      editDetail "QuoteLinesObj" set("field_X2YY3__c": field_X2YY3__c) where { x -> (x["product_id"] as String) == product_id }
      
      
      // 标准价
      BigDecimal price = 0
       BigDecimal field_1s9m5__c = 0  //销售标准价
        BigDecimal field_1s9m5__c_rounded = 0  //标准售价RMB
      if( field_trade_mode__c == '1' ){
        price = field_X2YY3__c
        field_1s9m5__c_rounded = field_X2YY3__c
      } else {
        def tax_rate_andMarkup_details__cMap = Fx.object.findOne("tax_rate_andMarkup_details__c", //查询加价系数
                     FQLAttribute.builder()
                        .columns(["_id", "name",
                        'field_cWuQI__c',
                        'field_qZmxk__c',
                        'is_deduct_chinese_tax__c',
                        'tax_rate__c',
                        ]) //返回的数据id
                        .queryTemplate( QueryTemplate.AND(['trade_method__c': QueryOperator.EQ(field_trade_mode__c)]) )
                        .build(),
                     SelectAttribute.builder()
                        .build()).result() as Map;
        log.info("查询到的加价系数：" + tax_rate_andMarkup_details__cMap)
        if ( tax_rate_andMarkup_details__cMap == null) {
          price = priceRes * 1
          field_1s9m5__c = priceRes * 1
        } else {
          boolean is_deduct_chinese_tax__c = tax_rate_andMarkup_details__cMap["is_deduct_chinese_tax__c"] as boolean
          BigDecimal field_cWuQI__c = tax_rate_andMarkup_details__cMap["field_cWuQI__c"] as BigDecimal
          BigDecimal field_qZmxk__c = tax_rate_andMarkup_details__cMap["field_qZmxk__c"] as BigDecimal
          String tax_rate__c = tax_rate_andMarkup_details__cMap["tax_rate__c"] as String
          BigDecimal china_tax_rate__c = 0
          if( is_deduct_chinese_tax__c ){
            def tax_rate_and_standard_pric__cMap = Fx.object.findById("tax_rate_and_standard_pric__c", //税率
                         tax_rate__c,
                         FQLAttribute.builder()
                            .columns(["_id","name",'china_tax_rate__c']) //返回的数据id
                            .build(),
                         SelectAttribute.builder()
                            .build()).result() as Map;
            china_tax_rate__c = tax_rate_and_standard_pric__cMap["china_tax_rate__c"] as BigDecimal
          }
          price = field_X2YY3__c / ( 1+ china_tax_rate__c / 100) * field_cWuQI__c  * (1 + field_qZmxk__c/100)
           field_1s9m5__c = field_X2YY3__c / ( 1+ china_tax_rate__c / 100) * field_cWuQI__c  * (1 + field_qZmxk__c/100)
            field_1s9m5__c_rounded = field_1s9m5__c.setScale(4, BigDecimal.ROUND_HALF_UP) as BigDecimal   //四舍五入保留4位小数
          log.info("计算公式：price = ${field_X2YY3__c} / (1+${china_tax_rate__c} / 100) * ${field_cWuQI__c} * (1+ ${field_qZmxk__c} / 100)")
          
        }
      }
      // editDetail "QuoteLinesObj" set("price": price) where { x -> (x["product_id"] as String) == product_id }
      editDetail "QuoteLinesObj" set("field_1s9m5__c": field_1s9m5__c_rounded) where { x -> (x["product_id"] as String) == product_id }
      
      if( is_blocked ){
        editMaster("is_pricing_set__c": "0")
      }else {
        editMaster("is_pricing_set__c": "1")
      }
    }
    
  }
  doCalculate(true)
  // 产品分类为线束时 备注字段必填 
   editDetailFields "QuoteLinesObj" fieldApiName("remark__c") hidden(false) readOnly(false) required(true) where { item -> (item["field_z60v1__c"]as String) =="67c54dd4b916a30001fc1cc0" }
     
}


// 获取从对象的数据
// List QuoteLinesObjList = context.details.QuoteLinesObj as List 
List QuoteLinesObjList1 = event.getCurrentAddDetail() as List
QuoteLinesObjList1.each {
  item ->
  Map itemMap = item as Map
  // 获取组合
  String primary_category_get__c = itemMap["primary_category_get__c"] as String
  String is_package= itemMap["is_package"] as String
  log.info("------是否组合"+is_package)

  // 获取报价属性
  String a= itemMap["attribute"] as String
  log.info("------属性"+a)
    // 获取报价编码
  String b = itemMap["product_id"] as String
   log.info("------报价编码="+b)
   
  // String field_L8215__c=item["field_L8215__c"] as String
  //     itemMap.put("field_yx1m3__c",field_L8215__c)
   
  if ( a == null && primary_category_get__c !="67c54dd4b916a30001fc1cc0"  ) {
    log.info("内容")
    // item["search_association_product__c"] = b
    itemMap.put("search_association_product__c",b)
    
  }
}

// is_package == "否" && 
return event