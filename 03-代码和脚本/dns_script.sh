#!/bin/bash

# 获取Wi-Fi服务名称
WIFI_SERVICE="Wi-Fi"
echo "Wi-Fi service name: $WIFI_SERVICE"

# 获取当前Wi-Fi的DNS地址
echo "Getting DNS IP..."
DNS_IP=$(networksetup -getdnsservers "$WIFI_SERVICE")

# 如果获取DNS失败或返回无效结果，使用scutil --dns作为备用方案
if [[ -z "$DNS_IP" || "$DNS_IP" == *"There aren't any DNS Servers set"* ]]; then
  echo "Failed to get DNS using networksetup, trying scutil --dns..."
  # 修改awk脚本，专门查找IPv4地址
  DNS_IP=$(scutil --dns | awk '
    /nameserver\[[0-9]+\] : [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/ {
      print $3
      exit
    }
  ')
fi

# 如果仍然无法获取DNS，使用默认的DNS地址
if [ -z "$DNS_IP" ]; then
  echo "Failed to get DNS IP, using default DNS *******..."
  DNS_IP="*******"
fi

echo "DNS IP: $DNS_IP" 

# 获取当前IP地址 - 修改为只获取Wi-Fi接口的IP地址
echo "Getting current IP..."
CURRENT_IP=$(ifconfig en0 | grep "inet " | awk '{print $2}' | head -1)
echo "Current IP: $CURRENT_IP"

# 修改/etc/resolver/stbvpn.firstshare.cn文件中的nameserver地址
echo "Updating /etc/resolver/stbvpn.firstshare.cn..."
echo "nameserver $DNS_IP" | sudo tee /etc/resolver/stbvpn.firstshare.cn > /dev/null
echo "Updated /etc/resolver/stbvpn.firstshare.cn"

# 设置名字为STB的DNS地址为此Wi-Fi提取的DNS的IP地址
echo "Setting DNS servers for STB..."
sudo networksetup -setdnsservers STB $DNS_IP
echo "Set DNS servers for STB"

# 设置路由地址
echo "Setting additional routes for STB..."
sudo networksetup -setadditionalroutes STB ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ************ *************** $CURRENT_IP
echo "Set additional routes for STB"

# 输出成功
echo "Success"