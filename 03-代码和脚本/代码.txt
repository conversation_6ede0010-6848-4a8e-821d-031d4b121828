[
  {
    "id": "custom_variable##str",
    "label": "字符串",
    "type": "text",
    "objectApiName": "",
    "default_value": "",
    "elementType": "",
    "elementObjectApiName": ""
  },
  {
    "id": "custom_variable##num",
    "label": "数值",
    "type": "number",
    "objectApiName": "",
    "default_value": "",
    "elementType": "",
    "elementObjectApiName": ""
  },
  {
    "id": "custom_variable##boolean",
    "label": "布尔值",
    "type": "boolean",
    "objectApiName": "",
    "default_value": false,
    "elementType": "",
    "elementObjectApiName": ""
  },
  {
    "id": "custom_variable##list",
    "label": "数组",
    "type": "list",
    "objectApiName": "",
    "default_value": "",
    "elementType": "text",
    "elementObjectApiName": ""
  },
  {
    "id": "custom_variable##map",
    "label": "Map类型",
    "type": "map",
    "dictDescribe": [
      {
        "id": "strKey",
        "label": "字符串",
        "type": "text"
      },
      {
        "id": "numKey",
        "label": "数字",
        "type": "number"
      }
    ]
  },
  {
    "id": "custom_variable##objectSingle",
    "label": "对象",
    "type": "object",
    "objectApiName": "object_solitude__c"
  },
  {
    "id": "custom_variable##objectUI1",
    "label": "fj-UI事件从1",
    "type": "list",
    "elementType": "object",
    "elementObjectApiName": "object_7wQM3__c"
  },
  {
    "id": "custom_variable##employee",
    "label": "人员",
    "type": "list",
    "fieldType": "employee"
  },
  {
    "id": "custom_variable##department",
    "label": "部门",
    "type": "list",
    "fieldType": "department_many"
  },
  {
    "id": "custom_variable##employees",
    "label": "人员",
    "type": "list",
    "fieldType": "employee_many"
  },
  {
    "id": "custom_variable##departmentsigle",
    "label": "部门单选",
    "type": "list",
    "fieldType": "department"
  },
  {
    "id": "custom_variable##object_many_list",
    "label": "对象多选",
    "type": "list",
    "elementType": "object",
    "elementObjectApiName": "AccountObj"
  },
  {
    "id": "custom_variable##d_list",
    "label": "部门",
    "type": "list",
    "fieldType": "department"
  },
  {
    "id": "custom_variable##d_many_list",
    "label": "部门多选",
    "type": "list",
    "fieldType": "department_many"
  },
  {
    "id": "custom_variable##e_list",
    "label": "人员",
    "type": "list",
    "fieldType": "employee"
  },
  {
    "id": "custom_variable##e_amny_list",
    "label": "人员多选",
    "type": "list",
    "fieldType": "department_many"
  },
  {
    "id": "custom_variable##obj_list_many",
    "label": "对象多选",
    "type": "list",
    "elementType": "object",
    "elementObjectApiName": "AccountObj"
  },
  {
    "id": "custom_variable##tes",
    "label": "对象test",
    "type": "object",
    "objectApiName": "AccountObj"
  },
  {
    "id": "custom_variable##display",
    "label": "fj-可用显示",
    "type": "object",
    "objectApiName": "object_o6elx__c"
  }
]


{
  "button": "{\"describe_api_name\":\"object_solitude__c\",\"api_name\":\"button_73c2C__c\",\"label\":\"业务按钮\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"detail\",\"list\",\"list_batch\"],\"wheres\":[],\"param_form\":[{\"is_required\":false,\"object_api_name\":\"object_solitude__c\",\"api_name\":\"form_attachment__c\",\"label\":\"附件\",\"type\":\"file_attachment\"},{\"default_to_zero\":false,\"is_required\":false,\"api_name\":\"form_field_MG5c7__c\",\"remark\":\"\",\"default_value\":\"\",\"label\":\"新测试单行\",\"type\":\"text\",\"max_length\":100},{\"is_required\":false,\"object_api_name\":\"object_solitude__c\",\"api_name\":\"form_field_wyOkp__c\",\"label\":\"单行文本\",\"type\":\"text\"}],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false,\"actions\":[\"682c77820bdb49000876c832\",\"682c77820bdb49000876c833\",\"682c77820bdb49000876c834\"],\"_id\":\"657291414b1edd0001005563\",\"tenant_id\":\"74255\",\"created_by\":\"1000\",\"create_time\":1702007105873,\"last_modified_time\":1747744643548,\"last_modified_by\":\"1000\",\"version\":11,\"is_batch\":null,\"url\":null,\"display\":null,\"extend_info\":\"{\\\"enableValidateRule\\\":false}\"}",
  "post_actions": [
    {
      "action_type": "custom_function",
      "label": "测试",
      "remark": "v1",
      "action_paramter": "{\"func_args\":[],\"func_api_name\":\"Btn_D6crY__c\"}",
      "describe_api_name": "object_solitude__c",
      "id": "682c77820bdb49000876c832",
      "tenantId": "74255",
      "stage": "pre"
    },
    {
      "action_type": "one_flow",
      "label": "测试一下",
      "remark": "v1",
      "action_paramter": "{\"one_flow_args\":[{\"id\":\"custom_variable##str\",\"type\":\"string\",\"value\":\"$form_field_MG5c7__c$\"},{\"id\":\"custom_variable##num\",\"type\":\"number\",\"value\":\"$field_qcs89__c$\"},{\"id\":\"custom_variable##display\",\"type\":\"object\",\"value\":\"$field_rFID2__c$\"},{\"id\":\"custom_variable##e_list\",\"type\":\"list\",\"value\":\"$owner$\"}],\"oneflow_api_name\":\"2azT7Ft7Hd__oneflow\"}",
      "describe_api_name": "object_solitude__c",
      "id": "682c77820bdb49000876c833",
      "tenantId": "74255",
      "stage": "current"
    },
    {
      "action_type": "updates",
      "label": "字段更新(示例)",
      "remark": "",
      "action_paramter": "{\"fields\":[{\"field\":\"attachment__c\",\"value\":\"$form_attachment__c$\",\"var_type\":\"variable\"},{\"field\":\"field_wyOkp__c\",\"value\":\"$form_field_wyOkp__c$\",\"var_type\":\"variable\"},{\"field\":\"field_qcs89__c\",\"value\":\"$var_230SD__g$\",\"var_type\":\"formula\"}]}",
      "describe_api_name": "object_solitude__c",
      "id": "682c77820bdb49000876c834",
      "tenantId": "74255",
      "stage": "current"
    }
  ],
  "roles": [
    "00000000000000000000000000000006"
  ],
  "handler_list": [],
  "sourceInfo": "object_management"
}