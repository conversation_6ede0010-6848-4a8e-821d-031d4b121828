#!/bin/bash

# 批量更新任务状态脚本
# 将task_status从in_process更新为completed

API_ENDPOINT="<your_api_endpoint>"

# 更新任务 1: 6844771965b3bc3f029454c3
echo "正在更新任务 1..."
curl -X POST "$API_ENDPOINT" \
  -H 'Content-Type: application/json' \
  -d '{"body":[{"data":{"taskId":"6844771965b3bc3f029454c3","taskStatus":"completed","tenantId":"736684"}}],"op":"update_schedule","reconsumeTimes":0,"tenantId":"736684"}'
echo "任务 1 更新完成"
echo ""

# 更新任务 2: 6844933865b3bc3f02945b88
echo "正在更新任务 2..."
curl -X POST "$API_ENDPOINT" \
  -H 'Content-Type: application/json' \
  -d '{"body":[{"data":{"taskId":"6844933865b3bc3f02945b88","taskStatus":"completed","tenantId":"736684"}}],"op":"update_schedule","reconsumeTimes":0,"tenantId":"736684"}'
echo "任务 2 更新完成"
echo ""

# 更新任务 3: 6855fd08aa00d91c0a089733
echo "正在更新任务 3..."
curl -X POST "$API_ENDPOINT" \
  -H 'Content-Type: application/json' \
  -d '{"body":[{"data":{"taskId":"6855fd08aa00d91c0a089733","taskStatus":"completed","tenantId":"736684"}}],"op":"update_schedule","reconsumeTimes":0,"tenantId":"736684"}'
echo "任务 3 更新完成"
echo ""

echo "所有任务更新完成！"
