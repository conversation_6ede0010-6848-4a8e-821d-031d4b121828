/**
 * <AUTHOR>
 * @codeName 悦见客户-领取操作
 * @description 悦见客户-领取操作
 * @createTime 2023-12-13
 */
def id = context.data._id as String 
def userId = context.userId as String 
def now = DateTime.now()
def before_sales =context.data.saler__c as List

def form_saler__c = [userId]

// 变更销售和负责人

def attribute = ActionAttribute.build{
      triggerApprovalFlow = false
      triggerWorkflow = false
      skipFunctionAction = true
}

//处理业务逻辑
def (ce,cd,cm) = Fx.object.changeOwner("yj_AccountObj__c", id, form_saler__c[0] as String , attribute)

if(ce){
  log.info("变更客户负责人失败,${cm}")
  return
}

def (ue,ud,um) = Fx.object.update("yj_AccountObj__c", id, ["saler__c":form_saler__c,"sea_type__c":"private","syncToYJ_Flag__c":now],  true, false)
if(ue){
  log.info("变更客户所属销售失败,${um}")
  return
}

def logData=[
  "owner":["-10000"],
  "yj_account__c":id,
  "change_type__c":"obtain",
  "operator__c":[userId],
  "before_sales__c": before_sales,
  "after_sales__c": form_saler__c
]

def ret =  Fx.object.create("yj_AccountObj_salesChange__c", logData,  null,  CreateAttribute.builder().build())
if(ret.isError()){
  log.info("创建变更日志出错：${ret.message()}")
}