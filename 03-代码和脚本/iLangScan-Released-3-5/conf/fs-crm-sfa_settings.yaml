# 扫描的文件扩展名
scan_types:
  - .java
# 导出结果类型，目前仅支持 .txt、.json、.xlsx
file_extensions:
  - .txt
# 扫描时，排除哪些文件。不支持正则
exclude_files:
  - CrmPackageObjectDescribe.java
  - SessionBOCItemKeys.java
  - BooleanFieldDescribeBuilder.java
  - PricePolicyFlowSessionServiceImpl.java
  - RelationshiProducer.java
  - Chinese2PinyinUtils.java
  - BomAbstractModuleInitService.java
  - SendCrmMessageProxy.java
  - ObjectFollowDealSettingService.java
  - CustomerPaymentObjectDescribe.java
  - OrderPaymentObjectDescribe.java
  - PaymentPlanObjectDescribe.java
  - PricePolicySalesOrderAddField.java
  - PricePolicyQuoteAddField.java
# 排除的文本，支持正则，数组
exclude_lines:
  - (^@)|(^targetRelatedListLabel)|(^label)|(^\.label)|(^description)|(^\.targetRelatedListLabel)