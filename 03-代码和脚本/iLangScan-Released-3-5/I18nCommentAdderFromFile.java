import java.io.*;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class I18nCommentAdderFromFile {

    public static void main(String[] args) {
        List<String> list = listDirectories("/Users/<USER>/IdeaProjects/fs-open-app-center");
        for (String s : list) {
            // String inputFilePath = "/Users/<USER>/Desktop/工作文档/pythonWorkspace/iLangScan-Released-3-5/bin/user/output/fs-open-app-center.txt"; // 替换为实际的1.txt文件路径
            String inputFilePath = "/Users/<USER>/Desktop/工作文档/03-代码和脚本/iLangScan-Released-3-5/output/fs-open-app-center.txt";
            String projectRoot = "/Users/<USER>/IdeaProjects/fs-open-app-center/"+ s +"/src/main/java/"; // 替换为项目的根目录
            processFile(inputFilePath, projectRoot);
        }
    }
    public static List<String> listDirectories(String directoryPath) {
        Path path = Paths.get(directoryPath);
        List<String> paths = new ArrayList<>();
        // 检查路径是否存在且是目录
        if (Files.exists(path) && Files.isDirectory(path)) {
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(path)) {
                for (Path entry : stream) {
                    if (Files.isDirectory(entry) && entry.getFileName().toString().contains("fs-open")) {
                        paths.add(entry.getFileName().toString());
                        System.out.println(entry.getFileName()); // 打印文件夹名称
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            System.err.println("指定的路径不存在或不是一个目录: " + directoryPath); //IgnoreI18n
        }
        return paths;
    }

    public static void processFile(String inputFilePath, String projectRoot) {
        try {
            List<String> lines = Files.readAllLines(Paths.get(inputFilePath));

            for (String line : lines) {
                // 解析文件路径和未翻译的文本
                String[] parts = line.split(":", 2);
                if (parts.length < 2) continue; // 如果格式不正确，跳过

                String className = parts[0].trim(); // 获取类名
                String[] codeParts = parts[1].split(" ", 2);
                if (codeParts.length < 2) continue; // 如果格式不正确，跳过

                String untranslatedText = codeParts[1].trim(); // 获取未翻译的文本

                // 获取文件路径
                String filePath = getFilePathFromClassName(className, projectRoot);
                if (filePath == null) {
                    System.err.println("未找到文件路径映射: " + className); //IgnoreI18n
                    continue; // 如果没有找到映射，跳过
                }

                // 检查文件是否存在
                if (!Files.exists(Paths.get(filePath))) {
                    System.err.println("文件不存在: " + filePath); //IgnoreI18n
                    continue; // 如果文件不存在，跳过
                }

                // 添加注释
                addIgnoreI18nComment(filePath, untranslatedText);
            }

            System.out.println("处理完成。"); //IgnoreI18n
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String getFilePathFromClassName(String className, String projectRoot) {
        // 将类名转换为文件路径
        String relativePath = className.replace('.', '/') + ".java"; // 假设.java文件与.class文件在同一目录
        return projectRoot  + relativePath; // 根据项目结构构建完整路径
    }

    public static void addIgnoreI18nComment(String filePath, String untranslatedText) {
        try {
            // 读取文件内容
            List<String> lines = Files.readAllLines(Paths.get(filePath));
            StringBuilder updatedContent = new StringBuilder();

            for (String line : lines) {
                // 检查每一行是否包含未翻译的文本
                if (line.contains(untranslatedText)) {
                    // 检查是否已经包含 //IgnoreI18n
                    if (!line.contains("// ignoreI18n")) {
                        line = line + " // ignoreI18n"; // 添加注释
                    }
                }
                updatedContent.append(line).append(System.lineSeparator());
            }

            // 写回文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
                writer.write(updatedContent.toString());
            }

            System.out.println("注释已成功添加到文件: " + filePath); //IgnoreI18n
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
