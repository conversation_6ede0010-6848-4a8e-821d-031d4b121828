# 📣📣📣 概述

iLangScan 是 一款 i18n 扫描器，用于扫描项目中包含的中文，并可将结果输出到文件中

# 🚀🚀🚀 更新文档

https://wiki.firstshare.cn/pages/viewpage.action?pageId=456952112

# 🔛 如何启动

解压文件后，进入 bin 目录，在 bin 目录执行

```shell
python iLangScan.py -ip {你的项目路径}
```

## 🤖 命令行语法

```shell
python iLangScan.py (选项)(参数)
```

## ☑️ 选项

```text
-ip<被扫描的项目路径，支持多个>
-op<扫描结果输出路径> 
-ext<扫描文件类型，支持多个>
-ignoreLine<忽略的行，正则表达式匹配>
```

## 🌰举例

### 将 fs-crm-sfa 工程的扫描结果输出成 .xlsx 和 .txt

```shell
python iLangScan.py -ip ./fs-crm-sfa -ext .xlsx .txt
```

### 将 fs-crm-sfa 工程的扫描结果输出到目录 user/output 中

```shell
python iLangScan.py -ip ./fs-crm-sfa -ext .xlsx .txt -op user/output
```

### 忽略包含 ignoreI18n 字符串的行。

如 throw new ValidateException("没有发现资源文件"); // ignoreI18n，扫描时此行将会被忽略。

```shell
python iLangScan.py -ext .xlsx .txt -ip ./fs-crm-sfa -ignoreLine ".*ignoreI18n.*"
```

### 指定需要扫描的文件

eg：仅扫描 AddAccountMeanwhileAddAddrProvider.java AccountAndAddrSyncUpdLocationProvider.java 这两个文件

```shell
python iLangScan.py -ip /Users/<USER>/IdeaProjects/fs-crm-sfa -ext .json -files AddAccountMeanwhileAddAddrProvider.java AccountAndAddrSyncUpdLocationProvider.java
```

# 💻运行环境

Python 版本：python 3.6 及以上。

# 🙌 注解

忽略某一行的多语检测：

- `//ignoreI18n`

```java
String test = "忽略这一行中文检测" // ignoreI18n
```

忽略某一个文件的多语检测（选择其一即可）

- **@IgnoreI18n** 
- **IgnoreI18nFile** 
- **@IgnoreI18nFile** 任意一个

```java
/**
 * <AUTHOR>
 * @time 2024-06-27 11:24
 * @Description
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile 选择其一即可
 */
public class PartnerChannelService {}
```

# ⚙️ 配置文件

请在工具程序的 conf 目录下，新建一个自定义配置文件进行详细配置，并在 `setting_mappig.yaml` 配置文件中将具体待被扫描工程与配置项关联。

## xxx_settings

程序中存在三种 settings：

1. `base_settings.yaml` 配置文件是基础配置。不可被覆盖，若添加了自定义配置文件，那么重复的配置项，**数组值**只会追加，不会覆盖。

2. `default_settings.yaml` 是默认配置。如果没有自定义的配置文件，默认使用该配置。

3. `{待被扫描工程名}_settings.yaml` 是自定义配置文件，如果其是**待被扫描的工程**的自定义配置文件，将会使用该配置文件。

### 配置文件加载规则

首先加载基础配置，再根据**待被扫描的工程**是否定义了自定义配置从而选择追加默认配置还是自定义配置，最后再追加控制台参数。

基础配置中的配置项不可被其他配置覆盖，只能被追加。不在基础配置中的配置项**数组值**将会被覆盖。

### 优先级

基础配置 > 控制台参数 > （自定义配置文件｜默认配置）

### 配置详解

| 配置项           | 值类型         | 是否是基础配置项 | 说明               |
| ---------------- | -------------- | ---------------- | ------------------ |
| exclude_lines    | 正则表达式数组 | 是               | 排除的行文本       |
| exclude_dirs     | 字符串数组     | 是               | 排除的目录         |
| exclude_projects | 字符串数组     | 是               | 排除的工程         |
| exclude_files    | 字符串数组     | 否               | 排除的文件         |
| scan_types       | 字符串数组     | 否               | 需要扫描的文件类型 |
| file_extensions  | 字符串数组     | 否               | 输出的文件类型     |
| output_path      | 字符串         | 是               | 输出目录           |

## setting_mappig

`setting_mappig.yaml` 配置文件是待被扫描工程与自定义配置文件的映射。不在映射中的工程将加载默认配置。

example：

```yaml
mappings:
 # 工程名: 配置名
 fs-crm-sfa: "fs-crm-sfa_settings.yaml"
```

fs-crm-sfa 工程将会加载 `fs-crm-sfa_settings.yaml` 配置。