import os
import re
import sys
from . import scanutils, setting_loader
from . import exporter
from . import logging_config
from . import spinner
import threading
from pathlib import Path

try:
    import yaml
except ImportError:
    os.system('pip install pyyaml')
    import yaml

logger = logging_config.setup_logging()

# 定义中文字符的正则表达式
CHINESE_CHAR_PATTERN = re.compile(r'[\u4e00-\u9fff]')

# 定义Java字符串的正则表达式
STRING_LITERAL_PATTERN = re.compile(r'"((\\["\\]|[^"\\])*)"')

# 定义Java枚举的正则表达式
ENUM_PATTERN = re.compile(r'\benum\b')

USE_ABLE_FILE_EXTENSION = ['.txt', '.json', '.xlsx']

# 定义正则预编译列表
PATTERN_LINE_LIST = []


def find_files(input_path, file_types, exclude_strs):
    project_directory = Path(input_path)
    all_files = dict()

    # 遍历每个文件类型
    for file_type in file_types:
        file_type_pattern = '*' + file_type
        # 使用rglob找到所有匹配的文件
        for file_path in project_directory.rglob(file_type_pattern):
            # 检查文件路径的字符串表示是否不包含任何排除字符串
            if all(exclude_str not in str(file_path) for exclude_str in exclude_strs):
                all_files[file_path] = None

    return all_files


def search_all_input_chinese(args_config):
    input_paths_ = args_config.get('input_paths', [])
    # 根目录，多项目扫描
    base_project_ = args_config.get('base_project', None)
    if base_project_ is not None:
        base_project_directory = Path(base_project_)
        input_paths_ = [d for d in base_project_directory.iterdir() if d.is_dir()]

    for input_path in input_paths_:
        project_name = os.path.basename(input_path)
        setting_instance = setting_loader.load_setting(project_name, args_config)
        logger.info(f'project_name：「{project_name}」, setting_instance：「{setting_instance.get_setting()}」')
        check_setting(setting_instance)
        if project_name in setting_instance.get_exclude_projects():
            logger.warn(f"项目【{project_name}】已被过滤")
            continue
        print(f"开始项目【{project_name}】的中文扫描")
        logger.info(f"开始项目【{project_name}】的中文扫描")
        # 在另一个线程中启动 spinner
        stop_event = threading.Event()
        spinner_thread = threading.Thread(target=spinner.spin, args=(stop_event,))
        spinner_thread.start()
        try:
            result_dict = search_chinese(setting_instance.get_exclude_files(), input_path,
                                         setting_instance.get_scan_types(), setting_instance.get_exclude_dirs(), setting_instance.get_only_files())
            exporter.export(project_name, setting_instance.get_output_path(), setting_instance.get_file_extensions(),
                            result_dict)
        except Exception as e:
            logger.error(f"项目【{project_name}】的中文扫描出现错误 {e}")
            stop_event.set()
            sys.exit()
        # 发送停止信号
        stop_event.set()
        # 等待 spinner 线程结束
        spinner_thread.join()

        logger.info(f"完成项目【{project_name}】的中文扫描\n")
        print(f"\n完成项目【{project_name}】的中文扫描\n")
        print(f"-------------------\n")


def check_setting(setting_instance):
    if setting_instance.get_scan_types() is None:
        print(f'scan_types is None \n')
        sys.exit()
    for scan_type in setting_instance.get_scan_types():
        if not scan_type.startswith('.'):
            print(f'scan_types is error {scan_type} \n')
            sys.exit()
    if setting_instance.get_output_path() is None:
        print(f'output_path is None \n')
        sys.exit()
    if setting_instance.get_exclude_lines() is not None:
        for line_pattern in setting_instance.get_exclude_lines():
            try:
                # 编译正则表达式
                pattern = re.compile(line_pattern)
                logger.info(f"正则表达式编译成功，没有语法错误。{line_pattern}")
                PATTERN_LINE_LIST.append(pattern)
            except re.error:
                print(f'exclude_line:{line_pattern} is error  \n')
                sys.exit()
    if setting_instance.get_file_extensions() is None:
        print(f'file_extension is None \n')
        sys.exit()
    for ext in setting_instance.get_file_extensions():
        if ext not in USE_ABLE_FILE_EXTENSION:
            print(f'file_extension is error {ext} \n')
            sys.exit()


def scan(args_config):
    # 开始处理
    logger.info(f'args_config：{args_config}')
    print("======开始中文扫描！======\n")
    search_all_input_chinese(args_config)
    print("\n======完成中文扫描！======")


def search_chinese_by_file(file_path, exclude_files):
    result_dict = {}
    with open(file_path, 'r', encoding='utf-8') as file:
        if scanutils.is_ignore_i18n_file(file):
            return
        if scanutils.is_enum_class(file):
            return
        class_name = scanutils.get_class_name(file)
        if class_name in exclude_files:
            return

        package = ""
        multi_line_comment = False
        for line_number, line in enumerate(file, start=1):
            if line_number == 1:
                package = scanutils.get_package(class_name, line)
                if not line.lstrip().startswith("package"):
                    return None
                else:
                    continue
            # 检查是否是多行注释的开始
            if not multi_line_comment and scanutils.MULTI_LINE_COMMENT_START in line:
                multi_line_comment = True
            # 如果在多行注释中，则跳过当前行
            if multi_line_comment:
                if scanutils.MULTI_LINE_COMMENT_END in line:
                    multi_line_comment = False
                continue
            # 如果是单行注释或多行注释的一部分，则跳过当前行
            if scanutils.is_a_part_of_comment(line):
                continue
            if scanutils.is_exclude_line(PATTERN_LINE_LIST, line.strip()):
                continue
            # 检查字符串字面量中的中文字符
            line = re.sub(r"//.*", "", line)
            # 去掉同一行中的注释，如 String str = "test"; \\ 注释;  变为 String str = "test";
            line = line.split('//')[0]
            for match in STRING_LITERAL_PATTERN.finditer(line):
                # 整个匹配的字符串字面量
                # string_literal = match.group(0)
                # 字符串字面量中的实际内容（不包括引号）
                string_content = match.group(1)
                # 检查字符串字面量中是否包含中文字符
                if CHINESE_CHAR_PATTERN.search(string_content):
                    key = package + ':' + str(line_number)
                    result_dict[key] = line.strip()
    return result_dict


def search_chinese(exclude_files, input_path, scan_types, exclude_dirs, only_files):
    result_dict = {}
    files = find_files(input_path, scan_types, exclude_dirs)
    for file_path in files:
        dict_ = None
        file_name = os.path.basename(file_path)
        if only_files is None:
            dict_ = search_chinese_by_file(file_path, exclude_files)
        else:
            if file_name in only_files:
                dict_ = search_chinese_by_file(file_path, exclude_files)
        if dict_ is not None:
            result_dict.update(dict_)
    return result_dict
