import os
import re

# 定义Java单行注释的正则表达式
SINGLE_LINE_COMMENT_PATTERN = re.compile(r'//.*')

# 定义Java多行注释的开始和结束标记
MULTI_LINE_COMMENT_START = '/*'
MULTI_LINE_COMMENT_END = '*/'
IGNORE_TAGS = ['@IgnoreI18n', 'IgnoreI18nFile', '@IgnoreI18nFile']


def is_enum_class(file):
    file_content = file.read()
    file.seek(0)
    # enum_match = 'public enum ' + get_base_name(file)
    enum_match = 'publicenum' + get_base_name(file)
    cleaned_s = ''.join(char for char in file_content if not char.isspace())
    return enum_match in cleaned_s


def is_interface_class(file):
    file_content = file.read()
    file.seek(0)
    # interface_match = 'public interface ' + get_base_name(file)
    interface_match = 'publicinterface' + get_base_name(file)
    cleaned_s = ''.join(char for char in file_content if not char.isspace())
    return interface_match in cleaned_s


def get_base_name(file):
    basename = os.path.basename(file.name)
    basename = os.path.splitext(basename)[0]
    return basename


def get_class_name(file):
    return os.path.basename(file.name)


def is_a_part_of_comment(line):
    """检查行是否是单行或多行注释"""
    # 检查是否以单行注释开始
    if SINGLE_LINE_COMMENT_PATTERN.match(line):
        return True
    # 检查是否包含多行注释的开始或结束
    return MULTI_LINE_COMMENT_START in line and MULTI_LINE_COMMENT_END in line

def is_exclude_line(pattern_line_list, line):
    if pattern_line_list is None:
        return False
    for pattern in pattern_line_list:
        if pattern.match(line):
            return True
    return False


def get_package(class_name, line):
    # 移除开头的空白字符
    package_str = line.lstrip()
    # 检查字符串是否以 'package ' 开头，并移除
    if package_str.startswith("package"):
        package_str = package_str[len("package"):]
    index = class_name.rfind('.')
    return package_str.strip().rstrip(';') + '.' + class_name[:index]


def is_ignore_i18n_file(file):
    file_content = file.read()
    file.seek(0)
    return any(tag in file_content for tag in IGNORE_TAGS)
