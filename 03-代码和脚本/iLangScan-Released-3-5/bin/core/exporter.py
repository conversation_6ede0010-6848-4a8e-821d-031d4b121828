import json
import os
from pathlib import Path
from . import logging_config

try:
    from openpyxl import Workbook
except ImportError:
    os.system('pip install openpyxl')
    from openpyxl import Workbook

logger = logging_config.setup_logging()


def generate_json_content(result_dict: dict, file_name_no_ext: str, file_extension: str):
    json_content = json.dumps(result_dict, indent=4, ensure_ascii=False, default=str)
    write_text_file(json_content, file_name_no_ext, file_extension)


def generate_text_content(result_dict: dict, file_name_no_ext: str, file_extension: str):
    content = ''
    for key in result_dict:
        content += key + " " + result_dict[key] + '\n'
    write_text_file(content, file_name_no_ext, file_extension)


def write_excel_file(wb: Workbook, file_name_no_ext: str, file_extension: str):
    full_file_name = file_name_no_ext + file_extension
    file_path = Path(str(full_file_name))
    try:
        file_path.parent.mkdir(parents=True, exist_ok=True)
        wb.save(full_file_name)
    except IOError as e:
        logger.error(f'写入文件失败：{e}')


def generate_excel_content(result_dict: dict, file_name_no_ext: str, file_extension: str):
    # 创建一个工作簿
    wb = Workbook()
    # 选择默认工作表
    ws = wb.active
    # 添加标题
    ws['A1'] = '类的坐标'
    ws['B1'] = '中文块'
    # 添加数据
    for key in result_dict:
        ws.append([key, result_dict[key]])
    # 保存文件
    write_excel_file(wb, file_name_no_ext, file_extension)


def export(project_name: str, output_path: str, file_types, result_dict: dict):
    file_name_no_ext = os.path.join(output_path, project_name)
    for file_type in file_types:
        if file_type.lower() == '.json':
            generate_json_content(result_dict, file_name_no_ext, file_type)
        elif file_type.lower() == '.xlsx':
            generate_excel_content(result_dict, file_name_no_ext, file_type)
        elif file_type.lower() == '.txt':
            generate_text_content(result_dict, file_name_no_ext, file_type)
        else:
            generate_text_content(result_dict, file_name_no_ext, '.txt')
            logger.error(f'不支持文件类型: {file_type}, 默认生成 .txt 文件')


def write_text_file(content: str, ile_name_no_ext: str, file_extension: str):
    full_file_name = ile_name_no_ext + file_extension
    file_path = Path(str(full_file_name))
    try:
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
    except IOError as e:
        logger.error(f'写入文件失败：{e}')
