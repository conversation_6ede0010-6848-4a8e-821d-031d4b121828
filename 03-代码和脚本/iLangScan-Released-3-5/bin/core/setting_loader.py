import os

import yaml


class Setting:
    def __init__(self, args_config, *config_files):
        self.config_files = config_files  # 配置文件的列表
        self.config_data = {}
        self.console_args_data = args_config
        self.base_item = ['exclude_lines', 'exclude_dirs', 'exclude_projects']
        self.root_dir = get_root_dir()
        self.default_output_path = None

    # 加载配置文件并按照优先级合并
    def load_config(self):
        self.default_output_path = os.path.join(self.root_dir, 'output')
        for config in self.config_files:
            with open(config, 'r') as file:
                data = yaml.safe_load(file)
                # 合并配置，优先级从后往前，优先使用最后加载的配置
                self._merge_config(data)
        self._merge_config(self.console_args_data)
        self._set_default_value()

    # 合并配置文件，确保后加载的配置覆盖之前的配置
    def _merge_config(self, new_data):
        for key, value in new_data.items():
            # 如果该字段没有在当前数据中出现，则加入
            if key not in self.config_data:
                self.config_data[key] = value
            else:
                if key in self.base_item:
                    existing_value = self.config_data[key]
                    if isinstance(existing_value, list) and isinstance(value, list):
                        # 使用集合合并去重，并转换回列表
                        self.config_data[key] = list(set(existing_value).union(value))
                    else:
                        self.config_data[key] = value
                else:
                    self.config_data[key] = value

    def _set_default_value(self):
        for key, value in self.config_data.items():
            if key == 'output_path' and value is None:
                self.config_data[key] = self.default_output_path
            if key == 'scan_types' and value is None:
                self.config_data[key] = ['.java']
            if key == 'file_extensions' and value is None:
                self.config_data[key] = ['.txt']

    def get_setting(self):
        return self.config_data

    def get_scan_types(self):
        return self.config_data['scan_types']

    def get_input_paths(self):
        return self.config_data['input_paths']

    def get_output_path(self):
        return self.config_data['output_path']

    def get_exclude_lines(self):
        return self.config_data['exclude_lines']

    def get_file_extensions(self):
        return self.config_data['file_extensions']

    def get_exclude_files(self):
        return self.config_data['exclude_files']

    def get_exclude_projects(self):
        return self.config_data['exclude_projects']

    def get_exclude_dirs(self):
        return self.config_data['exclude_dirs']

    def get_only_files(self):
        return self.config_data['files']


# 读取配置文件并加载成字典
def _load_setting_mapping(file_name):
    with open(file_name, "r") as file:
        config_data = yaml.safe_load(file)
    return config_data.get("mappings", {})


def load_setting(project_name, args_config):
    root_dir = get_root_dir()
    conf_mapping_path = os.path.join(root_dir, 'conf', 'setting_mapping.yaml')
    project_setting_mapping = _load_setting_mapping(conf_mapping_path)
    # 获取配置文件名
    project_setting = project_setting_mapping.get(project_name, 'default_settings.yaml')
    # 创建 Config 对象并载入配置文件的值
    project_setting_path = os.path.join(root_dir, 'conf', project_setting)
    base_setting_path = os.path.join(root_dir, 'conf', "base_settings.yaml")
    setting_instance = Setting(args_config, base_setting_path, project_setting_path)
    setting_instance.load_config()
    return setting_instance


def get_root_dir():
    current_path = os.path.dirname(__file__)
    return os.path.abspath(os.path.join(current_path, os.pardir, os.pardir))


if __name__ == "__main__":
    load_setting("fs-crm-sfa", {})
