import logging
import os
from datetime import datetime


def setup_logging():
    # 创建一个logger
    logger = logging.getLogger()
    if logger.handlers:
        return logger
    logger.setLevel(logging.INFO)  # 设置日志级别
    root_dir = get_root_dir()
    log_file_name = f"cli_{datetime.now().strftime('%Y-%m-%d')}.log"
    log_file_path = os.path.join(root_dir, 'logs', log_file_name)
    file_handler = logging.FileHandler(log_file_path)
    file_handler.setLevel(logging.INFO)

    # 创建一个格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 设置处理器的格式化器
    file_handler.setFormatter(formatter)
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARN)  # 控制台只输出 ERROR 及以上级别日志
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)

    # 将处理器添加到 logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 如果需要，可以返回logger对象
    return logger


def get_root_dir():
    current_path = os.path.dirname(__file__)
    return os.path.abspath(os.path.join(current_path, os.pardir, os.pardir))
