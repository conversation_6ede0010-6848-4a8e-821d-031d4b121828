import sys
import os
import time
import itertools
import threading


def spin(stop_event: threading.Event, process_time=0.1):
    for i in itertools.cycle(['-', '/', '|', '\\']):
        if stop_event.isSet():
            break
        if os.name == 'nt':
            sys.stdout.write(i + '\r')
            sys.stdout.flush()
        else:
            sys.stdout.write(f"\r{i}正在处理")
            sys.stdout.flush()
        time.sleep(process_time)
