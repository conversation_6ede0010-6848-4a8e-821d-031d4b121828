com.fxiaoke.biz.controller.UpgradeController:58 throw new RuntimeException("参数丢失");
com.fxiaoke.biz.controller.UpgradeController:114 throw new RuntimeException("参数丢失");
com.fxiaoke.biz.controller.UpgradeController:122 throw new RuntimeException("系统类型不对");
com.fxiaoke.biz.controller.UpgradeController:172 throw new RuntimeException("参数校验异常");
com.fxiaoke.biz.controller.UpgradeController:222 throw new RuntimeException("参数丢失");
com.fxiaoke.biz.controller.UpgradeController:243 throw new RuntimeException("系统类型不对");
com.fxiaoke.biz.controller.UpgradeController:290 throw new RuntimeException("参数丢失");
com.fxiaoke.biz.controller.UpgradeController:312 throw new RuntimeException("系统类型不对");
com.fxiaoke.biz.controller.UpgradeController:355 throw new RuntimeException("参数丢失");
com.fxiaoke.biz.controller.UpgradeController:363 throw new RuntimeException("系统类型不对");
com.fxiaoke.biz.controller.UpgradeController:388 throw new RuntimeException("参数丢失");
com.fxiaoke.biz.controller.UpgradeController:400 throw new RuntimeException("系统类型不对");
com.fxiaoke.biz.controller.UpgradeController:426 throw new RuntimeException("参数丢失");
com.fxiaoke.biz.controller.UpgradeController:438 throw new RuntimeException("系统类型不对");
com.fxiaoke.biz.controller.GrayManager:241 private static final Set<String> adminAuthors = Sets.newHashSet("陈晓彬", "尚壬鹏");
com.fxiaoke.biz.controller.GrayManager:274 return new AddOrUpdateRoute.Result("配置已有其他人修改,请重新刷新修改");
com.fxiaoke.biz.controller.GrayManager:284 return new AddOrUpdateRoute.Result("验证配置失败,请确认配置正确");
com.fxiaoke.biz.controller.GrayManager:300 return String.format("只能由 负责人:%s 修改 %s 对应的配置,或让 %s 将 %s 加入负责人列表", principalStr, key, principalStr, author);
com.fxiaoke.biz.controller.GrayManager:345 return new DeleteRouteByGroup.Result("配置已有其他人修改,请重新刷新修改");
com.fxiaoke.biz.profile.ConfigCoreProviderService:126 defaultMenus = JSON.parseArray(config.get("defaultMenus", "[{\"menuType\":1,\"name\":\"企信\",\"value\":\"message\"},{\"menuType\":1,\"name\":\"工作\",\"value\":\"xt.oa.collabration\"},{\"menuType\":1,\"name\":\"CRM\",\"value\":\"fs.crm\"},{\"menuType\":1,\"name\":\"应用\",\"value\":\"fs.common.appcenter\"},{\"menuType\":1,\"name\":\"我\",\"value\":\"fs.common.profile.me\"}]"), Menu.class);
com.fxiaoke.biz.profile.ConfigCoreProviderService:128 defaultMenuMap = JSON.parseObject(config.get("defaultMenuMap", "{7:[{\"menuType\":1,\"name\":\"CRM\",\"value\":\"fs.crm\"},{\"menuType\":1,\"name\":\"应用\",\"value\":\"fs.common.appcenter\"},{\"menuType\":1,\"name\":\"我\",\"value\":\"fs.common.profile.me\"}],8:[{\"menuType\":1,\"name\":\"CRM\",\"value\":\"fs.crm\"},{\"menuType\":1,\"name\":\"应用\",\"value\":\"fs.common.appcenter\"},{\"menuType\":1,\"name\":\"我\",\"value\":\"fs.common.profile.me\"}],9:[{\"menuType\":1,\"name\":\"CRM\",\"value\":\"fs.crm\"},{\"menuType\":1,\"name\":\"应用\",\"value\":\"fs.common.appcenter\"},{\"menuType\":1,\"name\":\"我\",\"value\":\"fs.common.profile.me\"}]}"), new TypeReference<Map<Integer, List<Menu>>>() {
com.fxiaoke.biz.profile.ConfigCoreProviderService:228 return getConfig("excludeMenuTemplateName", "不参与规则");
com.fxiaoke.biz.service.WEBSplashServiceImpl:55 throw new SplashException("没有配置闪屏图片！");
com.fxiaoke.biz.service.WEBSplashServiceImpl:63 throw new SplashException("配置冲突！新增配置跟原有版本号为 " +
com.fxiaoke.biz.service.WEBSplashServiceImpl:64 crossConfig.getVersionNumber() + " 的配置时间段相冲突，请删除后根据计划重新配置！");
com.fxiaoke.biz.service.WEBSplashServiceImpl:81 throw new SplashException("添加闪屏配置失败!",e);
com.fxiaoke.biz.service.WEBSplashServiceImpl:133 throw new SplashException("删除闪屏配置失败!",e);
com.fxiaoke.biz.service.PCSplashServiceImpl:47 throw new SplashException("没有配置闪屏图片！");
com.fxiaoke.biz.service.PCSplashServiceImpl:55 throw new SplashException("配置冲突！新增配置跟原有版本号为 " +
com.fxiaoke.biz.service.PCSplashServiceImpl:56 crossConfig.getVersionNumber() + " 的配置时间段相冲突，请删除后根据计划重新配置！");
com.fxiaoke.biz.service.PCSplashServiceImpl:68 throw new SplashException("添加闪屏配置失败!",e);
com.fxiaoke.biz.service.PCSplashServiceImpl:120 throw new SplashException("删除闪屏配置失败!",e);
com.fxiaoke.biz.service.SplashServiceImpl:68 throw new SplashException("图片数量必须是3张！");
com.fxiaoke.biz.service.SplashServiceImpl:71 throw new SplashException("消失时间必须大于等于0秒，小于等于3秒！");
com.fxiaoke.biz.service.SplashServiceImpl:80 throw new SplashException("配置冲突！新增配置跟原有版本号为 " +
com.fxiaoke.biz.service.SplashServiceImpl:81 crossConfig.getVersionNumber() + " 的配置时间段相冲突，请删除后根据计划重新配置！");
com.fxiaoke.biz.service.SplashServiceImpl:138 throw new SplashException("添加闪屏配置失败!", e);
com.fxiaoke.biz.service.SplashServiceImpl:199 throw new SplashException("删除闪屏配置失败!", e);
com.fxiaoke.biz.service.SplashServiceImpl:226 throw new SplashException("删除闪屏配置失败!", e);
com.fxiaoke.biz.service.AppConfigServiceImpl:122 throw new AppConfigException("找不到相应的配置类型");
com.fxiaoke.biz.service.AppConfigServiceImpl:164 throw new AppConfigException("添加应用配置失败!", e);
com.fxiaoke.biz.service.AppConfigServiceImpl:203 throw new SplashException("添加应用配置失败!", e);
com.fxiaoke.biz.service.AppConfigServiceImpl:222 throw new SplashException("删除应用配置失败!", e);
com.fxiaoke.biz.service.AppConfigServiceImpl:240 throw new SplashException("获取应用配置失败!", e);
com.fxiaoke.biz.service.AppConfigServiceImpl:286 throw new SplashException("获取应用配置失败!", e);
com.fxiaoke.biz.service.AppConfigServiceImpl:335 throw new SplashException("获取应用配置失败!", e);
com.fxiaoke.biz.service.AppConfigServiceImpl:357 throw new SplashException("获取应用配置失败!", e);
com.fxiaoke.biz.service.MenuTemplateServiceImpl:558 result.setMessage("菜单模板不能为空");
com.fxiaoke.biz.service.MenuTemplateServiceImpl:574 result.setMessage("预置菜单模板不存在");
com.fxiaoke.biz.service.MenuTemplateServiceImpl:646 result.setMessage("未知错误：" + ex.getMessage());
com.fxiaoke.biz.remote.QiXinNotifyService:30 private static final String SESSION_TITLE = "积分变动提醒";
com.fxiaoke.biz.remote.UserGroupRemoteService:21 desc = "获取用户组基本信息列表"
com.fxiaoke.api.constant.LayoutField:22 public static final String COMMON_MENU_GROUP_NAME = "常用菜单";
com.fxiaoke.api.service.DropListRestService:10 desc = "app自定义",
com.fxiaoke.api.service.PageTemplateRestService:12 desc = "app自定义",
com.fxiaoke.api.service.ClientDataRestService:14 desc = "app自定义",
com.fxiaoke.biz.component.helper.MenuComponentHelper:115 String createPrefix = qixinI18nService.getSingleI18nValue(arg.getEnterpriseId(), Constant.CREATE_PREFIX_KEY, "新建", arg.getLocale());
com.fxiaoke.biz.utils.UserExtensionExceptionHandler:25 logger.warn("业务错误", throwable);
com.fxiaoke.biz.utils.UserExtensionExceptionHandler:39 logger.warn("业务错误", throwable);
com.fxiaoke.biz.utils.UserExtensionExceptionHandler:47 logger.error("服务器内部错误", throwable);
com.fxiaoke.biz.utils.I18nUtils:5 String CURRENT_PAGE_DEFAULT_VALUE = "当前页面";
com.fxiaoke.biz.utils.I18nUtils:7 String ME_PAGE_I18NKey = "我";
com.fxiaoke.biz.utils.I18nUtils:8 String ME_PAGE_DEFAULT_VALUE = "我";
com.fxiaoke.biz.controller.PageTemplateControllerImpl:1820 filterComponent.setHeader("筛选器");
com.fxiaoke.biz.controller.PageTemplateControllerImpl:1842 dateFilter.setDateType("本月");
com.fxiaoke.biz.controller.ClientDataControllerImpl:1350 componentJSON.put("header", "筛选器");
com.fxiaoke.biz.controller.ClientDataControllerImpl:1988 key.setDefaultVal("扫名片");
com.fxiaoke.biz.service.NewCreateObjectQueryService:46 String createPrefix = qixinI18nService.getSingleI18nValue(arg.getEnterpriseId(), Constant.CREATE_PREFIX_KEY, "新建", arg.getLocale());
com.fxiaoke.biz.service.ConfigCoreService:180 defaultAppConfig = JSON.parseObject(config.get("defaultAppConfig", "{\"appName\":\"纷享销客\"}"), AppConfig.class);
com.fxiaoke.biz.convert.help.NewCreateMenuCovertHelper:43 String createPrefix = qixinI18nService.getSingleI18nValue(enterpriseId, Constant.CREATE_PREFIX_KEY, "新建", locale);
