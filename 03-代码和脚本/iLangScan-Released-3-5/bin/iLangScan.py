import argparse

import core.scanner as scanner


def main():
    # 创建解析器
    parser = argparse.ArgumentParser(description='命令行')

    # 扫描结果路径参数
    parser.add_argument('-op', type=str, help='这是一个可选参数，扫描结果路径', required=False)
    parser.add_argument('-dir', type=str, help='这是一个可选参数，扫描该路径下的所有项目', required=False)
    parser.add_argument('-ip', nargs='*', help='这是一个可选参数，输入项目路径', required=False)
    parser.add_argument('-ext', nargs='*', help='这是一个可选参数，输入文件扩展', required=False)
    parser.add_argument('-files', nargs='*', help='这是一个可选参数，输入扫描的文件', required=False)
    parser.add_argument('-ignoreLine', type=str, help='这是一个可选参数，输入需要忽略行的正则表达式', required=False)
    parser.parse_args()
    # 解析命令行输入
    args_config = parse_args_config(parser.parse_args())
    scanner.scan(args_config)


def parse_args_config(args):
    args_config = {}
    args_config.setdefault('files', None)
    if args.op:
        args_config['output_path'] = args.op
    if args.ip:
        args_config['input_paths'] = args.ip
    if args.ext:
        args_config['file_extensions'] = args.ext
    if args.ignoreLine:
        args_config['exclude_lines'] = []
        args_config['exclude_lines'].append(args.ignoreLine)
    if args.dir:
        args_config['base_project'] = args.dir
    if args.files:
        args_config['files'] = args.files
    return args_config


if __name__ == "__main__":
    main()
