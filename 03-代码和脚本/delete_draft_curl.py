import json

data = '''
[
{
"count": 1050,
"created_by": "21108"
},
{
"count": 1050,
"created_by": "6656"
},
{
"count": 1046,
"created_by": "21903"
},
{
"count": 1046,
"created_by": "22146"
},
{
"count": 1032,
"created_by": "22232"
},
{
"count": 1023,
"created_by": "22337"
},
{
"count": 1005,
"created_by": "16603"
},
{
"count": 1000,
"created_by": "10279"
},
{
"count": 994,
"created_by": "12635"
},
{
"count": 994,
"created_by": "11796"
},
{
"count": 993,
"created_by": "13511"
},
{
"count": 987,
"created_by": "19166"
},
{
"count": 978,
"created_by": "16994"
},
{
"count": 974,
"created_by": "3628"
},
{
"count": 967,
"created_by": "3690"
},
{
"count": 966,
"created_by": "21487"
},
{
"count": 964,
"created_by": "8542"
},
{
"count": 961,
"created_by": "9838"
},
{
"count": 957,
"created_by": "1540"
},
{
"count": 951,
"created_by": "18187"
},
{
"count": 948,
"created_by": "20054"
},
{
"count": 947,
"created_by": "9032"
},
{
"count": 942,
"created_by": "21824"
},
{
"count": 934,
"created_by": "17494"
},
{
"count": 933,
"created_by": "3563"
},
{
"count": 928,
"created_by": "17077"
},
{
"count": 927,
"created_by": "18677"
},
{
"count": 927,
"created_by": "11077"
},
{
"count": 926,
"created_by": "15282"
},
{
"count": 923,
"created_by": "21441"
},
{
"count": 922,
"created_by": "9815"
},
{
"count": 921,
"created_by": "12312"
},
{
"count": 918,
"created_by": "4779"
},
{
"count": 913,
"created_by": "16957"
},
{
"count": 912,
"created_by": "7085"
},
{
"count": 910,
"created_by": "15794"
},
{
"count": 909,
"created_by": "2319"
},
{
"count": 908,
"created_by": "8031"
},
{
"count": 904,
"created_by": "10311"
},
{
"count": 903,
"created_by": "17827"
},
{
"count": 899,
"created_by": "1280"
},
{
"count": 898,
"created_by": "22810"
},
{
"count": 892,
"created_by": "17796"
},
{
"count": 892,
"created_by": "9964"
},
{
"count": 883,
"created_by": "18236"
},
{
"count": 871,
"created_by": "18394"
},
{
"count": 866,
"created_by": "19073"
},
{
"count": 865,
"created_by": "7319"
},
{
"count": 865,
"created_by": "18926"
},
{
"count": 862,
"created_by": "2208"
},
{
"count": 862,
"created_by": "10283"
},
{
"count": 862,
"created_by": "11338"
},
{
"count": 862,
"created_by": "17775"
},
{
"count": 861,
"created_by": "3252"
},
{
"count": 859,
"created_by": "15999"
},
{
"count": 857,
"created_by": "12512"
},
{
"count": 855,
"created_by": "10273"
},
{
"count": 853,
"created_by": "21744"
},
{
"count": 850,
"created_by": "6307"
},
{
"count": 848,
"created_by": "16617"
},
{
"count": 847,
"created_by": "10549"
},
{
"count": 845,
"created_by": "22035"
},
{
"count": 836,
"created_by": "2859"
},
{
"count": 826,
"created_by": "15164"
},
{
"count": 824,
"created_by": "20709"
},
{
"count": 819,
"created_by": "6320"
},
{
"count": 819,
"created_by": "10428"
},
{
"count": 813,
"created_by": "11986"
},
{
"count": 812,
"created_by": "4289"
},
{
"count": 808,
"created_by": "11206"
},
{
"count": 807,
"created_by": "18555"
},
{
"count": 802,
"created_by": "16904"
},
{
"count": 800,
"created_by": "22900"
},
{
"count": 798,
"created_by": "21829"
},
{
"count": 796,
"created_by": "9653"
},
{
"count": 794,
"created_by": "6427"
},
{
"count": 793,
"created_by": "15089"
},
{
"count": 792,
"created_by": "18652"
},
{
"count": 788,
"created_by": "9586"
},
{
"count": 786,
"created_by": "11268"
},
{
"count": 783,
"created_by": "21888"
},
{
"count": 780,
"created_by": "9556"
},
{
"count": 775,
"created_by": "2589"
},
{
"count": 769,
"created_by": "15681"
},
{
"count": 767,
"created_by": "6426"
},
{
"count": 766,
"created_by": "20436"
},
{
"count": 763,
"created_by": "8915"
},
{
"count": 761,
"created_by": "3102"
},
{
"count": 761,
"created_by": "8503"
},
{
"count": 759,
"created_by": "21870"
},
{
"count": 758,
"created_by": "11819"
},
{
"count": 757,
"created_by": "22231"
},
{
"count": 754,
"created_by": "7311"
},
{
"count": 752,
"created_by": "7632"
},
{
"count": 751,
"created_by": "17161"
},
{
"count": 751,
"created_by": "7878"
},
{
"count": 744,
"created_by": "9962"
},
{
"count": 743,
"created_by": "9249"
},
{
"count": 743,
"created_by": "6689"
},
{
"count": 738,
"created_by": "10848"
}
]

'''

# Load JSON 
relations = json.loads(data)

# Generate curl commands 
for rel in relations:
    curl = f"""curl -i -X POST 'http://localhost/API/v1/rest/object/draft/service/delete_by_timestamp' -H  'content-type:application/json' -H 'x-fs-userInfo:{rel["created_by"]}' -H  'x-fs-ei:721787' -d '{{"timestamp":1722350891761}}'\n"""
    print(curl)

