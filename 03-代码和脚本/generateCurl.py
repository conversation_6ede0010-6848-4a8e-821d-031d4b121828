import json

data = '''
[
  {
    "tenant_id": "774027",
    "update_time": 1692792057962,
    "create_time": 1692792057962,
    "target_value": "button_send_msg_by_bactual__c",
    "source_label": "通知绩效系统",
    "target_type": "Function",
    "source_type": "button",
    "id": "64f6e1c743fbbe0001e52952",
    "source_value": "button_notice__c",
    "target_label": "函数"
  }
]
'''

# Load JSON 
relations = json.loads(data)

# Generate curl commands 
for rel in relations:
    curl = f"""curl -X POST 'http://localhost/API/v1/rest/object/function/service/deleteRelation' -H  'content-type:application/json' -H 'x-fs-userInfo:-10000' -H  'x-fs-ei:{rel["tenant_id"]}' -d '{{"type":"{rel["source_type"]}","value":"{rel["source_value"]}","funcApiName":"{rel["target_value"]}"}}'\n"""
    print(curl)