import json

# 从notepad获取的数据
data = """[{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202405_15_e5667b9c08504688a6dded2245a31546.pdf\",\"filename\":\"SKM_C26624051514230.pdf\",\"create_time\":1715753459186,\"size\":67810}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202406_15_4e2e9725ac88404099a8400a32b4a9ca.pdf\",\"filename\":\"SKM_C26624061515460.pdf\",\"create_time\":1718436339435,\"size\":45790.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202406_15_79a6cd9165184bc18ea714a308fef2df.pdf\",\"filename\":\"SKM_C26624061515463.pdf\",\"create_time\":1718436396428,\"size\":53935.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202407_22_57a584c3ca6941dfaf9ad32be80575bf.jpg\",\"filename\":\"717303b926ec0bdf622e91914d12831.jpg\",\"create_time\":1721640445865,\"size\":378455.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202404_12_8d4a934b51574081b0fa35d794e9226c.jpg\",\"filename\":\"625a3e9233068522b2144a6b35038ef.jpg\",\"create_time\":1712893341814,\"size\":1048576.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202406_15_c620d3f0ee6043eab2654523a22cfea4.pdf\",\"filename\":\"SKM_C26624061516250.pdf\",\"create_time\":1718438766644,\"size\":65831.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202402_28_da182b093e324fc6bb67d9487cc6e375.jpg\",\"filename\":\"dafddaf4bbcfc2f7e9504cb774285ac3.jpg\",\"create_time\":1709089316777,\"size\":187392.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202408_28_8d4e4afa310848d1891c767a7293839a.pdf\",\"filename\":\"SKM_C26624082817450.pdf\",\"create_time\":1724837018381,\"size\":81306.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202403_20_05531c4f6766410ea6e473ffd4b7d0ad.pdf\",\"filename\":\"SKM_C26624032016371_1.pdf\",\"create_time\":1710925008667,\"size\":359424.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202404_12_49c4204e76ff4acaa43b2e43b901a5c4.pdf\",\"filename\":\"验收证明.pdf\",\"create_time\":1712913130947,\"size\":304128.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202404_19_2ee25a52247c4c37a32ee330203f77ae.jpg\",\"filename\":\"河南豫达电力集团有限公司报告验收单 (1).jpg\",\"create_time\":1713497902327,\"size\":3145728.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202404_15_e38444706bb841cd8dc038db349aa587.pdf\",\"filename\":\"广佛西-四达检测验收证明.pdf\",\"create_time\":1713150895665,\"size\":5242880.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202404_15_0aa3afe6aa5f441c89126c970d5ef096.pdf\",\"filename\":\"C3-23-36贵州广益达验收单.pdf\",\"create_time\":1713158073638,\"size\":2097152.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202406_15_39daf8e717ac4c78b68b3747db74d691.pdf\",\"filename\":\"SKM_C26624061516262.pdf\",\"create_time\":1718438890003,\"size\":62393.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202501_09_87c9839b2cfd4c20b7c8d5141ac99fd5.jpg\",\"filename\":\"72fba8cc14784278506e4ef1b46bd65.jpg\",\"create_time\":1736389281326,\"size\":127689.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202401_27_7990c9db1a184f06adc2209d9715f28c.jpg\",\"filename\":\"8ae7a84264cfa9946daca4e73a2f628.jpg\",\"create_time\":1706324191582,\"size\":4194304.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202406_15_016e5a59053741fcb2b574b74d7d8492.pdf\",\"filename\":\"验收单.pdf\",\"create_time\":1718436183199,\"size\":252850.0}]"},{"value17":"[{\"ext\":\"png\",\"path\":\"N_202501_14_aabd9004333b472ebb44d4d9b76cc1bd.png\",\"filename\":\"1736835454006.png\",\"create_time\":1736835527959,\"size\":56312.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202404_18_20bf058c8e274213a0de63512979029d.pdf\",\"filename\":\"MX-C2622R_20240418_094118.pdf\",\"create_time\":1713425478566,\"size\":212992.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202407_22_fce16f6770a04bf6ae2a45237961afa7.jpg\",\"filename\":\"ee1f26060759e6bfb39aa764491ea14.jpg\",\"create_time\":1721640937664,\"size\":379018.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202406_15_ec344406f42f47348c3ff71aa0f7b6df.pdf\",\"filename\":\"SKM_C26624061516261.pdf\",\"create_time\":1718438872308,\"size\":61282.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202405_18_2ec5afb7e1d34a818208b17200b7335f.jpg\",\"filename\":\"衡水验收证明.jpg\",\"create_time\":1715997080276,\"size\":289322.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202405_24_25fc6bf87fcd463695572c6e19f7e5d5.jpg\",\"filename\":\"新疆送变电验收证明.jpg\",\"create_time\":1716512660628,\"size\":1183728.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202401_13_788123cc70164d9989e19f978c4a9613.jpg\",\"filename\":\"ec8d34a907213412aec80fd253208e5.jpg\",\"create_time\":1705112861724,\"size\":534528.0}]"},{"value17":"[{\"ext\":\"jpg\",\"path\":\"N_202401_24_8e76ef7731cc45efa20351c1d5a0215f.jpg\",\"filename\":\"f8a0552229809c3b0ddf04aefe97b8c.jpg\",\"create_time\":1706076880490,\"size\":2097152.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202401_29_b013b617a6cb44eaaf08725f09f6ee16.pdf\",\"filename\":\"SKM_C26624012914130.pdf\",\"create_time\":1706508443392,\"size\":124928.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202404_15_0cb7f721240a47ef969823c56135418c.pdf\",\"filename\":\"河南四达验收证明.pdf\",\"create_time\":1713161990295,\"size\":354304.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202403_26_dd25d0d4769c49e29abd9b2a682f9399.pdf\",\"filename\":\"验收单.pdf\",\"create_time\":1711432033449,\"size\":253952.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202401_29_5b146e7afb7d48bd869ffc27b8033549.pdf\",\"filename\":\"2023年4月--安徽明都能源建设集团有限公司-安徽明都2023年送电分公司耐张线夹X射线检测框架-无损检测-152000元-验收证明-JC-AH-WS-2023-062.pdf\",\"create_time\":1706508578493,\"size\":146432.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202403_23_5a1eb3b273f84f28a4400d652d6ea1c3.pdf\",\"filename\":\"SKM_C26624032309170.pdf\",\"create_time\":1711155741107,\"size\":159744.0}]"},{"value17":"[{\"ext\":\"pdf\",\"path\":\"N_202410_11_2611c29718b247588c85f0d9b39239cd.pdf\",\"filename\":\"C9-24-44-延安通和电业有限责任公司-验收证明.pdf\",\"create_time\":1728610595166,\"size\":119504.0}]"}]"""

# 解析数据
data_list = json.loads(data)
file_data = []

for item in data_list:
    try:
        value17_data = json.loads(item["value17"])[0]
        file_data.append(value17_data)
    except Exception as e:
        print(f"解析错误: {e}")

# 按文件名分组
filename_dict = {}
for item in file_data:
    filename = item["filename"]
    if filename in filename_dict:
        filename_dict[filename].append(item)
    else:
        filename_dict[filename] = [item]

# 找出重复的文件名
duplicates = {k: v for k, v in filename_dict.items() if len(v) > 1}

# 输出结果
if duplicates:
    print("找到以下重复的filename：")
    for filename, items in duplicates.items():
        print(f"\n文件名: {filename}")
        for item in items:
            print(f"路径: {item['path']}, 创建时间: {item['create_time']}, 大小: {item['size']}字节")
else:
    print("没有找到重复的filename") 