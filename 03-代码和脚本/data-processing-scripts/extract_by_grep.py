#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于grep搜索结果提取符合条件的任务记录
"""

import json
import subprocess
import re

def run_grep(pattern, file_path):
    """运行grep命令并返回结果"""
    try:
        result = subprocess.run(
            ['grep', '-n', pattern, file_path],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        return result.stdout.strip().split('\n') if result.stdout.strip() else []
    except Exception as e:
        print(f"运行grep命令时出错: {e}")
        return []

def extract_task_data_from_lines(file_path, start_line, end_line):
    """从指定行范围提取任务数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 提取指定范围的行
        task_lines = lines[start_line-1:end_line]
        task_text = ''.join(task_lines)
        
        # 尝试解析为JSON
        try:
            task_data = json.loads(task_text)
            return task_data
        except json.JSONDecodeError:
            # 如果不是完整的JSON，尝试找到完整的对象
            # 向前向后扩展寻找完整的JSON对象
            brace_count = 0
            start_idx = start_line - 1
            end_idx = end_line - 1
            
            # 向前找到对象开始
            while start_idx >= 0:
                line = lines[start_idx]
                if '{' in line:
                    break
                start_idx -= 1
            
            # 向后找到对象结束
            while end_idx < len(lines):
                line = lines[end_idx]
                if '}' in line and ',' not in line.strip()[-1:]:
                    break
                end_idx += 1
            
            # 重新提取
            task_text = ''.join(lines[start_idx:end_idx+1])
            task_text = task_text.strip()
            if task_text.endswith(','):
                task_text = task_text[:-1]
            
            try:
                task_data = json.loads(task_text)
                return task_data
            except json.JSONDecodeError:
                return None
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def extract_tasks_by_grep():
    """使用grep方式提取符合条件的任务"""
    
    file_path = '06-工具和配置/数据处理/datalist.json'
    
    print("🔍 正在搜索task_status为in_process的记录...")
    in_process_lines = run_grep('"task_status": "in_process"', file_path)
    
    print("🔍 正在搜索schedule为100的记录...")  
    schedule_100_lines = run_grep('"schedule": 100', file_path)
    
    print(f"找到 {len(in_process_lines)} 个in_process记录")
    print(f"找到 {len(schedule_100_lines)} 个schedule=100记录")
    
    # 提取行号
    in_process_line_nums = []
    for line in in_process_lines:
        if line:
            match = re.match(r'(\d+):', line)
            if match:
                in_process_line_nums.append(int(match.group(1)))
    
    schedule_100_line_nums = []
    for line in schedule_100_lines:
        if line:
            match = re.match(r'(\d+):', line)
            if match:
                schedule_100_line_nums.append(int(match.group(1)))
    
    print(f"in_process行号: {in_process_line_nums}")
    print(f"schedule=100行号: {schedule_100_line_nums}")
    
    # 找到可能匹配的记录（在相近行号范围内）
    matching_tasks = []
    
    for in_process_line in in_process_line_nums:
        for schedule_line in schedule_100_line_nums:
            # 如果两个条件在200行以内，可能是同一个任务
            if abs(in_process_line - schedule_line) <= 200:
                print(f"\n🎯 检查可能匹配的记录: in_process行{in_process_line}, schedule行{schedule_line}")
                
                # 确定检查范围
                start_line = min(in_process_line, schedule_line) - 50
                end_line = max(in_process_line, schedule_line) + 50
                start_line = max(1, start_line)
                
                # 提取这个范围的数据
                task_data = extract_task_data_from_lines(file_path, start_line, end_line)
                
                if task_data and isinstance(task_data, dict):
                    # 验证是否同时满足两个条件
                    if (task_data.get('task_status') == 'in_process' and 
                        task_data.get('schedule') == 100):
                        print(f"✅ 找到匹配的任务: {task_data.get('task_id')}")
                        matching_tasks.append(task_data)
                        break  # 避免重复添加同一个任务
    
    # 去重（基于task_id）
    unique_tasks = []
    seen_task_ids = set()
    for task in matching_tasks:
        task_id = task.get('task_id')
        if task_id and task_id not in seen_task_ids:
            unique_tasks.append(task)
            seen_task_ids.add(task_id)
    
    print(f"\n📊 找到 {len(unique_tasks)} 个符合条件的唯一任务记录:")
    print("=" * 80)
    
    # 输出结果并构造消息格式
    all_messages = []
    for i, task in enumerate(unique_tasks, 1):
        print(f"\n任务 {i}:")
        print(f"  任务ID: {task.get('task_id')}")
        print(f"  租户ID: {task.get('tenant_id')}")
        print(f"  任务描述: {task.get('task_describe', '')}")
        print(f"  业务API名称: {task.get('biz_api_name', '')}")
        print(f"  当前状态: {task.get('task_status')}")
        print(f"  进度: {task.get('schedule')}")
        
        # 构造目标消息格式
        message = {
            "body": [{
                "data": {
                    "taskId": task.get('task_id'),
                    "taskStatus": "completed",  # 根据要求改为completed
                    "tenantId": task.get('tenant_id')
                }
            }],
            "op": "update_schedule", 
            "reconsumeTimes": 0,
            "tenantId": task.get('tenant_id')
        }
        
        all_messages.append(message)
        
        print(f"\n  构造的消息格式:")
        print(f"  {json.dumps(message, ensure_ascii=False, indent=2)}")
        print("-" * 80)
    
    # 保存结果到文件
    if unique_tasks:
        # 保存详细格式
        with open('extracted_messages.json', 'w', encoding='utf-8') as f:
            json.dump(all_messages, f, ensure_ascii=False, indent=2)
        
        # 保存单行格式
        with open('extracted_messages_oneline.txt', 'w', encoding='utf-8') as f:
            for i, message in enumerate(all_messages, 1):
                f.write(f"消息 {i}: {json.dumps(message, ensure_ascii=False)}\n")
        
        # 保存任务详情
        with open('task_details.json', 'w', encoding='utf-8') as f:
            json.dump(unique_tasks, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 处理完成!")
        print(f"📁 所有消息已保存到: extracted_messages.json")
        print(f"📄 单行格式保存到: extracted_messages_oneline.txt") 
        print(f"📋 任务详情保存到: task_details.json")
    else:
        print("\n❌ 未找到符合条件的任务记录")
    
    return unique_tasks

if __name__ == "__main__":
    extract_tasks_by_grep() 