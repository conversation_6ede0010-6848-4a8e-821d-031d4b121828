#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于已知信息手动提取符合条件的任务记录
"""

import json
import re

def read_lines_range(file_path, start_line, end_line):
    """读取指定行范围的内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return lines[start_line-1:end_line]
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []

def extract_json_object_from_text(text):
    """从文本中提取JSON对象"""
    # 找到完整的JSON对象
    brace_count = 0
    start_pos = -1
    in_string = False
    escape_next = False
    
    for i, char in enumerate(text):
        if escape_next:
            escape_next = False
            continue
            
        if char == '\\':
            escape_next = True
            continue
            
        if char == '"' and not escape_next:
            in_string = not in_string
            
        if not in_string:
            if char == '{':
                if brace_count == 0:
                    start_pos = i
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0 and start_pos != -1:
                    # 找到完整的JSON对象
                    json_str = text[start_pos:i+1]
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        continue
    return None

def manual_extract():
    """手动提取已知的符合条件的任务记录"""
    
    file_path = '06-工具和配置/数据处理/datalist.json'
    
    # 基于之前grep搜索的结果，我们知道这些行号包含相关数据
    # in_process行号: [779, 919, 1060, 1201, 1342, 1483, 1624, 1765, 1906, 2047, 2188, 2329, 2470]
    # schedule=100行号: [969, 1110, 1251, 1392, 1533, 1674, 1815, 1956, 2097, 2238, 2379, 2520, 2801]
    
    in_process_lines = [779, 919, 1060, 1201, 1342, 1483, 1624, 1765, 1906, 2047, 2188, 2329, 2470]
    schedule_100_lines = [969, 1110, 1251, 1392, 1533, 1674, 1815, 1956, 2097, 2238, 2379, 2520, 2801]
    
    print(f"🔍 准备检查 {len(in_process_lines)} 个in_process记录和 {len(schedule_100_lines)} 个schedule=100记录")
    
    matching_tasks = []
    
    # 对于每个可能的匹配，读取更大的范围来获取完整的JSON对象
    check_ranges = [
        (770, 980),   # 包含779和969
        (910, 1120),  # 包含919和1110  
        (1050, 1260), # 包含1060和1251
        (1190, 1400), # 包含1201和1392
        (1330, 1540), # 包含1342和1533
        (1470, 1680), # 包含1483和1674
        (1610, 1820), # 包含1624和1815
        (1750, 1960), # 包含1765和1956
        (1890, 2100), # 包含1906和2097
        (2030, 2250), # 包含2047和2238
        (2170, 2390), # 包含2188和2379
        (2310, 2530), # 包含2329和2520
        (2460, 2810), # 包含2470和2801
    ]
    
    for i, (start, end) in enumerate(check_ranges, 1):
        print(f"\n🎯 检查范围 {i}: 第 {start}-{end} 行")
        
        lines = read_lines_range(file_path, start, end)
        if not lines:
            continue
            
        text = ''.join(lines)
        
        # 分割成多个可能的JSON对象
        objects = []
        current_obj = ""
        brace_count = 0
        in_string = False
        escape_next = False
        
        for char in text:
            if escape_next:
                current_obj += char
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                current_obj += char
                continue
                
            if char == '"' and not escape_next:
                in_string = not in_string
                
            if not in_string:
                if char == '{':
                    if brace_count == 0:
                        current_obj = ""
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        current_obj += char
                        # 尝试解析这个对象
                        try:
                            obj = json.loads(current_obj)
                            objects.append(obj)
                        except json.JSONDecodeError:
                            pass
                        current_obj = ""
                        continue
            
            current_obj += char
        
        # 检查每个对象是否符合条件
        for obj in objects:
            if (isinstance(obj, dict) and 
                obj.get('task_status') == 'in_process' and 
                obj.get('schedule') == 100):
                
                print(f"✅ 找到匹配的任务: {obj.get('task_id')} - {obj.get('task_describe', '')[:50]}")
                matching_tasks.append(obj)
    
    # 去重（基于task_id）
    unique_tasks = []
    seen_task_ids = set()
    for task in matching_tasks:
        task_id = task.get('task_id')
        if task_id and task_id not in seen_task_ids:
            unique_tasks.append(task)
            seen_task_ids.add(task_id)
    
    print(f"\n📊 找到 {len(unique_tasks)} 个符合条件的唯一任务记录:")
    print("=" * 80)
    
    # 输出结果并构造消息格式
    all_messages = []
    for i, task in enumerate(unique_tasks, 1):
        print(f"\n任务 {i}:")
        print(f"  任务ID: {task.get('task_id')}")
        print(f"  租户ID: {task.get('tenant_id')}")
        print(f"  任务描述: {task.get('task_describe', '')}")
        print(f"  业务API名称: {task.get('biz_api_name', '')}")
        print(f"  当前状态: {task.get('task_status')}")
        print(f"  进度: {task.get('schedule')}")
        
        # 构造目标消息格式
        message = {
            "body": [{
                "data": {
                    "taskId": task.get('task_id'),
                    "taskStatus": "completed",  # 根据要求改为completed
                    "tenantId": task.get('tenant_id')
                }
            }],
            "op": "update_schedule", 
            "reconsumeTimes": 0,
            "tenantId": task.get('tenant_id')
        }
        
        all_messages.append(message)
        
        print(f"\n  构造的消息格式:")
        print(f"  {json.dumps(message, ensure_ascii=False, indent=2)}")
        print("-" * 80)
    
    # 保存结果到文件
    if unique_tasks:
        # 保存详细格式
        with open('extracted_messages.json', 'w', encoding='utf-8') as f:
            json.dump(all_messages, f, ensure_ascii=False, indent=2)
        
        # 保存单行格式
        with open('extracted_messages_oneline.txt', 'w', encoding='utf-8') as f:
            for i, message in enumerate(all_messages, 1):
                f.write(f"消息 {i}: {json.dumps(message, ensure_ascii=False)}\n")
        
        # 保存任务详情
        with open('task_details.json', 'w', encoding='utf-8') as f:
            json.dump(unique_tasks, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 处理完成!")
        print(f"📁 所有消息已保存到: extracted_messages.json")
        print(f"📄 单行格式保存到: extracted_messages_oneline.txt") 
        print(f"📋 任务详情保存到: task_details.json")
        
        # 输出汇总
        print(f"\n📋 汇总信息:")
        print(f"   共找到 {len(unique_tasks)} 个需要更新状态的任务")
        print(f"   租户ID: {unique_tasks[0].get('tenant_id') if unique_tasks else 'N/A'}")
        
    else:
        print("\n❌ 未找到符合条件的任务记录")
    
    return unique_tasks

if __name__ == "__main__":
    manual_extract() 