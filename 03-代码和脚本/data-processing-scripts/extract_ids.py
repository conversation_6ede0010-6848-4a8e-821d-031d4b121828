#!/usr/bin/env python3
import json
import re

def extract_ids_with_team_member_role_0(filename):
    """提取包含teamMemberRole为0的记录的_id"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式匹配
        result_ids = []
        
        # 找到所有包含teamMemberRole: "0"的记录
        # 先找到所有_id和对应的teamMemberRole模式
        pattern = r'"_id":\s*"([^"]+)"[^}]*?"teamMemberRole":\s*"0"'
        matches = re.findall(pattern, content, re.DOTALL)
        
        if not matches:
            # 尝试另一种模式，可能teamMemberRole在_id之前
            pattern2 = r'"teamMemberRole":\s*"0"[^}]*?"_id":\s*"([^"]+)"'
            matches2 = re.findall(pattern2, content, re.DOTALL)
            
            # 或者使用更复杂的匹配，查找包含teamMemberRole: "0"的记录块
            records = re.findall(r'\{[^{}]*?"_id":\s*"([^"]+)"[^{}]*?\}', content, re.DOTALL)
            for record_match in records:
                full_record = re.search(rf'\{{"[^}}]*?"_id":\s*"{re.escape(record_match)}"[^}}]*?\}}', content, re.DOTALL)
                if full_record and '"teamMemberRole": "0"' in full_record.group():
                    matches.append(record_match)
        
        # 去重
        result_ids = list(set(matches))
        return result_ids
    
    except Exception as e:
        print(f"错误: {e}")
        return []

if __name__ == "__main__":
    filename = "temp.text"
    ids = extract_ids_with_team_member_role_0(filename)
    
    print("包含 'teamMemberRole': '0' 的记录的 _id:")
    for i, id_value in enumerate(ids, 1):
        print(f"{i}. {id_value}")
    
    print(f"\n总共找到 {len(ids)} 个记录") 