#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
脚本用途：从datalist.json中提取task_status为in_process且schedule为100的任务记录
使用文本处理方式避免JSON解析问题
"""

import re
import json

def extract_tasks_from_text():
    """使用文本处理方式提取符合条件的任务"""
    
    # 读取文件内容
    try:
        with open('06-工具和配置/数据处理/datalist.json', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if not content.strip():
            print("文件内容为空，请检查文件路径")
            return []
            
    except FileNotFoundError:
        print("文件未找到，请检查文件路径")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []
    
    # 分割成单个记录（假设每个记录以 { 开始，以 } 结束）
    # 先找到所有完整的JSON对象
    json_objects = []
    brace_count = 0
    current_object = ""
    in_string = False
    escape_next = False
    
    for char in content:
        if escape_next:
            current_object += char
            escape_next = False
            continue
            
        if char == '\\':
            escape_next = True
            current_object += char
            continue
            
        if char == '"' and not escape_next:
            in_string = not in_string
            
        if not in_string:
            if char == '{':
                if brace_count == 0:
                    current_object = ""
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    current_object += char
                    json_objects.append(current_object)
                    current_object = ""
                    continue
        
        current_object += char
    
    print(f"解析到 {len(json_objects)} 个JSON对象")
    
    # 分析每个对象
    matching_tasks = []
    
    for i, obj_str in enumerate(json_objects):
        try:
            # 解析JSON对象
            obj = json.loads(obj_str)
            
            # 检查条件
            if (obj.get('task_status') == 'in_process' and 
                obj.get('schedule') == 100):
                matching_tasks.append({
                    'task_id': obj.get('task_id'),
                    'tenant_id': obj.get('tenant_id'),
                    'task_describe': obj.get('task_describe', ''),
                    'biz_api_name': obj.get('biz_api_name', ''),
                    'schedule': obj.get('schedule'),
                    'task_status': obj.get('task_status'),
                    '_id': obj.get('_id')
                })
                
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，使用正则表达式提取
            task_status_match = re.search(r'"task_status":\s*"([^"]+)"', obj_str)
            schedule_match = re.search(r'"schedule":\s*(\d+)', obj_str)
            task_id_match = re.search(r'"task_id":\s*"([^"]+)"', obj_str)
            tenant_id_match = re.search(r'"tenant_id":\s*"([^"]+)"', obj_str)
            
            if (task_status_match and schedule_match and 
                task_status_match.group(1) == 'in_process' and 
                int(schedule_match.group(1)) == 100):
                
                task_describe_match = re.search(r'"task_describe":\s*"([^"]+)"', obj_str)
                biz_api_name_match = re.search(r'"biz_api_name":\s*"([^"]+)"', obj_str)
                id_match = re.search(r'"_id":\s*"([^"]+)"', obj_str)
                
                matching_tasks.append({
                    'task_id': task_id_match.group(1) if task_id_match else '',
                    'tenant_id': tenant_id_match.group(1) if tenant_id_match else '',
                    'task_describe': task_describe_match.group(1) if task_describe_match else '',
                    'biz_api_name': biz_api_name_match.group(1) if biz_api_name_match else '',
                    'schedule': int(schedule_match.group(1)),
                    'task_status': task_status_match.group(1),
                    '_id': id_match.group(1) if id_match else ''
                })
    
    print(f"\n找到 {len(matching_tasks)} 个符合条件的任务记录:")
    print("=" * 80)
    
    # 输出结果并构造消息格式
    all_messages = []
    for i, task in enumerate(matching_tasks, 1):
        print(f"\n任务 {i}:")
        print(f"  任务ID: {task['task_id']}")
        print(f"  租户ID: {task['tenant_id']}")
        print(f"  任务描述: {task['task_describe']}")
        print(f"  业务API名称: {task['biz_api_name']}")
        print(f"  当前状态: {task['task_status']}")
        print(f"  进度: {task['schedule']}")
        
        # 构造目标消息格式
        message = {
            "body": [{
                "data": {
                    "taskId": task['task_id'],
                    "taskStatus": "completed",  # 根据要求改为completed
                    "tenantId": task['tenant_id']
                }
            }],
            "op": "update_schedule",
            "reconsumeTimes": 0,
            "tenantId": task['tenant_id']
        }
        
        all_messages.append(message)
        
        print(f"\n  构造的消息格式:")
        print(f"  {json.dumps(message, ensure_ascii=False, indent=2)}")
        print("-" * 80)
    
    # 保存结果到文件
    if matching_tasks:
        # 保存详细格式
        with open('extracted_messages.json', 'w', encoding='utf-8') as f:
            json.dump(all_messages, f, ensure_ascii=False, indent=2)
        
        # 保存单行格式
        with open('extracted_messages_oneline.txt', 'w', encoding='utf-8') as f:
            for i, message in enumerate(all_messages, 1):
                f.write(f"消息 {i}: {json.dumps(message, ensure_ascii=False)}\n")
        
        # 保存任务详情
        with open('task_details.json', 'w', encoding='utf-8') as f:
            json.dump(matching_tasks, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 处理完成!")
        print(f"📁 所有消息已保存到: extracted_messages.json")
        print(f"📄 单行格式保存到: extracted_messages_oneline.txt") 
        print(f"📋 任务详情保存到: task_details.json")
    else:
        print("\n❌ 未找到符合条件的任务记录")
    
    return matching_tasks

if __name__ == "__main__":
    extract_tasks_from_text() 