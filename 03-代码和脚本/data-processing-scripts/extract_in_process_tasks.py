#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
脚本用途：从datalist.json中提取task_status为in_process且schedule为100的任务记录
并构造成指定的消息格式
"""

import json

def extract_tasks():
    """提取符合条件的任务并构造消息格式"""
    
    # 读取JSON文件
    with open('06-工具和配置/数据处理/datalist.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 存储符合条件的任务
    matching_tasks = []
    
    # 遍历所有任务记录
    for task in data:
        # 检查条件：task_status为in_process且schedule为100
        if (task.get('task_status') == 'in_process' and 
            task.get('schedule') == 100):
            matching_tasks.append({
                'task_id': task.get('task_id'),
                'tenant_id': task.get('tenant_id'),
                'task_describe': task.get('task_describe', ''),
                'biz_api_name': task.get('biz_api_name', ''),
                'submitted_by': task.get('submitted_by', []),
                'completed_num': task.get('completed_num', ''),
                'task_total_num': task.get('task_total_num', ''),
                '_id': task.get('_id')
            })
    
    print(f"找到 {len(matching_tasks)} 个符合条件的任务记录:")
    print("=" * 80)
    
    # 构造消息格式并输出
    for i, task in enumerate(matching_tasks, 1):
        print(f"\n任务 {i}:")
        print(f"  任务ID: {task['task_id']}")
        print(f"  租户ID: {task['tenant_id']}")
        print(f"  任务描述: {task['task_describe']}")
        print(f"  业务API名称: {task['biz_api_name']}")
        
        # 构造目标消息格式
        message = {
            "body": [{
                "data": {
                    "taskId": task['task_id'],
                    "taskStatus": "completed",  # 根据要求改为completed
                    "tenantId": task['tenant_id']
                }
            }],
            "op": "update_schedule",
            "reconsumeTimes": 0,
            "tenantId": task['tenant_id']
        }
        
        print(f"\n  构造的消息格式:")
        print(f"  {json.dumps(message, ensure_ascii=False, indent=2)}")
        print("-" * 80)
    
    # 将所有消息保存到文件
    if matching_tasks:
        all_messages = []
        for task in matching_tasks:
            message = {
                "body": [{
                    "data": {
                        "taskId": task['task_id'],
                        "taskStatus": "completed",
                        "tenantId": task['tenant_id']
                    }
                }],
                "op": "update_schedule",
                "reconsumeTimes": 0,
                "tenantId": task['tenant_id']
            }
            all_messages.append(message)
        
        # 保存到文件
        with open('extracted_messages.json', 'w', encoding='utf-8') as f:
            json.dump(all_messages, f, ensure_ascii=False, indent=2)
        
        print(f"\n所有消息已保存到 extracted_messages.json 文件中")
        
        # 同时生成单行格式方便复制
        with open('extracted_messages_oneline.txt', 'w', encoding='utf-8') as f:
            for i, message in enumerate(all_messages, 1):
                f.write(f"消息 {i}: {json.dumps(message, ensure_ascii=False)}\n")
        
        print(f"单行格式消息已保存到 extracted_messages_oneline.txt 文件中")
    
    return matching_tasks

if __name__ == "__main__":
    extract_tasks() 