#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def process_file(input_file, output_file):
    """
    处理文件，提取extra_info字段的值，并进行格式转换
    """
    results = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                # 解析JSON
                data = json.loads(line.strip())
                
                # 提取extra_info字段
                if 'extra_info' in data:
                    extra_info = data['extra_info']
                    
                    # 替换所有的\" 为 "
                    extra_info = extra_info.replace('\\"', '"')
                    
                    # 添加到结果列表
                    results.append(extra_info)
            except json.JSONDecodeError as e:
                print(f"解析JSON时出错: {e}")
                continue
    
    # 将结果写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for result in results:
            f.write(result + '\n')
    
    print(f"处理完成，结果已保存到 {output_file}")

if __name__ == "__main__":
    input_file = "/Users/<USER>/Desktop/工作文档/temp.text"
    output_file = "/Users/<USER>/Desktop/工作文档/extracted_data_improved.txt"
    
    # 如果文件太大，可以使用temp.copy.text作为测试
    # input_file = "/Users/<USER>/Desktop/工作文档/temp.copy.text"
    
    process_file(input_file, output_file)
