#!/usr/bin/env python3
import json
import re

# 读取原始curl命令
with open('curl_command.txt', 'r', encoding='utf-8') as f:
    content = f.read()

# 提取JSON数据部分
json_match = re.search(r"--data '({.*})'", content)
if not json_match:
    print("未找到JSON数据")
    exit(1)

json_str = json_match.group(1)
data = json.loads(json_str)
tenant_ids = data['tenantIds']

print(f"总共找到 {len(tenant_ids)} 个租户ID")

# 提取curl命令的基础部分（不包含--data）
base_curl = content.split("--data")[0].strip()

# 分批处理，每批500个
batch_size = 500
batches = []

for i in range(0, len(tenant_ids), batch_size):
    batch = tenant_ids[i:i + batch_size]
    batches.append(batch)

print(f"将分成 {len(batches)} 批")

# 生成分批的curl命令
output_content = ""
for i, batch in enumerate(batches, 1):
    batch_data = {"tenantIds": batch}
    batch_json = json.dumps(batch_data, separators=(',', ':'))
    
    curl_command = f"{base_curl} \\\n  --data '{batch_json}'\n\n"
    
    output_content += f"# 批次 {i} - 包含 {len(batch)} 个租户ID\n"
    output_content += curl_command
    output_content += f"echo \"批次 {i} 完成\"\n\n"

# 写入输出文件
with open('curl_commands_batched.txt', 'w', encoding='utf-8') as f:
    f.write(output_content)

print(f"已生成分批curl命令文件: curl_commands_batched.txt")
print(f"每批包含最多 {batch_size} 个租户ID") 