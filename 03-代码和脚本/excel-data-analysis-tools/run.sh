#!/bin/bash

# Excel大附件数据分析工具快速启动脚本
# 作者: Claude 4.0 sonnet 🐾

echo "🐾 Excel大附件数据分析工具"
echo "========================================"

echo "请选择要执行的操作:"
echo "1. 生成完整CSV统计报告（推荐）- 可直接用Excel打开"
echo "2. 运行数据完整性验证"
echo "3. 查看使用指南"
echo "4. 查看当前目录文件"
echo "5. 退出"

read -p "请输入选项 (1-5): " choice

case $choice in
    1)
        echo "🚀 正在生成CSV统计报告..."
        python3 enhanced_excel_report_generator.py
        echo ""
        echo "💡 提示：生成的CSV文件可以直接用Excel打开"
        echo "📁 输出文件已保存在当前目录"
        ;;
    2)
        echo "🔍 正在运行数据完整性验证..."
        python3 data_integrity_validator.py
        ;;
    3)
        echo "📚 打开使用指南..."
        if command -v open &> /dev/null; then
            open 使用指南.md
        elif command -v xdg-open &> /dev/null; then
            xdg-open 使用指南.md
        else
            echo "请手动打开 使用指南.md 文件"
        fi
        ;;
    4)
        echo "📁 当前目录文件列表："
        ls -la *.csv *.md *.py 2>/dev/null || echo "没有找到相关文件"
        ;;
    5)
        echo "👋 再见！"
        exit 0
        ;;
    *)
        echo "❌ 无效选项，请重新运行脚本"
        exit 1
        ;;
esac

echo "✅ 操作完成！"
