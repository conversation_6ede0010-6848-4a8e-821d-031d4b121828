#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Excel大附件数据完整性验证分析器
专门验证2025年6月1日前后数据中存储路径的重复情况

功能特性：
1. 提取两个时间段的存储路径数据
2. 交叉验证分析重复路径
3. 生成详细的验证报告
4. 提供数据清理建议

作者：Claude 4.0 sonnet 🐾
"""

import zipfile
import xml.etree.ElementTree as ET
import json
from datetime import datetime
import os
from collections import defaultdict, Counter

class DataIntegrityValidator:
    def __init__(self):
        self.excel_file = '/Users/<USER>/Desktop/工作文档/08-资源和引用/bf888888大附件数据处理.xlsx'
        self.target_date = datetime(2025, 6, 1)
        self.shared_strings = []
        
        # 数据存储
        self.before_data = []  # 6月1日前的数据
        self.after_data = []   # 6月1日后的数据
        self.all_data = []     # 全部数据
        
        # 验证结果
        self.before_paths = set()
        self.after_paths = set()
        self.duplicate_paths = set()
        self.duplicate_records = []
        
        print("🔍 Excel大附件数据完整性验证分析器启动")
        print("=" * 60)
    
    def read_shared_strings(self, zip_file):
        """读取Excel共享字符串表"""
        try:
            with zip_file.open('xl/sharedStrings.xml') as f:
                tree = ET.parse(f)
                root = tree.getroot()
                ns = {'': 'http://schemas.openxmlformats.org/spreadsheetml/2006/main'}
                
                for si in root.findall('.//si', ns):
                    t = si.find('.//t', ns)
                    self.shared_strings.append(t.text if t is not None else '')
                        
                print(f"📚 读取共享字符串: {len(self.shared_strings)} 个")
        except Exception as e:
            print(f"⚠️ 读取共享字符串失败: {e}")
    
    def get_column_index(self, cell_ref):
        """Excel列引用转数字索引"""
        col_str = ''.join([c for c in cell_ref if c.isalpha()])
        result = 0
        for char in col_str:
            result = result * 26 + (ord(char.upper()) - ord('A') + 1)
        return result - 1
    
    def get_row_index(self, cell_ref):
        """提取行号"""
        row_str = ''.join([c for c in cell_ref if c.isdigit()])
        return int(row_str) - 1 if row_str else 0
    
    def load_data_for_validation(self):
        """加载数据进行验证"""
        print("📊 加载数据进行完整性验证...")
        
        try:
            with zipfile.ZipFile(self.excel_file, 'r') as zip_file:
                self.read_shared_strings(zip_file)
                
                # 读取工作表
                with zip_file.open('xl/worksheets/sheet1.xml') as f:
                    tree = ET.parse(f)
                    root = tree.getroot()
                    ns = {'': 'http://schemas.openxmlformats.org/spreadsheetml/2006/main'}
                    
                    data = {}
                    max_row = max_col = 0
                    
                    for row in root.findall('.//row', ns):
                        for cell in row.findall('.//c', ns):
                            cell_ref = cell.get('r', '')
                            cell_type = cell.get('t', '')
                            
                            if cell_ref:
                                row_idx = self.get_row_index(cell_ref)
                                col_idx = self.get_column_index(cell_ref)
                                max_row = max(max_row, row_idx)
                                max_col = max(max_col, col_idx)
                                
                                v_elem = cell.find('.//v', ns)
                                if v_elem is not None:
                                    value = v_elem.text or ''
                                    
                                    if cell_type == 's':
                                        try:
                                            idx = int(value)
                                            if 0 <= idx < len(self.shared_strings):
                                                value = self.shared_strings[idx]
                                        except (ValueError, IndexError):
                                            pass
                                    
                                    data[(row_idx, col_idx)] = value
                    
                    # 转换为二维数组
                    excel_data = []
                    for r in range(max_row + 1):
                        row_data = []
                        for c in range(max_col + 1):
                            row_data.append(data.get((r, c), ''))
                        excel_data.append(row_data)
                    
                    print(f"📊 Excel数据: {max_row + 1} 行 x {max_col + 1} 列")
                    
                    # 解析所有附件数据
                    attachment_col_idx = 3  # 大附件数据列
                    
                    for row_idx, row in enumerate(excel_data[1:], 1):  # 跳过表头
                        if attachment_col_idx >= len(row):
                            continue
                            
                        attachment_data = row[attachment_col_idx]
                        if not attachment_data:
                            continue
                        
                        try:
                            attachments = json.loads(str(attachment_data))
                            if not isinstance(attachments, list):
                                attachments = [attachments]
                            
                            for attachment in attachments:
                                if isinstance(attachment, dict) and 'create_time' in attachment:
                                    create_time = attachment.get('create_time')
                                    dt = datetime.fromtimestamp(create_time / 1000)
                                    
                                    # 创建记录
                                    record = {
                                        'excel_row': row_idx + 1,
                                        'mt_data_id': row[1] if len(row) > 1 else '',
                                        'object_api_name': row[2] if len(row) > 2 else '',
                                        'filename': attachment.get('filename', ''),
                                        'ext': attachment.get('ext', ''),
                                        'size': attachment.get('size', 0),
                                        'size_mb': round(attachment.get('size', 0) / (1024 * 1024), 2),
                                        'create_time_formatted': dt.strftime('%Y-%m-%d %H:%M:%S'),
                                        'create_time_original': create_time,
                                        'path': attachment.get('path', ''),
                                        'draft_time': attachment.get('draft_time', ''),
                                        'time_group': '2025年6月1日前' if dt < self.target_date else '2025年6月1日后'
                                    }
                                    
                                    self.all_data.append(record)
                                    
                                    # 按时间分类
                                    if dt < self.target_date:
                                        self.before_data.append(record)
                                    else:
                                        self.after_data.append(record)
                                        
                        except Exception as e:
                            print(f"⚠️ 第{row_idx+1}行数据解析失败: {e}")
                    
                    print(f"✅ 数据加载完成:")
                    print(f"   总记录数: {len(self.all_data)}")
                    print(f"   6月1日前: {len(self.before_data)}")
                    print(f"   6月1日后: {len(self.after_data)}")
                    
                    return True
                    
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def validate_path_integrity(self):
        """验证存储路径的完整性"""
        print("🔍 开始存储路径完整性验证...")
        
        # 提取存储路径
        self.before_paths = {record['path'] for record in self.before_data if record['path']}
        self.after_paths = {record['path'] for record in self.after_data if record['path']}
        
        print(f"📊 路径统计:")
        print(f"   6月1日前唯一路径数: {len(self.before_paths)}")
        print(f"   6月1日后唯一路径数: {len(self.after_paths)}")
        
        # 查找重复路径
        self.duplicate_paths = self.before_paths.intersection(self.after_paths)
        
        print(f"🔍 重复路径检查:")
        print(f"   发现重复路径数: {len(self.duplicate_paths)}")
        
        if self.duplicate_paths:
            print("⚠️ 发现存储路径重复！")
            
            # 收集重复路径的详细记录
            for path in self.duplicate_paths:
                before_records = [r for r in self.before_data if r['path'] == path]
                after_records = [r for r in self.after_data if r['path'] == path]
                
                self.duplicate_records.append({
                    'path': path,
                    'before_records': before_records,
                    'after_records': after_records,
                    'total_count': len(before_records) + len(after_records)
                })
            
            # 按重复次数排序
            self.duplicate_records.sort(key=lambda x: x['total_count'], reverse=True)
            
        else:
            print("✅ 未发现存储路径重复，数据完整性良好！")
        
        return len(self.duplicate_paths) == 0
    
    def analyze_duplicates(self):
        """分析重复路径的详细情况"""
        if not self.duplicate_paths:
            return
        
        print("\n📋 重复路径详细分析:")
        print("=" * 60)
        
        for i, dup_info in enumerate(self.duplicate_records, 1):
            path = dup_info['path']
            before_records = dup_info['before_records']
            after_records = dup_info['after_records']
            
            print(f"\n🔍 重复路径 {i}: {path}")
            print(f"   总出现次数: {dup_info['total_count']}")
            print(f"   6月1日前: {len(before_records)} 次")
            print(f"   6月1日后: {len(after_records)} 次")
            
            # 显示具体记录
            print("   📄 6月1日前的记录:")
            for record in before_records:
                print(f"      - {record['filename']} ({record['create_time_formatted']}, {record['size_mb']} MB)")
            
            print("   📄 6月1日后的记录:")
            for record in after_records:
                print(f"      - {record['filename']} ({record['create_time_formatted']}, {record['size_mb']} MB)")
            
            # 分析可能的原因
            all_records = before_records + after_records
            filenames = [r['filename'] for r in all_records]
            sizes = [r['size'] for r in all_records]
            
            if len(set(filenames)) == 1:
                print("   💡 分析: 可能是同一文件的多次上传")
            elif len(set(sizes)) == 1:
                print("   💡 分析: 文件大小相同，可能是相同内容的不同版本")
            else:
                print("   💡 分析: 不同文件使用了相同的存储路径，需要进一步检查")
    
    def generate_validation_report(self):
        """生成验证报告"""
        print("\n📝 生成完整性验证报告...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"数据完整性验证报告_{timestamp}.md"
        
        # 计算统计信息
        total_paths = len(self.before_paths.union(self.after_paths))
        duplicate_ratio = (len(self.duplicate_paths) / total_paths * 100) if total_paths > 0 else 0
        
        report_content = f"""# Excel大附件数据完整性验证报告

## 验证概要
- **验证时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **数据来源**: bf888888大附件数据处理.xlsx
- **验证目标**: 检查2025年6月1日前后存储路径重复情况
- **验证结果**: {'❌ 发现数据完整性问题' if self.duplicate_paths else '✅ 数据完整性良好'}

## 数据统计
- **总记录数**: {len(self.all_data):,} 条
- **6月1日前记录**: {len(self.before_data):,} 条
- **6月1日后记录**: {len(self.after_data):,} 条
- **6月1日前唯一路径**: {len(self.before_paths):,} 个
- **6月1日后唯一路径**: {len(self.after_paths):,} 个
- **总唯一路径数**: {total_paths:,} 个

## 重复路径验证结果
- **重复路径数量**: {len(self.duplicate_paths)} 个
- **重复路径占比**: {duplicate_ratio:.2f}%
- **数据完整性状态**: {'存在问题' if self.duplicate_paths else '正常'}

"""
        
        if self.duplicate_paths:
            report_content += f"""
## 重复路径详细信息

### 重复路径汇总
共发现 {len(self.duplicate_paths)} 个重复的存储路径：

"""
            
            for i, dup_info in enumerate(self.duplicate_records, 1):
                path = dup_info['path']
                before_count = len(dup_info['before_records'])
                after_count = len(dup_info['after_records'])
                
                report_content += f"""
#### {i}. 路径: `{path}`
- **总出现次数**: {dup_info['total_count']}
- **6月1日前**: {before_count} 次
- **6月1日后**: {after_count} 次

**6月1日前的文件:**
"""
                
                for record in dup_info['before_records']:
                    report_content += f"- {record['filename']} ({record['create_time_formatted']}, {record['size_mb']} MB)\n"
                
                report_content += "\n**6月1日后的文件:**\n"
                
                for record in dup_info['after_records']:
                    report_content += f"- {record['filename']} ({record['create_time_formatted']}, {record['size_mb']} MB)\n"
            
            report_content += f"""
## 问题分析与建议

### 可能的原因
1. **文件重复上传**: 同一文件在不同时间被多次上传
2. **路径复用**: 系统错误地为不同文件分配了相同的存储路径
3. **数据同步问题**: 数据在不同时间点的同步导致重复
4. **存储系统问题**: 存储系统的路径分配机制存在缺陷

### 数据清理建议
1. **立即处理**: 检查重复路径对应的实际文件是否存在冲突
2. **数据去重**: 对于完全相同的文件，保留最新版本
3. **路径重新分配**: 为重复路径的文件重新分配唯一路径
4. **系统检查**: 检查文件上传和存储系统的路径分配逻辑
5. **监控机制**: 建立路径唯一性监控，防止未来出现类似问题

### 风险评估
- **数据风险**: 🔴 高风险 - 可能导致文件覆盖或丢失
- **系统风险**: 🟡 中风险 - 影响存储系统的可靠性
- **业务风险**: 🟡 中风险 - 可能影响文件的正确访问

"""
        else:
            report_content += """
## ✅ 验证通过

所有存储路径都是唯一的，未发现重复情况。数据完整性良好。

### 验证结论
- 6月1日前后的数据中，所有存储路径都是唯一的
- 没有发现路径复用或重复的情况
- 数据逻辑一致性良好，符合预期

### 建议
- 继续保持当前的数据质量
- 定期进行类似的完整性检查
- 建立自动化的数据质量监控机制

"""
        
        report_content += f"""
## 技术说明
- **验证方法**: 提取两个时间段的存储路径，进行交集运算
- **数据来源**: Excel文件中的JSON格式大附件数据
- **验证工具**: Python集合运算和数据分析
- **报告生成**: 自动化验证和报告生成

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*  
*验证工具: 数据完整性验证分析器 🐾*  
*技术支持: Claude 4.0 sonnet*
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📋 验证报告已生成: {report_file}")
        return report_file
    
    def run_validation(self):
        """执行完整的验证流程"""
        if not self.load_data_for_validation():
            return False
        
        is_valid = self.validate_path_integrity()
        
        if not is_valid:
            self.analyze_duplicates()
        
        report_file = self.generate_validation_report()
        
        print(f"\n🎉 数据完整性验证完成!")
        print(f"📁 验证报告: {report_file}")
        
        if self.duplicate_paths:
            print(f"\n⚠️ 发现 {len(self.duplicate_paths)} 个重复存储路径")
            print("📋 建议查看详细报告进行数据清理")
        else:
            print("\n✅ 数据完整性验证通过，未发现问题")
        
        return True

if __name__ == "__main__":
    validator = DataIntegrityValidator()
    validator.run_validation()
