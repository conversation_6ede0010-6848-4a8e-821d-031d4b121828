"""
# UI事件可控制按钮显示/隐藏灰度配置
ui_event_detail_button_gray={"udobj":["ALL","590064","736788","706089","739029","723381","738547","744767","703113","637179","744961","746614","736209","748060","731027","748175","736787","738798","724449","674400","730191","750714","683065","726368","158833","60000262","746440","80663","663196","725153","619318","710392","749591","741038","733452","62009308","704320","754008","750856","738545","675169","744069","750366","679483","746435","756675","721586","756682","758492","751701","758091","748879","761593","759135","753791","766785","766796","746861","767272","767335","761201","767576","767616","768040","768075","768253","768261","768278","768289","768283","768177","768225","750023","735463","768673","768738","768904","743385","684811","769283","769324","769417","769749","769674","769868","770008","770007","770006","770767","770942","771166","771177","771400","771385","771442","768070","771605","762419","771948","769217","769236","714848","767159","772572","772975","769785","773241","773295","773378","773783","773694","774022","768088","774168","774184","769907","774148","774135","774191","774332","774309","774325","619318"],"NewOpportunityObj":["746435","590061","731027","759135","726368","748879","771385","768070","767159","774022","774148","769907","774325"],"CasesObj":["ALL"],"AccountObj":["ALL"],"SalesOrderObj":["756682","706427","758474","758492","751701","758141","758141","758091","748879","768177","731027","710392","761593","746861","761201","768075","727077","741038","722160","714848","750366","772543","772646","772745","773238","773238","773295","774135","774148","762419","774184","774334","774332"],"SaleContractObj":["741038","759135"],"TPMActivityObj":["ALL"],"TPMDealerActivityCostObj":["ALL"],"CheckinsObj":["ALL"],"QuoteObj":["753791","768040","768225","770008","759135","768088"],"PaymentObj":["750023","769674","769749","769217","769236","772572","769785","774191"],"TPMActivityAgreementObj":["735454","761474","735463"],"MarketingEventObj":["684811"],"DeliveryNoteObj":["762419","750366","773241","774135","774334","774332"],"ReturnedGoodsInvoiceObj":["762419","773241","774334","774332"],"RequisitionNoteObj":["762419","773241","774334","774332"],"GoodsReceivedNoteObj":["762419","773241","774334","774332"],"InvoiceApplicationObj":["ALL"],"SparePartsConsumptionObj":["ALL"]}
# UI事件可支持从对象字段的只读、隐藏灰度配置
ui_event_detail_field_attribute_gray={"OutboundDeliveryNoteObj":["725906","715381","773255","773297"],"udobj":["ALL","590064","736788","706089","739029","678793","738490","730531","735343","704745","729607","733452","741523","725906","739711","721621","690816","62005003","691186","711995","746745","683065","744961","730191","690542","715381","736209","706427","674400","746614","743984","743109","744767","750262","724865","750714","737139","751264","731027","748060","726368","158833","746440","730112","725153","710392","748175","741038","619318","750445","751701","730173","739774","62009308","752466","711927","744069","703113","716315","735463","743385","725881","753006","754096","758048","67000015","758766","757545","728907","759307","475601","758492","759893","734209","726941","759649","755136","748879","701884","736531","758939","759731","758518","761593","********","********","756527","715992","758600","712037","755793","744964","758744","766785","766880","689436","767132","767327","767169","761597","761444","767610","767576","767720","767949","767982","768177","768261","768278","768289","768283","768444","768673","631156","768774","768904","684811","769283","739065","769324","769417","769427","769289","769544","769623","769773","770007","720836","770801","762574","770942","771166","771126","771400","771372","771442","771418","771605","762419","771793","771622","771943","772033","772230","772252","772221","772516","772547","772975","773297","773241","773255","773340","773378","773614","773783","773694","773962","750366","758973","774148","774135","774108","774184","774222","774332","774334","774309","619318"],"AccountObj":["ALL"],"CasesObj":["ALL"],"SaleContractObj":["754096","758048","679405","758766","746745","755136","758939","********","********","689436","767169","761597","767982","768774","762574","771943","772033","762419","773241","774334","774332"],"QuoteObj":["758766","757545","715992","758939","744964","689436","767169","756527","767949","753485","771622","771793","772033","772516","772547","773340","773614","774108"],"SalesOrderObj":["759307","728907","758492","759893","734209","726941","759649","748879","768177","701884","736531","710392","761593","715992","758939","712037","758600","744964","758744","689436","767169","739065","769289","769427","756527","767949","720836","753485","771372","771418","771622","771793","772033","772221","772221","772230","772516","772547","773340","762419","773614","774108","774148","774332"],"NewOpportunityObj":["755793","716315","761444","769773","774334"],"TPMActivityObj":["ALL"],"TPMDealerActivityCostObj":["ALL"],"CheckinsObj":["ALL"],"MarketingEventObj":["684811"],"DeliveryNoteObj":["762419","773241","750366","774135","774334","774332"],"ReturnedGoodsInvoiceObj":["762419","773241","774334","774332"],"RequisitionNoteObj":["762419","773241","774334","774332"],"GoodsReceivedNoteObj":["762419","773241","774334","774332"],"InvoiceApplicationObj":["ALL"],"PaymentObj":["739711"],"SparePartsConsumptionObj":["ALL"]}
""" 
# 用python解析注释中的配置，并将包含"689436"的数组最后补充"767131"
def add_767131(config):
    for key in config:
        if "689436" in config[key]:
            config[key].append("767131")
    return config



def add_767131_test():
    config = add_767131(ui_event_detail_field_attribute)
    print(config)
    assert "767131" in config["udobj"]
    config = add_767131(ui_event_detail_field_attribute)
    print(config)
    assert "767131" in config["udobj"]
    assert "767131" in config["OutboundDeliveryNoteObj"]
    assert "767131" in config["AccountObj"]
    assert "767131" in config["SaleContractObj"]
    assert "767131" in config["QuoteObj"]
    assert "767131" in config["SalesOrderObj"]
    assert "767131" in config["NewOpportunityObj"]
    assert "767131" in config["TPMActivityObj"]
    assert "767131" in config["TPMDealerActivityCostObj"]
    assert "767131" in config["CheckinsObj"]
    assert "767131" in config["MarketingEventObj"]
    assert "767131" in config["DeliveryNoteObj"]
    assert "767131" in config["ReturnedGoodsInvoiceObj"]
    assert "767131" in config["RequisitionNoteObj"]
    assert "767131" in config["GoodsReceivedNoteObj"]
    assert "767131" in config["InvoiceApplicationObj"]  
# 运行add_767131方法
add_767131_test()
