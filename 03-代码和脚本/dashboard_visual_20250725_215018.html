
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异步任务可视化仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        .alerts {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .alert-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            color: #856404;
        }
        .success-alert {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 异步任务可视化仪表板</h1>
        <p>生成时间: 2025-07-25 21:50:18</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">20</div>
            <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">5.0%</div>
            <div class="stat-label">成功率</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">6087.8s</div>
            <div class="stat-label">平均执行时间</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">1</div>
            <div class="stat-label">性能告警</div>
        </div>
    </div>

    <div class="charts-grid">
        <div class="chart-container">
            <div class="chart-title">📊 任务状态分布</div>
            <canvas id="statusChart"></canvas>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">🏢 租户性能对比</div>
            <canvas id="tenantChart"></canvas>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">⏰ 24小时任务分布</div>
            <canvas id="hourlyChart"></canvas>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">🔧 API使用统计</div>
            <canvas id="apiChart"></canvas>
        </div>
    </div>

    <div class="alerts">
        <h3>⚠️ 系统告警</h3>
        <div class="alert-item">🚨 执行时间过长: 6087.8秒</div>
    </div>

    <div class="footer">
        <p>异步任务处理系统 - 可视化仪表板 | 基于 Chart.js 生成</p>
    </div>

    <script>
        // 状态分布饼图
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ["be_queuing", "in_process", "canceled", "completed"],
                datasets: [{
                    data: [5, 13, 1, 1],
                    backgroundColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // 租户性能柱状图
        const tenantCtx = document.getElementById('tenantChart').getContext('2d');
        new Chart(tenantCtx, {
            type: 'bar',
            data: {"labels": ["736684"], "datasets": [{"label": "\u6210\u529f\u4efb\u52a1", "data": [1], "backgroundColor": "#4BC0C0"}, {"label": "\u5931\u8d25\u4efb\u52a1", "data": [1], "backgroundColor": "#FF6384"}]},
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 小时分布折线图
        const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
        new Chart(hourlyCtx, {
            type: 'line',
            data: {
                labels: ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"],
                datasets: [{
                    label: '任务数量',
                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    borderColor: '#36A2EB',
                    backgroundColor: '#36A2EB33',
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // API使用水平柱状图
        const apiCtx = document.getElementById('apiChart').getContext('2d');
        new Chart(apiCtx, {
            type: 'horizontalBar',
            data: {
                labels: ["\u5b9a\u65f6\u67e5\u8be2\u5408\u540c\u7b7e\u7f72\u72b6\u6001", "\u3010CCRM\u3011\u9500\u552e\u8ba2\u5355\u53f7\u62c9\u53d6-\u6574\u70b9", "\u3010CCRM\u3011\u9500\u552e\u8ba2\u5355\u4e1a\u7ee9\u4fdd\u5b58-\u6574\u70b9", "\u6bcf\u65e5\u66f4\u65b0\u4ea4\u4ed8\u72b6\u6001\uff08\u7ebf\u4e0b\uff09", "\u3010\u6570\u636e\u521d\u59cb\u5316\u3011\u8ba2\u5355\u4ea7\u54c1\u521d\u59cb\u5316\u8ba2\u5355\u6210\u4ea4\u4ef7", "\u5ba2\u6237\u6e05\u6d17-\u6807\u8bb0\uff08\u5f00\u542f\uff09"],
                datasets: [{
                    label: '使用次数',
                    data: [9, 6, 2, 1, 1, 1],
                    backgroundColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40"]
                }]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
