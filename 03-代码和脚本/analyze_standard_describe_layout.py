#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
StandardDescribeLayoutController StopWatch日志分析工具
专门用于分析StandardDescribeLayoutController的StopWatch日志
"""

import sys
import os
import re

try:
    from stopwatch_sorter import parse_stopwatch_log, sort_tasks_by_percent, print_sorted_tasks, generate_chart, save_to_csv
except ImportError as e:
    print(f"导入stopwatch_sorter模块时出错: {e}")
    print("请确保stopwatch_sorter.py文件与本脚本位于同一目录")
    sys.exit(1)

# 检查matplotlib是否可用
MATPLOTLIB_AVAILABLE = False
try:
    import matplotlib
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    pass

# 示例日志
EXAMPLE_LOG = """StopWatch 'StandardDescribeLayoutController': running time = 15007 ms; [findHandlerDescribes] 3049 ms = 20%; [beforeListener] 8 ms = 0%; [findObjectData] 113 ms = 1%; [runPlugin_before] 48 ms = 0%; [findDomainPluginDescribes] 888 ms = 6%; [runDomainPlugin_before] 21 ms = 0%; [executeHandler_before] 0 ms = 0%; [findObject] 137 ms = 1%; [findDetailDescribes] 436 ms = 3%; [filterDetailDescribes] 12 ms = 0%; [findLayout] 1421 ms = 9%; [findButtons] 1392 ms = 9%; [checkMultiLanguage] 0 ms = 0%; [renderEditLayout] 2220 ms = 15%; [handleFieldAlign] 28 ms = 0%; [findRecordTypes] 60 ms = 0%; [findMobileListLayouts] 212 ms = 1%; [processWaterMarkField] 80 ms = 1%; [getDetailLayout] 844 ms = 6%; [handleDescribeExtra] 1135 ms = 8%; [handleRecordTypeMatchRelation] 122 ms = 1%; [handleSlaveObjectSupportAdd] 22 ms = 0%; [processLayoutRule] 131 ms = 1%; [processLayoutByDomainPlugin] 45 ms = 0%; [calculateExpression] 2463 ms = 16%; [fillInfo] 88 ms = 1%; [buildResult] 12 ms = 0%; [executeHandler_act] 0 ms = 0%; [runPlugin_after] 0 ms = 0%"""

def analyze_standard_describe_layout(log_text=None, output_file=None, top_n=None, chart_type='bar', csv_file=None):
    """分析StandardDescribeLayoutController的StopWatch日志"""
    if not log_text:
        log_text = EXAMPLE_LOG

    try:
        # 解析日志
        watch_name, total_time, tasks = parse_stopwatch_log(log_text)

        # 排序任务
        sorted_tasks = sort_tasks_by_percent(tasks)

        # 打印结果
        print_sorted_tasks(watch_name, total_time, sorted_tasks)

        # 生成图表
        if output_file:
            if MATPLOTLIB_AVAILABLE:
                generate_chart(watch_name, total_time, sorted_tasks, output_file, top_n, chart_type)
            else:
                print("警告: matplotlib库不可用，无法生成图表。请安装matplotlib或使用CSV选项保存结果。")
                print("可以使用以下命令安装matplotlib: pip install matplotlib")

        # 保存CSV
        if csv_file:
            save_to_csv(watch_name, total_time, sorted_tasks, csv_file)

        return True
    except Exception as e:
        print(f"处理日志时出错: {e}")
        return False

def main():
    import argparse

    parser = argparse.ArgumentParser(description='分析StandardDescribeLayoutController的StopWatch日志')
    parser.add_argument('-f', '--file', help='StopWatch日志文件路径')
    parser.add_argument('-o', '--output', help='输出图表文件路径')
    parser.add_argument('-t', '--top', type=int, help='只显示前N个任务')
    parser.add_argument('-c', '--chart-type', choices=['pie', 'bar'], default='bar',
                        help='图表类型: pie(饼图) 或 bar(条形图)')
    parser.add_argument('--csv', help='将结果保存为CSV文件')
    parser.add_argument('--example', action='store_true', help='使用示例日志进行分析')
    args = parser.parse_args()

    # 获取日志内容
    if args.example:
        # 使用示例日志
        analyze_standard_describe_layout(
            output_file=args.output,
            top_n=args.top,
            chart_type=args.chart_type,
            csv_file=args.csv
        )
    elif args.file:
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                log_text = f.read()
            analyze_standard_describe_layout(
                log_text=log_text,
                output_file=args.output,
                top_n=args.top,
                chart_type=args.chart_type,
                csv_file=args.csv
            )
        except Exception as e:
            print(f"读取文件失败: {e}")
    else:
        # 如果没有提供文件，则从标准输入读取
        print("请输入StandardDescribeLayoutController的StopWatch日志内容 (输入完成后按Ctrl+D或Ctrl+Z):")
        log_text = sys.stdin.read()
        if log_text.strip():
            analyze_standard_describe_layout(
                log_text=log_text,
                output_file=args.output,
                top_n=args.top,
                chart_type=args.chart_type,
                csv_file=args.csv
            )
        else:
            parser.print_help()

if __name__ == "__main__":
    main()
