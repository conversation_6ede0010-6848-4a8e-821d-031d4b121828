
一轮技术方案评审问题汇总
1. 服务编排动作可以添加几次？是不是保持和函数一样？   **一次，和函数一样**
2. 位于列表批量、列表通用的UI按钮，是不支持配置“发起服务编排”动作，还是可选范围有限制？是的话为什么和业务按钮有区别？**需求中有列表**
3. 业务按钮执行动作配置屏幕流，批量按钮怎么交互？
4. oneflow 变量数据类型等于什么的时候，可以匹配到按钮配置页面的集合变量？
5. oneflow 变量数据类型=文件？字典？数组？  **不支持？**
   - 按钮配置页匹配变量怎么选？交互如何定？
6. 变量匹配中，变量配置了计算/引用/统计字段，若计算未完成，是直接触发还是计算完再触发oneflow？**计算完再触发oneflow**
7. 变量匹配中，单选、关联字段 标注的取显示值是什么意思？怎么取？**暂定取显示值**
@杨逍yark  以上问题需要明确下

1. 需要考虑沙盒更改集
2. 按钮编排动作删除后，引用关系如何处理？
如上两点，需要@冯津FengJin  津哥关注

##　建议
1. 和函数保持一样
2. 屏幕流只能在UI按钮中配置，业务按钮不支持。 列表批量、列表通用的不支持配置“发起服务编排”
    a. 目前批量操作不能匹配当前对象的变量
    ![alt text](../image/image1.png)
    

id,tenant_id,lock_status,extend_obj_data_id,package,object_describe_id,object_describe_api_name,version,lock_user,lock_rule,life_status_before_invalid,is_deleted,data_auth_code,change_type,out_data_auth_code,order_by,data_auth_id,out_data_auth_id,origin_source,sys_modified_time
