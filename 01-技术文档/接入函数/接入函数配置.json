[{"methodName": "Fx.global.findByApiName", "description": "根据全局变量ApiName查询全局变量值", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/global_variable/service/findGlobalVariableInfoAssignLang", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "全局变量ApiName", "type": "String"}, {"name": "lang", "description": "语言环境", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "String", "dataReturnDescription": "全局变量Value值"}}, {"methodName": "Fx.teamMember.addTeamMember", "description": "添加相关团队角色", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/action/AddTeamMember", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "dataIDs", "description": "数据ID列表", "type": "List"}, {"name": "teamMemberInfos", "description": "相关团队数据列表", "type": "List"}, {"name": "ignoreSendingRemind", "description": "忽略发送CRM消息提醒", "type": "Boolean"}], "responseMapping": {"codeField": "code", "dataReturnType": "Map", "dataField": "data", "dataReturnDescription": "成功返回的data是空对象", "errorMsgField": "message", "successCode": "0"}}}, {"methodName": "Fx.teamMember.addTeamMemberV2", "description": "添加相关团队角色V2", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/action/AddTeamMember?skipFunctionAction=${skipFunctionAction}", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "dataIDs", "description": "数据ID列表", "type": "List"}, {"name": "teamMemberInfos", "description": "相关团队数据列表", "type": "List"}, {"name": "ignoreSendingRemind", "description": "忽略发送CRM消息提醒", "type": "Boolean"}, {"name": "skipFunctionAction", "description": "跳过前后函数", "type": "Boolean"}], "responseMapping": {"codeField": "code", "dataReturnType": "Map", "dataField": "data", "dataReturnDescription": "成功返回的data是空对象", "errorMsgField": "message", "successCode": "0"}}}, {"methodName": "Fx.tag.createTagGroup", "description": "创建标签分组", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/createTagGroup", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describe_api_names", "description": "该标签分组适用的对象列表", "type": "List"}, {"name": "api_name", "description": "分组apiName", "type": "String"}, {"name": "is_applied_to_all", "description": "是否适用全部对象", "type": "Boolean"}, {"name": "tag_group_name", "description": "分组名称", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "新建的数据信息"}}, {"methodName": "Fx.tag.updateTagGroup", "description": "修改标签分组", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/updateTagGroup", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "api_name", "description": "分组apiName", "type": "String"}, {"name": "id", "description": "标签分组id", "type": "String"}, {"name": "describe_api_names", "description": "该标签分组适用的对象列表", "type": "List"}, {"name": "is_applied_to_all", "description": "是否适用全部对象", "type": "Boolean"}, {"name": "tag_group_name", "description": "分组名称", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "修改后的数据信息"}}, {"methodName": "Fx.tag.findTagsByGroupIdOrName", "description": "根据标签分组id查询标签信息", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/findTagsByGroupIdOrName", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "tag_group_id", "description": "标签分组id", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "标签信息"}}, {"methodName": "Fx.tag.deleteTagGroup", "description": "删除标签分组", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/deleteTagGroup", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "id", "description": "标签分组id", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "无"}}, {"methodName": "Fx.tag.enableTag", "description": "启用标签", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/enableTag", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "tag_api_name", "description": "标签apiName", "type": "String"}, {"name": "tag_group_api_name", "description": "标签分组apiName", "type": "String"}, {"name": "tag_group_id", "description": "标签分组id", "type": "String"}, {"name": "tag_id", "description": "标签id", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "无"}}, {"methodName": "Fx.tag.disableTag", "description": "禁用标签", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/disableTag", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "tag_api_name", "description": "标签apiName", "type": "String"}, {"name": "tag_group_api_name", "description": "标签分组apiName", "type": "String"}, {"name": "tag_group_id", "description": "标签分组id", "type": "String"}, {"name": "tag_id", "description": "标签id", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "无"}}, {"methodName": "Fx.tag.deleteTag", "description": "删除标签", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/deleteTag", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "tag_api_name", "description": "标签apiName", "type": "String"}, {"name": "tag_group_api_name", "description": "标签分组apiName", "type": "String"}, {"name": "tag_group_id", "description": "标签分组id", "type": "String"}, {"name": "tag_id", "description": "标签id", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "无"}}, {"methodName": "Fx.tag.findTagGroup", "description": "根据apiName查找标签分组及其适用对象", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/findTagGroupByName", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "group_api_name", "description": "分组标签apiName", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "分组名称及其适用对象"}}, {"methodName": "Fx.tag.findTagByTagIds", "description": "根据标签id批量查询标签描述", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/findTagByTagIds", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "tagIds", "description": "标签id列表", "type": "List"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "标签描述"}}, {"methodName": "Fx.modifyLog.getSnapShotForWeb", "description": "查询新建修改记录的快照信息", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/controller/SnapShotForWeb", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "logId", "description": "修改记录id", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "修改记录的快照信息"}}, {"methodName": "Fx.tag.removeTagForData", "description": "移除数据标签", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/remove_tag", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describeApiName", "description": "对象apiName", "type": "String"}, {"name": "tagInfo", "description": "数据id和标签id映射", "type": "Map"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "是否删除"}}, {"methodName": "Fx.tag.findTagGroups", "description": "查询所有标签组", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tag/service/findTagAndSubTag", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": []}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "标签组及标签信息"}}, {"methodName": "Fx.smartform.list", "description": "查询智能表单列表", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_global}/smartform/rest/api/list", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "objectApiName", "description": "对象apiName", "type": "String"}, {"name": "searchQueryInfo", "description": "筛选条件", "type": "String"}], "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "只能表单列表页"}}}, {"methodName": "Fx.currency.add_currency", "description": "添加货币", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/add_currency", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "currencyCode", "description": "币种代码", "type": "String"}, {"name": "exchangeRate", "description": "汇率", "type": "String"}]}}, {"methodName": "Fx.currency.find_currency_by_code", "description": "根据货币代码查询货币信息", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/find_currency_by_code", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "currencyCode", "description": "币种代码", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "货币信息"}}, {"methodName": "Fx.currency.batch_modify_rate", "description": "批量编辑汇率信息", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/batch_modify_rate", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "exchangeRateList", "description": "币种列表", "type": "List"}]}}, {"methodName": "Fx.currency.find_functional_currency", "description": "查询本位币", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/find_functional_currency", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": []}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "本位币信息"}}, {"methodName": "Fx.currency.find_currency_list", "description": "查询币种列表", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/find_currency_list", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": []}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "币种列表信息"}}, {"methodName": "Fx.currency.query_rate", "description": "根据货币代码查询货币信息", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/query_rate", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "currencyCode", "description": "币种代码", "type": "String"}, {"name": "startTime", "description": "起始时间", "type": "<PERSON>"}, {"name": "endTime", "description": "终止时间", "type": "<PERSON>"}, {"name": "pageNumber", "description": "第几页", "type": "Integer"}, {"name": "pageSize", "description": "每页多少条", "type": "Integer"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "查询历史汇率"}}, {"methodName": "Fx.global.findAreaByCodes", "description": "通过国家地区编码查询区域", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/global_data/service/get_area_by_codes", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "codes", "description": "国家地区编码", "type": "List"}], "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "地区信息"}}}, {"methodName": "Fx.global.findAreaByFsCodes", "description": "通过纷享编码查询区域", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/global_data/service/get_area_by_fs_unique_codes", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "fsUniqueCodes", "description": "纷享唯一编码", "type": "List"}], "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "地区信息"}}}, {"methodName": "Fx.object.findAllOptionDependence", "description": "查询有依赖关系的选项字段", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/option_dependence/service/findAll", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describeApiName", "description": "对象apiName", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "查询有依赖关系的选项字段"}}, {"methodName": "Fx.object.findOptionDependence", "description": "查询选项依赖关系", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/option_dependence/service/find", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describeApiName", "description": "对象apiName", "type": "String"}, {"name": "fieldApiName", "description": "父字段", "type": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "子字段", "type": "<PERSON>"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "查询选项依赖关系"}}, {"methodName": "Fx.object.findAllOptionSet", "description": "查询选项集", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/options/service/findAll", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": []}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "查询选项集"}}, {"methodName": "Fx.object.findOptionSet", "description": "指定apiName，查询选项集", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/options/service/find", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "api_name", "description": "选项集apiName", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "指定apiName，查询选项集"}}, {"methodName": "Fx.object.updateOptionSet", "description": "更新选项集", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/options/service/update", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "option", "description": "选项集", "type": "Map"}, {"name": "onlyUpdateOptions", "description": "只更新选项", "type": "Boolean"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "更新选项集"}}, {"methodName": "Fx.object.findFieldControlConfigs", "description": "查询配置的字段管控", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/resource/service/findFieldControlConfigs", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describeApiName", "description": "对象apiName", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "查询配置的字段管控"}}, {"methodName": "Fx.object.updateFieldControlConfigs", "description": "更新管控的字段", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/resource/service/updateFieldControlConfigs", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describeApiName", "description": "对象apiName", "type": "String"}, {"name": "fieldControlInfos", "description": "字段的管控信息", "type": "Map"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "查询配置的字段管控"}}, {"methodName": "Fx.global.findAllAreaByLabel", "description": "通过地区类型和名称查询区域", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/global_data/service/get_all_area_by_label", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "type", "description": "地区类型", "type": "String"}, {"name": "label", "description": "名称", "type": "String"}], "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "地区信息"}}}, {"methodName": "Fx.tools.findHandlerDescribes", "description": "查询hanlder描述", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tools/service/findHandlerDescribes", "grayTenantIds": ["1", "771080", "794608"], "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "objectApiName", "description": "对象apiName", "type": "String"}, {"name": "interfaceCode", "description": "分组apiName", "type": "String"}, {"name": "tenantId", "description": "企业Id", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "handler描述集合"}}, {"methodName": "Fx.tools.transferBrushHandler", "description": "执行handler刷库", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tools/service/transferBrushHandler", "grayTenantIds": ["1", "771080", "794608"], "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describeApiName", "description": "对象apiName", "type": "String"}, {"name": "interfaceCode", "description": "接口方法", "type": "String"}, {"name": "envList", "description": "环境集合", "type": "List"}, {"name": "envT<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "环境路由企业", "type": "String"}, {"name": "enterpriseIds", "description": "企业Id集合", "type": "List"}, {"name": "handlerDescribes", "description": "handler描述集合", "type": "List"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "handler刷库结果"}}, {"methodName": "Fx.tools.validateHandler", "description": "查询hanlder描述", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tools/service/validateHandler", "grayTenantIds": ["1", "771080", "794608"], "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "objectApiName", "description": "对象apiName", "type": "String"}, {"name": "interfaceCode", "description": "分组apiName", "type": "String"}, {"name": "tenantId", "description": "企业Id", "type": "String"}, {"name": "handlerDescribes", "description": "handler描述集合", "type": "List"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "handler描述集合"}}, {"methodName": "Fx.object.duplicatedSearchV2", "description": "执行查重规则", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/controller/DuplicateSearch", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describe_api_name", "description": "对象apiName", "type": "String"}, {"name": "type", "description": "查重的类型", "type": "String"}, {"name": "object_data", "description": "查重的数据", "type": "Map"}, {"name": "duplicate_rule_api_name", "description": "制定查重的apiName", "type": "String"}, {"name": "page_number", "description": "查重结果的页数", "type": "Integer"}, {"name": "pageSize", "description": "查重结果的数量", "type": "Map"}, {"name": "is_need_duplicate", "description": "是否需要查重", "type": "Boolean"}, {"name": "include_object_describes", "description": "是否都需要返回描述", "type": "Boolean"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "查重结果"}}, {"methodName": "Fx.object.decrypt_file", "description": "文件解密", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://***********:37810/fs-open-custom-hisense-decrypt-file/decryptFile/uploadByPath", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "path", "description": "文件path", "type": "String"}, {"name": "expireDay", "description": "有效期", "type": "Integer"}, {"name": "originalFilename", "description": "文件原始名称", "type": "String"}, {"name": "fileType", "description": "文件类型", "type": "String"}, {"name": "extension", "description": "文件扩展名", "type": "String"}, {"name": "fileSize", "description": "文件大小", "type": "Integer"}, {"name": "securityGroup", "description": "安全组（网盘文件必传）", "type": "Integer", "value": "0"}, {"name": "storageType", "description": "存储类型", "type": "String", "value": "n"}, {"name": "business", "description": "业务类型", "type": "String", "value": "CRM"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "msg", "successCode": "0", "dataReturnType": "String", "dataReturnDescription": "解密后的文件"}}, {"methodName": "Fx.object.encrypt_file", "description": "文件加密", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://***********:37810/fs-open-custom-hisense-decrypt-file/decryptFile/downloadByPath", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "path", "description": "文件path", "type": "String"}, {"name": "expireDay", "description": "有效期", "type": "Integer"}, {"name": "originalFilename", "description": "文件原始名称", "type": "String"}, {"name": "fileType", "description": "文件类型", "type": "String"}, {"name": "extension", "description": "文件扩展名", "type": "String"}, {"name": "fileSize", "description": "文件大小", "type": "Integer"}, {"name": "securityGroup", "description": "安全组（网盘文件必传）", "type": "Integer", "value": "0"}, {"name": "storageType", "description": "存储类型", "type": "String", "value": "n"}, {"name": "business", "description": "业务类型", "type": "String", "value": "CRM"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "msg", "successCode": "0", "dataReturnType": "String", "dataReturnDescription": "加密后的文件"}}, {"methodName": "Fx.object.changeOwnerSkipValidate", "description": "更换负责人", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${describeApiName}/action/ChangeOwner", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "describeApiName", "description": "对象apiName", "type": "String"}, {"name": "Data", "description": "数据和新负责人", "type": "List"}, {"name": "skipValidate", "description": "跳过校验", "type": "Boolean"}, {"name": "skipPreAction", "description": "跳过函数", "type": "Boolean"}, {"name": "skipButtonConditions", "description": "跳过按钮条件", "type": "Boolean"}, {"name": "skipTriggerApprovalFlow", "description": "跳过审批流", "type": "Boolean"}]}}, {"methodName": "Fx.object.updateOriginOrder", "description": "原单点击生效,更新原单数据的接口", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/action/Effective", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "object_id", "description": "object_id", "type": "String"}, {"name": "describe_api_name", "description": "describe_api_name", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "生效信息"}}, {"methodName": "Fx.object.changeOrder", "description": "编辑保存数据的时候需要走变更单的逻辑", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/action/Edit", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "object_data", "description": "object_data", "type": "Map"}, {"name": "details", "description": "details", "type": "Map"}, {"name": "maskFieldApiNames", "description": "maskFieldApiNames", "type": "Map"}, {"name": "seriesId", "description": "seriesId", "type": "String"}, {"name": "originalData", "description": "originalData", "type": "Map"}, {"name": "originalDetails", "description": "originalDetails", "type": "Map"}, {"name": "action_type", "description": "action_type", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "生效信息"}}, {"methodName": "Fx.rule.convertSave", "description": "转换规则", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/action/ConvertSave", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "ruleApiName", "description": "规则apiName", "type": "String"}, {"name": "sourceIds", "description": "数据ID列表", "type": "List"}, {"name": "sourceApiName", "description": "源对象apiName", "type": "String"}], "responseMapping": {"codeField": "code", "dataReturnType": "Map", "dataField": "data", "dataReturnDescription": "成功转换后保存的数据", "errorMsgField": "message", "successCode": "0"}}}, {"methodName": "Fx.teamMember.addTeamMemberV3", "description": "添加相关团队角色V3", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/action/AddTeamMember?skipFunctionAction=${skipFunctionAction}", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "dataIDs", "description": "数据ID列表", "type": "List"}, {"name": "teamMemberInfos", "description": "相关团队数据列表", "type": "List"}, {"name": "ignoreSendingRemind", "description": "忽略发送CRM消息提醒", "type": "Boolean"}, {"name": "addTeamMemberRole", "description": "同一个人的角色是否追加", "type": "Boolean"}, {"name": "skipFunctionAction", "description": "跳过前后函数", "type": "Boolean"}], "responseMapping": {"codeField": "code", "dataReturnType": "Map", "dataField": "data", "dataReturnDescription": "成功返回的data是空对象", "errorMsgField": "message", "successCode": "0"}}}, {"methodName": "Fx.teamMember.getTeamMember", "description": "查询相关团队角色", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/data_privilege/service/getTeamMember", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "objectDescribeApiName", "description": "对象apiName", "type": "String"}, {"name": "dataID", "description": "数据ID", "type": "String"}, {"name": "includeOutMember", "description": "包含外部成员", "type": "Boolean"}], "responseMapping": {"codeField": "code", "dataReturnType": "Map", "dataField": "data", "dataReturnDescription": "成功返回的data是空对象", "errorMsgField": "message", "successCode": "0"}}}, {"methodName": "Fx.teamMember.EditTeamMember", "description": "编辑相关团队", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${apiName}/action/EditTeamMember", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "apiName", "description": "对象apiName", "type": "String"}, {"name": "dataID", "description": "数据id", "type": "String"}, {"name": "teamMemberInfos", "description": "相关团队数据列表", "type": "List"}, {"name": "mode", "description": "编辑相关团队类型", "type": "String"}], "responseMapping": {"codeField": "code", "dataReturnType": "Map", "dataField": "data", "dataReturnDescription": "成功返回的data是空对象", "errorMsgField": "message", "successCode": "0"}}}, {"methodName": "Fx.global.updateVariable", "description": "更新全局变量", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/global_variable/service/update", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "json_data", "description": "全局变量数据JSON字符串", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "更新全局变量结果"}}, {"methodName": "Fx.currency.findCurrencyExchangeRate", "description": "查询货币汇率", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/find_currency_exchange_rate", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "toCurrencyCode", "description": "目标货币代码", "type": "String"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "货币汇率信息"}}, {"methodName": "Fx.currency.updateCurrencyExchangeRate", "description": "更新货币汇率", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/currency/service/update_currency_exchange_rate", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "toCurrencyCode", "description": "目标货币代码", "type": "String"}, {"name": "currencyExchangeArgs", "description": "货币汇率信息列表", "type": "List"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "更新货币汇率结果"}}, {"methodName": "Fx.tools.findPersonByMobile", "description": "根据手机号查找人员信息", "socketTimeOut": 10000, "ownerId": "1000", "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/tools/service/findPersonByMobile", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "ei", "description": "企业标识", "type": "String"}, {"name": "phone", "description": "手机号", "type": "String"}, {"name": "includeMainRole", "description": "是否包含主要角色信息", "type": "Boolean"}, {"name": "includeMainRoleName", "description": "是否包含主要角色名称", "type": "Boolean"}]}, "responseMapping": {"codeField": "code", "dataField": "data", "errorMsgField": "message", "successCode": "0", "dataReturnType": "Map", "dataReturnDescription": "人员信息查询结果"}}]