```groovy
def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.smartform.list", "", "{\"filters\":[],\"page\":{\"pageSize\":20,\"pageNumber\":1}}")
if (error) {
  log.info("error: " + message)
} else {
  log.info(data)
}
```


```groovy
def(boolean error, Map data, String message) = Fx.biz.callAPI('Fx.tag.findTagGroups')
if (error) {
  log.info('error: ' + message)
}else {
    log.info(data)
}
// 获取禁用和启用的标签
def(boolean error, Map data, String message) = Fx.biz.callAPI('Fx.tag.findTagGroups')
if (error) {
  log.info('error: ' + message)
} else {
  def enabledTags = []
  def disabledTags = []
  def groups = data.groups as List
  log.info(groups)
  groups.each { it ->
    def tags = (it as Map).label_names as List
    tags.each { tag ->
      def tagMap = (tag as Map)
      if (tagMap.enable) {
        enabledTags.add(tagMap.tag_api_name)
     } else {
        disabledTags.add(tagMap.tag_api_name)
      }
    }
  }
  log.info(enabledTags)
  log.info(disabledTags)
}

def tagInfo = [:]
def tagIds = [
  "5eaa31e181e2e30001400be3",
  // "5eaa31f681e2e30001400be4",
  // "5fc1dde40680680001fc190e",
  "5fc1de020680680001fc1913",
  "61b8168d72775b0001bcc49d",
  "64140d22205c08000167205c",
  "64182307b0308f0001ef1164",
  "642fb2458085c60001d03745"
]
tagInfo.put('635f3e3ffd487e0001cef0ac',tagIds)
def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.tag.removeTagForData", 'object_qep6N__c', tagInfo)

if(error){
  log.info("error: " + message)
}else{
  log.info(data)
}
```

```groovy
def ret =  Fx.biz.callAPI("Fx.teamMember.addTeamMember","object_solitude__c",["653b975abbff600001b623d3"], teamMembers, false)
log.info(ret)

def id = context.data._id as String
def apiName = 'object_HKjsx__c'
if (id) {
    def teamMemberInfos = Fx.biz.callAPI("Fx.teamMember.getTeamMember", apiName, id, false)
    log.info(teamMemberInfos)
}
```
```groovy
// 示例2：简化参数传递方式
def describeApiName = "assistant_and_app_consider__c"
def(boolean error, List data, String message) = Fx.biz.callAPI("Fx.recordType.findRecordTypes", describeApiName, true)
if (error) {
    log.info("error: " + message)
} else {
    log.info(data)
}
```

```groovy
// 示例：更新全局变量
// 构造全局变量数据
Map globalVariable = [
    "_id": "5c1391127cfed9b0e56b2bbc",
    "tenant_id": "74255",
    "api_name": "var_e62h1__g",
    "created_by": null,
    "create_time": 1544786194333,
    "last_modified_by": null,
    "last_modified_time": 1744184036448,
    "package": null,
    "define_type": "custom",
    "is_deleted": false,
    "label": "全局日期",
    "value": "2019-12-25",
    "type": "date",
    "remarks": "",
    "type_str": "日期"
]

// 将Map转换为JSON字符串
String jsonData = Fx.json.toJson(globalVariable)

// 调用API更新全局变量
def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.global.updateVariable", jsonData)

if (error) {
    log.info("更新全局变量失败: " + message)
} else {
    log.info("更新全局变量成功: " + data)
}

// 更新全局变量
def (Boolean error, String result, String errorMessage) = Fx.global.findByApiName("var_e62h1__g", "zh_CN")
if (error) {
    log.error("获取对象异常" + errorMessage)
} else {
  def globalVariableRet = Fx.json.parse(result) as Map
  if(globalVariableRet['success'] as Boolean) {
    def globalVariable = globalVariableRet['global_Variable'] as Map
    globalVariable['value'] = "2021-12-25"
    String jsonData = Fx.json.toJson(globalVariable)
    log.info("jsonData:" + jsonData)
    // 调用API更新全局变量
    def(boolean err, Map resultData, String msg) = Fx.biz.callAPI("Fx.global.updateVariable", jsonData)
    if (err) {
        log.info("更新全局变量失败: " + msg)
    } else {
        log.info("更新全局变量成功: " + resultData)
    }
 }
}

```

```groovy
// 示例：查询货币汇率
// 调用API查询货币汇率
String toCurrencyCode = "BOB"
def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.currency.findCurrencyExchangeRate", toCurrencyCode)

if (error) {
    log.info("查询货币汇率失败: " + message)
} else {
    log.info("查询货币汇率成功: " + data)
}
```

```groovy
// 示例：更新货币汇率
// 调用API查询货币汇率
String toCurrencyCode = "BOB"
def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.currency.findCurrencyExchangeRate", toCurrencyCode)
if (error) {
    log.info("查询货币汇率失败: " + message)
} else {
  def currencyExchanges = data['currencyExchanges'] as List
  currencyExchanges.each {
    def item = (it as Map)
    if('CNY'.equals(item['fromCurrencyCode'])) {
      item['exchangeRate'] = '1.0'
      log.info(item['exchangeRate'])
    }
  }
  log.info(currencyExchanges)
  // 调用API更新货币汇率
  def(boolean err, Map resultData, String msg) = Fx.biz.callAPI("Fx.currency.updateCurrencyExchangeRate", toCurrencyCode, currencyExchanges)
  if (err) {
      log.info("更新货币汇率失败: " + msg)
  } else {
      log.info("更新货币汇率成功: " + resultData)
  }
}
```

```groovy
// 示例：查询历史汇率信息
// 查询USD货币在指定时间范围内的历史汇率，分页查询
String currencyCode = "USD"
Long startTime = 1640995200000L  // 2022-01-01 00:00:00 的时间戳
Long endTime = 1672531199000L    // 2022-12-31 23:59:59 的时间戳
Integer pageNumber = 1
Integer pageSize = 10

def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.currency.query_rate", currencyCode, startTime, endTime, pageNumber, pageSize)

if (error) {
    log.info("查询历史汇率失败: " + message)
} else {
    log.info("查询历史汇率成功: " + data)
    
    // 处理返回的汇率数据
    if (data && data.containsKey('rates')) {
        def rates = data['rates'] as List
        log.info("共查询到 ${rates.size()} 条汇率记录")
        
        rates.each { rate ->
            def rateInfo = rate as Map
            log.info("日期: ${rateInfo['date']}, 汇率: ${rateInfo['rate']}")
        }
    }
}
```

```groovy
// 示例：查询最近30天的EUR汇率历史
String currencyCode = "EUR"
Long currentTime = System.currentTimeMillis()
Long thirtyDaysAgo = currentTime - (30 * 24 * 60 * 60 * 1000L)  // 30天前
Integer pageNumber = 1
Integer pageSize = 30

def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.currency.query_rate", currencyCode, thirtyDaysAgo, currentTime, pageNumber, pageSize)

if (error) {
    log.info("查询EUR历史汇率失败: " + message)
} else {
    log.info("查询EUR历史汇率成功")
    log.info("返回数据: " + data)
    
    // 可以进一步处理汇率数据，比如计算平均汇率
    if (data && data.containsKey('rates')) {
        def rates = data['rates'] as List
        if (rates && rates.size() > 0) {
            def totalRate = 0.0
            rates.each { rate ->
                def rateInfo = rate as Map
                if (rateInfo['rate']) {
                    totalRate += Double.parseDouble(rateInfo['rate'].toString())
                }
            }
            def avgRate = totalRate / rates.size()
            log.info("EUR最近30天平均汇率: ${avgRate}")
        }
    }
}
```

```groovy
// 示例：分页查询CNY汇率历史记录
String currencyCode = "CNY"
Long startTime = 1609459200000L  // 2021-01-01 00:00:00
Long endTime = 1640995199000L    // 2021-12-31 23:59:59
Integer pageNumber = 1
Integer pageSize = 20

// 循环分页查询所有数据
def allRates = []
def hasMoreData = true

while (hasMoreData) {
    def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.currency.query_rate", currencyCode, startTime, endTime, pageNumber, pageSize)
    
    if (error) {
        log.info("查询第${pageNumber}页数据失败: " + message)
        hasMoreData = false
    } else {
        log.info("成功查询第${pageNumber}页数据")
        
        if (data && data.containsKey('rates')) {
            def rates = data['rates'] as List
            if (rates && rates.size() > 0) {
                allRates.addAll(rates)
                pageNumber++
                
                // 如果返回的数据少于pageSize，说明已经是最后一页
                if (rates.size() < pageSize) {
                    hasMoreData = false
                }
            } else {
                hasMoreData = false
            }
        } else {
            hasMoreData = false
        }
    }
}

log.info("总共查询到 ${allRates.size()} 条CNY汇率记录")
```


```groovy
// 示例：根据手机号查找人员信息
// 基础查询示例
String ei = "78057"
String phone = "13641287059"
Boolean includeMainRole = true
Boolean includeMainRoleName = true

def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.tools.findPersonByMobile", ei, phone, includeMainRole, includeMainRoleName)

if (error) {
    log.info("查询人员信息失败: " + message)
} else {
    log.info("查询人员信息成功: " + data)
    
    // 处理返回的人员信息
    if (data && data.containsKey('persons')) {
        def persons = data['persons'] as List
        log.info("找到 ${persons.size()} 个人员")
        
        persons.each { person ->
            def personInfo = person as Map
            log.info("人员ID: ${personInfo['id']}, 姓名: ${personInfo['name']}")
            if (personInfo.containsKey('mainRole')) {
                log.info("主要角色: ${personInfo['mainRole']}")
            }
        }
    }
}
```

```groovy
// 示例：批量查询多个手机号的人员信息
List<String> phoneNumbers = ["13641287059", "13800138000", "13900139000"]

phoneNumbers.each { phoneNum ->
    def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.tools.findPersonByMobile", "78057", phoneNum, true, true)
    
    if (error) {
        log.info("查询手机号 ${phoneNum} 失败: " + message)
    } else {
        log.info("查询手机号 ${phoneNum} 成功")
        if (data && data.containsKey('persons')) {
            def persons = data['persons'] as List
            log.info("手机号 ${phoneNum} 关联 ${persons.size()} 个人员")
        }
    }
}
```

```groovy
// 示例：简化参数调用（只查询基本信息）
String phone = "13641287059"

def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.tools.findPersonByMobile", "78057", phone, false, false)

if (error) {
    log.info("查询失败: " + message)
} else {
    log.info("查询成功，返回基本人员信息: " + data)
}
```

```groovy
// 示例：带错误处理和重试机制的查询
String phone = "13641287059"
int maxRetries = 3
int retryCount = 0
boolean success = false

while (retryCount < maxRetries && !success) {
    def(boolean error, Map data, String message) = Fx.biz.callAPI("Fx.tools.findPersonByMobile", "78057", phone, true, true)
    
    if (error) {
        retryCount++
        log.info("第 ${retryCount} 次查询失败: " + message)
        if (retryCount < maxRetries) {
            log.info("等待1秒后重试...")
            Thread.sleep(1000)
        }
    } else {
        success = true
        log.info("查询成功: " + data)
        
        // 验证返回数据的完整性
        if (data && data.containsKey('persons')) {
            def persons = data['persons'] as List
            if (persons && persons.size() > 0) {
                log.info("找到有效人员信息")
            } else {
                log.info("未找到匹配的人员信息")
            }
        }
    }
}

if (!success) {
    log.info("经过 ${maxRetries} 次重试后仍然失败")
}
```
