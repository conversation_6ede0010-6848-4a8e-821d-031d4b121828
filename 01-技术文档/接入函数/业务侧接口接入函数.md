## **一、背景**       

        以前业务接入函数api的方式主要有两种，一是直接由函数侧直接负责，这就需要函数侧对接许多业务侧的东西，而且大多是重复劳动，意义不大；二是通过Proxy API接入，只需要修改下配置文件就能直接接入函数，比较方便，但这也有局限性，比如文档不规范，大多局限于公司内部使用，还有就是只能放在Fx.proxy下，不能对应的业务下，例如Fx.object/Fx.crm/Fx.bpm中。

       因此参数上述两种接入方式，将函数接入工作更多释放给业务侧，提供新的接入规范，明确函数侧和业务侧的分工

## **二、接入步骤**

#### 1、增加自己业务的配置文件

总的配置文件为名：fs-paas-function-bizApi-config，为方便管理采用includes方式引入，例如业务方增加了个 includes\_function\_for\_flow的配置，则需要在fs-paas-function-bizApi-config 加上，配置的key可以自己定义

{  
"业务流程业务": #include includes\_flow\_for\_bpm\_function,  
"阶段推进器业务": #include includes\_flow\_for\_stage\_function,  
"审批流业务": #include includes\_flow\_for\_approval\_function  
}

然后在includes\_flow\_for\_stage\_function中增加自己的配置，格式为List类型的json

#### 2、根据接口增加配置项

配置项说明：

以【对象的增量更新】举例说明

（1）原始接口为：

接口的返回结构为：

`{`

    `"code"``:` `0``,`

    `"message"``:` `"OK"``,`

    `"data"``: {`

        `"success"``:` `true`

    `}`6k,/.u\
jm'l,

`}`

-   函数方法名可以定位 Fx.object.incrementUpdate，表示对象的增量更新操作（名称不要过程，一般为Fx.xxx.xxxMethod）
-   接口的含义为：对象的增量更新操作
-   负责人填上自己的userId
-   超时时间10000ms
-   接口地址：http://${variables\_endpoint.svc\_apibus\_ncrm}/API/v1/rest/object/${describeApiName}/action/IncrementUpdate，需要传递describeApiName参数，这个需要配置在requestMapping中
-   Header：需通过 x-fs-ei 传递当前企业${tenantId}，x-fs-userinfo 传递当前用户 ${userId}，类型都是String
-   Body：两个参数data和optionInfo都是Map类型
-   Response中，code字段叫code；errorMessage字段叫message；数据字段叫data，类型为Map，含义是更新是否成功

（2）对应Config配置为：

`{`

    `"methodName"``:``"Fx.object.incrementUpdate"``,`

    `"description"``:``"对象的增量更新操作"``,`

    `"socketTimeOut"``:``10000``,`

    `"ownerId"``:` `"1000"``,`

    `"url"``:``"http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${describeApiName}/action/IncrementUpdate"``,`

    `"requestMapping"``: {`

        `"header"``: [`

            `{`

                `"name"``:``"x-fs-ei"``,`

                `"description"``:``"企业"``,`

                `"type"``:``"String"``,`

                `"value"``:``"${tenantId}"`

            `},`

            `{`

                `"name"``:``"x-fs-userinfo"``,`

                `"description"``:``"用户id"``,`

                `"type"``:``"String"``,`

                `"value"``:``"${userId}"`

            `}`

        `],`

        `"body"``: [`

            `{`

                `"name"``:``"describeApiName"``,`

                `"description"``:``"对象apiName"``,`

                `"type"``:``"String"`

            `},`

            `{`

                `"name"``:``"data"``,`

                `"description"``:``"对象数据"``,`

                `"type"``:``"Map"`

            `},`

            `{`

                `"name"``:``"optionInfo"``,`

                `"description"``:``"操作信息"``,`

                `"type"``:``"Map"`

            `}`

        `]`

    `},`

    `"responseMapping"``:{`

        `"codeField"``:``"code"``,`

        `"dataField"``:``"data"``,`

        `"errorMsgField"``:``"message"``,`

        `"successCode"``:``"0"``,`

        `"dataReturnType"``:``"Map"``,`

        `"dataReturnDescription"``:` `"更新是否成功"`

    `}`

`}`

#### 对嵌套对象扁平化处理---了解

函数中的方法中的参数一般都是平铺模式，如果接口的request body有嵌套的情况的话，希望进行扁平化处理，但这个不是强制的，具体由业务决定

假设原始请求为

`{`

    `"a"``:` `1234``,`

    `"inner"``: {`

        `"x"``: [`

            `"first"`

        `],`

        `"y"``:` `"xnoad"`

    `}`

`}`

`"body"``: [`

    `{`

        `"name"``:``"a"``,`

        `"description"``:``"xxx"``,`

        `"type"``:``"Integer"`

    `},`

    `{`

        `"name"``:``"inner"``,`

        `"description"``:``"xxx"``,`

        `"type"``:``"Map"`

    `}`

`]`

扁平化后的配置

`{`

    `"a"``:` `1234``,`

    `"inner.x[0]"``:` `"first"``,`

    `"inner.y"``:` `"xnoad"`

`}`

`"body"``: [`

    `{`

        `"name"``:``"a"``,`

        `"description"``:``"xxx"``,`

        `"type"``:``"Integer"`

    `},`

    `{`

        `"name"``:``"inner.x[0]"``,`

        `"description"``:``"xxx"``,`

        `"type"``:``"String"`

    `},`

    `{`

        `"name"``:``"inner.y"``,`

        `"description"``:``"xxx"``,`

        `"type"``:``"String"`

    `}`

`]`

**业务侧将这个config加后，通过下方 使用方法中的方法1 进行方法测试**

## **三、使用方法**

#### 1、不依赖函数发布，加好配置就可以使用，使用[Fx.biz](http://fx.biz/).callAPI调用，用于紧急需求\------不可在官网文档暴漏出去

函数写法

`String describeApiName =` `"object_o37WJ__c"`

`Map data = [`

  `"name"``:``"test1234"``,`

  `"_id"``:` `"61de84a7e033b10001719390"`

`]`

`Map optionInfo = [`

  `"isDuplicateSearch"``:` `false``,`

  `"skipFuncValidate"``:` `false`

`]`

`def(``boolean` `error, Map data, String message) = Fx.biz.callAPI(``"Fx.object.incrementUpdate"``, describeApiName, data, optionInfo)`

`if` `(error) {`

  `log.info(``"error: "` `+ message)`

`}` `else` `{`

  `log.info(data)`

`}`

通过该方式来调用接口确认接口是否调用成功

如果你不需要对外提供接口，到这里就可以结束了

#### 2、标准写法，依赖于函数服务的发布\------对外提供标准文档

上方配置完成且测试完毕后，联系函数同学增加接口 [韩统武](https://wiki.firstshare.cn/display/~hantw8341) [斯作益](https://wiki.firstshare.cn/display/~sizy9766) 

**需要你做什么？----非常重要，不提供函数测则不对接**

\----详见函数API对接文档

最终的函数写法

`String describeApiName =` `"object_o37WJ__c"`

`Map data = [`

  `"name"``:``"test1234"``,`

  `"_id"``:` `"61de84a7e033b10001719390"`

`]`

`Map optionInfo = [`

  `"isDuplicateSearch"``:` `false``,`

  `"skipFuncValidate"``:` `false`

`]`

`def(``boolean` `error, Map data, String message) = Fx.object.incrementUpdate(describeApiName, data, optionInfo)`

`if` `(error) {`

  `log.info(``"error: "` `+ message)`

`}` `else` `{`

  `log.info(data)`

`}`

## **四、后续增加参数的流程**

**如果方法提供好了已经给客户使用了，但后面又想再增加参数，只通过改配置是不行的，需要通知函数侧将方法进行重载，而且上线时也得等函数服务发布全网，才能改配置加上参数，要不然对旧写法有兼容性问题**

## ~**五、编写api使用demo，需要生成文档给客户使用-----已废弃，对接函数业务接入api对象**~

最后还需要编写下demo和记录下面的表格，我们也会review配置是否正确，出错时也方便找到负责人