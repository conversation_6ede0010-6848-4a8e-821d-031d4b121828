Date now = Date.now();
now = now -1.days;
BigDecimal nowLong = now.toTimestamp();
DateTime nowDate = DateTime.now();
BigDecimal nowDateLong = nowDate.toTimestamp(); 

def closure  = {
     List dataList ->//循环聚合所有数据 100条为一批 
     // 根据发货单id集合 查出所有发货单产品
     List deliveryNoteIds = []
     List customerIds = []
     dataList.each {
         item -> 
            deliveryNoteIds.add(item["_id"] as String)
            customerIds.add(tem["account_id"] as String)
     }

     def deliveryNoteProClosure = {
         List dataList ->//循环聚合所有数据 100条为一批 
            dataList.each{ item ->
                def devliveryNoteId = item["delivery_note_id"] as String 
                

            }
     }
    def(Boolean detailEr,PageQueryData pageQueryData, String detailMsg) = Fx.object.find("DeliveryNoteProductObj",[["delivery_note_id": Operator.IN(deliveryNoteIds)]], deliveryNoteProClosure);
    if (detailEr) {
        log.info(detailMsg)
    }  else {
        if (pageQueryData["notFinished"] && pageQueryData["lastId"]) {
            log.info("查询发货单产品数据超过2W条，最后处理id为：" + pageQueryData["lastId"])
        }
    }

     dataList.each {item ->
       String customerID = item["account_id"] as String;//客户名称
       def(Boolean detailEr, QueryResult detailData, String detailMsg) = Fx.object.find("DeliveryNoteProductObj",[["delivery_note_id": item["_id"]]], 100, 0);
       if(!detailEr){
         List detailList = detailData["dataList"] as List;
         detailList.each{item2 ->
           List findList1 = [["field_SncRO__c": item2["product_id"]], ["life_status": "normal"],["field_52gQ2__c": customerID]];
           def(Boolean detailEr1, QueryResult detailData1, String detailMsg1) = Fx.object.find("object_n7b95__c",findList1, 100, 0);  
           if(detailEr1){
             log.info("detailEr1:"+detailMsg1);
           }else if( null != detailData1 && detailData1.dataList.size()>0){
             List dataList1 = detailData1.dataList as List;
             Map map = dataList1.get(0) as Map;
             BigDecimal  field_4bnn5__c = map["field_4bnn5__c"] as BigDecimal;
             log.info(customerID +"原来在途库存数："+field_4bnn5__c);
             field_4bnn5__c = (item2["auxiliary_delivery_quantity"] as BigDecimal) + field_4bnn5__c;
             String id = map["_id"] as String;
             FxLock lock1 = Fx.lock.lock("func_lock_func_4wee2_"+id, 2);
             def (error1, data1, errorMessage1) = Fx.object.update("object_n7b95__c",id,["field_4bnn5__c":field_4bnn5__c])
             if (error1) {
               log.info(errorMessage1);
               } else {
                 log.info(customerID +"更新后在途库存数："+field_4bnn5__c+"在途库存更新成功。");
               }
             lock1.unlock();
           }else{
             Map mainMap = [
               "field_SncRO__c": item2["product_id"], //产品名称  
               "record_type":"default__c",
               "field_52gQ2__c":customerID,
               "field_4bnn5__c": item2["auxiliary_delivery_quantity"],//在途数量 
               "field_c6IBy__c":nowLong
             ]
           FxLock lock2 = Fx.lock.lock("func_lock_func_4wee2_"+customerID, 2);
           def (error2, data2, errorMessage2) = Fx.object.create("object_n7b95__c",mainMap);
           if (error2) {
             log.info(errorMessage2);
             } else {
               log.info("在途库存新建成功。");
             }
           // 需要加锁的函数代码
           lock2.unlock();
           }
         }
       }
     } 
}
 

List findList = [["life_status": "normal"], ["field_cc9m6__c": "record_E82OA__c"],["field_0w5vP__c":"option1"]];
def(Boolean findEr, PageQueryData pageQueryData, String findMsg) = Fx.object.find("DeliveryNoteObj", findList, closure);
if (findEr) {
     log.info(findMsg)
}  else {
    if (pageQueryData["notFinished"] && pageQueryData["lastId"]) {
        log.info("查询发货单数据超过2W条，最后处理id为：" + pageQueryData["lastId"])
    }
}


 // 1、查询已发货状态的发货单 2、根据客户id做分类，同一个客户下面的单子放在一起["customer_id1":[DeliveryNoteObjData1, DeliveryNoteObjData2...]]
 // 3、根据当天每个客户下的发货单查询发货单产品 4、根据产品id集合和客户id查询在途库存明细 5、此客户下已经新建在途库存明细的产品


// 1、查询已发货状态的发货单 2、根据客户id做分类，同一个客户下面的单子放在一起["customer_id1":[DeliveryNoteObjData1, DeliveryNoteObjData2...]]
// 3、根据每个客户下的发货单查询发货单产品 4、根据产品id集合和客户id查询在途库存明细 5、汇总在途数量并批量更新到库存在途明细对象中


//——————————————————————————————————————————————————————————————————————————————————
Date now = Date.now()
now = now -1.days
BigDecimal nowLong = now.toTimestamp()
DateTime nowDate = DateTime.now()
BigDecimal nowDateLong = nowDate.toTimestamp() 

def customerIdAndDeliveryIdsMap = [:]
def computeIfAbsent = {
  key, value ->
    def list = (customerIdAndDeliveryIdsMap as Map)[key] as List
    if(list == null) {
        (customerIdAndDeliveryIdsMap as Map).put(key, [value])
    } else {
        list.add(value)
        (customerIdAndDeliveryIdsMap as Map).put(key, list)
    }
}

// log.info(customerIdAndDeliveryIdsMap)
Range range = Ranges.of(1,100)
String deliveryNoteId
range.find{
  def closure  = {
      List dataList ->//循环聚合所有数据 100条为一批 
      dataList.each {
          item -> 
              computeIfAbsent(item["account_id"] as String, item["_id"] as String)
      }
  }
  List findList = [["life_status": "normal"], ["field_cc9m6__c": "record_E82OA__c"],["field_0w5vP__c":"option1"]]
  def(Boolean findEr, PageQueryData pageQueryData, String findMsg) = Fx.object.find("DeliveryNoteObj", findList, closure, deliveryNoteId)
  if (findEr) {
      log.info(findMsg)
      return true
  } else {
      log.info("发货单分页查询：" + pageQueryData)
      if (pageQueryData["notFinished"] && pageQueryData["lastId"]) {
          deliveryNoteId = pageQueryData["lastId"] as String
          return false
      } else {
          return true
      }
  }
}

log.info("处理发货单中客户数量：" + customerIdAndDeliveryIdsMap.size())
customerIdAndDeliveryIdsMap.each { customerId, deliveryIds ->
  def productIdAndQuantity = [:]
  def computeIfAbsentAdd = {
      productId, quantity ->
      def quantityBefore = (productIdAndQuantity as Map)[productId] as BigDecimal
      if(quantityBefore == null) {
          (productIdAndQuantity as Map).put(productId, quantity as BigDecimal)
      } else {
          (productIdAndQuantity as Map).put(productId,  quantityBefore + (quantity as BigDecimal))
      }
  }
  String deliveryNoteProductId
  def productObjClosure = {
      List dataList -> 
      
      dataList.each{item ->
          computeIfAbsentAdd(item["product_id"] as String, item["auxiliary_delivery_quantity"] as BigDecimal)
      }
  }
  def(Boolean detailEr, PageQueryData detailPageQueryData, String detailMsg) = Fx.object.find("DeliveryNoteProductObj",[["delivery_note_id": Operator.IN(deliveryIds)]], productObjClosure, deliveryNoteProductId)
  if (detailEr) {
    log.info("DeliveryNoteProductObj find error : " + detailMsg)
  } else {
    log.info("发货单产品分页查询：" + detailPageQueryData)
    if (detailPageQueryData["notFinished"] && detailPageQueryData["lastId"]) {
        log.info("查询发货单产品数据超过2W条，最后处理id为：" + detailPageQueryData["lastId"])
    }
    if(productIdAndQuantity) {
      def productIdAndId = [:]
      def inventoryDetailsClosure = { List dataList -> 
          dataList.each { item ->
            log.info(item)
            productIdAndId.put(item["field_SncRO__c"] as String, item["_id"] as String)
          }
      }
      def productIdList = (productIdAndQuantity as Map).keys() as List

      def(Boolean inventoryErr, PageQueryData inventoryData, String inventoryMsg) = Fx.object.find("object_n7b95__c",[["field_52gQ2__c": customerId],["field_c6IBy__c":nowLong],["field_SncRO__c":Operator.IN(productIdList)]], inventoryDetailsClosure)
      if (inventoryErr) {
          log.info("在途库存明细分页查询异常：" + inventoryMsg)
      } else {
          log.info("在途库存明细分页查询结果：" + inventoryData)
          if (inventoryData["notFinished"] && inventoryData["lastId"]) {
              log.info("查询库存&在途库存明细数据超过2W条，最后处理id为：" + inventoryData["lastId"])
          }
      }
      def batchUpdateData = [:]
      log.info("productIdAndId : " + productIdAndId)
      if (productIdAndId) {
        productIdAndId.each {
          productId, id ->
            batchUpdateData.put(id, ["field_4bnn5__c": productIdAndQuantity[productId] as BigDecimal])
        }
        def groupList = Fx.utils.listPartition((batchUpdateData as Map).keys(), 500) as List
        log.info(groupList)
        groupList.each {it ->
          def outerMap = batchUpdateData
          def groupMap = [:]
          it.each {item ->
            groupMap.put(item, outerMap[item])
          }
          def (Boolean err,List data, String errMsg) = Fx.object.batchUpdate("object_n7b95__c", groupMap)
          if(err){
            log.info("库存&在途库存明细批量更新异常：" + errMsg)
          } else {
            log.info("库存&在途库存明细批量更新条数：" + data.size())
          }
        }
      }
    }
  }
}