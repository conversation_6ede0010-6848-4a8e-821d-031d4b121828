String account_id = context.data.field_2nn2t__c as String
String CGP_id="6185f91c50b48f0001109eea" // 常规品
String SHUI_id="6185f92f80533c00014dc026" // 水
String XJ_id="6185731784e28900015f2574" // 现金账户
String HT_id="6185f93c80533c00014dcd3c" // 海苔

//---------------------------------------------优化逻辑-------------------------------------------------------------------
List queryList = [["customer_id": account_id], ["fund_account_id": Operator.IN(CGP_id, SHUI_id, XJ_id, HT_id)]]
def(err, customerAccountData, errMsg) = Fx.object.find("NewCustomerAccountObj", queryList, 4, 0)
BigDecimal CGP=0
BigDecimal SHUI=0
BigDecimal XJJE=0
BigDecimal HT=0
boolean flag = false
if (!err) {
  List customerAccountList=customerAccountData["dataList"] as List
  if(customerAccountList.size()>0){
    customerAccountList.each {item -> 
      def id = item["fund_account_id"] as String
      def availalbeBalance = item["available_balance"] as BigDecimal
      if (id == CGP_id) {
          CGP = availalbeBalance
          flag = true
      } else if (id == SHUI_id) {
          SHUI = availalbeBalance
      } else if (id == XJ_id) {
          XJJE = availalbeBalance
      } else if (id == HT_id) {
          HT = availalbeBalance
      }
    }
  }
} else {
  log.info("客户账户余额查询异常：" + errMsg)
}
log.info("常规品DC余额："+ CGP + " ，水DC余额："+ SHUI + " ，现金账户余额："+XJJE + " ，海苔DC余额："+HT)
//----------------------------------------------------------------------------------------------------------------

// List CGP_queryList = [["customer_id": account_id], ["fund_account_id": CGP_id]]
// List SHUI_queryList = [["customer_id": account_id], ["fund_account_id": SHUI_id]]
// List XJ_queryList = [["customer_id": account_id], ["fund_account_id": XJ_id]]
// List HT_queryList = [["customer_id": account_id], ["fund_account_id": HT_id]]

// def(error4, data4, msg4) = Fx.object.find("NewCustomerAccountObj", CGP_queryList, 1, 0)
// BigDecimal CGP=0
// if(!error4){
//   List CGP_list=data4["dataList"] as List
//   if(CGP_list.size()>0){
//     CGP =CGP_list[0]["available_balance"] as BigDecimal
//   }
// }else{
//   log.info("常规品DC余额查询异常：" + msg4)
// }
// log.info("常规品DC余额："+ CGP)
// def(error5, data5, msg5) = Fx.object.find("NewCustomerAccountObj", SHUI_queryList, 1, 0)
// BigDecimal SHUI=0
// if(!error5){
//   List SHUI_list=data5["dataList"] as List
//   if(SHUI_list.size()>0){
//     SHUI =SHUI_list[0]["available_balance"] as BigDecimal
//   }
// }else{
//   log.info("水DC余额查询异常：" + msg5)
// }
// log.info("水DC余额："+ SHUI)
// def(error6, data6, msg6) = Fx.object.find("NewCustomerAccountObj", XJ_queryList, 1, 0)
// BigDecimal XJJE=0
// if(!error6){
//   List XJJE_list=data6["dataList"] as List
//   if(XJJE_list.size()>0){
//     XJJE =XJJE_list[0]["available_balance"] as BigDecimal
//   }
// }else{
//     log.info("现金账户余额查询异常：" + msg6)
// }
// log.info("现金账户余额："+XJJE)
// def(error7, data7, msg7) = Fx.object.find("NewCustomerAccountObj", HT_queryList, 1, 0)
// BigDecimal HT=0
// if(!error7){
//   List HT_list=data7["dataList"] as List
//   if(HT_list.size()>0){
//     HT =HT_list[0]["available_balance"] as BigDecimal
//   }
// }else{
//     log.info("海苔DC余额查询异常：" + msg7)
// }
// log.info("海苔DC余额："+HT)

//统计客户TPM费用单结余金额
BigDecimal ZR = 0;
def (Boolean error1, String data1, String errorMessage1) =Fx.object.aggregate("object_2zTso__c",Aggregate.SUM("field_zn4XW__c"),
  2,[["life_status":"normal"],["field_174nE__c":account_id],["field_4v942__c":"option1"]])
if( error1 ){
  log.info("汇总客户TPM费用单结余金额失败："+ errorMessage1)
}else{
  if(flag){
    ZR = data1 as BigDecimal;
  }
  log.info("汇总客户TPM费用单结余金额成功:" + ZR)
}
UIEvent event = UIEvent.build(context) {
  editMaster("field_UIs4I__c": SHUI, "field_Mj0Ug__c": HT, "field_X1lSH__c": CGP, "field_21ISl__c": ZR)
}
return event