//start
//log.info(context.data)
//1.获取本订单信息。
String orderId = context.data["_id"] as  String 
BigDecimal orderPrice = context.data.field_yBa8t__c as BigDecimal 
BigDecimal testPrice = context.data.field_y2j1C__c as BigDecimal 
String customerId = context.data["account_id"] as String //客户
def(cusEr,cusData,cusMsg) =Fx.object.findById("AccountObj",  customerId) 
String customerNum =cusData["field_as18N__c"]  as String //客户编码
log.info("客户编码："+customerNum)

//查询客户账户余额下账户可用金额
BigDecimal crmPrice = 0;
BigDecimal accountBalance = 0;
String accountId = "";
def (Boolean accerror, QueryResult accdata, String accerrorMessage) = Fx.object.find("FundAccountObj",[
  ["account_no":Operator.EQ("01")],
  ["life_status": Operator.EQ("normal")]
], 1, 0);
if(!accerror && accdata.total > 0){
  accountId = accdata.dataList[0]["_id"] as String;
  log.info("账户：" + accountId)
  def (Boolean accerror1, QueryResult accdata1, String accerrorMessage1) = Fx.object.find("NewCustomerAccountObj",[
    ["customer_id":Operator.EQ(customerId)],
    ["fund_account_id":Operator.EQ(accountId)],
    ["life_status": Operator.EQ("normal")]
  ], 1, 0);
  if(!accerror1 && accdata1.total > 0){
    crmPrice = accdata1.dataList[0]["available_balance"] as BigDecimal;
    accountBalance = accdata1.dataList[0]["account_balance"] as BigDecimal;
    log.info("客户账户余额--账户可用金额：" + crmPrice)
    log.info("客户账户余额--账户余额：" + accountBalance)
  }
}

log.info("orderId:" + orderId)
//log.info("orderPrice:" + orderPrice)
log.info("customerId:" + customerId) 
OrderClient.syncAccountBalance(accountId, customerNum, crmPrice, accountBalance)
// //2.调用SAP接口获取客户最新账户余额a
// //BigDecimal sapAccount = 0.00;
// String cusAccountUrl ="http://sappi-dev.orion.com.cn:18000/sap/xi/adapter_plain?namespace=urn%3Aorion-com%3Aerp%3Andms%3Asd_credit&interface=CREDIT_NDMS_SYN_MI&service=ORION_DMS_BS&party=&agency=&scheme=&QOS=BE&sap-user=sdsc-pi01&sap-password=pi12345&sap-client=200&sap-language=EN";
// String billingUrl ="http://sappi-dev.orion.com.cn:18000/sap/xi/adapter_plain?namespace=urn%3Aorion-com%3Aerp%3Andms%3Asd_billing&interface=BILLING_NDMS_SYN_MI&service=ORION_DMS_BS&party=&agency=&scheme=&QOS=BE&sap-user=sdsc-pi01&sap-password=pi12345&sap-client=200&sap-language=EN";
// Map header=["Authorization":"Basic " +Fx.crypto.base64.encode(Fx.utils.toUTF8Bytes("username:password")),"Content-Type":"text/xml; charset=utf-8"];

// //请求报文
// String cusAccountXml =
// "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"+
// "<ns0:ZSDX_R_CREDIT_F_DMS xmlns:ns0=\"urn:sap-com:document:sap:rfc:functions\">"+
//   "<PI_KUNNR>"+customerNum+"</PI_KUNNR>"+
// //  "<PI_KUNNR>**********</PI_KUNNR>"+
// "</ns0:ZSDX_R_CREDIT_F_DMS>";  
// log.info("客户账户余额请求入参：" + cusAccountXml)
//  //请求数据：执行调用ERP接口的WEB请求
// def (Boolean cusRequestEr,HttpResult cusRequestData,String cusRequestMsg) =  Fx.http.post(cusAccountUrl,header,cusAccountXml,30000,false,0)
// if(cusRequestData==null){
//   Fx.message.throwErrorMessage("接口超时");  
// }
// cusRequestData["bytes"]=null;
// log.info("返回报文："+cusRequestData);
// def xmlSlurper =new XmlSlurper();
// String statusCode=cusRequestData["statusCode"];
// BigDecimal sapAccount = 0.00;
// if("200" == statusCode){
//   String content=cusRequestData["content"];
//   def responseMap=xmlSlurper.parseText(content)
//   def PE_SKFOR=responseMap["PE_SKFOR"] as NodeChildren;
//   sapAccount = BigDecimal.of(PE_SKFOR as String)
//   //******** 对比客户账户余额-账户余额 
//   Map createMap = [:];
//   BigDecimal differ = sapAccount - accountBalance;
//   if(differ > 0){
//     log.info("SAP接口查询金额-查询当天'账户余额' > 0，生成该客户『收入』类型，收入金额=差额；类型：其他")
//     //业务类型-收入
//     createMap.put("record_type", "income_record_type__c");
//     //收入金额
//     createMap.put("revenue_amount", differ);
//     //收入类型-其他
//     createMap.put("revenue_type", "other");
//   }else if(differ < 0){
//     log.info("SAP接口查询金额-查询当天'账户余额' < 0，生成该『支出』类型，支出金额=差额；类型：其他")
//     //业务类型-支出
//     createMap.put("record_type", "default__c");
//     //支出金额
//     createMap.put("expense_amount", differ * -1);
//     //支出类型-其他
//     createMap.put("expense_type", "other");
//   }
//   createMap.put("transaction_date",  Date.now());
  
//   //客户
//   createMap.put( "customer_id", customerId);
//   createMap.put("fund_account_id", accountId);//海苔
  
//   log.info("账户收支流水新建入参：" + createMap);
//   Long startStamp = DateTime.now().toTimestamp()
//   FxLock lock = Fx.lock.lock("lock1234", 3)
//   def (Boolean error2, Map data2, String errorMessage2) = Fx.object.create("AccountTransactionFlowObj", createMap, null, true);
//   if(!error2){
//     log.info("账户收支流水创建成功。")
//   }else{
//     log.info("账户收支流水创建失败：" + errorMessage2)
//   }
//   lock.unlock()
//   Long endStamp = DateTime.now().toTimestamp()
//   Long differStamp = endStamp - startStamp;
//   log.info("耗时：" + differStamp)
// }else{
//   sapAccount = crmPrice
// }
// //sapAccount = testPrice;
// log.info("客户账户余额："+sapAccount);

// //3.查询客户下所有已出库（field_kXuxq__c=option1）状态、Billing单数量(field_h2wHZ__c)等于0的VDO单
// def (Boolean error, QueryResult data, String errorMessage) = Fx.object.find("object_1e08K__c",[
//     ["field_kXuxq__c":"option1"],
//     ["field_h2wHZ__c": 0],
//     ["field_4vm7C__c": customerId]
// ], 100, 0);
// int total = 0 ;

// if(!error && data.total > 0){
//   total = data['total'] as int;
// }

// log.info("VDO total："+ total);
// int end = total/100 as int;
// List vdoList = [];

// Range range = Ranges.of(0, end)
//   range.each{
//     def (Boolean error11,QueryResult data11,String errorMessage11) =  Fx.object.find("object_1e08K__c",[
//       ["field_kXuxq__c":"option1"],
//       ["field_h2wHZ__c": 0],
//       ["field_4vm7C__c": customerId]
//     ], 100, it*100);
//     if(!error11 && data11.total > 0){
//       vdoList.addAll(data11.dataList as List)
//     }
    
//   }
// //log.info("vdoList："+ vdoList)  
// //4.遍历VDOList,调用SAP Billing单查询接口,获取billing单信息，更新VDO结算状态（field_iu657__c=M9KDcA2y7）。生成SAP-Billing单数据，关联客户、VDO单、Billing单。
// vdoList.each { item -> 
//   //String orderId = item["field_m1w83__c"] as String
//   //log.info("orderId:" + orderId)
  
//   List vdoDetailList = []
//   def(Boolean error1, QueryResult data1, String msg1) = Fx.object.find("object_ebco1__c", [["field_742dr__c" : item["_id"]]], 100, 0);
//   if(error1 == false && data1.dataList.size() > 0 ){
//     vdoDetailList = data1.dataList as List
//   }

//   //调用SAP Billing单查询接口,获取billing单信息，更新VDO结算状态（field_iu657__c=M9KDcA2y7）。生成SAP-Billing单数据，关联客户、VDO单、Billing单。
//   String vdoId = item["_id"]as String
//   String vdoCode = item["field_Ndikq__c"]as String
//   log.info("VDO ID" + vdoId)
//   log.info("VDO 编号" + vdoCode)
//   log.info("VDO 关联客户" + item["field_4vm7C__c"])

//   //请求报文
//   String billingXml =
//     "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
//     "    <ns0:ZSDX_R_BILLING_F_DMS xmlns:ns0=\"urn:sap-com:document:sap:rfc:functions\">\n" +
//     "    <PT_VBELN>\n" +
//     "       <item>\n" +
//     "             <IHREZ_E>" + vdoCode + "</IHREZ_E>\n" +
// //    "             <IHREZ_E>0105402886</IHREZ_E>\n" +
//     "       </item>\n" +
//     "  </PT_VBELN>\n" +
//     "</ns0:ZSDX_R_BILLING_F_DMS>";

//   log.info("请求报文：" + billingXml);
//   //请求数据：执行调用ERP接口的WEB请求
//   def(Boolean billingReqEr, HttpResult billingReqData, String billingReqMsg) = Fx.http.post(billingUrl, header, billingXml, 30000, false, 0)
//   if (billingReqData == null) {
//       Fx.message.throwErrorMessage("接口超时，请编辑需要同步字段重新触发同步");
//   }
//   billingReqData["bytes"] = null;
//   log.info("返回报文：" + billingReqData);
  
//   //返回报错信息,报错信息会显示在数据维护的日志里面
//   if (billingReqEr) {
//       Fx.message.throwErrorMessage(billingReqMsg);
//   } else if (billingReqData["statusCode"] != 200) {
//       Fx.message.throwErrorMessage("请检查【CRM填写】参数是否错误");
//   }

//   def billingXmlSlurper = new XmlSlurper();
//   String billingStatusCode = billingReqData["statusCode"];
//   if ("200" == billingStatusCode) {
//     String billingContent = billingReqData["content"];
        
//     def billingRspMap = billingXmlSlurper.parseText(billingContent)
//     log.info("billingRspMap:" + billingRspMap);
        
//     def PT_HEAD = billingRspMap["PT_HEAD"] as NodeChildren;
//     def PT_ITEM = billingRspMap["PT_ITEM"] as NodeChildren;

//     if ("" == PT_HEAD as String) {
//       log.info("根据Vdo编号获取Billing单数据为空")
//     } else {
//       Map headMap = PT_HEAD as Map
//       def IHREZ_E = billingRspMap["PT_HEAD"]["item"]["IHREZ_E"] as NodeChildren;
//       def VBELN = billingRspMap["PT_HEAD"]["item"]["VBELN"] as NodeChildren;
//       def FKDAT = billingRspMap["PT_HEAD"]["item"]["FKDAT"] as NodeChildren;
//       def NETWR = billingRspMap["PT_HEAD"]["item"]["NETWR"] as NodeChildren;
//       def MWSBK = billingRspMap["PT_HEAD"]["item"]["MWSBK"] as NodeChildren;
            
//       def PT_ITEMitem = billingRspMap["PT_ITEM"] as NodeChildren;
//       log.info("PT_ITEMitem: "+ PT_ITEMitem)
//       List sapBillingDetailList = []
//       List returnFields = ["VBELN","MATNR","FKIMG","KZWI1","KZWI4","KZWI5","KZWI6"]
      
//       PT_ITEMitem.children().each{
//         Map dataMap=[:]
//         returnFields.each{field->
//           String fieldV=(it[field] as NodeChildren).text() as String;
//           if(fieldV!=null&&""!=fieldV ){
//              dataMap.put(field,fieldV)
//           }
//         }
//         sapBillingDetailList.add(dataMap)
//       }
//       log.info("sapBillingDetailList: " + sapBillingDetailList)

//       def MATNR = billingRspMap["PT_ITEM"]["item"]["MATNR"] as NodeChildren;
//       def FKIMG = billingRspMap["PT_ITEM"]["item"]["FKIMG"] as NodeChildren;
//       def KZWI1 = billingRspMap["PT_ITEM"]["item"]["KZWI1"] as NodeChildren;
//       def KZWI4 = billingRspMap["PT_ITEM"]["item"]["KZWI4"] as NodeChildren;
//       def KZWI5 = billingRspMap["PT_ITEM"]["item"]["KZWI5"] as NodeChildren;
//       def KZWI6 = billingRspMap["PT_ITEM"]["item"]["KZWI6"] as NodeChildren;
//       List orderOwnerList = []
//       log.info("orderId:" + orderId)
//       def(Boolean orderEr, QueryResult orderData, String orderMsg) = Fx.object.findById( "SalesOrderObj", orderId) 
//       log.info("获取订单:" + orderEr+orderMsg)
//       if(!orderEr){
//         orderOwnerList = orderData["owner"] as List //订单负责人
//         log.info("订单负责人:" + orderOwnerList)
//       }
//       Map billingObjectData = [
//           "field_J0q1V__c": item["field_4vm7C__c"] as String, //客户
//           "field_w19ph__c": item["_id"]  as String, //VDO单ID
//           "field_3l7Dj__c": IHREZ_E as String, //VDO编码
//           "field_nh3qo__c": orderId, //订单
//           "field_eyoTD__c": NETWR.text() as String, //未税净额
//           "field_585H8__c": MWSBK.text() as String, //税值
//           "field_bDplS__c": VBELN as String, //开票凭证
//           "field_SWel9__c": Date.of(FKDAT.text() as String)//出具发票索引和打印的出具发票日期
//       ]
//       Map billingDetailsData = [:];
//       List billingDetailsList = [];
//       sapBillingDetailList.each{sapBillingDetail ->
//       log.info("sapBillingDetail: " + sapBillingDetail)
//         if(vdoDetailList.size() > 0){
//           vdoDetailList.each{vdoDetail->
//               String productCode = vdoDetail["field_QmM8n__c"] as String
//               log.info("VDO明细产品编码：" + productCode)
//               String sapMATNR = sapBillingDetail["MATNR"] as String;
//               log.info("sapBillingDetail产品编码：" + sapMATNR)
//               String productId = vdoDetail["field_011pL__c"] as String
//               if(productCode == sapMATNR){
//                   Map billingDetailsMap = [
//                     "field_yKRyd__c": vdoDetail["field_0b4fK__c"] as String, //订单产品ID
//                     "field_v11SP__c": productId, //产品ID
//                     "field_rpXO1__c": sapBillingDetail["MATNR"] as String, //产品编码
//                     "field_a9nb9__c": sapBillingDetail["FKIMG"] as String, //实际已开票数量
//                     "field_DEw1t__c": sapBillingDetail["KZWI1"] as String, //未税价格
//                     "field_jrbdY__c": sapBillingDetail["KZWI4"] as String, //合同折扣
//                     "field_0Lu6S__c": sapBillingDetail["KZWI5"] as String, //促销折扣
//                     "field_f92pt__c": sapBillingDetail["KZWI6"] as String //事后折扣
//                   ]
//                   billingDetailsList.add(billingDetailsMap);
//                 }else{
//                   log.info("产品编码不匹配，忽略")
//                 }
//             }
//         }else{
//             def(error2, data2, errorMessage2) = Fx.object.create("object_jGT25__c", billingObjectData)
//             if( error2 ){
//               log.info("创建SAP-Billing单失败：" + errorMessage2)
//             }else{
//               log.info("创建SAP-Billing单成功：" + errorMessage2)
//               Map vdoUpdateMap = [
//                "field_iu657__c": "M9KDcA2y7"
//                 ]
//               def(error3, data3, errorMessage3) = Fx.object.update("object_1e08K__c", vdoId, vdoUpdateMap)
//               if( error2 ){
//                 log.info("更新VDO失败：" + errorMessage3)
//               }else{
//               log.info("更新VDO成功：" + errorMessage3)
//               }
//             }
//         }
//       }
//       billingDetailsData.put("object_l8m4Z__c", billingDetailsList)
//       log.info("billingObjectData: " + billingObjectData);
//       log.info("billingDetailsData: " + billingDetailsData);
//       def(Boolean error2, Map data2, String errorMessage2) = Fx.object.create("object_jGT25__c", billingObjectData, billingDetailsData, false)
//       if( error2 ){
//         log.info("创建SAP-Billing单失败：" + errorMessage2)
//       }else{
//         log.info("创建SAP-Billing单成功")
//         Map vdoUpdateMap = [
//         "field_iu657__c": "M9KDcA2y7"
//          ]
//         def(error3, data3, errorMessage3) = Fx.object.update("object_1e08K__c", vdoId, vdoUpdateMap)
//         if( error2 ){
//           log.info("更新VDO失败：" + errorMessage3)
//         }else{
//           log.info("更新VDO成功")
//         }
//       }
//     }
//   }else {
//     log.info("请求接口异常")
//   }
// }

OrderClient.createBillingAccount(customerId, orderId, context.data["owner"] as List)

//5.汇总客户总订单（life_status=normal）现金支付金额含税（field_yBa8t__c）值b。
BigDecimal orderPriceSum = 0
BigDecimal billingPriceSum =0
BigDecimal coolPriceSum =0
def (Boolean error4, String data4, String errorMessage4) =Fx.object.aggregate("SalesOrderObj",Aggregate.SUM("field_yBa8t__c"),
  2,[["life_status":"normal"],["account_id":customerId]])
  if( error4 ){
    log.info("汇总客户订单金额失败："+ errorMessage4)
  }else{
    log.info("汇总客户订单金额成功：" + data4)
  }
  
//5.汇总客户总订单（life_status=normal）冻结释放金额含税（field_Z2Ts6__c）值b。
def (Boolean error10, String data10, String errorMessage10) =Fx.object.aggregate("SalesOrderObj",Aggregate.SUM("field_Z2Ts6__c"),
  2,[["life_status":"normal"],["account_id":customerId]])
  if( error10 ){
    log.info("汇总冻结释放金额失败："+ errorMessage10)
  }else{
    log.info("汇总冻结释放金额成功：" + data10)
  }

//6.汇总客户总Billing含税金额（field_dx9F5__c）值c。
def (Boolean error5, String data5, String errorMessage5) =Fx.object.aggregate("object_jGT25__c",Aggregate.SUM("field_dx9F5__c"),
  2,[["life_status":"normal"],["field_in156__c":"option1"],["field_J0q1V__c":customerId]])
  if( error5 ){
    log.info("汇总客户Billing金额失败：" + errorMessage5)
  }else{
    log.info("汇总客户Billing金额成功：" + data5)
  }


//7.在途金额d=b-c。插入到客户订货额度对象(客户、a、b、c、d),根据客户更新或新增
if(data4!=null)
{
  orderPriceSum = data4 as BigDecimal
}
if(data5!=null)
{
  billingPriceSum = data5 as BigDecimal
} 
if(data10!=null)
{
  coolPriceSum = data10 as BigDecimal
} 
 
BigDecimal onWayPrice = orderPriceSum - billingPriceSum + coolPriceSum
log.info("orderPriceSum:"+orderPriceSum+";billingPriceSum:"+billingPriceSum+";onWayPrice:"+onWayPrice)

def (Boolean error6, QueryResult data6, String errorMessage6) = Fx.object.find("object_81q3I__c",[
    ["field_12XQT__c": customerId]
], 1, 0);
log.info("查询客户订货额度结果：" + data6.total + "条")
Map customOrderQuotaMap = [
    "field_12XQT__c":customerId,
    "field_m77vN__c":sapAccount, //现金账户金额
    "field_IL1JF__c":orderPriceSum, //订单总金额（含税）
    "field_z96tv__c":billingPriceSum, //Billing总金额（含税）
    "field_hn17g__c":onWayPrice, //在途金额
    "field_O2o21__c":coolPriceSum//总冻结释放金额（含税）
    ]
log.info("客户订货额度入参：" + customOrderQuotaMap)
if(data6.dataList.size() > 0){
  Map customOrderMap = (data6.dataList as List).get(0)
  String customOrderId = customOrderMap["_id"] as String
  def (error7, data7, errorMessage7) = Fx.object.update("object_81q3I__c", customOrderId, customOrderQuotaMap)
  if( error7 ){
    log.info("更新客户订货额度失败："+ errorMessage7)
  }else{
    log.info("更新客户订货额度成功！")
  }
}else{
  def (error8, data8, errorMessage8) = Fx.object.create("object_81q3I__c", customOrderQuotaMap)
  if( error8 ){
    log.info("创建客户订货额度失败："+ errorMessage8)
  }else{
    log.info("创建客户订货额度成功！")
  }
}
//8.本订单现金支付金额（含税）e。【e <= (a-d) ? 已回款（field_irr56__c=wbOcwaI7g）: 未回款（field_irr56__c=omE1rdxEN）】
Map orderUpdateDMap = [:];
BigDecimal endPrice = sapAccount - onWayPrice;
log.info("客户账户余额-在途金额（订单总金额-Billing总金额 + 冻结释放金额） = " + endPrice)
log.info("订单金额：" + orderPrice)
//orderUpdateDMap.put("field_y2j1C__c", sapAccount)
if(0 <= endPrice){
  log.info(endPrice+">=0,"+"订单回款状态：已回款")
  orderUpdateDMap.put("field_q1Wq0__c", "7Ui1d8JXP")
}else{
  log.info("订单回款状态：未回款")
  orderUpdateDMap.put("field_q1Wq0__c", "option1")
}
log.info("更新订单入参：" + orderUpdateDMap)
def (error9, data9, errorMessage9) = Fx.object.update("SalesOrderObj", orderId, orderUpdateDMap)
if(error9){
  log.info("更新订单回款状态失败：" + errorMessage9)
}else{
  log.info("更新订单回款状态成功")
}
//end