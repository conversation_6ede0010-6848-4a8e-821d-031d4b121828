String id =  context.data._id as String;
log.info(id);
Map updateMap = [
  "field_yvmv1__c":0,//海苔DC占用金额
  "field_QpoL4__c":0,//水DC占用金额
  "field_2WfTj__c":0,//常规DC占用金额
  "field_yBa8t__c":0,//现金支付金额含税
  "field_dsxDa__c":0//待打款金额
  ]
  
def (error, data, errorMessage) = Fx.object.update("SalesOrderObj", id,  updateMap);
if(error){
  log.info(errorMessage);
}else{

  log.info("经销商订单取消，释放DC占用金额成功");
}

def (Boolean error2, data2, String errorMessage2) = Fx.object.unlock("SalesOrderObj", id, true);
if (error2) {
    log.info("errorMessage2 : " + errorMessage2)
}
//调用编辑接口，触发生成冻结记录
Map edit = [
        "object_data": ["_id": id]
];
def (Boolean error1, HttpResult data1, String errorMessage1) = Fx.proxy.callAPI("object.edit", ["describe_api_name": "SalesOrderObj"], [:], edit);
if (error1) {
    log.info("errorMessage1 ：" + errorMessage1);
}
//编辑后，数据锁定
def (Boolean error3, data3, String errorMessage3) = Fx.object.lock("SalesOrderObj", id, true);
if (error3) {
    log.info("errorMessage3 : " + errorMessage3)
}
