String account_id = context.data.account_id as String;
String tenantId = context.tenantId as String;
String DDLX = context.data.record_type as String //订单类型 交易处订单default__c 经销商订单record_hV12A__c 直供订单record_yxtdd__c 
log.info("订单类型：" + DDLX)
log.info("客户id：" + account_id);
String customCode = context.data.field_y18Wn__c as String;
log.info("售达方编码："+customCode)

String CGP_id="6185f91c50b48f0001109eea" // 常规品
String SHUI_id="6185f92f80533c00014dc026" // 水
String XJ_id="6185731784e28900015f2574" // 现金账户
String HT_id="6185f93c80533c00014dcd3c" // 海苔
//---------------------------------------------优化逻辑-------------------------------------------------------------------
List queryList = [["customer_id": account_id], ["fund_account_id": Operator.IN(CGP_id, SHUI_id, XJ_id, HT_id)]]
def(err, customerAccountData, errMsg) = Fx.object.find("NewCustomerAccountObj", queryList, 4, 0)
BigDecimal CGP=0
BigDecimal SHUI=0
BigDecimal XJJE=0
BigDecimal HT=0
if (!err) {
  List customerAccountList=customerAccountData["dataList"] as List
  if(customerAccountList.size()>0){
    customerAccountList.each {item -> 
      def id = item["fund_account_id"] as String
      def availalbeBalance = item["available_balance"] as BigDecimal
      if (id == CGP_id) {
          CGP = availalbeBalance
      } else if (id == SHUI_id) {
          SHUI = availalbeBalance
      } else if (id == XJ_id) {
          XJJE = availalbeBalance
      } else if (id == HT_id) {
          HT = availalbeBalance
      }
    }
  }
} else {
  log.info("客户账户余额查询异常：" + errMsg)
}
log.info("常规品DC余额："+ CGP + " ，水DC余额："+ SHUI + " ，现金账户余额："+XJJE + " ，海苔DC余额："+HT)
//----------------------------------------------------------------------------------------------------------------

// List CGP_queryList = [["customer_id": account_id], ["fund_account_id": CGP_id]]
// List SHUI_queryList = [["customer_id": account_id], ["fund_account_id": SHUI_id]]
// List XJ_queryList = [["customer_id": account_id], ["fund_account_id": XJ_id]]
// List HT_queryList = [["customer_id": account_id], ["fund_account_id": HT_id]]



// def(error4, data4, msg4) = Fx.object.find("NewCustomerAccountObj", CGP_queryList, 1, 0)
// BigDecimal CGP=0
// if(!error4){
//   List CGP_list=data4["dataList"] as List
//   if(CGP_list.size()>0){
//     CGP =CGP_list[0]["available_balance"] as BigDecimal
//   }
// }else{
//   log.info("常规品DC余额查询异常：" + msg4)
// }
// log.info("常规品DC余额："+ CGP)
// def(error5, data5, msg5) = Fx.object.find("NewCustomerAccountObj", SHUI_queryList, 1, 0)
// BigDecimal SHUI=0
// if(!error5){
//   List SHUI_list=data5["dataList"] as List
//   if(SHUI_list.size()>0){
//     SHUI =SHUI_list[0]["available_balance"] as BigDecimal
//   }
// }else{
//   log.info("水DC余额查询异常：" + msg5)
// }
// log.info("水DC余额："+ SHUI)
// def(error6, data6, msg6) = Fx.object.find("NewCustomerAccountObj", XJ_queryList, 1, 0)
// BigDecimal XJJE=0
// if(!error6){
//   List XJJE_list=data6["dataList"] as List
//   if(XJJE_list.size()>0){
//     //******** 改为取账户余额，与SAP接口余额比较
//     XJJE =XJJE_list[0]["account_balance"] as BigDecimal
//   }
// }else{
//     log.info("现金账户余额查询异常：" + msg6)
// }
// log.info("现金账户余额："+XJJE)
// def(error7, data7, msg7) = Fx.object.find("NewCustomerAccountObj", HT_queryList, 1, 0)
// BigDecimal HT=0
// if(!error7){
//   List HT_list=data7["dataList"] as List
//   if(HT_list.size()>0){
//     HT =HT_list[0]["available_balance"] as BigDecimal
//   }
// }else{
//     log.info("海苔DC余额查询异常：" + msg7)
// }
// log.info("海苔DC余额："+HT)

//******** 去SAP接口余额，写入订单现金账户可用余额，再与账户余额比较
// String cusAccountUrl ="http://sappi-dev.orion.com.cn:18000/sap/xi/adapter_plain?namespace=urn%3Aorion-com%3Aerp%3Andms%3Asd_credit&interface=CREDIT_NDMS_SYN_MI&service=ORION_DMS_BS&party=&agency=&scheme=&QOS=BE&sap-user=sdsc-pi01&sap-password=pi12345&sap-client=200&sap-language=EN";
// String billingUrl ="http://sappi-dev.orion.com.cn:18000/sap/xi/adapter_plain?namespace=urn%3Aorion-com%3Aerp%3Andms%3Asd_billing&interface=BILLING_NDMS_SYN_MI&service=ORION_DMS_BS&party=&agency=&scheme=&QOS=BE&sap-user=sdsc-pi01&sap-password=pi12345&sap-client=200&sap-language=EN";
// Map header=["Authorization":"Basic " +Fx.crypto.base64.encode(Fx.utils.toUTF8Bytes("username:password")),"Content-Type":"text/xml; charset=utf-8"];

// //请求报文
// String cusAccountXml =
// "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"+
// "<ns0:ZSDX_R_CREDIT_F_DMS xmlns:ns0=\"urn:sap-com:document:sap:rfc:functions\">"+
//   "<PI_KUNNR>"+customCode+"</PI_KUNNR>"+
// //  "<PI_KUNNR>**********</PI_KUNNR>"+
// "</ns0:ZSDX_R_CREDIT_F_DMS>";  
// log.info("客户账户余额请求入参：" + cusAccountXml)
//  //请求数据：执行调用ERP接口的WEB请求
// def (Boolean cusRequestEr,HttpResult cusRequestData,String cusRequestMsg) =  Fx.http.post(cusAccountUrl,header,cusAccountXml,30000,false,0)
// if(cusRequestData==null){
//   Fx.message.throwErrorMessage("接口超时");  
// }
// cusRequestData["bytes"]=null;
// log.info("返回报文："+cusRequestData);
// def xmlSlurper =new XmlSlurper();
// String statusCode=cusRequestData["statusCode"];
// BigDecimal sapAccount = 0.00;
// if("200" == statusCode){
//   String content=cusRequestData["content"];
//   def responseMap=xmlSlurper.parseText(content)
//   def PE_SKFOR=responseMap["PE_SKFOR"] as NodeChildren;
//   sapAccount = BigDecimal.of(PE_SKFOR as String)
//   log.info("SAP接口返回账户余额：" + sapAccount)
//   Map createMap = [:];
//   BigDecimal differ = sapAccount - XJJE as BigDecimal;
//   if(differ > 0){
//     log.info("SAP接口查询金额-查询当天'账户余额' > 0，生成该客户『收入』类型，收入金额=差额；类型：其他")
//     //业务类型-收入
//     createMap.put("record_type", "income_record_type__c");
//     //收入金额
//     createMap.put("revenue_amount", differ);
//     //收入类型-其他
//     createMap.put("revenue_type", "other");
//   }else if(differ < 0){
//     log.info("SAP接口查询金额-查询当天'账户余额' < 0，生成该『支出』类型，支出金额=差额；类型：其他")
//     //业务类型-支出
//     createMap.put("record_type", "default__c");
//     //支出金额
//     createMap.put("expense_amount", differ * -1);
//     //支出类型-其他
//     createMap.put("expense_type", "other");
//   }
//   createMap.put("transaction_date",  Date.now());
  
//   //客户
//   createMap.put( "customer_id", account_id);
//   createMap.put("fund_account_id", XJ_id);//海苔
  
//   log.info("账户收支流水新建入参：" + createMap);
//   Long startStamp = DateTime.now().toTimestamp()
//   FxLock lock = Fx.lock.lock("lock1234", 3)
//   def (Boolean error2, Map data2, String errorMessage2) = Fx.object.create("AccountTransactionFlowObj", createMap, null, true);
//   if(!error2){
//     XJJE = sapAccount;
//     log.info("账户收支流水创建成功。")
//   }else{
//     log.info("账户收支流水创建失败：" + errorMessage2)
//   }
//   lock.unlock()
//   Long endStamp = DateTime.now().toTimestamp()
//   Long differStamp = endStamp - startStamp;
//   log.info("耗时：" + differStamp)
  
// }else{
//   log.info("SAP接口查询账户余额异常：" + cusRequestMsg)
// }

// OrderClient.syncAccountBalance(account_id, customCode, XJJE)

OrderClient.syncAccountBalance(XJ_id, account_id, customCode, XJJE)

//付款方名称 field_3s0qC__c

UIEvent event = UIEvent.build(context) {
  
//查询客户是否存在生命状态=审核中的经销商订单，有则提示
def (Boolean error, QueryResult data, String errorMessage) = Fx.object.find("SalesOrderObj",[
    ["account_id":account_id],
    ["life_status": "under_review"],
    ["record_type": "record_hV12A__c"]
], 10, 0);
if(!error){
  if(null != data.dataList && data.dataList.size()>0){
    log.info("未审核订单条数：" + data.dataList.size())
      //设置提醒消息
      remind Remind.Text("当前客户有待确认的订单，不能创建新订单")
  }
}
String sendCode = "";
def (Boolean error1, QueryResult data1, String errorMessage1) = Fx.object.find("AccountAddrObj",[
    ["account_id": account_id],["add_type": Operator.EQ("AKlkyh5j1")]], ["last_modified_time":1], 10, 0);
if( !error1 ){
  if( data1.total > 0 ){
    List data1List = data1.dataList as List;
    data1List.each{item ->
      Map data1Map = item as Map;
      if(item["is_ship_to_add"]){
        sendCode = data1Map["_id"] as String;
      }
    }
    if("" == sendCode){
       Map data1Map = data1List.get(0) as Map;
       sendCode = data1Map["_id"] as String;
    }
    log.info("送达方地址编码：" + sendCode)
  }
}

//经销商订单-根据订单【创建人】判断【销售订单来源】，如创建人无员工编码，则销售订单来源变更为「经销商」，默认来源为CRM
//********--创建人跟从客户获取的外部负责人是否是同一个人，如果不是，订单销售来源就是crm
def(cusEr, cusData, cusMsg) = Fx.object.findById("AccountObj", account_id)
    if (!cusEr) {
        List deptList = cusData["field_ZRvgo__c"]as List //营业所
        List GSBBList = cusData["field_4a5Z5__c"]as List //本部
        List GSQYList = cusData["field_9Y9cp__c"]as List //区域  
        editMaster("field_ro9Z8__c": deptList, "field_80S1u__c": GSBBList, "field_IHtsw__c": GSQYList, 
        "field_m4gRl__c": CGP, "field_vrNGl__c": SHUI, "field_y2j1C__c": XJJE, "field_L1M5h__c": HT, "field_3s0qC__c": account_id,
        "field_0315j__c": sendCode)
    }
}

return event
