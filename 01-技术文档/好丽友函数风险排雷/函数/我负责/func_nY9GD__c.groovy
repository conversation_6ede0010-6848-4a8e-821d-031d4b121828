// 函数示例：计划任务批量更新当前对象数

// 业务说明： 
// 注意：计划任务在自定义函数编写时运行可以获取到**context**关键字，但是在实际执行时是获取不到的

// 如要移植到其他对象上，仅需要修改objName的值，不需要修改其他内容
List ids = context.objectIds as List //获取所有符合条件的数据id，新建计划任务会有筛选条件
// ids = [context.data._id as String] //**测试用，别动
String objName = "object_1e08K__c" //***对象名，如果需要再其他对象执行需要更换api name***
//循环处理数据，在里面写处理逻辑
ids.eachWithIndex {items,int i->
  String billingUrl ="http://sappi-dev.orion.com.cn:18000/sap/xi/adapter_plain?namespace=urn%3Aorion-com%3Aerp%3Andms%3Asd_billing&interface=BILLING_NDMS_SYN_MI&service=ORION_DMS_BS&party=&agency=&scheme=&QOS=BE&sap-user=sdsc-pi01&sap-password=pi12345&sap-client=200&sap-language=EN";
  Map header=["Authorization":"Basic " +Fx.crypto.base64.encode(Fx.utils.toUTF8Bytes("username:password")),"Content-Type":"text/xml; charset=utf-8"];
  
  //根据id查询数据
  String id = items as String //当前数据的id
  //根据id查询出数据的详细信息
  def(Boolean error, Map item, String msg) = Fx.object.findById(objName, id);
  //log.info(item)
  String orderId = item["field_m1w83__c"] as String
  
  List vdoDetailList = []
  def(Boolean error1, QueryResult data1, String msg1) = Fx.object.find("object_ebco1__c", [["field_742dr__c" : id]], 100, 0);
  if(error1 == false && data1.dataList.size() > 0 ){
    vdoDetailList = data1.dataList as List
    log.info("VDO明细数量" + vdoDetailList.size())

  }

  //调用SAP Billing单查询接口,获取billing单信息，更新VDO结算状态（field_iu657__c=M9KDcA2y7）。生成SAP-Billing单数据，关联客户、VDO单、Billing单。
  String vdoId = item["_id"]as String
  String vdoCode = item["field_Ndikq__c"]as String
  log.info("VDO ID" + vdoId)
  log.info("VDO 编号" + vdoCode)
  log.info("VDO 关联客户" + item["field_4vm7C__c"])

  //请求报文
  String billingXml =
    "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
    "    <ns0:ZSDX_R_BILLING_F_DMS xmlns:ns0=\"urn:sap-com:document:sap:rfc:functions\">\n" +
    "    <PT_VBELN>\n" +
    "       <item>\n" +
    "             <IHREZ_E>" + vdoCode + "</IHREZ_E>\n" +
//    "             <IHREZ_E>0105402886</IHREZ_E>\n" +
    "       </item>\n" +
    "  </PT_VBELN>\n" +
    "</ns0:ZSDX_R_BILLING_F_DMS>";

  log.info("请求报文：" + billingXml);
  //请求数据：执行调用ERP接口的WEB请求
  def(Boolean billingReqEr, HttpResult billingReqData, String billingReqMsg) = Fx.http.post(billingUrl, header, billingXml, 30000, false, 0)
  if (billingReqData == null) {
    log.info("接口超时，请编辑需要同步字段重新触发同步")
    //  Fx.message.throwErrorMessage("接口超时，请编辑需要同步字段重新触发同步");
  }
  billingReqData["bytes"] = null;
  log.info("返回报文：" + billingReqData);
//return
  //返回报错信息,报错信息会显示在数据维护的日志里面
  if (billingReqEr) {
     log.info(billingReqMsg)
      //Fx.message.throwErrorMessage(billingReqMsg);
  } else if (billingReqData["statusCode"] != 200) {
     log.info("请检查【CRM填写】参数是否错误")
      //Fx.message.throwErrorMessage("请检查【CRM填写】参数是否错误");
  }

  def billingXmlSlurper = new XmlSlurper();
  String billingStatusCode = billingReqData["statusCode"];
  if ("200" == billingStatusCode) {
    String billingContent = billingReqData["content"];
        
    def billingRspMap = billingXmlSlurper.parseText(billingContent)
    log.info("billingRspMap:" + billingRspMap);
        
    def PT_HEAD = billingRspMap["PT_HEAD"] as NodeChildren;
    def PT_ITEM = billingRspMap["PT_ITEM"] as NodeChildren;

    if ("" == PT_HEAD as String) {
      log.info("根据Vdo编号获取Billing单数据为空")
    } else {
      Map headMap = PT_HEAD as Map
      def IHREZ_E = billingRspMap["PT_HEAD"]["item"]["IHREZ_E"] as NodeChildren;
      def VBELN = billingRspMap["PT_HEAD"]["item"]["VBELN"] as NodeChildren;
      def FKDAT = billingRspMap["PT_HEAD"]["item"]["FKDAT"] as NodeChildren;
      def NETWR = billingRspMap["PT_HEAD"]["item"]["NETWR"] as NodeChildren;
      def MWSBK = billingRspMap["PT_HEAD"]["item"]["MWSBK"] as NodeChildren;
            
      def PT_ITEMitem = billingRspMap["PT_ITEM"] as NodeChildren;
      log.info("PT_ITEMitem: "+ PT_ITEMitem)
      List sapBillingDetailList = []
      List returnFields = ["VBELN","MATNR","FKIMG","KZWI1","KZWI4","KZWI5","KZWI6"]
      
      PT_ITEMitem.children().each{
        Map dataMap=[:]
        returnFields.each{field->
          String fieldV=(it[field] as NodeChildren).text() as String;
          if(fieldV!=null&&""!=fieldV ){
             dataMap.put(field,fieldV)
          }
        }
        sapBillingDetailList.add(dataMap)
      }
      log.info("sapBillingDetailList: " + sapBillingDetailList)

      def MATNR = billingRspMap["PT_ITEM"]["item"]["MATNR"] as NodeChildren;
      def FKIMG = billingRspMap["PT_ITEM"]["item"]["FKIMG"] as NodeChildren;
      def KZWI1 = billingRspMap["PT_ITEM"]["item"]["KZWI1"] as NodeChildren;
      def KZWI4 = billingRspMap["PT_ITEM"]["item"]["KZWI4"] as NodeChildren;
      def KZWI5 = billingRspMap["PT_ITEM"]["item"]["KZWI5"] as NodeChildren;
      def KZWI6 = billingRspMap["PT_ITEM"]["item"]["KZWI6"] as NodeChildren;
      List orderOwnerList = []
      log.info("orderId:" + orderId)
      def(Boolean orderEr, Map orderData, String orderMsg) = Fx.object.findById( "SalesOrderObj", orderId) 
      log.info("获取订单:" + orderEr+orderMsg)
      if(!orderEr){
        orderOwnerList = orderData["owner"] as List //订单负责人
        log.info("订单负责人:" + orderOwnerList)
      }

      Map billingObjectData = [
          "field_J0q1V__c": item["field_4vm7C__c"] as String, //客户
          "field_w19ph__c": item["_id"]  as String, //VDO单ID
          "field_3l7Dj__c": IHREZ_E as String, //VDO编码
          "field_nh3qo__c": orderId, //订单
          "field_eyoTD__c": NETWR.text() as String, //未税净额
          "field_585H8__c": MWSBK.text() as String, //税值
          "field_bDplS__c": VBELN as String, //开票凭证
          "field_SWel9__c": Date.of(FKDAT.text() as String),   //出具发票索引和打印的出具发票日期
          "owner":orderOwnerList
      
      ]
      Map billingDetailsData = [:];
      List billingDetailsList = [];
      sapBillingDetailList.each{sapBillingDetail ->
      log.info("sapBillingDetail: " + sapBillingDetail)
        if(vdoDetailList.size() > 0){
          vdoDetailList.each{vdoDetail->
              String productCode = vdoDetail["field_QmM8n__c"] as String
              log.info("VDO明细产品编码：" + productCode)
              String sapMATNR = sapBillingDetail["MATNR"] as String;
              log.info("sapBillingDetail产品编码：" + sapMATNR)
              String productId = vdoDetail["field_011pL__c"] as String
              if(productCode == sapMATNR){
                  Map billingDetailsMap = [
                    "field_yKRyd__c": vdoDetail["field_0b4fK__c"] as String, //订单产品ID
                    "field_v11SP__c": productId, //产品ID
                    "field_rpXO1__c": sapBillingDetail["MATNR"] as String, //产品编码
                    "field_a9nb9__c": sapBillingDetail["FKIMG"] as String, //实际已开票数量
                    "field_DEw1t__c": sapBillingDetail["KZWI1"] as String, //未税价格
                    "field_jrbdY__c": sapBillingDetail["KZWI4"] as String, //合同折扣
                    "field_0Lu6S__c": sapBillingDetail["KZWI5"] as String, //促销折扣
                    "field_f92pt__c": sapBillingDetail["KZWI6"] as String //事后折扣
                  ]
                  billingDetailsList.add(billingDetailsMap);
                }else{
                  log.info("产品编码不匹配，忽略")
                }
            }
        }else{
            def(error2, data2, errorMessage2) = Fx.object.create("object_jGT25__c", billingObjectData)
            if( error2 ){
              log.info("创建SAP-Billing单失败：" + errorMessage2)
            }else{
              log.info("创建SAP-Billing单成功：" + errorMessage2)
              Map vdoUpdateMap = [
               "field_iu657__c": "M9KDcA2y7"
                ]
              def(error3, data3, errorMessage3) = Fx.object.update("object_1e08K__c", vdoId, vdoUpdateMap)
              if( error2 ){
                log.info("更新VDO失败：" + errorMessage3)
              }else{
              log.info("更新VDO成功：" + errorMessage3)
              }
            }
        }
      }
      billingDetailsData.put("object_l8m4Z__c", billingDetailsList)
      log.info("billingObjectData: " + billingObjectData);
      log.info("billingDetailsData: " + billingDetailsData);
      def(Boolean error2, Map data2, String errorMessage2) = Fx.object.create("object_jGT25__c", billingObjectData, billingDetailsData, false)
      if( error2 ){
        log.info("创建SAP-Billing单失败：" + errorMessage2)
      }else{
        log.info("创建SAP-Billing单成功")
        Map vdoUpdateMap = [
        "field_iu657__c": "M9KDcA2y7"
         ]
        def(error3, data3, errorMessage3) = Fx.object.update("object_1e08K__c", vdoId, vdoUpdateMap)
        if( error2 ){
          log.info("更新VDO失败：" + errorMessage3)
        }else{
          log.info("更新VDO成功")
        }
      }
    }
  }else {
    log.info("请求接口异常")
  }
}
log.info("执行完成")
