String accountId = context.data.account_id as String;//售达方名称
log.info("客户ID：" + accountId)
Date orderTime = context.data.order_time as Date;
log.info("下单日期：" + orderTime)
String sendCode = context.data.field_0315j__c as String;
log.info("送达方：" + sendCode)
String warehouse = context.data.field_aS3vx__c as String;
log.info("仓库：" + warehouse)

def (Boolean error, QueryResult data, String errorMessage) = Fx.object.find("object_r62mc__c",[
    ["field_XCh3o__c":accountId]
], 1, 0);
BigDecimal num = 0;
log.info(data)
if(!error && data.total > 0){
  List dataList = data.dataList as List;
  Map dataMap = dataList.get(0) as Map;
  num = dataMap["field_fI3TA__c"] as BigDecimal;
}
log.info("允差范围：" + num)
UIEvent event = UIEvent.build(context) {
//主对象、从对象数据修改，详见上
} 
//获取当前操作的从对象数据
Map currentData = event.getCurrentDetail()
// List<Map> currentDataList = event.getCurrentAddDetail()

//修改当前操作的从对象数据（主要用于新建明细和编辑明细的场景下）
  String productId = currentData["PROD_CODE__c"] as String;
  log.info("产品编码：" + productId)
  
  def (Boolean error1, QueryResult data1, String errorMessage1) = Fx.object.find("object_MpMYt__c",[
    ["field_cTk2N__c":Operator.EQ(accountId)],
    ["field_92vf2__c":Operator.EQ(productId)],
    ["field_58vwt__c": Operator.LTE(orderTime)],
    ["field_waD3F__c": Operator.GTE(orderTime)]
  ], 1, 0);
  if(!error1 && data1.total > 0){
    Map dataList = data1.dataList[0] as Map;
    
    int quantity = currentData["quantity"] as int;//订货数量
    log.info("订货数量:"+quantity);
    int field_hn59s__c = dataList["field_hn59s__c"] as int;//客户剩余量
    String field_i8P42__c = "正常";
    //订货数量小于等于限量剩余量，返回正常；订货数量大于限量剩余量 返回超限量
    if(quantity>field_hn59s__c){
      field_i8P42__c = "超限量";
    }
    currentData.put("field_i8P42__c", field_i8P42__c);
    
    String limitType = dataList["field_lOb2X__c"] as String;
    log.info("限量类型：" + limitType)
    if(limitType == "option1"){
      currentData.put("field_o8x0L__c", dataList["_id"])
      currentData.put("field_i4nfk__c", dataList["field_hn59s__c"])
    }else if(limitType == "0v12N3dbV"){
      String sendcode1 = dataList["field_g1s0a__c"] as String;
      if(sendCode == sendcode1 && sendCode != null){
        currentData.put("field_o8x0L__c", dataList["_id"])
        currentData.put("field_i4nfk__c", dataList["field_hn59s__c"])
      }
    }else if(limitType == "Dxmw6K1ZL"){
      String warehouse1 = dataList["field_1ksb5__c"] as String;
      if(warehouse == warehouse1 && warehouse != null){
        currentData.put("field_o8x0L__c", dataList["_id"])
        currentData.put("field_i4nfk__c", dataList["field_hn59s__c"])
      }
    }
  }else{
    log.info("客户限量查询失败：" + errorMessage1)
  }
  currentData.put("field_2lm1K__c", num)

//获取当前新增的从对象数据

return event