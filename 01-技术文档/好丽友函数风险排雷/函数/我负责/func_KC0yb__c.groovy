String orderId = context.data["_id"] as String
log.info(context.details["SalesOrderProductObj"])
log.info("销售订单编码：" + context.data.name)
def (Boolean error1,QueryResult data1,String msg1) =  Fx.object.find("SalesOrderProductObj",[["order_id":orderId]],100,0); 
if(!error1){
//   if(null != data1.dataList && data1.dataList.size()>0){
//     List detailList = data1["dataList"] as List
//     detailList.each{item->
//       String detailId = item["_id"] as String;//明细ID
//       String updateType = item["field_jG3v3__c"] as String;//更新类型
//       log.info("订单产品编号:" + item["name"]);
//       log.info("updateType:" + updateType);
//       Map updateMap = [:]
//       if(updateType == "Lock"){
//         updateMap.put("field_s2Kt4__c", "option1");//已冻结
//       }else if( updateType == "UnLock" ){
//         updateMap.put("field_s2Kt4__c", "Ze9s2o2JF");//未冻结
//       }
//       log.info("updateMap:" + updateMap)
//       def (error2, data2, errorMessage2) = Fx.object.update("SalesOrderProductObj",detailId,updateMap);
//       if(error2){
//         log.info("更新订单产品SCM行冻结状态失败：" + errorMessage2)
//       }else{
//         log.info("更新订单产品SCM行冻结状态成功")
//       }
//     }
//   }
  if(null != data1.dataList && data1.dataList.size()>0){
    List detailList = data1["dataList"] as List
    Map batchMap = [:]
    List fields = ["field_s2Kt4__c"]
    detailList.each{item->
      String detailId = item["_id"] as String;//明细ID
      String updateType = item["field_jG3v3__c"] as String;//更新类型
      log.info("订单产品编号:" + item["name"]);
      log.info("updateType:" + updateType);
      Map updateMap = [:]
      if(updateType == "Lock"){
        updateMap.put("field_s2Kt4__c", "option1");//已冻结
      }else if( updateType == "UnLock" ){
        updateMap.put("field_s2Kt4__c", "Ze9s2o2JF");//未冻结
      }
      batchMap.put(detailId, updateMap)
      log.info("updateMap:" + updateMap)
    }
    def (error2, data2, errorMessage2) = Fx.object.batchUpdate("SalesOrderProductObj", batchMap, fields);
      if(error2){
        log.info("批量更新订单产品SCM行冻结状态失败：" + errorMessage2)
        log.info("数据：batchMap = " + batchMap)
      }else{
        log.info("批量更新订单产品SCM行冻结状态成功")
      }
  }
}else{
  log.info(msg1)
}




