String dataId = context.data._id as String;
String SCUSTCD = context.data.field_fJ1h1__c as String;
String SALEDT = context.data.field_2p2jA__c as String;
SALEDT = SALEDT.replaceAll("-", "")
log.info("客户编码：" + SCUSTCD)
log.info("销售日期：" + SALEDT)
String appId = "crm-test";
String apiKey = "MYT3E5tLjXIR1NC4xNTAuMiwxMjAuMTMzLjU1LjUsMTAzLjIzNS4yMzkuMzQsMTAzLjIzNS4yMzAuNjU=";
// SCUSTCD = "S0000403";
// SALEDT = "20211013";
Map param = [
 "apiKey": apiKey,
 "appId": appId,
 "dataId": dataId,
 "SCUST_CD": SCUSTCD,
 "SALE_DT": SALEDT
]
Map header = [:]
log.info(param)
def ret = Fx.proxy.callAPI("sanbox.dc.getTryCount", header, param)
Fx.log.info(ret)
if(ret[0]){
  log.info("调用接口失败")
}else{
  Map resultMap = ret[1]["content"] as Map;
  log.info(resultMap)
}


