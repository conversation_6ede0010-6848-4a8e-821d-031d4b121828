String record_type = context.data.record_type as String;
if("record_hV12A__c" != record_type){
 log.info( "业务类型为非经销商订单");
  return;
}
//销售订单状态为 订单终止 或 订单取消的时候不执行
String field_irr56__c = context.data.field_irr56__c as String;
if("1q29byJAj" == field_irr56__c || "61O401vaS" == field_irr56__c){
 log.info( "销售订单状态为："+field_irr56__c);
  return;
}
String accountId = context.data.account_id as String
String dataId = context.data._id as String
def (Boolean error1, QueryResult data1, String msg1) = Fx.object.find("NewCustomerAccountObj", [["customer_id": accountId]], 10, 0)
// log.info(data1)
if (error1) {
    log.info("查询客户账户余额失败：" + msg1)
}

BigDecimal DC_blance = 0 //DC账户可用余额
BigDecimal HT_blance = 0 //海苔账户可用余额
BigDecimal XJ_blance = 0 //现金账户可用余额
BigDecimal WATER_blance = 0 //水账户可用余额
List accountMoneyList = data1["dataList"] as List
def (Boolean error2, QueryResult data2, String msg2) = Fx.object.find("FundAccountObj", [], 10, 0) //查询对应关系
    List accountTypeList = data2["dataList"] as List
    Map accountTypeMap = [:]
    accountTypeList.each {
        o ->
            accountTypeMap.put(o["_id"], o["account_no"])
    }
    log.info(accountTypeMap)
if (accountMoneyList.size() > 1) {
    accountMoneyList.each {
        o ->
            // log.info(o)
            if (accountTypeMap.containsKey(o["fund_account_id"])) {
                String accountNum = accountTypeMap[o["fund_account_id"]]
                // log.info(accountNum)
                BigDecimal blance = 0
                if (o["available_balance"] != null) {
                    blance = o["available_balance"] as BigDecimal
                }
                
                switch (accountNum) {
                    case "02":
                        DC_blance = blance
                        break;
                    case "03":
                        WATER_blance = blance
                        break;
                    case "01":
                        XJ_blance = blance
                        break;
                    case "04":
                        HT_blance = blance
                        break;
                    default: break
                }
            }
    }
}


//常规DC帐户余额 field_m4gRl__c 
//水DC帐户余额 field_vrNGl__c 
//现金账户余额 field_y2j1C__c
Map salesOrderUpdateMap = [:]
salesOrderUpdateMap.put("field_m4gRl__c", DC_blance)
salesOrderUpdateMap.put("field_y2j1C__c", XJ_blance)
salesOrderUpdateMap.put("field_vrNGl__c", WATER_blance)
salesOrderUpdateMap.put("field_L1M5h__c", HT_blance)
// log.info("DC账户可用余额" + DC_blance)
// log.info("HT账户可用余额" + HT_blance)
// log.info("WATER账户可用余额" + WATER_blance)

BigDecimal DC_ratio = 0.9 //DC账户可用余额占比
BigDecimal HT_ratio = 0.9 //海苔账户可用占比
BigDecimal XJ_ratio = 0.9 //现金账户可用占比
BigDecimal WATER_ratio = 0.9 //水账户可用占比
def (Boolean error4, QueryResult data4, String msg4) = Fx.object.find("object_G1shM__c", [], 20, 0)//查询比例
List accountRatioList = data4["dataList"] as List
accountRatioList.each {
    o ->
        String name = accountTypeMap[o["field_2f3a5__c"]]as String
        BigDecimal ratio = 0.9
        if (o["field_vb4Mm__c"] != null) {
            ratio = o["field_vb4Mm__c"] as BigDecimal
        }
        switch (name) {
            case "02":
                DC_ratio = ratio
                break;
            case "03":
                WATER_ratio = ratio
                break;
            case "01":
                XJ_ratio = ratio
                break;
            case "04":
                HT_ratio = ratio
                break;
            default: break
        }
}

BigDecimal DC_amount = context.data.field_2zWl6__c as BigDecimal //DC订货金额
BigDecimal HT_amount = context.data.field_0UYpv__c as BigDecimal //海苔订货金额
BigDecimal WATER_amount = context.data.field_O5p6j__c as BigDecimal //水订货金额

BigDecimal DC_occupancyAmount = DC_amount * DC_ratio //DC占用金额
BigDecimal HT_occupancyAmount = HT_amount * HT_ratio //海苔订货金额
BigDecimal WATER_occupancyAmount = WATER_amount * WATER_ratio //水订货金额

if (WATER_amount * WATER_ratio > WATER_blance) {
    WATER_occupancyAmount = WATER_blance
}

if (DC_amount * DC_ratio > DC_blance) {
    DC_occupancyAmount = DC_blance
}

if (HT_amount * HT_ratio > HT_blance) {
    HT_occupancyAmount = HT_blance
}
log.info("DC占用金额" + DC_occupancyAmount)
log.info("HT占用金额" + HT_occupancyAmount)
log.info("WATER占用金额" + WATER_occupancyAmount)
BigDecimal DC_occupancyPercent = 0
if (DC_amount != 0) {
    DC_occupancyPercent = DC_occupancyAmount / DC_amount
}
BigDecimal WATER_occupancyPercent = 0
if (WATER_amount != 0) {
    WATER_occupancyPercent = WATER_occupancyAmount / WATER_amount
}
BigDecimal HT_occupancyPercent = 0
if (HT_amount != 0) {
    HT_occupancyPercent = HT_occupancyAmount / HT_amount
}

salesOrderUpdateMap.put("field_xIA3r__c", DC_occupancyPercent)
salesOrderUpdateMap.put("field_91dM1__c", WATER_occupancyPercent)
salesOrderUpdateMap.put("field_b63bI__c", HT_occupancyPercent)

salesOrderUpdateMap.put("field_2WfTj__c", DC_occupancyAmount)
salesOrderUpdateMap.put("field_QpoL4__c", WATER_occupancyAmount)
salesOrderUpdateMap.put("field_yvmv1__c", HT_occupancyAmount)



List details = context.details["SalesOrderProductObj"] as List

List productIdList = []
details.each{
  item -> 
  productIdList.add(item["product_id"])
}
log.info("productIdList:"+productIdList);
Map categoryMap = [:]
def (Boolean error7, QueryResult data7, String msg7) = Fx.object.find("ProductObj", [["_id": Operator.IN(productIdList)]], 100, 0);
if(null != data7 && data7.size>0){
  List productList = data7["dataList"] as List
  productList.each{item -> 
    String id = item["_id"] as String
    String code = item["category"] as String
    categoryMap.put(id,  code)
  }
}

// log.info("订单产品size1 ： " + details1.size())
List waterList = []
List htList = []
List otherList = []
//第一次遍历  算出水total  其他total 并且给订单产品分组（水一组，其他一组）
//定义一个updateMap
//第二次遍历 水组 计算水占比和金额
//第三次遍历 other组 计算other占比和金额
//执行update
BigDecimal waterSubTotal = 0
BigDecimal otherSubTotal = 0
BigDecimal htSubTotal = 0
details.each {
    item ->
        
        BigDecimal temp = item["subtotal"] as BigDecimal
        String code = categoryMap[item["product_id"]] as String
        if (code == "15") {
            waterList.add(item)
            waterSubTotal = waterSubTotal + temp
        } else if (code == "11") {
            htList.add(item)
            htSubTotal = htSubTotal + temp
        } else {
            otherList.add(item)
            otherSubTotal = otherSubTotal + temp
        }
}
log.info(waterList.size())
log.info(htList.size())
log.info(otherList.size())
//水DC占用金额 field_QpoL4__c * DC分摊占比 field_7utVg__c = 事后DC-金额 field_cU91J__c
//常规DC占用金额 field_2WfTj__c * DC分摊占比 field_7utVg__c = 事后DC-金额 field_cU91J__c
// BigDecimal waterDC = context.data.field_QpoL4__c as BigDecimal
// BigDecimal otherDC = context.data.field_2WfTj__c as BigDecimal
// log.info("waterDC:" + waterDC)
// log.info("otherDC:" + otherDC)

//遍历水List
Map updateMap = [:]
if (waterList.size() >= 1) {
    Range range = Ranges.of(0, waterList.size() - 1)
    log.info("水产品共计：" + waterList.size())
    BigDecimal waterFlag = 0
    range.each {
        i ->
            BigDecimal subTotal = waterList[i]["subtotal"] as BigDecimal
            if (waterSubTotal != 0) {
                if (i == waterList.size() - 1) {
                    //计算比例
                    BigDecimal percent = 1 - waterFlag
                    String id = waterList[i]["_id"] as String
                    //计算金额
                    BigDecimal dcAmount = WATER_occupancyAmount * percent
                    dcAmount = dcAmount.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                    Map map = [:]
                    map.put("field_7utVg__c", percent)
                    map.put("field_C0f43__c", dcAmount)
                    // map.put("_id", id)
                    updateMap.put(id, map)
                } else {
                    //计算比例
                    BigDecimal percent = subTotal / waterSubTotal
                    percent = percent.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                    // log.info( percent)
                    waterFlag = waterFlag + percent
                    //计算金额
                    BigDecimal dcAmount = WATER_occupancyAmount * percent
                    dcAmount = dcAmount.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                    Map map = [:]
                    map.put("field_7utVg__c", percent)
                    map.put("field_C0f43__c", dcAmount)
                    String id = waterList[i]["_id"] as String
                    // map.put("_id", id)
                    updateMap.put(id, map)
                }
            }
    }
}

//遍历海苔
if (htList.size() >= 1) {
    Range range1 = Ranges.of(0, htList.size() - 1)
    BigDecimal htFlag = 0
    range1.each {
        i ->
            BigDecimal subTotal = htList[i]["subtotal"] as BigDecimal
            if (htSubTotal != 0) {
                if (i == htList.size() - 1) {
                    //计算比例
                    BigDecimal percent = 1 - htFlag
                    String id = htList[i]["_id"] as String
                    //计算金额
                    BigDecimal dcAmount = HT_occupancyAmount * percent
                    dcAmount = dcAmount.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                    Map map = [:]
                    map.put("field_7utVg__c", percent)
                    map.put("field_C0f43__c", dcAmount)
                    // map.put("_id", id)
                    updateMap.put(id, map)
                } else {
                    //计算比例
                    BigDecimal percent = subTotal / htSubTotal
                    percent = percent.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                    // log.info( percent)
                    htFlag = htFlag + percent
                    //计算金额
                    BigDecimal dcAmount = HT_occupancyAmount * percent
                    dcAmount = dcAmount.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                    Map map = [:]
                    map.put("field_7utVg__c", percent)
                    map.put("field_C0f43__c", dcAmount)
                    String id = htList[i]["_id"] as String
                    // map.put("_id", id)
                    updateMap.put(id, map)
                }

            }
    }
}

if (otherList.size() >= 1) {
    //遍历otherList
    Range range2 = Ranges.of(0, otherList.size() - 1)
    BigDecimal otherFlag = 0
    range2.each { i ->
        BigDecimal subTotal = otherList[i]["subtotal"] as BigDecimal
        if (otherSubTotal != 0) {
            BigDecimal flag = 0
            if (i == otherList.size() - 1) {
                //计算比例
                BigDecimal percent = 1 - otherFlag
                String id = otherList[i]["_id"] as String
                //计算金额
                BigDecimal dcAmount = DC_occupancyAmount * percent
                dcAmount = dcAmount.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                Map map = [:]
                map.put("field_7utVg__c", percent)
                map.put("field_C0f43__c", dcAmount)
                // map.put("_id", id)
                updateMap.put(id, map)
            } else {
                //计算比例
                BigDecimal percent = subTotal / otherSubTotal
                percent = percent.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                // log.info( percent)
                otherFlag = otherFlag + percent
                //计算金额
                BigDecimal dcAmount = DC_occupancyAmount * percent
                dcAmount = dcAmount.setScale(9, BigDecimal.ROUND_HALF_DOWN)
                String id = otherList[i]["_id"] as String
                Map map = [:]
                map.put("field_7utVg__c", percent)
                map.put("field_C0f43__c", dcAmount)
                // map.put("_id", id)
                updateMap.put(id, map)
            }
        }
    }
}


//(销售订单金额 - (常规占用 + 水占用 + 海苔占用)) * （1 + 税率）= 订单现金支付金额（含税）field_yBa8t__c
BigDecimal field_yBa8t__c = 0
BigDecimal orderAmount = context.data.order_amount as BigDecimal
BigDecimal rate = context.data.field_setzH__c as BigDecimal
field_yBa8t__c = (orderAmount - ( DC_occupancyAmount + WATER_occupancyAmount + HT_occupancyAmount )) * (1 + rate)
salesOrderUpdateMap.put("field_yBa8t__c", field_yBa8t__c)
String life_status = context.data.life_status as String;
//判断一下“生命状态”为“审核中”后，将销售订单状态变更为“待确认”
if(life_status == "under_review"){
  salesOrderUpdateMap.put("field_irr56__c", "xM8g9r2ZZ")
}

log.info("updateMap:" + updateMap)
def (Boolean error5, List data5, String errorMessage5) = object.batchUpdate("SalesOrderProductObj", updateMap)
if(error5){
  log.info(error5)
}

def (Boolean error6, Map data6, String errorMessage6) = object.update( "SalesOrderObj",  dataId,  salesOrderUpdateMap)
log.info(errorMessage6)
if (error6) {
    log.info(errorMessage6)
}
// Map productUpdateMap = ["SalesOrderProductObj":updateMap.values()]
// salesOrderUpdateMap.put("_id",  dataId)
// Map data  = [
//   "object_data":salesOrderUpdateMap, //主对象数据
//   "details": productUpdateMap     //从对象数据，如果仅更新主对象字段，details 一定不要传！！；如果传了 details 字段，则相当于主从一起新建，从对象数据会覆盖！！！
// ]
// log.info(Fx.json.toJson(data))
// def ret = Fx.proxy.callAPI("object.edit",
//   ["describe_api_name":"SalesOrderObj"],    //要更新的对象 ApiName
//   [:],                          //ProxyAPI要传递的 header，固定传空Map
//   data                          //ProxyAPI要提交的 body 数据
//   )
// log.info(ret)
String orderId = context.data._id as String;
log.info("销售订单编号：" + context.data.name)
List orderDetailList = []
def (Boolean errorSop, QueryResult dataSop, String errorMessageSop) = Fx.object.find("SalesOrderProductObj",[
    ["order_id":orderId]
], 10, 0);
if(!errorSop){
  int orderTotal = dataSop.total as int
  log.info("销售订单明细行数量：" + orderTotal)
  if(orderTotal > 0){
    int orderEnd = orderTotal / 100 as int;
    List orderList = [];
    Range orderRange = Ranges.of(0, orderEnd)
    orderRange.each {
      def(Boolean errorSopo, QueryResult dataSopo, String errorMessageSopo) = Fx.object.find("SalesOrderProductObj", [
          ["order_id":orderId]
      ], 100, it * 100);
      orderList.addAll(dataSopo.dataList as List)
    }
    orderList.each {item->
      log.info("订单产品编号：" + item["name"])
      String detailId = item["_id"] as String;
      def (Boolean error3, QueryResult data3, String errorMessage3) = Fx.object.find("AmortizeInfoObj",[
          ["sales_order_product_id":detailId]
      ], 10, 0);
      if(!error3){
        log.info("分摊明细行数量：" + data3.total)
        if(data3.total > 0){
          Map orderMap = [:]
          List data3List = data3.dataList as List;
          data3List.each{share->
            log.info("分摊明细编号：" + share["name"])
            String ruleName = share["field_rriPp__c"] as String;
            String pricePolicyId = share["price_policy_id"] as String;
            def (Boolean error8, Map data8, String errorMessage8) = Fx.object.findById("PricePolicyObj",pricePolicyId)
            if(!error5){
              String promType = data8["field_w7h3J__c"] as String;
              String ruleType = share["rule_type"] as String;

              log.info("规则名称：" + ruleName)
              log.info("促销类型：" + promType)
              log.info("规则类型：" + ruleType)
            
              if(ruleType != "gift"){
                BigDecimal price = share["amortize_amount"] as BigDecimal;
                log.info("分摊金额：" + price)
                if(promType.contains("option1")){
                  log.info("合同折扣金额")
                  orderMap.put("field_4adVU__c", price * -1 ); //合同折扣金额（新）
                }else{
                  log.info("促销折扣金额")
                  orderMap.put("field_g01a2__c", price * -1 ); //促销折扣金额（新）
                }
              }
            }
            
            
          }
          log.info("更新订单产品金额入参：" + orderMap)
          def (Boolean error9, Map data9, String errorMessage9) = Fx.object.update("SalesOrderProductObj",detailId,orderMap)
          if(error9){
            log.info("更新订单产品金额异常：" + errorMessage9)
          }else{
            log.info("更新订单产品金额成功")
          }
        }
      }
    }
  }
}

