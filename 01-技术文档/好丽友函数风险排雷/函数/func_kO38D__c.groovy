String orderId = context.data._id as String;
log.info("销售订单编号：" + context.data.name)
List orderDetailList = []
def (Boolean error, QueryResult data, String errorMessage) = Fx.object.find("SalesOrderProductObj",[
    ["order_id":orderId]
], 10, 0);
if(!error){
  int orderTotal = data.total as int
  log.info("销售订单明细行数量：" + orderTotal)
  if(orderTotal > 0){
    int orderEnd = orderTotal / 100 as int;
    List orderList = [];
    Range orderRange = Ranges.of(0, orderEnd)
    orderRange.each {
      def(Boolean error1, QueryResult data1, String errorMessage1) = Fx.object.find("SalesOrderProductObj", [
          ["order_id":orderId]
      ], 100, it * 100);
      orderList.addAll(data1.dataList as List)
    }
    orderList.each {item->
      log.info("订单产品编号：" + item["name"])
      String detailId = item["_id"] as String;
      def (Boolean error3, QueryResult data3, String errorMessage3) = Fx.object.find("AmortizeInfoObj",[
          ["sales_order_product_id":detailId]
      ], 10, 0);
      if(!error3){
        log.info("分摊明细行数量：" + data3.total)
        if(data3.total > 0){
          Map orderMap = [:]
          List data3List = data3.dataList as List;
          data3List.each{share->
            log.info("分摊明细编号：" + share["name"])
            String ruleName = share["field_rriPp__c"] as String;
            String pricePolicyId = share["price_policy_id"] as String;
            def (Boolean error5, Map data5, String errorMessage5) = Fx.object.findById("PricePolicyObj",pricePolicyId)
            if(!error5){
              String promType = data5["field_w7h3J__c"] as String;
              String ruleType = share["rule_type"] as String;

              log.info("规则名称：" + ruleName)
              log.info("促销类型：" + promType)
              log.info("规则类型：" + ruleType)
            
              if(ruleType != "gift"){
                BigDecimal price = share["amortize_amount"] as BigDecimal;
                log.info("分摊金额：" + price)
                if(promType.contains("option1")){
                  log.info("合同折扣金额")
                  orderMap.put("field_4adVU__c", price * -1 ); //合同折扣金额（新）
                }else{
                  log.info("促销折扣金额")
                  orderMap.put("field_g01a2__c", price * -1 ); //促销折扣金额（新）
                }
              }
            }
            
            
          }
          log.info("更新订单产品金额入参：" + orderMap)
          def (Boolean error4, Map data4, String errorMessage4) = Fx.object.update("SalesOrderProductObj",detailId,orderMap)
          if(error4){
            log.info("更新订单产品金额异常：" + errorMessage4)
          }else{
            log.info("更新订单产品金额成功")
          }
        }
      }
    }
  }
}
