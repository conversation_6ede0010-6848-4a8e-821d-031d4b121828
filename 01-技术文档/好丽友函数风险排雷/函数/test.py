# Assuming the data is stored in a file named 'data.txt'
def extract_cas_service_prefix(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    cas_service_prefixes = []
    for line in lines:
        if 'casServicePrefix=' in line:
            # Split the line by spaces and find the part with 'casServicePrefix='
            parts = line.split()
            for part in parts:
                if part.startswith('casServicePrefix='):
                    # Extract the URL part
                    prefix = part.split('=')[1]
                    cas_service_prefixes.append(prefix)
                    break
    
    return cas_service_prefixes

# Example usage
file_path = 'data.txt'  # Replace with your actual file path
prefixes = extract_cas_service_prefix(file_path)
for prefix in prefixes:
    print(prefix)