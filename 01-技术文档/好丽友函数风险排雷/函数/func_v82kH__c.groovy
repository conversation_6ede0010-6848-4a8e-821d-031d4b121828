
  
  Map updateMap=[:]
  String updateTp = context.data["field_a0215__c"] as String;
  String tagging  = context.data["field_1qkw4__c"] as String;
  String dataId   = context.data["_id"] as String;
  
  if("Lock" == updateTp){
    if(tagging == "AllLocked"){
      updateMap.put("field_3rK4H__c", "w4e3o5Vl4")
    }else if(tagging == "PartLocked"){
      updateMap.put("field_3rK4H__c", "D5Ao9yxgP")
    }
  }else if("UnLock"==updateTp){
    if(tagging == "None"){
      updateMap.put("field_3rK4H__c", "option1")
    }
  }
  log.info("订单更新入参：" + updateMap)
  def (error,data, errorMessage)  = Fx.object.update("SalesOrderObj", dataId, updateMap)
  log.info("订单更新完成：" + errorMessage) 
 
