String customId = context.data.account_id as String;
log.info("客户ID：" + customId)
Date orderTime = context.data.order_time as Date;
log.info("下单日期：" + orderTime)
String sendCode = context.data.field_0315j__c as String;
log.info("送达方：" + sendCode)
String warehouse = context.data.field_aS3vx__c as String;
log.info("仓库：" + warehouse)
UIEvent event = UIEvent.build(context) {
//主对象、从对象数据修改，详见上
} 
//获取当前操作的从对象数据

Map currentData = event.getCurrentDetail()
//修改当前操作的从对象数据（主要用于新建明细和编辑明细的场景下）
String productId = currentData["PROD_CODE__c"] as String;
log.info("产品编码：" + productId)
def (Boolean error, QueryResult data, String errorMessage) = Fx.object.find("object_MpMYt__c",[
  ["field_cTk2N__c":Operator.EQ(customId)],
  ["field_92vf2__c":Operator.EQ(productId)],
  ["field_58vwt__c": Operator.LTE(orderTime)],
  ["field_waD3F__c": Operator.GTE(orderTime)]
], 1, 0);
if(!error && data.total > 0){
  Map dataList = data.dataList[0] as Map;
  String limitType = dataList["field_lOb2X__c"] as String;
  log.info("限量类型：" + limitType)
  if(limitType == "option1"){
    currentData.put("field_o8x0L__c", dataList["_id"])
    currentData.put("field_i4nfk__c", dataList["field_hn59s__c"])
  }else if(limitType == "0v12N3dbV"){
    String sendcode1 = dataList["field_g1s0a__c"] as String;
    if(sendCode == sendcode1 && sendCode != null){
      currentData.put("field_o8x0L__c", dataList["_id"])
      currentData.put("field_i4nfk__c", dataList["field_hn59s__c"])
    }
  }else if(limitType == "Dxmw6K1ZL"){
    String warehouse1 = dataList["field_1ksb5__c"] as String;
    if(warehouse == warehouse1 && warehouse != null){
      currentData.put("field_o8x0L__c", dataList["_id"])
      currentData.put("field_i4nfk__c", dataList["field_hn59s__c"])
    }
  }
}else{
  log.info("客户限量查询失败：" + errorMessage)
}

if(currentData.containsKey("field_dt1Ro__c")){
  currentData.put("quantity", currentData["field_dt1Ro__c"])
  BigDecimal conversion_ratio = currentData["conversion_ratio"]
  BigDecimal quantity = currentData["field_dt1Ro__c"] as BigDecimal
  log.info("转换比例：" + conversion_ratio)
  currentData.put("base_unit_count", conversion_ratio * quantity)
}else{
  log.info("建议订货为空")
}
if(currentData.containsKey("sales_price")){
  currentData.put("subtotal", (currentData["quantity"] as BigDecimal) * (currentData["sales_price"] as BigDecimal))
}else{
  log.info("销售单价为空")
}
if(currentData.containsKey("price_book_price")){
  currentData.put("price_book_subtotal", (currentData["quantity"] as BigDecimal) * (currentData["price_book_price"] as BigDecimal))
}else{
  log.info("销售单价为空")
}

//获取当前新增的从对象数据
List currentList = event.getCurrentAddDetail()
log.info("currentList:" + currentList)
return event