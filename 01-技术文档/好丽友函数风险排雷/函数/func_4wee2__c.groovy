Date now = Date.now();
now = now -1.days;
BigDecimal nowLong = now.toTimestamp();
DateTime nowDate = DateTime.now();
BigDecimal nowDateLong = nowDate.toTimestamp();
List findList = [["life_status": "normal"], ["field_cc9m6__c": "record_E82OA__c"],["field_0w5vP__c":"option1"]];
def(Boolean findEr, QueryResult findData, String findMsg) = Fx.object.find("DeliveryNoteObj", findList, 100, 0);
List dataList = findData.dataList as List;
dataList.each {item ->
  String customerID = item["account_id"] as String;//客户名称
  def(Boolean detailEr, QueryResult detailData, String detailMsg) = Fx.object.find("DeliveryNoteProductObj",[["delivery_note_id": item["_id"]]], 100, 0);
  if(!detailEr){
    List detailList = detailData["dataList"] as List;
    detailList.each{item2 ->
      List findList1 = [["field_SncRO__c": item2["product_id"]], ["life_status": "normal"],["field_52gQ2__c": customerID]];
      def(Boolean detailEr1, QueryResult detailData1, String detailMsg1) = Fx.object.find("object_n7b95__c",findList1, 100, 0);  
      if(detailEr1){
        log.info("detailEr1:"+detailMsg1);
      }else if( null != detailData1 && detailData1.dataList.size()>0){
        List dataList1 = detailData1.dataList as List;
        Map map = dataList1.get(0) as Map;
        BigDecimal  field_4bnn5__c = map["field_4bnn5__c"] as BigDecimal;
        log.info(customerID +"原来在途库存数："+field_4bnn5__c);
        field_4bnn5__c = (item2["auxiliary_delivery_quantity"] as BigDecimal) + field_4bnn5__c;
        String id = map["_id"] as String;
        FxLock lock1 = Fx.lock.lock("lock12", 2);
        def (error1, data1, errorMessage1) = Fx.object.update("object_n7b95__c",id,["field_4bnn5__c":field_4bnn5__c])
        if (error1) {
          log.info(errorMessage1);
          } else {
            log.info(customerID +"更新后在途库存数："+field_4bnn5__c+"在途库存更新成功。");
          }
        lock1.unlock();
      }else{
        Map mainMap = [
          "field_SncRO__c": item2["product_id"], //产品名称  
          "record_type":"default__c",
          "field_52gQ2__c":customerID,
          "field_4bnn5__c": item2["auxiliary_delivery_quantity"],//在途数量 
          "field_c6IBy__c":nowLong
        ]
      FxLock lock2 = Fx.lock.lock("lock1234", 2);
      def (error2, data2, errorMessage2) = Fx.object.create("object_n7b95__c",mainMap);
      if (error2) {
        log.info(errorMessage2);
        } else {
          log.info("在途库存新建成功。");
        }
      // 需要加锁的函数代码
      lock2.unlock();
      }
    }
  }
}

