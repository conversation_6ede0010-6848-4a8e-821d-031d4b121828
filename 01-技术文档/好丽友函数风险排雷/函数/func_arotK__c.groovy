//将订单关联的交易处客户所属经销商的客户编码刷到销售订单对象上
Map data = context.data as Map;
String recordType =data.record_type as String //业务类型
String orderCode =data.name as String //销售订单编码
String id = data._id;
String SalesOrderObj = "SalesOrderObj";
String AccountObj = "AccountObj";
Map updateData=[:];
// 根据id查询出数据的详细信息
def(error,salesOrderObj,msg) = Fx.object.findById(SalesOrderObj, id);
String account_id = salesOrderObj["account_id"] as String;
def(error1,accountObj,msg1) = Fx.object.findById(AccountObj, account_id);
String field_juVig__c = accountObj["field_juVig__c"] as String;
if(recordType=="record_hV12A__c"  || recordType=="record_58wrz__c"  )
{
   updateData=["CUST_NM__c":field_juVig__c,"field_5AQY2__c":orderCode];
}else{
    updateData=["CUST_NM__c":field_juVig__c];
}

//外部企业id更新
String outTenantId =context.outTenantId;
//将订单负责人姓名赋值到负责人业代字段
List user_list = context.data.owner as List
String user_id=user_list[0] as String

def (Boolean err,QueryResult personobj,String errMesg) =  Fx.object.find("PersonnelObj",[["user_id":user_id]],10,0); 
String user_name=personobj.dataList[0]["name"] as String
log.info("负责人姓名:"+ user_name)
String partner_id = salesOrderObj["partner_id"] as String;
if(partner_id == null && outTenantId == null){
  //上游用户
  String field_1Ry8w__c = salesOrderObj["field_1Ry8w__c"] as String;
  String downstreamOuterTenantId = null
  log.info("field_1Ry8w__c:"+field_1Ry8w__c)
  if(field_1Ry8w__c){
    def(Boolean error3,HttpResult data3,String errorMessage3) = Fx.proxy.callAPI("er.getDownstreamOuterTenantIdByMappObjectId",["x-eip-appid":"FSAID_114910b5"],["mapperObjectId":field_1Ry8w__c])
    downstreamOuterTenantId= data3['content']['data']
    log.info("downstreamOuterTenantId:"+downstreamOuterTenantId)
    updateData=["CUST_NM__c":field_juVig__c,"out_tenant_id":downstreamOuterTenantId];
    //查询主负责人
    def(Boolean error4,HttpResult data4,String errorMessage4) = Fx.proxy.callAPI("er.getPublicRelationOwnerOuterUid",["x-eip-appid":"FSAID_114910b5"],["downstreamOuterTenantId":downstreamOuterTenantId])
    if(error4){
      log.info("查询主负责人失败")
    }else {
      String downstreamOuterOwnerId= data4['content']['data']
      log.info("downstreamOuterOwnerId:"+downstreamOuterOwnerId)
      //updateData.put("out_owner",[downstreamOuterOwnerId])
      updateData = ["CUST_NM__c":field_juVig__c,"out_tenant_id":downstreamOuterTenantId,"out_owner":[downstreamOuterOwnerId],"field_33u1Q__c":user_name];
    }
  }
}

def (Boolean error2,Map data2,String errorMessage2) =  Fx.object.update(SalesOrderObj,id,updateData,false);
if( error2 ){
  log.info(errorMessage2)
  log.info("将订单关联的交易处客户所属经销商的客户编码刷到销售订单对象上失败:订单_id为"+id);
}else{
  log.info("将订单关联的交易处客户所属经销商的客户编码刷到销售订单对象上成功:订单_id为"+id);
}


