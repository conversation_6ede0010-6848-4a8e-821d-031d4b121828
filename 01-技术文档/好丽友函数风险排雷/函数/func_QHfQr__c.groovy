String resource = context.data.resource;
log.info('resource = ' + resource);
// List out_ownerList = context.data.out_owner as List;
// String out_owner = "";
// if(null != out_ownerList && out_ownerList.size()>0){
//   out_owner = out_ownerList.get(0);
// }
// log.info("外部负责人ID："+out_owner);
// String userId = context.userId as String;
// log.info("userId:"+userId);
String source = "option1";//CRM
// //20220323--创建人跟从客户获取的外部负责人是否是同一个人，如果不是，订单销售来源就是crm
// if(out_owner != null && userId == out_owner){
//     source = "n4GNu0YTq";//经销商
//   }
  String outUserId = context.outUserId as String;//获取外部人员ID 如果不为空 订单销售来源就经销商
  log.info("outUserId:"+outUserId);
if(outUserId != ""){
   source = "n4GNu0YTq";//经销商
}
log.info("source:"+source);
// 判断不是来自订货通的，就认为不是下游的订单
if (resource != "1") {
   Map item = context.data as Map
   if(item["record_type"]!="record_hV12A__c"){
     return context.data;
   }
  String accountId=item["account_id"];
  if(accountId == null || accountId.isEmpty())
    return ;
  Map data1 = [
      "mapperObjectId":accountId  
    ]
  
  def(Boolean error1,HttpResult result1,String errorMessage1) = Fx.proxy.callAPI("er.getDownstreamOuterTenantIdByMappObjectId",["x-eip-appid":"FSAID_11490c84"],data1)
  if(error1){
    log.info("q1 fail!:"+errorMessage1);
    log.info(result1);
    return;
  }
    
  log.info(result1.content["data"] );
  String outEi = result1.content["data"];
  
  def(Boolean error,HttpResult data,String errorMessage) = Fx.proxy.callAPI("er.getPublicRelationOwnerOuterUid",["x-eip-appid":"FSAID_11490c84"],["downstreamOuterTenantId":outEi])
  if(error){
     log.info("q2 fail!:"+errorMessage);
     log.info(data);
    return;
  }
  
  log.info(data["content"]["data"]);
  String outId = data["content"]["data"];
  
  
 /* def (Boolean error3,Map data3,String errorMessage3) =  Fx.object.update("SalesOrderObj",id,["out_owner":[outId],"out_tenant_id":outEi],false)
  
  log.info(error3)
  log.info(errorMessage3)*/
  context.data.out_owner = [outId] ;
  context.data.out_tenant_id = outEi;
  context.data.field_Es3a0__c = source;   
  return context.data;
  }