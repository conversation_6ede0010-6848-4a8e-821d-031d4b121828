String dataId = context.data._id as String
log.info('销售订单：' + context.data.name)
String field_5AQY2__c = context.data.field_5AQY2__c as String
log.info('客户采购订单号：' + field_5AQY2__c)
def (Boolean error, Map data, String errorMessage) = Fx.object.update('SalesOrderObj', dataId, ['field_uViyD__c':'t1MMu1Boo'])
if (!error) {
  log.info('更新销售订单发送状态为已发送成功。')
}else {
  log.info('更新销售订单发送状态为已发送异常：' + errorMessage)
}
def (Boolean error1, QueryResult data1, String errorMessage1) = Fx.object.find('object_6h08G__c', [
    ['field_EnkMa__c':Operator.EQ(field_5AQY2__c)],
    ['life_status': Operator.NE('invalid')]
], 10, 0)
if (!error1 && data1.total > 0) {
  List data1List = data1.dataList as List
  def batchUpdateMap = [:]
  data1List.each { item->
    batchUpdateMap.put(item['_id'] as String, ['field_k36vK__c': 'option1'])
  }
  def (Boolean error2, Map data2, String errorMessage2) = Fx.object.batchUpdate('object_6h08G__c', batchUpdateMap, ['field_k36vK__c'])
  if (!error2) {
    log.info('批量更新汉询直供订单发送状态为已发送成功。')
    }else {
    log.info('批量更新汉询直供订单发送状态为已发送异常：' + errorMessage2)
  }
  return
}
