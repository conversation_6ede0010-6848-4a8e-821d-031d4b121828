String accountId = context.data.field_vJd2b__c
Map requestParam = [
        'accountId'         : accountId,
        'enableIsValidorder': false  //原价目表逻辑true:编辑时下发,禁用和过期的也可以出现，false:新建时用,不出现禁用和过期的
]
//汉询直供订单id
String id = context.data['_id'] as String
def (Boolean pricebookError, HttpResult pricebookResult, String pricebookMsg) = Fx.proxy.callAPI('sfa.available.getAvailablePriceBookList', [:], requestParam)
List pricebookList = pricebookResult['content']['result']['priceBookDataList'] as List
if (pricebookList.size() == null || pricebookList.size() < 1) {
    log.info('未匹配到价目表')
    log.info('审核状态修改：审核异常1')
    def (Boolean error7, Map data7, String errorMessage7) = Fx.object.update('object_6h08G__c', id, ['field_uIu2b__c': '3wxh5IEQ1', 'field_gi7ns__c': '未匹配到该客户产品价目表'], false)
    if (error7) {
        log.info('errorMessage7 : ' + errorMessage7)
    }
    return
}
//保证客户可售价目表只有一个
String pricebookId = pricebookList.get(0)['_id']

def (Boolean error5, Map data5, String errorMessage5) = Fx.object.findOne('object_r62mc__c', [['field_XCh3o__c': accountId]], ['field_fI3TA__c'])
def (Boolean error6, Map data6, String errorMessage6) = Fx.object.findOne('store__c', [['pointcode__c': context.data.field_Oiw1A__c]], ['_id'])
BigDecimal num = 0
if (!error5 && data5.objectData) {
    Map dataMap = data5.objectData as Map
    num = dataMap['field_fI3TA__c'] as BigDecimal
}
//请求参数 - 产品详情
Map detailDataMap = [:]
List productIds = []//pricebookproductid
BigDecimal OrderAmount = 0

Map localDetailDataMap = [:]
context.details.object_z0622__c.each {
    item ->
        String productId = item['field_f1Onm__c'] as String
        productIds.add(productId)
        localDetailDataMap.put(productId, item)
}
//todo:改为findByIds
def (Boolean PR_err, List PR_data, String PR_err_data_errMes) = Fx.object.findByIds('ProductObj', productIds)
if (PR_data.size() != productIds.size()) {
    log.info('审核状态修改：审核异常2')
    def (Boolean error9, Map data9, String errorMessage9) = Fx.object.update('object_6h08G__c', id, ['field_uIu2b__c': '3wxh5IEQ1', 'field_gi7ns__c': '订单中包含已下架产品，请删除下架产品'], false)
    if (error9) {
        log.info('errorMessage9 : ' + errorMessage9)
    }
    return
}
Map productIdToDataMap = [:]
PR_data.each {
    item ->
        productIdToDataMap.put(item['_id'], item)
}
//查询产品的价目表价格
//todo：clourse批量查
def pbData = []
def closure = {
    List dataList ->
    pbData.addAll(dataList)
}
def (Boolean PB_err, PageQueryData pageQueryData, String PB_errMes) = Fx.object.find('PriceBookProductObj', [['product_id': Operator.IN(productIds)], ['pricebook_id': pricebookId]], closure)
if (PB_err) {
    log.info('PB_errMes : ' + PB_errMes)
} else {
    log.info('pageQueryData : ' + pageQueryData)
}
boolean returnflag = false
pbData.each { item ->
    BigDecimal temp = item['pricebook_sellingprice'] as BigDecimal
    if (temp == 0) {
        returnflag = true
    }
}
if (returnflag) {
    log.info('审核状态修改：审核异常4')
    def (Boolean error9, Map data9, String errorMessage9) = Fx.object.update('object_6h08G__c', id, ['field_uIu2b__c': '3wxh5IEQ1', 'field_gi7ns__c': '订单含有合同价（未税）为0的产品'], false)
    if (error9) {
        log.info('errorMessage9 : ' + errorMessage9)
    }
    return
}
//结果集组map
Map productIdToPricebookDataMap = [:]
pbData.each {
    o ->
        String productId = o['product_id']
        productIdToPricebookDataMap.put(productId, o)
}
// log.info("productIdToPricebookDataMap : " + productIdToPricebookDataMap)
List modifiedDataIndexList = []
Map productIdToTimeMap = [:]
//each改为批量查询，产品价格用客户单价（未税）
List detailList = context.details.object_z0622__c as List
Range range = Ranges.of(0, detailList.size() - 1)
//送达方编码
String songCode = context.data['field_sz71z__c']
//发送仓
String cangCode = context.data['field_c5riY__c']
//汉询接口订单售达方名称
String shouId = context.data['field_vJd2b__c']
log.info('shouId : ' + shouId)
//汉询订单创建日期
DateTime createTime = context.data['create_time'] as DateTime
log.info('createTime : ' + createTime)
Long createTimeStamp = createTime.toTimestamp()
log.info('createTimeStamp : ' + createTimeStamp)
//查询客户限量设定
//todo:两次查询合并 cloure
def customerListSettingList = []
def customerLimitClosure = {
    List dataList ->
    customerListSettingList.add(dataList)
}
def (Boolean customerLimitError, PageQueryData custLimitPageQueryData, String errorMesscustomerLimitErrorMessage) = Fx.object.find('object_MpMYt__c', [['field_cTk2N__c': shouId], ['field_lOb2X__c': Operator.IN(['option1', 'Dxmw6K1ZL'])]], customerLimitClosure)
if (customerLimitError) {
    log.info('errorMesscustomerLimitErrorMessage : ' + errorMesscustomerLimitErrorMessage)
} else {
    log.info('custLimitPageQueryData : ' + custLimitPageQueryData)
}
def accord = []
//筛选符合日期的
customerListSettingList.findAll { item -> item['field_lOb2X__c'] as String == 'option1' || (cangCode == item['field_1ksb5__c'] as String && item['field_lOb2X__c'] as String == 'Dxmw6K1ZL') }.each { item ->
    Date start = item['field_58vwt__c'] as Date
    Date end = item['field_waD3F__c'] as Date
    if (start != null && start.toTimestamp() <= createTimeStamp && end != null && end.toTimestamp() >= createTimeStamp) {
        accord.add(item)
    }
}
log.info('accord : ' + accord)
//限量产品设定id集合
List limitProductIds = []
//key：客户限量设定id，val：客户限量设定Obj
Map limitIdToLimitObj = [:]
accord.each { item ->
    limitProductIds.add(item['field_1xErt__c'])
    String key = item['_id']
    limitIdToLimitObj.put(key, item)
}
log.info('limitIdToLimitObj : ' + limitIdToLimitObj)
//查询限量产品设定
def (Boolean error12, List data12, String errorMessage12) = Fx.object.findByIds('object_L2m1X__c', limitProductIds)
if (error12) {
    log.info('errorMessage12 : ' + errorMessage12)
}
log.info('data12 : ' + data12)
//key ：限量产品设定对象id，val：产品对象id
Map limitProductIdToProductId = [:]
data12.each { item ->
    String key = item['_id']
    String val = item['field_lsF5T__c']
    limitProductIdToProductId.put(key, val)
}
log.info('limitProductIdToProductId : ' + limitProductIdToProductId)
//key ：产品对象id，val ：客户限量设定对象id
Map productIdToLimitId = [:]
accord.each { item ->
    String val = item['_id']
    String limitProductId = item['field_1xErt__c']
    String key = limitProductIdToProductId[limitProductId]
    productIdToLimitId.put(key, val)
}
log.info('productIdToLimitId : ' + productIdToLimitId)
//系统
String system = context.data['field_e29OR__c']
String shouCode = context.data['field_8qhej__c']
BigDecimal discount = null
if (system != '华润万家') {
    def (Boolean disError, QueryResult disData, String disMsg) = Fx.object.find('object_1VzoG__c', [['field_vMhLJ__c':shouCode], ['field_4Hef2__c': 'option1']], 1, 0)
    if (disError) {
        log.info('disMsg : ' +  disMsg)
    }
    if (disData.size > 0) {
        discount = disData.dataList[0]['field_M6hZ3__c'] as BigDecimal
    }
}
log.info('discount : ' + discount)
range.each {
    i ->
        Map item = detailList.get(i) as Map
        String productId = item['field_f1Onm__c'] as String
        Date time = Date.now()
        BigDecimal timestamp = time.toTimestamp() as BigDecimal
        String timestampKey = (timestamp + i) as String
        productIdToTimeMap.put(productId, timestampKey)
        Map tmpPricebookData = productIdToPricebookDataMap[productId]
        // log.info("tmpPricebookData : " + tmpPricebookData);
        if (tmpPricebookData == null) {
        log.info('未匹配到可售范围 ：' + productId)
        }

        BigDecimal tmpPBPrice = tmpPricebookData['pricebook_price'] as BigDecimal
        String tmpPBId = tmpPricebookData['pricebook_id']
        String tmpUnit = tmpPricebookData['unit']

        BigDecimal tmpSubtotal = tmpPBPrice * (item['field_t7P43__c'] as BigDecimal)
        Map tmpDeatil = [:]
        tmpDeatil.put('record_type', 'record_5dFgd__c')
        tmpDeatil.put('object_describe_id', '5fad1536e03ca900010aff0e')
        tmpDeatil.put('object_describe_api_name', 'SalesOrderProductObj')
        tmpDeatil.put('base_unit_count', item['field_t7P43__c'])
        tmpDeatil.put('lock_rule', 'default_lock_rule')
        tmpDeatil.put('lock_status', '0')
        tmpDeatil.put('order_product_amount', tmpSubtotal)
        tmpDeatil.put('quantity', item['field_t7P43__c'])
        tmpDeatil.put('life_status', 'normal')
        tmpDeatil.put('conversion_ratio', '1')
        tmpDeatil.put('price_book_product_id', tmpPricebookData['_id'])
        tmpDeatil.put('stand_price', tmpPricebookData['stand_price'])
        tmpDeatil.put('product_id', productId)
        tmpDeatil.put('price_book_id', tmpPBId)
        tmpDeatil.put('actual_unit', tmpUnit)
        tmpDeatil.put('discount', tmpPricebookData['discount'])
        tmpDeatil.put('price_book_price', tmpPBPrice)
        tmpDeatil.put('product_price', item['field_74k2S__c'])
        tmpDeatil.put('is_multiple_unit', false)
        tmpDeatil.put('unit', tmpUnit)
        tmpDeatil.put('price_book_subtotal', tmpSubtotal)
        tmpDeatil.put('subtotal', tmpSubtotal)
        //订单单价未税
        BigDecimal field_74k2S__c = item['field_74k2S__c'] as BigDecimal
        log.info('field_74k2S__c : ' + field_74k2S__c)
        BigDecimal temp = discount == null ? field_74k2S__c : (field_74k2S__c * ((100 - discount) / 100))
        tmpDeatil.put('field_pASu6__c', temp)
        tmpDeatil.put('field_2lm1K__c', num)
        tmpDeatil.put('PROD_CODE__c', item['field_3ogXP__c'])
        tmpDeatil.put('field_z8Gh5__c', item['field_9YwqN__c'])
        BigDecimal priceNoTax = tmpPricebookData['pricebook_sellingprice'] as BigDecimal
        BigDecimal boxGauge = item['field_U415v__c'] as BigDecimal
        tmpDeatil.put('field_33A4D__c', (priceNoTax / boxGauge))
        tmpDeatil.put('field_5iGmC__c', (temp / boxGauge))
        //客户限量设定关联
        //如果存在该限量产品
        if (productIdToLimitId.containsKey(productId)) {
        String limitId = productIdToLimitId[productId]
        Map limitObj = limitIdToLimitObj[limitId] as Map
        //限量类型
        String limitType = limitObj['field_lOb2X__c']
        boolean flag = true
        if (limitType == '0v12N3dbV') {
            //送达方编码
            String songTemp = limitObj['field_g1s0a__c']
            if (songTemp != songCode) {
                flag = false
            }
            } else if (limitType == 'Dxmw6K1ZL') {
            String cangTemp = limitObj['field_1ksb5__c']
            if (cangTemp != cangCode) {
                flag = false
            }
        }
        if (flag) {
            tmpDeatil.put('field_o8x0L__c', limitId)
            tmpDeatil.put('field_i4nfk__c', limitObj['field_hn59s__c'])
            BigDecimal quantity = item['field_t7P43__c'] as BigDecimal
            BigDecimal field_hn59s__c = limitObj['field_hn59s__c'] as BigDecimal
            log.info('quantity : ' + quantity)
            log.info('field_hn59s__c : ' + field_hn59s__c)
            tmpDeatil.put('field_i8P42__c', quantity <= field_hn59s__c ? '正常' : '超限量')
        }
        }

        detailDataMap.put(timestampKey, tmpDeatil)
        modifiedDataIndexList.add(timestampKey)

        OrderAmount += tmpSubtotal
}
log.info('detailDataMap : ' + detailDataMap)
String requestId = (DateTime.now().toTimestamp() as String) + Fx.random.nextInt(********)
//请求参数 - 订单主信息
Map masterData = [:]
masterData.put('requestId', requestId)
masterData.put('object_describe_api_name', 'SalesOrderObj')
masterData.put('record_type', 'default__c')
masterData.put('created_by', context.data.created_by)
masterData.put('owner', context.data.owner)
masterData.put('data_own_department', context.data.data_own_department)
masterData.put('is_user_define_work_flow', false)
masterData.put('plan_payment_amount', null)
masterData.put('payment_amount', null)
masterData.put('invoice_amount', null)
masterData.put('returned_goods_amount', null)
masterData.put('refund_amount', null)
masterData.put('product_amount', OrderAmount)
masterData.put('price_book_amount', OrderAmount)
masterData.put('order_status', '7')
masterData.put('policy_dynamic_amount', '0.00')
masterData.put('dynamic_amount', '0.00')
masterData.put('policy_total', OrderAmount)
masterData.put('order_amount', OrderAmount)
masterData.put('no_invoice_amount', OrderAmount)
masterData.put('discount', '100.0000')
masterData.put('receivable_amount', OrderAmount)
masterData.put('policy_discount', null)
masterData.put('invoice_status', '3')
masterData.put('order_time', Date.now().toTimestamp())
masterData.put('account_id', context.data.field_vJd2b__c)
masterData.put('price_policy_id', '')
masterData.put('record_type', 'record_yxtdd__c')
masterData.put('field_0315j__c', context.data.field_sz71z__c)
if (!error6 && data6.objectData) {
    Map dataMap = data6.objectData as Map
    masterData.put('field_aS3vx__c', dataMap['_id'])//送达方
}
masterData.put('field_80S1u__c', context.data.field_lG1vx__c)
masterData.put('field_5AQY2__c', context.data.field_EnkMa__c)
masterData.put('field_IHtsw__c', context.data.field_X5Y2q__c)
masterData.put('field_ro9Z8__c', context.data.field_iCzlJ__c)
DateTime dateTime1 = context.data.field_w5Uh2__c as DateTime
masterData.put('field_wB6tc__c', dateTime1.toTimestamp())
masterData.put('field_3s0qC__c', context.data.field_vJd2b__c)
DateTime dateTime2 = context.data.field_So219__c as DateTime
masterData.put('field_41jvf__c', dateTime2.toTimestamp())
masterData.put('field_setzH__c', 0.13)
def (Boolean accountError, Map accountData, String accountMsg) = Fx.object.findById('AccountObj', accountId)
if (accountError) {
    log.info('accountMsg : ' + accountMsg)
}
masterData.put('field_CKal1__c', accountData['field_as18N__c'])
//发货仓
if (cangCode != null) {
    masterData.put('field_aS3vx__c', cangCode)
//如果不存在，则查询送达方编码上的发货仓
} else {
    def (Boolean addressError, Map addressData, String addressErrorMessage) = Fx.object.findById('AccountAddrObj', songCode)
    if (addressError) {
        log.info('addressErrorMessage : ' + addressErrorMessage)
    }
    masterData.put('field_aS3vx__c', addressData['field_tfDb2__c'])
}
String batchNo = (DateTime.now().toTimestamp() as String) + Fx.random.nextInt(********)
Map requestParams = [
        'requestId'               : requestId,
        'accountId'               : accountId,
        'masterObjectApiName'     : 'SalesOrderObj',
        'masterData'              : masterData,
        'detailDataMap'           : detailDataMap,
        'modifiedDataIndexList'   : modifiedDataIndexList,
        'matchType'               : 'detail',
        'removeGroupKeySet'       : [],
        'batchNo'                 : batchNo,
        'exactlyMatchModifiedData': false
]
log.info('requestParams : ' + requestParams)
//todo
def (Boolean error1, HttpResult result1, String msg1) = Fx.proxy.callAPI('sfa.pircePolicy.match', null, ['x-fs-userinfo': '-10000'], requestParams)
if (error1) {
    log.info('接口匹配政策：' + msg1)
}
log.info('result1 : ' + result1)
Map matchResult = result1['content'] as Map
Map detailPolicyData = result1.content['result']['detailDataMap'] as Map
detailDataMap.each {
    Object key, value ->
        String key1 = key as String
        Map value1 = value as Map
        if (detailPolicyData[key1] != null) {
        Map detailMatchPolicy = detailPolicyData[key1] as Map
        detailMatchPolicy.each {
                Object matchKey, matchValue ->
                    value1.put(matchKey, matchValue)
        }
        }
}

//销售订单主信息
if (result1.content['result']['masterData'] != null) {
    Map masterPolicyData = result1.content['result']['masterData'] as Map
    masterPolicyData.each {
        Object key, value ->
            masterData.put(key, value)
    }
}

Map calculateMap = [:]
calculateMap.put('masterObjectApiName', 'SalesOrderObj')
calculateMap.put('masterData', masterData)
calculateMap.put('detailDataMap', ['SalesOrderProductObj': detailDataMap])
calculateMap.put('calculateFields', json.parse("{\"SalesOrderProductObj\":[{\"fieldName\":\"field_fF0zf__c\",\"order\":8},{\"fieldName\":\"subtotal\",\"order\":5},{\"fieldName\":\"field_XU3xw__c\",\"order\":10},{\"fieldName\":\"sales_price\",\"order\":5},{\"fieldName\":\"field_7F1ys__c\",\"order\":8},{\"fieldName\":\"discount\",\"order\":6},{\"fieldName\":\"policy_subtotal\",\"order\":5},{\"fieldName\":\"field_yu2G5__c\",\"order\":8},{\"fieldName\":\"field_j62vI__c\",\"order\":6},{\"fieldName\":\"order_product_amount\",\"order\":12},{\"fieldName\":\"policy_price\",\"order\":5},{\"fieldName\":\"policy_discount\",\"order\":5}],\"SalesOrderObj\":[{\"fieldName\":\"field_0UYpv__c\",\"order\":7},{\"fieldName\":\"field_EBp32__c\",\"order\":13},{\"fieldName\":\"field_3idu2__c\",\"order\":11},{\"fieldName\":\"payment_status\",\"order\":17},{\"fieldName\":\"field_yBa8t__c\",\"order\":12},{\"fieldName\":\"discount\",\"order\":11},{\"fieldName\":\"receivable_amount\",\"order\":16},{\"fieldName\":\"field_O8oH2__c\",\"order\":8},{\"fieldName\":\"field_qPrzi__c\",\"order\":12},{\"fieldName\":\"policy_discount\",\"order\":9},{\"fieldName\":\"field_kV6eo__c\",\"order\":2},{\"fieldName\":\"field_z5tO1__c\",\"order\":9},{\"fieldName\":\"field_9ibtb__c\",\"order\":11},{\"fieldName\":\"order_amount\",\"order\":10},{\"fieldName\":\"field_2zWl6__c\",\"order\":7},{\"fieldName\":\"field_31JI7__c\",\"order\":13},{\"fieldName\":\"no_invoice_amount\",\"order\":12},{\"fieldName\":\"field_O5p6j__c\",\"order\":7},{\"fieldName\":\"field_cp7z7__c\",\"order\":12},{\"fieldName\":\"product_amount\",\"order\":6},{\"fieldName\":\"policy_total\",\"order\":8},{\"fieldName\":\"invoice_status\",\"order\":12}]}"))
calculateMap.put('calculateFieldApiNames', json.parse("{\"SalesOrderProductObj\":[\"field_fF0zf__c\",\"subtotal\",\"field_XU3xw__c\",\"sales_price\",\"field_7F1ys__c\",\"discount\",\"policy_subtotal\",\"field_yu2G5__c\",\"field_j62vI__c\",\"order_product_amount\",\"policy_price\",\"policy_discount\"],\"SalesOrderObj\":[\"field_0UYpv__c\",\"field_EBp32__c\",\"field_3idu2__c\",\"payment_status\",\"field_yBa8t__c\",\"discount\",\"receivable_amount\",\"field_O8oH2__c\",\"field_qPrzi__c\",\"policy_discount\",\"field_kV6eo__c\",\"field_z5tO1__c\",\"field_9ibtb__c\",\"order_amount\",\"field_2zWl6__c\",\"field_31JI7__c\",\"no_invoice_amount\",\"field_O5p6j__c\",\"field_cp7z7__c\",\"product_amount\",\"policy_total\",\"invoice_status\"]}"))
//调用计算服务
def (Boolean calculateError, HttpResult calculateResult, String calMsg) = Fx.proxy.callAPI('object.batchCalculate', [:], calculateMap)
Map calculateMasterResult = [:]
Map calculateDetailResult = [:]
if (calculateResult['content']['data'] != null) {
    calculateMasterResult = calculateResult['content']['data']['calculateResult']['SalesOrderObj'] as Map
    calculateDetailResult = calculateResult['content']['data']['calculateResult']['SalesOrderProductObj'] as Map
}

if (calculateMasterResult != null) {
    Map eachMap = calculateMasterResult['0'] as Map
    eachMap.each {
        Object key, value ->
            masterData.put(key, value)
    }
}

detailDataMap.each {
    Object key, value ->
        Map value2 = value as Map
        Map item2 = calculateDetailResult[key] as Map
        if (item2 != null) {
        item2.each {
                Object key1, value1 ->
                    value2.put(key1, value1)
        }
        }
}
//汉询直供订单name
String hName = context.data['_id']
masterData.put('field_N0j70__c', hName)
Map createDetailMap = [:]
createDetailMap.put('SalesOrderProductObj', detailDataMap.values() as List)
log.info('masterData : ' + masterData)
log.info('createDetailMap : ' + createDetailMap)
def (Boolean error, Map data, String errorMessage) = Fx.object.create('SalesOrderObj', masterData, createDetailMap, true)
if (error) {
    log.info('errorMessage : ' + errorMessage)
    if (errorMessage.contains('最大可优惠总数量超出')) {
        log.info('审核状态修改：审核异常3')
        int index1 = errorMessage.indexOf('最大可优惠总数量超出[')
        int index2 = errorMessage.indexOf(']，请更换或取消政策')
        String reMsg = '可下单数量超出限量数量' + errorMessage.substring(index1 + 11, index2) + '件'
        def (Boolean error10, Map data10, String errorMessage10) = Fx.object.update('object_6h08G__c', id, ['field_uIu2b__c': '3wxh5IEQ1', 'field_gi7ns__c': reMsg], false)
        if (error10) {
            log.info('errorMessage10 : ' + errorMessage10)
        }
    } else if (data != null && data['data'] != null && data['data']['_id'] != null) {
        log.info('审核状态修改：已审核')
        def (Boolean error8, Map data8, String errorMessage8) = Fx.object.update('object_6h08G__c', id, ['field_uIu2b__c': 'C55dI1070'], false)
        if (error8) {
            log.info('errorMessage8 : ' + errorMessage8)
        }
        def (Boolean error14, data14, String errorMessage14) = Fx.object.lock('object_6h08G__c', id, true)
        if (error14) {
            log.info('errorMessage14 : ' + errorMessage14)
        }
    }
} else {
    log.info('审核状态修改：已审核')
    def (Boolean error8, Map data8, String errorMessage8) = Fx.object.update('object_6h08G__c', id, ['field_uIu2b__c': 'C55dI1070'], false)
    if (error8) {
        log.info('errorMessage8 : ' + errorMessage8)
    }
    def (Boolean error14, data14, String errorMessage14) = Fx.object.lock('object_6h08G__c', id, true)
    if (error14) {
        log.info('errorMessage14 : ' + errorMessage14)
    }
}
return null
