String id = context.data._id as String;
String customerId = context.data.field_vJd2b__c as String;
String wareCode = context.data.field_Oiw1A__c as String //仓库编码
String zhuangtai = "审核"
String fanhuixinxi = "上传成功"
Map updateMap = [:]

def (Boolean wareEr, QueryResult wareData, String wareMsg) = Fx.object.find("store__c",[["store_number__c":wareCode]], 1, 0);
if(!wareEr ){
  if(wareData.total > 0){
    List wareList = wareData.dataList as List;
    Map wareMap = wareList.get(0) as Map;
    String wareId = wareMap["_id"] as String; 
      updateMap.put("field_c5riY__c", wareId)   
  }
}
 
def (Boolean error1, QueryResult data1, String errorMessage1) = Fx.object.find("object_22LZR__c",[["field_FcCo2__c":customerId]], 1, 0);
if( !error1 ){
  if(data1.total > 0){
    List data1List = data1.dataList as List;
    Map data1Map = data1List.get(0) as Map;
    String type = data1Map["field_c9dA1__c"] as String;
    if(type == "xa4Baw4wx"){
       updateMap.put("field_eVbLf__c", "option1")
    }else{
      updateMap.put("field_eVbLf__c", "9vy562sOT")
    }
  }else{
    updateMap.put("field_eVbLf__c", "9vy562sOT")
  }
}else{
  updateMap.put("field_eVbLf__c", "9vy562sOT")
}
//客户下销售区域、营业所
def (Boolean error2, Map data2, String errorMessage2) = Fx.object.findById( "AccountObj", customerId);
if( !error2 ){
  List area = data2["field_9Y9cp__c"] as List;
  List office = data2["field_ZRvgo__c"] as List;

  updateMap.put("field_X5Y2q__c", area);//销售区域
  updateMap.put("field_iCzlJ__c", office );//营业所
}
updateMap.put("field_rxiiV__c", zhuangtai);//状态
updateMap.put("field_jSd25__c", fanhuixinxi );//返回信息
log.info("更新订单入参：" + updateMap)
def (Boolean error, Map data, String errorMessage) = Fx.object.update("object_6h08G__c", id, updateMap)
if(!error){
  log.info("更新成功")
}else{
  log.info("更新失败：" + errorMessage)
}