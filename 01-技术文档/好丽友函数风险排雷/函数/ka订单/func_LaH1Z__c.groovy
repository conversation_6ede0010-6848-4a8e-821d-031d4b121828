// 函数示例：计划任务批量更新当前对象数

// 业务说明：查询所有商机数据，判断负责人部门和归属部门是否一致，如不一致更新归属部门
// 注意：计划任务在自定义函数编写时运行可以获取到**context**关键字，但是在实际执行时是获取不到的

// 如要移植到其他对象上，仅需要修改objName的值，不需要修改其他内容
List ids = context.objectIds as List //获取所有符合条件的数据id，新建计划任务会有筛选条件
// List ids = [context.data._id as String] //**测试用，别动
String objName = 'AccountObj' //***对象名，如果需要再其他对象执行需要更换api name***
if (ids) {
    //对账日期
    Date nowDate = Date.now()
    log.info('当前时间：' + nowDate)
    Date firstDate = nowDate.withDay(1)
    log.info('当前月第一天：' + firstDate)
    Date endDate = ((nowDate.withDay(1)) - 1.days)
    log.info('上月最后一天：' + endDate)
    Date startDate = endDate.withDay(1)
    log.info('上月第一天：' + startDate)
    Date monthDate = (nowDate.withMonth(nowDate.month + 1).withDay(1)) - 1.days
    log.info('本月最后一天：' + monthDate)
    Date lastMonthDay =  Date.now() - 1.months
    String yearStr = lastMonthDay.year as String
    String monthStr
    if (lastMonthDay.month < 10) {
        monthStr = '0' + lastMonthDay.month as String
        } else {
        monthStr = lastMonthDay.month as String
    }
    String lastMonth = yearStr + monthStr
    log.info('上月日期：' + lastMonth)

    log.info('------------------开始汇总市场费对账单---------------------')
    def customerIdAndInitialMarketFee = [:]
    Range range = Ranges.of(1,100)
    String marketFeeId
    range.find {
        def initialMarketFeeClosure = { List dataList ->
            dataList.each { item ->
                    def beginPrice = item['field_qH2VM__c'] as BigDecimal
                    customerIdAndInitialMarketFee.put(item['field_1311b__c'] as String, beginPrice ? beginPrice : 0)
            }
        }
        def(Boolean mfError, PageQueryData mfPageQueryData, String mfErrorMessage) = Fx.object.find('object_78Muc__c', [
                    ['field_1311b__c': Operator.IN(ids)],
                    ['field_dd1vY__c': lastMonth]
                ], initialMarketFeeClosure, marketFeeId)
        if (mfError) {
            log.info('find object_78Muc__c error : ' + mfErrorMessage)
        } else {
            log.info('object_78Muc__c query page : ' + mfPageQueryData)
            if (mfPageQueryData["notFinished"] && mfPageQueryData["lastId"]) {
                marketFeeId = mfPageQueryData["lastId"] as String
                return false
            } else {
                return true
            }
        }
    }

    def batchMarketFeeObjMap = [:]
    ids.each { id ->
            id = id as String
            BigDecimal beginPrice = customerIdAndInitialMarketFee[id] as BigDecimal
            Map marketFeeObjectMap = [:]
            marketFeeObjectMap.put('field_epaV1__c', id) //客户
            marketFeeObjectMap.put('field_1iOk3__c', nowDate) //对账日期
            marketFeeObjectMap.put('field_xEd17__c', lastMonth)
            marketFeeObjectMap.put('field_CYKu4__c', beginPrice ? beginPrice : 0)
            batchMarketFeeObjMap.put(id, marketFeeObjectMap)
    }

    DateTime startTime = DateTime.of((firstDate as String) + ' 00:00')
    DateTime endTime = DateTime.of((monthDate as String) + ' 23:59')
    log.info('startTime：' + startTime)
    log.info('endTime：' + endTime)

    def computeIfAbsent = {
            map, key, value ->
        def list = (map as Map)[key] as List
        if (list == null) {
            (map as Map).put(key, [value])
                } else {
            list.add(value)
            (map as Map).put(key, list)
        }
    }

    def marketFeeDetailForPayMap = [:] // 市场费返还明细--垫付
    def marketFeeDetailForReturnMap = [:] // 市场费返还明细--返还

    String marketReturnId
    range.find {
        def marketReturnDetailsClource = { List dataList ->
            dataList.each { item ->
                    if ('default__c' == item['record_type']) {
                        Map marketFeeForPayMap = [: ]
                        marketFeeForPayMap.put('field_joP79__c', item['_id']) //SAP市场费
                        marketFeeForPayMap.put('field_f920J__c', item['field_7Za9d__c']) //TPM行号
                        marketFeeForPayMap.put('field_g7m1P__c', item['field_1ph13__c']) //活动名称
                        marketFeeForPayMap.put('field_gWPqo__c', item['field_pvs2L__c']) //垫付金额
                        marketFeeForPayMap.put('field_hbfb9__c', item['field_2f949__c']) //活动申请日期
                        marketFeeForPayMap.put('field_AMril__c', item['field_B3nso__c']) //科目
                        marketFeeForPayMap.put('field_01XeE__c', item['field_bxobU__c']) //门店
                        marketFeeForPayMap.put('field_nQ8ph__c', item['field_h9B4c__c']) //品牌
                        computeIfAbsent(marketFeeDetailForPayMap, item['field_aCz4i__c'] as String, marketFeeForPayMap)
                    } else if ('record_sfa93__c' == item['record_type']) {
                        Map marketFeeForReturnMap = [: ]
                        marketFeeForReturnMap.put('field_JeL37__c', item['_id']) //SAP市场费
                        marketFeeForReturnMap.put('field_aocnI__c', item['field_7Za9d__c']) //TPM行号
                        marketFeeForReturnMap.put('field_f2kXU__c', item['field_1ph13__c']) //活动名称
                        marketFeeForReturnMap.put('field_2b9G2__c', item['field_pvs2L__c']) //返还金额
                        marketFeeForReturnMap.put('field_Sma18__c', item['field_bxobU__c']) //门店
                        marketFeeForReturnMap.put('field_0xHmr__c', item['field_h9B4c__c']) //品牌
                        marketFeeForReturnMap.put('field_iP60K__c', item['field_B3nso__c']) //科目
                        computeIfAbsent(marketFeeDetailForReturnMap, item['field_aCz4i__c'] as String, marketFeeForReturnMap)
                    }
            }
        }
        def(Boolean mrError, PageQueryData mrPageQueryData, String mrErrorMessage) = Fx.object.find('object_Yv7ah__c', [
                    ['field_aCz4i__c': Operator.IN(ids)],
                    ['create_time': Operator.GTE(startTime)],
                    ['create_time': Operator.LTE(endTime)],
                    //["field_16fVI__c": Operator.GTE(startDate)],
                    //["field_16fVI__c": Operator.LTE(endDate)],
                    ['record_type': Operator.IN(['default__c', 'record_sfa93__c'])]
                ], marketReturnDetailsClource, marketReturnId)
        if (mrError) {
            log.info('find object_Yv7ah__c error : ' + mrErrorMessage)
        } else {
            log.info('object_Yv7ah__c query page : ' + mrPageQueryData)
            if (mrPageQueryData["notFinished"] && mrPageQueryData["lastId"]) {
                marketReturnId = mrPageQueryData["lastId"] as String
                return false
            } else {
                return true
            }
        }
    }
    log.info("batchMarketFeeObjMap : " + batchMarketFeeObjMap)
    // 可以改成分开批量创建主从对象？
    batchMarketFeeObjMap.each { key, value ->
            def marketFeeDetailMap = [: ]
            def marketFeeDetailForPayList = marketFeeDetailForPayMap[key] as List
            marketFeeDetailMap.put('object_616e8__c', marketFeeDetailForPayList ? marketFeeDetailForPayList : [])
            def marketFeeDetailForReturnList = marketFeeDetailForReturnMap[key] as List
            marketFeeDetailMap.put('object_01o2w__c', marketFeeDetailForReturnList ? marketFeeDetailForReturnList : [])
            log.info("marketFeeDetailMap : " + marketFeeDetailMap)
            def(Boolean error13, data13, String errorMessage13) = Fx.object.create('object_5j3wh__c', value as Map, marketFeeDetailMap, false)
            if ( error13 ) {
            log.info('新建市场费对账单返回信息：' + errorMessage13)
            }else {
            log.info('新建市场费对账单成功')
            }
    }
    def groupList = Fx.utils.listPartition((batchMarketFeeObjMap as Map).keys(), 500) as List
    log.info(groupList)
    def customerIdAndmarketFeeId = [:]
    groupList.each {it ->
        def outerMap = batchMarketFeeObjMap
        def innerMarketFeeDetailForPayMap = marketFeeDetailForPayMap
        def innerMarketFeeDetailForReturnMap = marketFeeDetailForReturnMap
        def marketFeeObjectList = []
        it.each {item ->
            if (innerMarketFeeDetailForPayMap[item] || innerMarketFeeDetailForReturnMap[item]) {
                marketFeeObjectList.add(outerMap[item] as Map)
            }
        }
        if (marketFeeObjectList) {
            def (Boolean err,List data, String errMsg) = Fx.object.batchCreate("object_n7b95__c", marketFeeObjectList)
            if(err){
                log.info("市场费对账单批量新建异常：" + errMsg)
            } else {
                data.each { item ->
                    customerIdAndmarketFeeId.put(item["field_epaV1__c"] as String,item["_id"] as String)
                }
                log.info("市场费对账单批量条数：" + data.size())
            }
        }
    }

    customerIdAndmarketFeeId.each { key, value ->
        def marketFeeDetailForPayList = marketFeeDetailForPayMap[key] as List
        marketFeeDetailForPayList.each { item ->
            (item as Map).put("field_v8r1y__c", value as String)
        }
        def marketFeeDetailForReturnList = marketFeeDetailForReturnMap[key] as List
        marketFeeDetailForReturnList.each { item ->
            (item as Map).put("field_m7bz2__c", value as String)
        }             
    }
    def marketFeeDetailGroupList = Fx.utils.listPartition((marketFeeDetailForPayMap as Map).keys(), 500) as List
    log.info(marketFeeDetailGroupList)
    marketFeeDetailGroupList.each {it ->
        def outerMap = marketFeeDetailForPayMap
        def marketFeeDetailForPayBatchList = []
        (it as List).each {item ->
            def marketFeeDetailMap = outerMap[item] as Map
            if (marketFeeDetailMap) {
                marketFeeDetailForPayBatchList.add(marketFeeDetailMap)
            }
        }
        if(marketFeeDetailForPayBatchList) {
            def (Boolean err1,List data1, String errMsg1) = Fx.object.batchCreate("object_616e8__c", marketFeeDetailForPayBatchList)
            if(err1){
                log.info("2、市场费垫付明细批量新建异常：" + errMsg1)
            } else {
                log.info("2、市场费垫付明细批量条数：" + data1.size())
            }
        }
    }

    def marketFeeDetailForReturnGroupList = Fx.utils.listPartition((marketFeeDetailForReturnMap as Map).keys(), 500) as List
    log.info(marketFeeDetailForReturnGroupList)
    marketFeeDetailForReturnGroupList.each {it ->
        def outerMap = marketFeeDetailForReturnMap
        def marketFeeDetailForReturnBatchList = []
        (it as List).each {item ->
            def map = outerMap[item] as Map
            if (map) {
                marketFeeDetailForReturnBatchList.add(map)
            }
        }
        if(marketFeeDetailForReturnBatchList) {
            def (Boolean err2,List data2, String errMsg2) = Fx.object.batchCreate("object_01o2w__c", marketFeeDetailForReturnBatchList)
            if(err2){
                log.info("1、市场费返还明细批量新建异常：" + errMsg2)
            } else {
                log.info("1、市场费返还明细批量条数：" + data2.size())
            }
        }
    }
}



    
    //循环处理数据，在里面写处理逻辑
    ids.eachWithIndex {
    items,
    int i->
    //根据id查询数据
    String id = items as String //当前数据的id
    //根据id查询出数据的详细信息
    def(Boolean error100, Map customMap, String msg100) = Fx.object.findById(objName, id)

    log.info("客户名称：" + customMap["name"] as String)
    String customId = customMap["_id"] as String//客户ID
    //对账日期
    Date nowDate = Date.now()
    log.info("当前时间：" + nowDate)
    Date firstDate = nowDate.withDay(1)
    log.info("当前月第一天：" + firstDate)
    Date endDate = ((nowDate.withDay(1)) - 1.days)
    log.info("上月最后一天：" + endDate)
    Date startDate = endDate.withDay(1)
    log.info("上月第一天：" + startDate)
    Date monthDate = (nowDate.withMonth(nowDate.month+1).withDay(1))-1.days
    log.info("本月最后一天："+monthDate)
    
    log.info("------------------开始汇总市场费对账单---------------------")
    Map marketFeeObjectMap = [: ]
    Map marketFeeDetailMap = [: ]
    
    marketFeeObjectMap.put("field_epaV1__c", customMap["_id"]) //客户
    marketFeeObjectMap.put("field_1iOk3__c", nowDate) //对账日期
    //20220301-cwh-start
    Map keyValue = [
      "date" : nowDate
    ]
    Map param = [
      "data":keyValue
    ]
    def (Boolean error,String lastMonth,String errorMessage) = Fx.function.executeFunc("func_0K47b__c",param)
    if(!error){
      marketFeeObjectMap.put("field_xEd17__c", lastMonth) //对账区间
      log.info("对账区间:"+lastMonth)
    }
    //20220301-cwh-end
    BigDecimal beginPrice = 0
    
    def(Boolean error1, QueryResult data1, String errorMessage1) = Fx.object.find("object_78Muc__c", [
                ["field_1311b__c": customId],
                ["field_dd1vY__c": lastMonth]
            ], 1, 0)
    if(error1) {
      log.info("errorMessage1 : " + errorMessage1)
    }
    log.info("data1 : "+data1)
    if(null != data1 && data1.dataList.size()>0){
      Map map = data1.dataList.get(0) as Map
     beginPrice = map["field_qH2VM__c"] as BigDecimal
    }
     marketFeeObjectMap.put("field_CYKu4__c", beginPrice)
     log.info("期初金额:"+beginPrice)
    // marketFeeObjectMap.put("field_poaCS__c", endPrice)
    //2.市场费返还明细--垫付
    List marketFeeDetailList = []

    DateTime startTime = DateTime.of((firstDate as String) + " 00:00")
    DateTime endTime = DateTime.of((monthDate as String) + " 23:59")
    log.info("startTime：" + startTime)
    log.info("endTime：" + endTime)
    
    def(Boolean error9, QueryResult data9, String errorMessage9) = Fx.object.find("object_Yv7ah__c", [
                ["field_aCz4i__c": customMap["_id"]],
                ["create_time": Operator.GTE(startTime)],
                ["create_time": Operator.LTE(endTime)],
                //["field_16fVI__c": Operator.GTE(startDate)],
                //["field_16fVI__c": Operator.LTE(endDate)],
                ["record_type": "default__c"]
            ], 100, 0)
    if(error9) {
      log.info("errorMessage9 : " + errorMessage9)
    }
    log.info("data9 : "+data9)
    int marketFeeTotal = data9["total"]as int
    log.info("SAP市场费-垫付 total：" + marketFeeTotal)
    if (marketFeeTotal > 0) {
        int marketFeeEnd = marketFeeTotal / 100 as int
        List marketFeeList = []
        Range marketFeeRange = Ranges.of(0, marketFeeEnd)
            marketFeeRange.each {
            def(Boolean error10, QueryResult data10, String errorMessage10) = Fx.object.find("object_Yv7ah__c", [
                        ["field_aCz4i__c": customMap["_id"]],
                        ["create_time": Operator.GTE(startTime)],
                        ["create_time": Operator.LTE(endTime)],
                        //["field_16fVI__c": Operator.GTE(startDate)],
                        //["field_16fVI__c": Operator.LTE(endDate)],
                        ["record_type": "default__c"]
                    ], 100, it * 100)
            marketFeeList.addAll(data10.dataList as List)
            //log.info("marketFeeList："+ marketFeeList)
        }
        marketFeeList.each {
            item->
            Map marketFeeMap = [: ]
            marketFeeMap.put("field_joP79__c", item["_id"]) //SAP市场费
            marketFeeMap.put("field_f920J__c", item["field_7Za9d__c"]) //TPM行号
            marketFeeMap.put("field_g7m1P__c", item["field_1ph13__c"]) //活动名称
            marketFeeMap.put("field_gWPqo__c", item["field_pvs2L__c"]) //垫付金额
            marketFeeMap.put("field_hbfb9__c", item["field_2f949__c"]) //活动申请日期
            marketFeeMap.put("field_AMril__c", item["field_B3nso__c"]) //科目
            marketFeeMap.put("field_01XeE__c", item["field_bxobU__c"]) //门店
            marketFeeMap.put("field_nQ8ph__c", item["field_h9B4c__c"]) //品牌
            marketFeeDetailList.add(marketFeeMap)
        }
    }
    marketFeeDetailMap.put("object_616e8__c", marketFeeDetailList)
    
    //2.市场费返还明细--返还
    List marketFeeDetailList1 = []
    def(Boolean error11, QueryResult data11, String errorMessage11) = Fx.object.find("object_Yv7ah__c", [
                ["field_aCz4i__c": customMap["_id"]],
                ["create_time": Operator.GTE(startTime)],
                ["create_time": Operator.LTE(endTime)],
                //["field_16fVI__c": Operator.GTE(startDate)],
                //["field_16fVI__c": Operator.LTE(endDate)],
                ["record_type": "record_sfa93__c"]
            ], 100, 0)
    int marketFeeTotal1 = data11["total"]as int
    log.info("SAP市场费-返还 total：" + marketFeeTotal1)
    if (marketFeeTotal1 > 0) {
        int marketFeeEnd1 = marketFeeTotal1 / 100 as int
        List marketFeeList1 = []
        Range marketFeeRange1 = Ranges.of(0, marketFeeEnd1)
            marketFeeRange1.each {
            def(Boolean error12, QueryResult data12, String errorMessage12) = Fx.object.find("object_Yv7ah__c", [
                        ["field_aCz4i__c": customMap["_id"]],
                        ["create_time": Operator.GTE(startTime)],
                        ["create_time": Operator.LTE(endTime)],
                        //["field_16fVI__c": Operator.GTE(startDate)],
                        //["field_16fVI__c": Operator.LTE(endDate)],
                        ["record_type": "record_sfa93__c"]
                    ], 100, it * 100)
            marketFeeList1.addAll(data12.dataList as List)
            //log.info("marketFeeList1："+ marketFeeList1)
        }
        marketFeeList1.each {
            item->
            Map marketFeeMap1 = [: ]
            marketFeeMap1.put("field_JeL37__c", item["_id"]) //SAP市场费
            marketFeeMap1.put("field_aocnI__c", item["field_7Za9d__c"]) //TPM行号
            marketFeeMap1.put("field_f2kXU__c", item["field_1ph13__c"]) //活动名称
            marketFeeMap1.put("field_2b9G2__c", item["field_pvs2L__c"]) //返还金额
            marketFeeMap1.put("field_Sma18__c", item["field_bxobU__c"]) //门店
            marketFeeMap1.put("field_0xHmr__c", item["field_h9B4c__c"]) //品牌
            marketFeeMap1.put("field_iP60K__c", item["field_B3nso__c"]) //科目
            marketFeeDetailList1.add(marketFeeMap1)
        }
    }
    marketFeeDetailMap.put("object_01o2w__c", marketFeeDetailList1)
    
    log.info("市场费对账单主表参数:" + marketFeeObjectMap)
    log.info("市场费对账单明细表参数:" + marketFeeDetailMap)
    def(Boolean error13, data13, String errorMessage13) = Fx.object.create("object_5j3wh__c", marketFeeObjectMap, marketFeeDetailMap, false)
    if( error13 ){
      log.info("新建市场费对账单返回信息：" + errorMessage13)
    }else{
      log.info("新建市场费对账单成功")
    }
}

log.info("执行完成")
