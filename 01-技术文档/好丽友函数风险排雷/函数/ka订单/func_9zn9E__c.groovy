// 函数示例：计划任务批量更新当前对象数

// 业务说明：查询所有商机数据，判断负责人部门和归属部门是否一致，如不一致更新归属部门
// 注意：计划任务在自定义函数编写时运行可以获取到**context**关键字，但是在实际执行时是获取不到的

// 如要移植到其他对象上，仅需要修改objName的值，不需要修改其他内容
List ids = context.objectIds as List //获取所有符合条件的数据id，新建计划任务会有筛选条件
    // ids = [context.data._id as String] //**测试用，别动
    String objName = "AccountObj" //***对象名，如果需要再其他对象执行需要更换api name***
    //循环处理数据，在里面写处理逻辑
    ids.eachWithIndex {
    items,
    int i->
    //根据id查询数据
    String id = items as String //当前数据的id
    //根据id查询出数据的详细信息
    def(Boolean error100, Map customMap, String msg100) = Fx.object.findById(objName, id)

    Map paymentObjectMap = [: ];
    Map paymentDetailMap = [: ];
    log.info("客户名称：" + customMap["name"] as String)
     String customId = customMap["_id"] as String;//客户ID
    //客户
    paymentObjectMap.put("field_nCp20__c", customMap["_id"]);
    //对账日期
    Date nowDate = Date.now();
    log.info("当前时间：" + nowDate)
    Date firstDate = nowDate.withDay(1);
    log.info("当前月第一天：" + firstDate)
    Date endDate = ((nowDate.withDay(1)) - 1.days);
    log.info("上月最后一天：" + endDate)
    Date startDate = endDate.withDay(1);
    log.info("上月第一天：" + startDate)

    paymentObjectMap.put("field_E2qIP__c", nowDate.toTimestamp());
    //20220301-cwh-start
    Map keyValue = [
      "date" : nowDate
    ]
    Map param = [
      "data":keyValue
    ]
    def (Boolean errorDz,String lastMonth,String errorMessageDz) = Fx.function.executeFunc("func_0K47b__c",param)
    if(!errorDz){
      paymentObjectMap.put("field_jSRpk__c", lastMonth); //对账期间
    }
     //20220301-cwh-end
     BigDecimal beginPrice = 0;
     BigDecimal dcBeginPrice = 0;
    def(Boolean error9, QueryResult data9, String errorMessage9) = Fx.object.find("object_xW3cI__c", [
                ["field_414an__c": customId],
                ["field_rjrhw__c": lastMonth]
            ], 1, 0);
    if(error9) {
      log.info("errorMessage9 : " + errorMessage9)
    }
    log.info("data9 : "+data9)
    if(null != data9 && data9.dataList.size()>0){
      Map map = data9.dataList.get(0) as Map;
     beginPrice = map["field_gEkqi__c"] as BigDecimal;//货款期初金额
     dcBeginPrice = map["field_5x39x__c"] as BigDecimal;//DC期初金额
    }
    paymentObjectMap.put("field_CYKu4__c", dcBeginPrice)//DC期初金额
    paymentObjectMap.put("field_1kJVe__c", beginPrice)//货款期初金额
    log.info("------------------开始汇总货款对账单---------------------")
    //1.对账明细-收款
    List collectionDetailList = [];  // SAP-收款    object_F77qS__c
    def(Boolean error, QueryResult data, String errorMessage) = Fx.object.find("object_F77qS__c", [
      ["field_m1suI__c": customMap["_id"]],
      ["field_7XX4L__c": Operator.GTE(startDate)],
      ["field_7XX4L__c": Operator.LTE(endDate)]
    ], 100, 0);
    int collectionTotal = data['total']as int;
    log.info("SAP-收款 total：" + collectionTotal);
    if (collectionTotal > 0) {
        int collectionEnd = collectionTotal / 100 as int;
        List collectionList = [];
        Range collectionRange = Ranges.of(0, collectionEnd)
            collectionRange.each {
            def(Boolean error1, QueryResult data1, String errorMessage1) = Fx.object.find("object_F77qS__c", [
              ["field_m1suI__c": customMap["_id"]],
              ["field_7XX4L__c": Operator.GTE(startDate)],
              ["field_7XX4L__c": Operator.LTE(endDate)]
            ], 100, it * 100);
            collectionList.addAll(data1.dataList as List)
            //log.info("collectionList："+ collectionList)
            }
        collectionList.each {
            item->
            Map collectionMap = [: ];
            collectionMap.put("field_1tFyf__c", item["_id"]);
            collectionMap.put("field_Y761u__c", item["field_4AQPM__c"]); //金额
            collectionMap.put("field_Yf1fd__c", item["field_7XX4L__c"]); //收款日期
            collectionDetailList.add(collectionMap);
        }
    }
    paymentDetailMap.put("object_0w1NM__c", collectionDetailList);// 对账明细-到款

    //2.对账明细-订单
    log.info("客户ID：" + customMap["_id"])
    log.info("开始日期：" + startDate)
    log.info("结束日期：" + endDate)
    List orderDetailList = [];
    BigDecimal dcAll = 0;
    BigDecimal yjsAll = 0;// SalesOrderObj   销售订单
    def(Boolean error2, QueryResult data2, String errorMessage2) = Fx.object.find("SalesOrderObj", [
      ["account_id": customMap["_id"]],
      ["field_g2wo4__c": Operator.GTE(startDate)],
      ["field_g2wo4__c": Operator.LTE(endDate)],
      ["field_q1Wq0__c": Operator.IN(["7Ui1d8JXP","u31rNLyTS"])]
    ], 100, 0);
    int orderTotal = data2['total']as int;
    log.info("销售订单 total：" + orderTotal);
    if (orderTotal > 0) {
        int orderEnd = orderTotal / 100 as int;
        List orderList = [];
        Range orderRange = Ranges.of(0, orderEnd)
            orderRange.each {
            def(Boolean error3, QueryResult data3, String errorMessage3) = Fx.object.find("SalesOrderObj", [
              ["account_id": customMap["_id"]],
              ["field_g2wo4__c": Operator.GTE(startDate)],
              ["field_g2wo4__c": Operator.LTE(endDate)],
              ["field_q1Wq0__c": Operator.IN(["7Ui1d8JXP","u31rNLyTS"])]
            ], 100, it * 100);
            orderList.addAll(data3.dataList as List)
            //log.info("orderList："+ orderList)
            }
        orderList.each {
            item->
            Map orderMap = [: ];
            orderMap.put("field_wcni5__c", item["_id"]); //订单编号
            orderMap.put("field_KQkk6__c", item["field_31JI7__c"]); //订单金额（含税）
            orderMap.put("field_xgJ0f__c", item["field_yBa8t__c"]); //货款金额（含税）
            BigDecimal price = item["field_G9d4s__c"] as BigDecimal;
            if(null == price){
              price = 0;
            }
            orderMap.put("field_9CTIO__c", price * 1.13); //DC金额（含税）
            orderMap.put("field_Hl1Fs__c", item["field_41iKc__c"]); //已结算货款金额（含税）
            dcAll = dcAll + price * 1.13;
            BigDecimal price1 = item["field_D7242__c"] as BigDecimal;
            if(null == price1){
              price1 = 0;
            }
            orderMap.put("field_lN5k7__c",  1.13 * price1); //已结算DC金额 （含税）
            yjsAll = dcAll + price * 1.13;
            orderDetailList.add(orderMap);
        }
    }
    paymentDetailMap.put("object_152d9__c", orderDetailList);// 对账明细-订单
    paymentObjectMap.put("field_chcNR__c", yjsAll);//已结算货款
    paymentObjectMap.put("field_l59tl__c", dcAll);//已结算DC金额
    paymentObjectMap.put("field_CmHKz__c", beginPrice - dcAll);//货款期末金额=货款期初金额-已结算货款
    paymentObjectMap.put("field_poaCS__c", dcBeginPrice - dcAll);//DC期末金额=DC期初金额-已结算DC金额

    //3.对账明细-发票
    List invoiceDetailList = [];// SAP-发票     object_ahIP5__c
    def(Boolean error4, QueryResult data4, String errorMessage4) = Fx.object.find("object_ahIP5__c", [
      ["field_G24NL__c": customMap["_id"]],
      ["field_ekzFj__c": Operator.GTE(startDate)],
      ["field_ekzFj__c": Operator.LTE(endDate)]
    ], 100, 0);
    log.info("SAP发票 total：" + data4['total']);
    if (data4.total > 0) {
      int invoiceTotal = data4['total']as int;
      int invoiceEnd = invoiceTotal / 100 as int;
      List invoiceList = [];
      Range invoiceRange = Ranges.of(0, invoiceEnd)
      invoiceRange.each {
        def(Boolean error5, QueryResult data5, String errorMessage5) = Fx.object.find("object_ahIP5__c", [
              ["field_G24NL__c": customMap["_id"]],
              ["field_ekzFj__c": Operator.GTE(startDate)],
              ["field_ekzFj__c": Operator.LTE(endDate)]
            ], 100, it * 100);
            invoiceList.addAll(data5.dataList as List)
      //log.info("invoiceList："+ invoiceList)
      }
        invoiceList.each {
            item->
            Map invoiceMap = [: ];
            invoiceMap.put("field_WxA4h__c", item["_id"]);
            invoiceMap.put("field_f3efb__c", item["field_ekzFj__c"]); //发票日期
            invoiceMap.put("field_Cys61__c", item["field_uBy7A__c"]); //发票号码
            invoiceMap.put("field_4W4Gn__c", item["field_t01P1__c"]); //折扣前金额（含税）
            invoiceMap.put("field_g3yUp__c", item["field_NQzro__c"]); //折扣金额（含税）
            invoiceMap.put("field_83cuc__c", item["field_41Hbs__c"]); //发票金额
            invoiceDetailList.add(invoiceMap);
        }
    }
    paymentDetailMap.put("object_wJnu9__c", invoiceDetailList);// 对账明细-发票

    //3.对账明细-TPM折扣
    List dcDetailList = [];  // TPM单-DC    object_r04cg__c
    def(Boolean error6, QueryResult data6, String errorMessage6) = Fx.object.find("object_r04cg__c", [
      ["field_G73cQ__c": customMap["_id"]],
      ["field_nixhr__c": Operator.GTE(startDate)],
      ["field_nixhr__c": Operator.LTE(endDate)],
      ["field_8t90k__c":Operator.EQ(true)]
    ], 100, 0);
    int dcTotal = data6['total']as int;
    log.info("TPM票扣-DC total：" + dcTotal);
    if (dcTotal > 0) {
        int dcEnd = dcTotal / 100 as int;
        List dcList = [];
        Range dcRange = Ranges.of(0, dcEnd)
            dcRange.each {
            def(Boolean error7, QueryResult data7, String errorMessage7) = Fx.object.find("object_r04cg__c", [
              ["field_G73cQ__c": customMap["_id"]],
              ["field_nixhr__c": Operator.GTE(startDate)],
              ["field_nixhr__c": Operator.LTE(endDate)],
              ["field_8t90k__c":Operator.EQ(true)]
            ], 100, it * 100);
            log.info("data7 : " + data7)
            dcList.addAll(data7.dataList as List)
            //log.info("invoiceList："+ invoiceList)
            }
        dcList.each {
            item->
            Map dcMap = [: ];
            dcMap.put("field_4S277__c", item["_id"]); //TPM单号
            //dcMap.put("field_lO88w__c", item["field_dn4iR__c"]); //TPM单号
            dcMap.put("field_coya9__c", item["field_yLwx5__c"]); //未税金额
            dcMap.put("field_t1I84__c", item["field_641tn__c"]); //税金
            //dcMap.put("field_a04zT__c", item["field_co3G5__c"]); //含税金额
            log.info("对账明细TPM" + json.toJson(dcMap))
            dcDetailList.add(dcMap);
        }
        log.info("dcDetailList : " + dcDetailList)
    }
    paymentDetailMap.put("object_X8g1q__c", dcDetailList);// 对账明细-TPM费用

    log.info("货款对账单主表参数:" + json.toJson(paymentObjectMap));
    log.info("货款对账单明细表参数:" + json.toJson(paymentDetailMap))

    def(Boolean error8, data8, String errorMessage8) = Fx.object.create("object_A0no9__c", paymentObjectMap, paymentDetailMap, false);
    if( error8 ){
      log.info("新建货款对账单返回信息：" + errorMessage8)
    }else{
      log.info("新建货款对账单成功")
    }


// 函数示例：计划任务批量更新当前对象数

// 业务说明：查询所有商机数据，判断负责人部门和归属部门是否一致，如不一致更新归属部门
// 注意：计划任务在自定义函数编写时运行可以获取到**context**关键字，但是在实际执行时是获取不到的

// 如要移植到其他对象上，仅需要修改objName的值，不需要修改其他内容
// List ids = context.objectIds as List //获取所有符合条件的数据id，新建计划任务会有筛选条件
List ids = [context.data._id as String] //**测试用，别动
String objName = 'AccountObj' //***对象名，如果需要再其他对象执行需要更换api name***
if (ids) {
    //对账日期
    Date nowDate = Date.now()
    log.info('当前时间：' + nowDate)
    Date firstDate = nowDate.withDay(1)
    log.info('当前月第一天：' + firstDate)
    Date endDate = ((nowDate.withDay(1)) - 1.days)
    log.info('上月最后一天：' + endDate)
    Date startDate = endDate.withDay(1)
    log.info('上月第一天：' + startDate)
    Date monthDate = (nowDate.withMonth(nowDate.month + 1).withDay(1)) - 1.days
    log.info('本月最后一天：' + monthDate)
    Date lastMonthDay =  Date.now() - 1.months
    String yearStr = lastMonthDay.year as String
    String monthStr
    if (lastMonthDay.month < 10) {
        monthStr = '0' + lastMonthDay.month as String
        } else {
        monthStr = lastMonthDay.month as String
    }
    String lastMonth = yearStr + monthStr
    log.info('上月日期：' + lastMonth)

    // 经销商货款对账期初记录
    def customerIdAndInitialRecord = [:]
    Range range = Ranges.of(1,100)
    String initialRecordOfLoanId
    range.find{
        it ->
         def initialRecordOfLoanClosure = { List dataList ->
            dataList.each { item ->
                    def beginPrice = item['field_gEkqi__c'] as BigDecimal //货款期初金额
                    def dcBeginPrice = item['field_5x39x__c'] as BigDecimal //DC期初金额
                    def map = [:]
                    map.put("beginPrice", beginPrice)
                    map.put("dcBeginPrice", dcBeginPrice)
                    customerIdAndInitialRecord.put(item['field_414an__c'] as String, map)
            }
        } 
        def(Boolean recordError, PageQueryData recordPageQueryData, String recordErrorMessage) = Fx.object.find("object_xW3cI__c", [
          ["field_414an__c": Operator.IN(ids)],
          ["field_rjrhw__c": lastMonth]
        ], initialRecordOfLoanClosure, initialRecordOfLoanId)
        if (recordError) {
            log.info('find object_xW3cI__c error : ' + recordErrorMessage)
            return true
          } else {
            log.info('object_xW3cI__c query page : ' + recordPageQueryData)
            if (recordPageQueryData["notFinished"] && recordPageQueryData["lastId"]) {
                initialRecordOfLoanId = recordPageQueryData["lastId"] as String
                return false
            } else {
                return true
            }
        }
    }

    log.info('------------------开始汇总市场费对账单---------------------')


    def computeIfAbsent = {
        map, key, value ->
        def list = (map as Map)[key] as List
        if (list == null) {
            (map as Map).put(key, [value])
        } else {
            list.add(value)
            (map as Map).put(key, list)
        }
    }
     //1.对账明细-收款
    def customerIdAndCollectionDetail = [:] 
  
    String collectionDetailId
    range.find{
        it ->
          def collectionDetailClosure = { List dataList ->
            dataList.each { item ->
                Map collectionMap = [: ];
                collectionMap.put("field_1tFyf__c", item["_id"]);
                collectionMap.put("field_Y761u__c", item["field_4AQPM__c"]); //金额
                collectionMap.put("field_Yf1fd__c", item["field_7XX4L__c"]); //收款日期
                computeIfAbsent(customerIdAndCollectionDetail, item['field_m1suI__c'] as String, collectionMap)
            }
        }

        def(Boolean error, PageQueryData data, String errorMessage) = Fx.object.find("object_F77qS__c", [
        ["field_m1suI__c": Operator.IN(ids)],
        ["field_7XX4L__c": Operator.GTE(startDate)],
        ["field_7XX4L__c": Operator.LTE(endDate)]
        ], collectionDetailClosure, collectionDetailId);
        if (error) {
            log.info('find object_F77qS__c error : ' + errorMessage)
            return true
          } else {
            log.info('object_F77qS__c query page : ' + data)
            if (data["notFinished"] && data["lastId"]) {
                collectionDetailId = data["lastId"] as String
                return false
            } else {
                return true
            }
        }
    }


    //2.对账明细-订单
    log.info("开始日期：" + startDate)
    log.info("结束日期：" + endDate)
    def customerIdAndSalesOrder = [:]
    def customerIdAndPrice = [:]
    String salesOrderId
    range.find {
      def computeIfAbsentAdd = {
          customId, price ->
          def priceBefore = (customerIdAndPrice as Map)[customId] as BigDecimal
          if(priceBefore == null) {
              (customerIdAndPrice as Map).put(customId, price as BigDecimal)
          } else {
              (customerIdAndPrice as Map).put(customId,  priceBefore + (price as BigDecimal))
          }
      }
    
      def salesOrderClosure = { List dataList ->
          dataList.each { item ->
              Map orderMap = [: ];
              def customerId = item["account_id"] as String
              orderMap.put("field_wcni5__c", item["_id"]); //订单编号
              orderMap.put("field_KQkk6__c", item["field_31JI7__c"]); //订单金额（含税）
              orderMap.put("field_xgJ0f__c", item["field_yBa8t__c"]); //货款金额（含税）
              BigDecimal price = item["field_G9d4s__c"] as BigDecimal;
              if(null == price){
                price = 0;
              }
              orderMap.put("field_9CTIO__c", price * 1.13); //DC金额（含税）
              orderMap.put("field_Hl1Fs__c", item["field_41iKc__c"]); //已结算货款金额（含税）
              BigDecimal price1 = item["field_D7242__c"] as BigDecimal;
              if(null == price1){
                price1 = 0;
              }
              orderMap.put("field_lN5k7__c",  1.13 * price1); //已结算DC金额 （含税）

              computeIfAbsent(customerIdAndSalesOrder, item['account_id'] as String, orderMap)
              computeIfAbsentAdd(item['account_id'] as String, price * 1.13)
          }
      }

      def(Boolean error2, PageQueryData data2, String errorMessage2) = Fx.object.find("SalesOrderObj", [
        ["account_id": Operator.IN(ids)],
        ["field_g2wo4__c": Operator.GTE(startDate)],
        ["field_g2wo4__c": Operator.LTE(endDate)],
        ["field_q1Wq0__c": Operator.IN(["7Ui1d8JXP","u31rNLyTS"])]
      ], salesOrderClosure, salesOrderId)
      if (error2) {
          log.info('find SalesOrderObj error : ' + errorMessage2)
          return true
        } else {
          log.info('SalesOrderObj query page : ' + data2)
          if (data2["notFinished"] && data2["lastId"]) {
              salesOrderId = data2["lastId"] as String
              return false
          } else {
              return true
          }
      }
    }
    log.info("customerIdAndPrice : " + customerIdAndPrice)
    // 经销商货款对账单 ——主对象
    def batchLoanStatementObjMap = [:]
    log.info("ids : " + ids)
    ids.each { id ->
            id = id as String
            BigDecimal beginPrice = 0
             BigDecimal dcBeginPrice = 0
            def priceMap = customerIdAndInitialRecord.id as Map
            if(priceMap) {
               beginPrice = priceMap.beginPrice as BigDecimal
               dcBeginPrice = priceMap.dcBeginPrice as BigDecimal
            }
            Map paymentObjectMap = [:]
            paymentObjectMap.put('field_nCp20__c', id) //客户
            paymentObjectMap.put('field_E2qIP__c', nowDate.toTimestamp()) //对账日期
            paymentObjectMap.put('field_jSRpk__c', lastMonth)
            paymentObjectMap.put("field_CYKu4__c", dcBeginPrice ? dcBeginPrice : 0)//DC期初金额
            paymentObjectMap.put("field_1kJVe__c", beginPrice ? beginPrice : 0)//货款期初金额


            // paymentObjectMap.put("field_chcNR__c", yjsAll);//已结算货款
            // paymentObjectMap.put("field_l59tl__c", dcAll);//已结算DC金额
            def dcAll = customerIdAndPrice[id] as BigDecimal
            dcAll = dcAll ? dcAll : 0
            paymentObjectMap.put("field_CmHKz__c", beginPrice - dcAll);//货款期末金额=货款期初金额-已结算货款
            paymentObjectMap.put("field_poaCS__c", dcBeginPrice - dcAll);//DC期末金额=DC期初金额-已结算DC金额
            batchLoanStatementObjMap.put(id, paymentObjectMap)
    }
    //3.对账明细-发票
    def customerIdAndInvoiceDetail = [:]
    String invoiceDetailId
    range.find {
      def invoiceDetailClourse = { List dataList -> 
        dataList.each { item ->
            Map invoiceMap = [: ];
            invoiceMap.put("field_WxA4h__c", item["_id"]);
            invoiceMap.put("field_f3efb__c", item["field_ekzFj__c"]); //发票日期
            invoiceMap.put("field_Cys61__c", item["field_uBy7A__c"]); //发票号码
            invoiceMap.put("field_4W4Gn__c", item["field_t01P1__c"]); //折扣前金额（含税）
            invoiceMap.put("field_g3yUp__c", item["field_NQzro__c"]); //折扣金额（含税）
            invoiceMap.put("field_83cuc__c", item["field_41Hbs__c"]); //发票金额
            computeIfAbsent(customerIdAndInvoiceDetail, item['field_G24NL__c'] as String, invoiceMap)
        }
      }
      def(Boolean error4, PageQueryData data4, String errorMessage4) = Fx.object.find("object_ahIP5__c", [
        ["field_G24NL__c": Operator.IN(ids)],
        ["field_ekzFj__c": Operator.GTE(startDate)],
        ["field_ekzFj__c": Operator.LTE(endDate)]
      ], invoiceDetailClourse, invoiceDetailId);
      if (error4) {
          log.info('find object_ahIP5__c error : ' + errorMessage4)
          return true
        } else {
          log.info('object_ahIP5__c query page : ' + data4)
          if (data4["notFinished"] && data4["lastId"]) {
              invoiceDetailId = data4["lastId"] as String
              return false
          } else {
              return true
          }
      }
    }
   

    //4.对账明细-TPM折扣
    def customerIdAndTPMDiscount = [:]
    String tpmDiscountId
    range.find {
        def dcDetailClourse = { List dataList ->
          dataList.each {item->
              Map dcMap = [: ];
              dcMap.put("field_4S277__c", item["_id"]); //TPM单号
              //dcMap.put("field_lO88w__c", item["field_dn4iR__c"]); //TPM单号
              dcMap.put("field_coya9__c", item["field_yLwx5__c"]); //未税金额
              dcMap.put("field_t1I84__c", item["field_641tn__c"]); //税金
              //dcMap.put("field_a04zT__c", item["field_co3G5__c"]); //含税金额
              log.info("对账明细TPM" + json.toJson(dcMap))
              computeIfAbsent(customerIdAndTPMDiscount, item['field_G73cQ__c'] as String, dcMap)
          }
        }
      def(Boolean error6, PageQueryData data6, String errorMessage6) = Fx.object.find("object_r04cg__c", [
        ["field_G73cQ__c": Operator.IN(ids)],
        ["field_nixhr__c": Operator.GTE(startDate)],
        ["field_nixhr__c": Operator.LTE(endDate)],
        ["field_8t90k__c":Operator.EQ(true)]
      ], dcDetailClourse, tpmDiscountId);
      if (data6) {
          log.info('find object_r04cg__c error : ' + errorMessage6)
          return true
        } else {
          log.info('object_r04cg__c query page : ' + data6)
          if (data6["notFinished"] && data6["lastId"]) {
              tpmDiscountId = data6["lastId"] as String
              return false
          } else {
              return true
          }
      }
    }
  
    log.info("batchLoanStatementObjMap : " + batchLoanStatementObjMap)
    batchLoanStatementObjMap.each { key, value ->
        def paymentDetailMap = [: ]
        def collectionDetails = customerIdAndCollectionDetail[key] as List
        paymentDetailMap.put("object_0w1NM__c", collectionDetails ? collectionDetails : []);// 对账明细-到款
        def salesOrders = customerIdAndSalesOrder[key] as List
        paymentDetailMap.put("object_152d9__c", salesOrders ? salesOrders : [:]);// 对账明细-订单
        def invoiceDetails = customerIdAndInvoiceDetail[key] as List
        paymentDetailMap.put("object_wJnu9__c", invoiceDetails ? invoiceDetails :[]);// 对账明细-发票
        def tpmDiscounts = customerIdAndTPMDiscount[key] as List
        paymentDetailMap.put("object_X8g1q__c", tpmDiscounts ? tpmDiscounts : []);// 对账明细-TPM费用
        log.info("object_A0no9__c :" + Fx.json.toJson(value))
        log.info("paymentDetailMap : " +  Fx.json.toJson(paymentDetailMap))
        def(Boolean error8, data8, String errorMessage8) = Fx.object.create("object_A0no9__c", value as Map, paymentDetailMap, false);
        if( error8 ){
          log.info("新建货款对账单返回信息：" + errorMessage8)
        }else{
          log.info("新建货款对账单成功")
        }
    }
    def groupList = Fx.utils.listPartition((batchLoanStatementObjMap as Map).keys(), 500) as List
    log.info(groupList)
    def customerIdAndLoanStatementId = [:]
    groupList.each {it ->
        def batchLoanStatementObjGroupMap = batchLoanStatementObjMap
        def innerCustomerIdAndCollectionDetail = customerIdAndCollectionDetail
        def innerCustomerIdAndSalesOrder = customerIdAndSalesOrder
        def innerCustomerIdAndInvoiceDetail = customerIdAndInvoiceDetail
        def innerCustomerIdAndTPMDiscount = customerIdAndTPMDiscount
        def loanStatementObjectList = []
        it.each {item ->
            if (innerCustomerIdAndCollectionDetail[item] || innerCustomerIdAndSalesOrder[item] || innerCustomerIdAndInvoiceDetail[item] || innerCustomerIdAndTPMDiscount[item]) {
                loanStatementObjectList.add(batchLoanStatementObjGroupMap[item] as Map)
            }
        }
        if (loanStatementObjectList) {
            def (Boolean err,List data, String errMsg) = Fx.object.batchCreate("object_A0no9__c", loanStatementObjectList)
            if(err){
                log.info("贷款对账单批量新建异常：" + errMsg)
            } else {
                data.each { item ->
                    customerIdAndLoanStatementId.put(item["field_nCp20__c"] as String,item["_id"] as String)
                }
                log.info("贷款对账单批量条数：" + data.size())
            }
        }
    }

    customerIdAndLoanStatementId.each { key, value ->
        def collectionDetailList = customerIdAndCollectionDetail[key] as List
        collectionDetailList.each { item ->
            (item as Map).put("field_y5n3Q__c", value as String)
        }
        def salesOrderList = customerIdAndSalesOrder[key] as List
        salesOrderList.each { item ->
            (item as Map).put("field_55tQl__c", value as String)
        }
        def invoiceDetailList = customerIdAndInvoiceDetail[key] as List
        invoiceDetailList.each { item ->
            (item as Map).put("field_fcWX9__c", value as String)
        }
        def TPMDiscountList = customerIdAndTPMDiscount[key] as List
        TPMDiscountList.each { item ->
            (item as Map).put("field_Kfn3i__c", value as String)
        }                                       
    }
    def customerIdAndCollectionDetailGroup = Fx.utils.listPartition((customerIdAndCollectionDetail as Map).keys(), 500) as List
    log.info(customerIdAndCollectionDetailGroup)
    customerIdAndCollectionDetailGroup.each {it ->
        def outerMap = customerIdAndCollectionDetail
        def collectionDetailList = []
        (it as List).each {item ->
            def collectionDetailMap = outerMap[item] as Map
            if (collectionDetailMap) {
                collectionDetailList.add(collectionDetailMap)
            }
        }
        if(collectionDetailList) {
            def (Boolean err1,List data1, String errMsg1) = Fx.object.batchCreate("object_0w1NM__c", collectionDetailList)
            if(err1){
                log.info("对账明细-到款批量新建异常：" + errMsg1)
            } else {
                log.info("对账明细-到款批量条数：" + data1.size())
            }
        }
    }

    def salesOrderGroupList = Fx.utils.listPartition((customerIdAndSalesOrder as Map).keys(), 500) as List
    log.info(salesOrderGroupList)
    salesOrderGroupList.each {it ->
        def outerMap = customerIdAndSalesOrder
        def salesOrderBatchList = []
        (it as List).each {item ->
            def map = outerMap[item] as Map
            if (map) {
                salesOrderBatchList.add(map)
            }
        }
        if(salesOrderBatchList) {
            def (Boolean err2,List data2, String errMsg2) = Fx.object.batchCreate("object_152d9__c", salesOrderBatchList)
            if(err2){
                log.info("对账明细-订单批量新建异常：" + errMsg2)
            } else {
                log.info("对账明细-订单批量条数：" + data2.size())
            }
        }
    }
    def invoiceDetailGroupList = Fx.utils.listPartition((customerIdAndInvoiceDetail as Map).keys(), 500) as List
    log.info(invoiceDetailGroupList)
    invoiceDetailGroupList.each {it ->
        def outerMap = customerIdAndSalesOrder
        def invoiceDetailBatchList = []
        (it as List).each {item ->
            def map = outerMap[item] as Map
            if (map) {
                invoiceDetailBatchList.add(map)
            }
        }
        if(invoiceDetailBatchList) {
            def (Boolean err3,List data3, String errMsg3) = Fx.object.batchCreate("object_wJnu9__c", invoiceDetailBatchList)
            if(err3){
                log.info("对账明细-发票批量新建异常：" + errMsg3)
            } else {
                log.info("对账明细-发票批量条数：" + data3.size())
            }
        }
    }

    def TPMDiscountGroupList = Fx.utils.listPartition((customerIdAndTPMDiscount as Map).keys(), 500) as List
    log.info(TPMDiscountGroupList)
    TPMDiscountGroupList.each {it ->
        def outerMap = customerIdAndTPMDiscount
        def TPMDiscountBatchList = []
        (it as List).each {item ->
            def map = outerMap[item] as Map
            if (map) {
                TPMDiscountBatchList.add(map)
            }
        }
        if(TPMDiscountBatchList) {
            def (Boolean err4,List data4, String errMsg4) = Fx.object.batchCreate("object_X8g1q__c", TPMDiscountBatchList)
            if(err4){
                log.info("对账明细-TPM费用批量新建异常：" + errMsg4)
            } else {
                log.info("对账明细-TPM费用批量条数：" + data4.size())
            }
        }
    }


}
log.info("执行完成")
