## 1、打印请求

````json
{"validatePreAction":false,"templateId":"60f5291e8eca3d00015a4374","instanceId":"607cf1533bdcd50001a2d8a0","dataId":"607cf153ae2b6b0001481acc","orientation":"Landscape","skipCheckButtonConditions":false}
````

**打印带token：**

````json
{"validatePreAction":false,"templateId":"60f5291e8eca3d00015a4374","instanceId":"607cf1533bdcd50001a2d8a0","dataId":"607cf153ae2b6b0001481acc","orientation":"Landscape","skipCheckButtonConditions":false,"token":"1df0274f-c057-4f72-bf41-e029b939044d"}
````

打印上送参数：（excel）

````json
{"validatePreAction":false,"templateId":"60f5291e8eca3d00015a4374","instanceId":"607cf1533bdcd50001a2d8a0","dataId":"607cf153ae2b6b0001481acc","orientation":"Landscape","skipCheckButtonConditions":false}
````

批量打印：（pdf或word）

````json
{"templateId":"5efd4386a5083d97d6ae8966","printMode":"save","dataIds":["606c21159b4b1d000108cd8f","607cf1034d8d6f0001580d62","607ceaea4d8d6f000157d2e3"]}
````

批量导出：

````json
{"dataIdList":["60f03fdc07373300011516d2","60efe96d105ed30001133408","60efe966105ed30001133330"],"object_describe_api_name":"object_p0I5m__c","search_template_id":"5d0c806a7cfed91f3e95ba8e","include_describe":false,"search_template_type":"default","ignore_scene_record_type":false,"search_query_info":"{\"limit\":20,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}","pageSizeOption":[20,50,100,200],"no_export_relevant_team":false}
````

## 2、导出大概逻辑

1. 
