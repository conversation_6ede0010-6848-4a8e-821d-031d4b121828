### 一、find函数可以返回关联关系，主从关系的主属性，单选多选值字段的value值------775

```groovy
FindAttribute attribute = FindAttribute.build{
    //是否使用当前用户身份查询数据（默认false）（之前是find的独立参数，整合到FindAttribute里，之前的写法兼容）
    useCurrentIdentity = false
    //是否返回相关数据id对应的value值（默认false）
    returnRelatedValue = false
    //是否从DB查询数据（默认false）
    forceQueryFromDB = true
}
APIResult find(String apiName, List criteria, Map orderBy, int limit, int skip, FindAttribute attribute)
```

通过returnRelatedValue属性进行判断

1. 填充lookup字段的主属性__r  

   com.facishare.paas.appframework.metadata.MetaDataMiscService#fillObjectDataWithRefObject

2. 填充单选多选字段补充__r  如：field_N89H7__c__r=苹果    field_Ef1Sz__c__r=[苹果,橘子]

### 二、update支持主子更新，参数支持ActionAttribute



### 三、如果预置对象的批量接口不支持业务触发，运行时自动转成单个接口（研发优化）

​    优化范围：
​        batchCreate-------无限制
​        batchUpdate-------不支持的预制对象转为单个update接口
​        bulkRemove------无限制

### 四、互联场景字段的自动填充

​    outUserId=100018916, outTenantId=200074255, appId=FSAID_11490d9
​    1、互联场景中判断appId不为空时调用Add接口时补充outUserId和outTenantId属性

### 五、【范围规则、自增编号、UI函数的引用关系优化】

​    1、deleteDescribe接口删除对象时增加解除函数引用的逻辑。
​    2、UI事件未绑定引用关系

### 六、产品分类相关函数

### 七、数据后动作函数  后动作trigger

​    背景：数据变更的后动作目前是放在业务流后动作上的，并不是所有途径的数据变更都能触发工作流，需要一个底层数据的统一的函数后动作来处理。
​    

    设计：
    新增加命名空间《数据后动作》，创建时需要必须要选择以下信息：单选绑定对象、单选触发操作（新建、更新、删除、恢复？？）
    触发器中可以通过context获取数据的old数据（变更前数据）和new数据（变更后数据，删除操作没有后数据，新建操作没有前数据），old和new数据是list结构，update时的context里，old和new list的index相同的话，对应的是一条数据（现阶段，只支持单挑数据的话，建议还是做成list接口，为以后扩展考虑）
    所有途径引发的数据变化都会调用此接口
    引用字段和计算字段的变更不会触发数据后动作函数，统计字段会触发数据后动作函数
    数据后动作函数中更新同一条数据会引发递归调用，递归调用时如果到达系统预设的调用链深度时，报错。
    函数保存时把数据后动作的递归调用作为检查项（判断是否更新同一条数据有困难，如果在函数中对同一个对象做了同样类型的DML操作，保存时警告）

八、UI按钮支持UI事件
    1、UIAction接口，增加

```json
{
    "hasReturnValue": true,
    "returnValue": {
        "data": {
            "object_10be5__c": {},
            "object_bemhw__c": {
                "a": [],
                "u": {}
            },
            "object_2YeVj__c": {
                "a": [],
                "u": {}
            }
        },
        "fieldAttribute": {
            "field_M2kdz__c": {
                "readOnly": true
            }
        },
        "type": "event",
        "action": "WebAction",
        "url": null
    },
    "returnType": "UIAction"
}
```

    2、UI事件TriggerEvent接口，

