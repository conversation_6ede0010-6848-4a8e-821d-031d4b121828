# 配置合并工具

这个工具用于合并不同环境的配置数据，将指定环境的数据作为基础数据，将其他环境中不同的配置项合并到基础数据中，生成新的配置文件。

## 功能特点

- 获取指定环境的配置数据作为基础数据
- 获取其他环境的配置数据
- 比较并合并不同环境中value不同的配置项
- 合并其他环境中有而基础环境中没有的配置项
- 保留配置项的注释信息
- 生成新的配置文件，命名为 `${文件名}-${环境}`

## 合并规则

1. 以指定的基础环境配置作为基础数据
2. 对于其他环境中与基础环境相同key但value不同的配置项，使用其他环境的value
3. 对于其他环境中有而基础环境中没有的配置项，添加到合并后的配置中
4. 保留所有配置项的注释信息

## 使用方法

### 安装依赖

```bash
pip install requests
```

### 运行脚本

```bash
python merge_config.py --base <基础环境> --profiles <其他环境,用逗号分隔> --name <配置文件名> --token <API令牌>
```

### 参数说明

- `--base`: 基础环境名称，如 fstest
- `--profiles`: 其他环境名称，用逗号分隔，如 dev,test,prod
- `--name`: 配置文件名称，如 gray-rel-webPage
- `--token`: API访问令牌

### 示例

```bash
python merge_config.py --base fstest --profiles fktest,prod --name gray-rel-webPage --token 2E983C21F7E2212E4181E5E2A28EC54DA83C1AE084D901B0B5131DC1FF86F70C
```

这将生成 `gray-rel-webPage-fktest` 和 `gray-rel-webPage-prod` 两个文件，分别包含 fstest 环境的基础配置，以及 fktest 和 prod 环境中与 fstest 环境不同的配置项。

## 输出示例

假设基础环境 fstest 的配置为：

```
# 这是注释1
key1=value1
# 这是注释2
key2=value2
key3=value3
```

其他环境 fktest 的配置为：

```
# 这是注释1
key1=value1
# 这是注释2修改版
key2=newvalue2
key3=value3
key4=value4
```

合并后生成的 `gray-rel-webPage-fktest` 文件内容为：

```
# 这是注释1
key1=value1
# 这是注释2修改版
key2=newvalue2
key3=value3
key4=value4
```

## 注意事项

- 脚本会保留配置项的注释信息
- 只有当其他环境中的配置项与基础环境不同时，才会生成新的合并配置文件
- 如果其他环境中没有与基础环境不同的配置项，则不会生成对应的合并配置文件
- 脚本会处理其他环境中有而基础环境中没有的配置项 