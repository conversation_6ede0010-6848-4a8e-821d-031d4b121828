树形组件自关联字段变更消息（原）
```json
{
    "tenant_id": "74255",
    "describe_api_name": "NewOpportunityObj",
	"dataIds":["61e4e05dcc30650001a12872","61e4e05dcc30650001a12873"]
// 下面去除
    "body": [
        {
            "dataId": "61e4e05dcc30650001a12872",
            "before": {
                "parent_directory": "",
                "tree_view": ""
            },
            "after": {
                "tree_view": "5d244976d124120001fe083e.5d2449a2d124120001fe08de.5d88a9d6b13fa90001d55ea4",
                "parent_directory": "5d2449a2d124120001fe08de"
            }
        }
    ]
}
```

1.  filter如何拼
1.  controller里面的逻辑  （功能权限，不搞单独的功能权限，默认走列表权限，不考虑数据权限）
1.  下发指定层级（后期）
1.  树支持搜索节点，MVP 搜本地，中长期从服务端搜索，服务端返回完整 Path，加到树里（后期）
1.  主线程处理当前数据，要考虑并发（防底层处理自己发消息）
1.  列表页树形组件，是否展示叶子节点
1.  导入处理细化



树形组件二次评审问题

1. 查找关联标志
2. 可选择的视图不能放到layout中，用户配置的可放到layout中
3. 点击树查询数据，将id作为筛选条件，后端转换为tree_path ~ '*.id.*' 查询所有层级数据并返回。  参考location
4. 列表页新建数据时，树结构保持新建前样子。
5. 搜索树中数据交互。

