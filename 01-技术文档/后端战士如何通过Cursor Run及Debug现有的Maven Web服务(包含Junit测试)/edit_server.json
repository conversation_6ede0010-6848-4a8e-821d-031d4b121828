{"args.override.boolean": "true", "args.program.override.string": "start", "args.shutdown.program.override.string": "stop", "args.shutdown.vm.override.string": "-Djava.util.logging.config.file=\"/Users/<USER>/software/tomcat-8.5.58/conf/logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Dcatalina.base=\"/Users/<USER>/software/tomcat-8.5.58\" -Dcatalina.home=\"/Users/<USER>/software/tomcat-8.5.58\" -Djava.io.tmpdir=\"/Users/<USER>/software/tomcat-8.5.58/temp\"", "args.vm.override.string": "-Dfile.encoding=\"UTF-8\" -Dprocess.profile=\"fstest\" -Dspring.profiles.active=\"fstest\" -Dlogback.configurationFile=\"/Users/<USER>/workspace/logback.xml\" -Djava.util.logging.config.file=\"/Users/<USER>/software/tomcat-8.5.58/conf/logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Dcatalina.base=\"/Users/<USER>/software/tomcat-8.5.58\" -Dcatalina.home=\"/Users/<USER>/software/tomcat-8.5.58\" -Djava.io.tmpdir=\"/Users/<USER>/software/tomcat-8.5.58/temp\" --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED", "id": "tomcat-8558", "id-set": "true", "mapProperty.launch.env": {}, "org.jboss.tools.rsp.server.typeId": "org.jboss.ide.eclipse.as.server.tomcat.85", "server.autopublish.enabled": "true", "server.base.dir": "/Users/<USER>/software/tomcat-8.5.58", "server.classpath.additions": "", "server.deploy.dir": "${server.base.dir}/webapps/", "server.home.dir": "/Users/<USER>/software/tomcat-8.5.58", "server.http.host": "localhost", "server.http.port": "8080", "vm.install.path": "", "deployables": {"/Users/<USER>/workspace/fxiaoke/fs-paas-workflow/fs-paas-workflow-provider/target/paas-workflow": {"label": "/Users/<USER>/workspace/fxiaoke/fs-paas-workflow/fs-paas-workflow-provider/target/paas-workflow", "path": "/Users/<USER>/workspace/fxiaoke/fs-paas-workflow/fs-paas-workflow-provider/target/paas-workflow", "options": {"option": {"deployment.output.name": "ROOT"}}}}}