{
    "tabnine.experimentalAutoImports": true,
    "cmake.useCMakePresets": "always",
    "cmake.options.statusBarVisibility": "compact",
    "cmake.options.advanced": {
        "build": {
            "statusBarVisibility": "inherit",
            "inheritDefault": "visible"
        },
        "launch": {
            "statusBarVisibility": "inherit",
            "inheritDefault": "visible"
        },
        "debug": {
            "statusBarVisibility": "inherit",
            "inheritDefault": "visible"
        }
    },
    "[java]": {
        "editor.suggest.snippetsPreventQuickSuggestions": false
    },
    "maven.settingsFile": "/Users/<USER>/software/maven-3.2.5/conf/settings.xml",
    "mssql.objectExplorer.groupBySchema": false,
    "explorer.confirmPasteNative": false,
    "window.systemColorTheme": "dark",
    "database-client.autoSync": true,
    "security.workspace.trust.untrustedFiles": "open",
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "jdk.jdkhome": "/Library/Java/JavaVirtualMachines/jdk1.8.0_261.jdk/Contents/Home",
    "settingsSync.ignoredSettings": [
        "-jdk.jdkhome",
        "-jdk.project.jdkhome",
        "-jdk.serverVmOptions"
    ],
    "jdk.project.jdkhome": "/Library/Java/JavaVirtualMachines/jdk1.8.0_261.jdk/Contents/Home",
    "java.configuration.maven.userSettings": "/Users/<USER>/software/maven-3.2.5/conf/settings.xml",
    "java.configuration.maven.globalSettings": "/Users/<USER>/software/maven-3.2.5/conf/settings.xml",
    "redhat.telemetry.enabled": true,
    "editor.linkedEditing": true,
    "editor.minimap.enabled": false,
    "editor.rulers": [
        {
            "column": 80,
            "color": "#00FF0010"
        },
        {
            "column": 100,
            "color": "#BDB76B15"
        },
        {
            "column": 120,
            "color": "#FA807219"
        }
    ],
    "editor.unicodeHighlight.includeComments": true,
    "workbench.colorCustomizations": {
        "[Default Dark Modern]": {
            "tab.activeBorderTop": "#00FF00",
            "tab.unfocusedActiveBorderTop": "#00FF0088",
            "textCodeBlock.background": "#00000055"
        },
        "editor.wordHighlightStrongBorder": "#FF6347",
        "editor.wordHighlightBorder": "#FFD700",
        "editor.selectionHighlightBorder": "#A9A9A9"
    },
    "workbench.editor.revealIfOpen": true,
    "workbench.tree.indent": 20,
    "emmet.variables": {
        "lang": "zh"
    },
    "cSpell.diagnosticLevel": "Hint",
    "trailing-spaces.backgroundColor": "rgba(255,0,0,0.1)",
    "trailing-spaces.includeEmptyLines": false,
    "terminal.integrated.tabs.hideCondition": "never",
    "terminal.integrated.enablePersistentSessions": false,
    "java.compile.nullAnalysis.mode": "automatic",
    "java.configuration.detectJdksAtStart": false,
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.debug.settings.hotCodeReplace": "auto",
    "java.dependency.packagePresentation": "hierarchical",
    "java.maxConcurrentBuilds": 16,
    "java.sources.organizeImports.staticStarThreshold": 1,
    "java.server.launchMode": "Standard",
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-1.8",
            "path": "/Library/Java/JavaVirtualMachines/jdk1.8.0_261.jdk/Contents/Home",
            "default": true
        },
        {
            "name": "JavaSE-11",
            "path": "/usr/local/Cellar/openjdk@11/11.0.15/libexec/openjdk.jdk/Contents/Home",
        },
        {
            "name": "JavaSE-16",
            "path": "/Library/Java/JavaVirtualMachines/jdk-16.0.2.jdk/Contents/Home"
        },
        {
            "name": "JavaSE-17",
            "path": "/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"
        },
        {
            "name": "JavaSE-21",
            "path": "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home"
        },
        {
            "name": "JavaSE-23",
            "path": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk/java/latest"
        }
    ],
    "terminal.integrated.profiles.osx": {
        "zsh": {
            "path": "zsh",
            "env": {
                "JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-11.0.8.jdk/Contents/Home",
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk"
            }
        },
        "fish": {
            "path": "fish",
            "env": {
                "JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-11.0.8.jdk/Contents/Home",
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk"
            }
        },
        "JavaSE-1.8 LTS": {
            "overrideName": true,
            "env": {
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk",
                "JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk1.8.0_261.jdk/Contents/Home"
            },
            "path": "zsh"
        },
        "JavaSE-11 LTS": {
            "overrideName": true,
            "env": {
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk",
                "JAVA_HOME": "/usr/local/Cellar/openjdk@11/11.0.15/libexec/openjdk.jdk/Contents/Home"
            },
            "path": "zsh"
        },
        "JavaSE-16": {
            "overrideName": true,
            "env": {
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk",
                "JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-16.0.2.jdk/Contents/Home"
            },
            "path": "zsh"
        },
        "JavaSE-17 LTS": {
            "overrideName": true,
            "env": {
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk",
                "JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"
            },
            "path": "zsh"
        },
        "JavaSE-21 LTS": {
            "overrideName": true,
            "env": {
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk",
                "JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home"
            },
            "path": "zsh"
        },
        "JavaSE-23": {
            "overrideName": true,
            "env": {
                "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk",
                "JAVA_HOME": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk/java/latest"
            },
            "path": "zsh"
        }
    },
    "terminal.integrated.defaultProfile.osx": "fish",
    "maven.terminal.customEnv": [
        {
            "environmentVariable": "JAVA_HOME",
            "value": "/Library/Java/JavaVirtualMachines/jdk1.8.0_261.jdk/Contents/Home"
        },
        {
            "environmentVariable": "ZDOTDIR",
            "value": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk"
        }
    ],
    "java.import.gradle.java.home": "/Library/Java/JavaVirtualMachines/jdk1.8.0_261.jdk/Contents/Home",
    "rsp-ui.rsp.java.home": "/Library/Java/JavaVirtualMachines/jdk-11.0.8.jdk/Contents/Home",
    "plantuml.java": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk/java/latest/bin/java",
    "maven.executable.path": "/Users/<USER>/software/maven-3.2.5/bin/mvn",
    "java.import.gradle.home": "/Users/<USER>/software/gradle-6.7",
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "http.noProxy": [
    ],
    "rsp-ui.enableStartServerOnActivation": [
        {
            "id": "redhat.vscode-community-server-connector",
            "name": "Community Server Connector",
            "startOnActivation": true
        }
    ],
    "java.editor.reloadChangedSources": "auto",
    "explorer.confirmDelete": false,
    "gitlab.customQueries": [

        {
            "name": "Issues assigned to me",
            "type": "issues",
            "scope": "assigned_to_me",
            "state": "opened",
            "noItemText": "No issues assigned to you."
        },
        {
            "name": "Issues created by me",
            "type": "issues",
            "scope": "created_by_me",
            "state": "opened",
            "noItemText": "No issues created by you."
        },
        {
            "name": "Merge requests assigned to me",
            "type": "merge_requests",
            "scope": "assigned_to_me",
            "state": "opened",
            "noItemText": "No merge requests assigned to you."
        },
        {
            "name": "Merge requests I'm reviewing",
            "type": "merge_requests",
            "reviewer": "<current_user>",
            "state": "opened",
            "noItemText": "No merge requests for you to review."
        },
        {
            "name": "Merge requests created by me",
            "type": "merge_requests",
            "scope": "created_by_me",
            "state": "opened",
            "noItemText": "No merge requests created by you."
        },
        {
            "name": "All project merge requests",
            "type": "merge_requests",
            "scope": "all",
            "state": "opened",
            "noItemText": "The project has no merge requests"
        }
    ],
    "cSpell.blockCheckingWhenTextChunkSizeGreaterThan": 10000,
    "python.createEnvironment.trigger": "off",
    "cursor.aipreview.enabled": true,
    "terminal.external.osxExec": "iTerm.app",
    "vs-kubernetes": {
        "vscode-kubernetes.helm-path-mac": "/Users/<USER>/.vs-kubernetes/tools/helm/darwin-amd64/helm",
        "vscode-kubernetes.kubectl-path-mac": "/Users/<USER>/.vs-kubernetes/tools/kubectl/kubectl",
        "vscode-kubernetes.minikube-path-mac": "/Users/<USER>/.vs-kubernetes/tools/minikube/darwin-amd64/minikube"
    },
    "workbench.colorTheme": "Visual Studio Light",
    "commentTranslate.maxTranslationLength": 100000,
    "vsicons.dontShowNewVersionMessage": true,
    "cloudfoundry-manifest.ls.java.home": "",
    "java.maven.updateSnapshots": true,
    "code-runner.executorMap": {
    
        "javascript": "node",
        "java": "cd $dir && javac $fileName && java $fileNameWithoutExt",
        "c": "cd $dir && gcc $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        "zig": "zig run",
        "cpp": "cd $dir && g++ $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        "objective-c": "cd $dir && gcc -framework Cocoa $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        "php": "php",
        "python": "python -u",
        "perl": "perl",
        "perl6": "perl6",
        "ruby": "ruby",
        "go": "go run",
        "lua": "lua",
        "groovy": "cd $dir && groovy $fileName",
        "powershell": "powershell -ExecutionPolicy ByPass -File",
        "bat": "cmd /c",
        "shellscript": "bash",
        "fsharp": "fsi",
        "csharp": "scriptcs",
        "vbscript": "cscript //Nologo",
        "typescript": "ts-node",
        "coffeescript": "coffee",
        "scala": "scala",
        "swift": "swift",
        "julia": "julia",
        "crystal": "crystal",
        "ocaml": "ocaml",
        "r": "Rscript",
        "applescript": "osascript",
        "clojure": "lein exec",
        "haxe": "haxe --cwd $dirWithoutTrailingSlash --run $fileNameWithoutExt",
        "rust": "cd $dir && rustc $fileName && $dir$fileNameWithoutExt",
        "racket": "racket",
        "scheme": "csi -script",
        "ahk": "autohotkey",
        "autoit": "autoit3",
        "dart": "dart",
        "pascal": "cd $dir && fpc $fileName && $dir$fileNameWithoutExt",
        "d": "cd $dir && dmd $fileName && $dir$fileNameWithoutExt",
        "haskell": "runghc",
        "nim": "nim compile --verbosity:0 --hints:off --run",
        "lisp": "sbcl --script",
        "kit": "kitc --run",
        "v": "v run",
        "sass": "sass --style expanded",
        "scss": "scss --style expanded",
        "less": "cd $dir && lessc $fileName $fileNameWithoutExt.css",
        "FortranFreeForm": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        "fortran-modern": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        "fortran_fixed-form": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        "fortran": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        "sml": "cd $dir && sml $fileName",
        "mojo": "mojo run",
        "erlang": "escript",
        "spwn": "spwn build",
        "pkl": "cd $dir && pkl eval -f yaml $fileName -o $fileNameWithoutExt.yaml",
        "gleam": "gleam run -m $fileNameWithoutExt"
    },
    "code-runner.clearPreviousOutput": true,
    "code-runner.temporaryFileName": "codeRunnerTmpFile",
    "gradle.allowParallelRun": true,
    "gradle.debug": true,
    "java.import.gradle.enabled": false,
    "java.import.gradle.wrapper.enabled": false
}