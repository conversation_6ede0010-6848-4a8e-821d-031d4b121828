<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xml xmlns:oa="urn:schemas-microsoft-com:office:activation" xmlns:p="urn:schemas-microsoft-com:office:powerpoint" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml"><o:shapelayout v:ext="edit"><o:idmap v:ext="edit" data="1"/></o:shapelayout><v:shapetype id="_x0000_t75" coordsize="21600,21600" o:spt="75" o:preferrelative="t" path="m@4@5l@4@11@9@11@9@5xe"
  filled="f" stroked="f">
  <v:stroke joinstyle="miter"/>
  <v:formulas>
  <v:f eqn="if lineDrawn pixelLineWidth 0"/>
  <v:f eqn="sum @0 1 0"/>
  <v:f eqn="sum 0 0 @1"/>
 <v:f eqn="prod @2 1 2"/>
 <v:f eqn="prod @3 21600 pixelWidth"/>
 <v:f eqn="prod @3 21600 pixelHeight"/>
  <v:f eqn="sum @0 0 1"/>
  <v:f eqn="prod @6 1 2"/>
  <v:f eqn="prod @7 21600 pixelWidth"/>
  <v:f eqn="sum @8 21600 0"/>
 <v:f eqn="prod @7 21600 pixelHeight"/>
<v:f eqn="sum @10 21600 0"/>
  </v:formulas>
  <v:path o:extrusionok="f" gradientshapeok="t" o:connecttype="rect"/>
  <o:lock v:ext="edit" aspectratio="t"/>
</v:shapetype>

<v:shape id="_x0000_s1029" o:spt="75" alt="oleimage" type="#_x0000_t75" style="position:absolute;left:0pt;top:0pt;margin-left:404.9pt;margin-top:842.9pt;height:372.15pt;width:372.4pt;" o:ole="t" fillcolor="#FFFFFF" filled="t" o:preferrelative="t" stroked="t" coordsize="21600,21600"><v:path/><v:fill on="t" color2="#FFFFFF" focussize="0,0"/><v:stroke color="#000000" miterlimit="8" joinstyle="miter"/><v:imagedata o:relid="rId1" o:title="oleimage"/><o:lock v:ext="edit" aspectratio="t"/><x:ClientData ObjectType="Pict"><x:SizeWithCells/><x:Anchor>1,11,3,10,1,383,3,382</x:Anchor></x:ClientData></v:shape></xml>