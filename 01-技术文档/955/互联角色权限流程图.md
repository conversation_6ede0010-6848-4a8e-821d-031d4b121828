```mermaid
graph TD
    %% 场景1的流程图
    subgraph 场景1[场景1: 单一应用对象关系]
        A1[互联角色1拥有应用1权限]
        B1[应用1包含对象a]
        C1[互联角色1拥有对象a的功能权限]
        D1[应用1移除对象a]
        E1[互联角色1自动移除对象a的功能权限]
        
        A1 --> B1
        B1 --> C1
        C1 --> D1
        D1 --> E1
    end
    
    %% 场景2的流程图
    subgraph 场景2[场景2: 多应用共享对象关系]
        A2[互联角色2拥有应用1和应用2权限]
        B2[应用1和应用2都包含对象a]
        C2[互联角色2拥有对象a的功能权限]
        
        %% 分支场景
        D2a[仅应用1移除对象a]
        D2b[仅应用2移除对象a]
        D2c[应用1和应用2都移除对象a]
        
        E2a[互联角色2保留对象a的功能权限]
        E2b[互联角色2保留对象a的功能权限]
        E2c[互联角色2自动移除对象a的功能权限]
        
        A2 --> B2
        B2 --> C2
        C2 --> D2a
        C2 --> D2b
        C2 --> D2c
        D2a --> E2a
        D2b --> E2b
        D2c --> E2c
    end
    
    %% 判断逻辑流程图
    subgraph 判断逻辑[权限移除判断逻辑]
        Start[开始: 应用移除对象]
        Check[检查: 该对象是否还存在于角色可访问的其他应用中]
        Keep[保留对象功能权限]
        Remove[移除对象功能权限]
        
        Start --> Check
        Check -->|是| Keep
        Check -->|否| Remove
    end
``` 