## O1：【KPI】PaaS平台系统稳定性及核心质量指标达标
KR1：各个团队缺陷密度，冒烟通过率，自动化测试达标

KR2：无3及以上故障

KR3：完善并落地发布规范及发布系统，无由于违反发布规范，或者发布系统操作失误导致的线上故障

KR4：拒绝Bug下降比例达标（KPI or OKR）

**KR3：提高存量代码单元测试覆盖率，新增代码覆盖率70%才可提交代码。** 

## O2：【KPI】PaaS 800&805&810版本发布，保质保量，符合业务预期及建设目标

KR1：发布无严重质量问题，严重Bug数量低于10

KR2：不影响Top60客户使用

KR3：无主版本发布导致的3及以上故障

## O5：【KPI】重点产品能力突破：分层代码开发框架成熟，支撑业务及方案落地	

KR1：分层代码开发能力	

KR2：上层业务、业务中台及业务插件能力	

KR3：APL+PWC能力

## O8：通过专项性能优化及QA体系化建设提升平台整体稳定性与质量


KR1：关键性能优化措施上线，降低系统调用量或者耗时，PG负载

KR2：自动化测试平台主流程跑通



## O10：PaaS自己把PaaS用起来
KR1：内部系统，产品及技术日常工作整合到FS，新的内部支撑工具及平台基于PaaS能力构建（团队核心质量数据看板，常用工具入口，小程序发布平台，内部发布及资源申请审批等）

**KR2:**

**KR2:**

