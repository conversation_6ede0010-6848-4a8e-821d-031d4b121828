[{"apiName": "biDashboardCom", "tenantPrivilege": {"disEnableObjects": ["JournalObj", "PersonnelObj"]}, "name": "驾驶舱", "limit": 1, "nameI18nKey": "MenuCode_BI_DASHBOARD", "widgetScope": {"widgetAntiAffinity": "right"}, "id": "biDashboardCom", "widgetType": 1}, {"apiName": "richTextWidget", "tenantPrivilege": {"enableObjects": []}, "name": "富文本", "limit": 1, "nameI18nKey": "UIPaaS.richTextWidget.name", "id": "richTextWidget", "widgetType": 600}, {"apiName": "public_employee_user_center", "extProp": {"buttons": [], "bizTypeList": [], "isAll": true}, "name": "个人中心", "limit": 1, "nameI18nKey": "eip.pe.public_employee_user_center", "id": "public_employee_user_center", "widgetType": 600}, {"apiName": "CheckinPlanDetails", "extProp": {"field_section": []}, "name": "拜访计划明细", "unDeletable": false, "nameI18nKey": "paas.udobj.CheckinPlanDetails", "widgetScope": {"widgetAffinity": "bottom", "widgetAntiAffinity": "tabs"}, "id": "CheckinPlanDetails", "widgetType": 401}, {"apiName": "CheckinSalesRanking", "extProp": {"field_section": []}, "name": "门店销量排行", "unDeletable": false, "nameI18nKey": "paas.udobj.CheckinSalesRanking", "widgetScope": {"widgetAffinity": "bottom", "widgetAntiAffinity": "tabs"}, "id": "CheckinSalesRanking", "widgetType": 401}, {"apiName": "CheckinPersonVisitSummary", "extProp": {"field_section": []}, "name": "个人拜访汇总", "unDeletable": false, "nameI18nKey": "paas.udobj.CheckinPersonVisitSummary", "widgetScope": {"widgetAffinity": "bottom", "widgetAntiAffinity": "tabs"}, "id": "CheckinPersonVisitSummary", "widgetType": 401}, {"apiName": "CheckinDepartmentVisitSummary", "extProp": {"field_section": []}, "name": "部门拜访汇总", "unDeletable": false, "nameI18nKey": "paas.udobj.CheckinDepartmentVisitSummary", "widgetScope": {"widgetAffinity": "bottom", "widgetAntiAffinity": "tabs"}, "id": "CheckinDepartmentVisitSummary", "widgetType": 401}, {"apiName": "tile_related_list", "tenantPrivilege": {"enableObjects": ["<PERSON><PERSON><PERSON><PERSON>", "SalesOrderObj", "SalesOrderProductObj", "Quote<PERSON><PERSON><PERSON>", "QuoteLinesObj", "InvoiceApplicationObj", "InvoiceApplicationLinesObj", "SaleContractObj", "SaleContractLineObj", "MarketingEventObj"]}, "limit": 15, "name": "相关列表", "nameI18nKey": "paas.udobj.tile_related_list", "widgetScope": {"widgetAffinity": "grid"}, "id": "tile_related_list", "widgetType": 401}, {"apiName": "biSplitView", "tenantPrivilege": {"enableObjects": []}, "name": "图表分屏视图", "limit": 1, "nameI18nKey": "bi.split.view", "id": "biSplitView", "widgetType": 1}, {"apiName": "order_settlement", "tenantPrivilege": {"bizConfKeys": [], "enableObjects": ["SalesOrderObj"]}, "extProp": {"ref_object_api_name": "SalesOrderObj", "list_header": ["product_amount", "discount", "order_amount"]}, "name": "订单结算", "unDeletable": false, "nameI18nKey": "paas.udobj.order_settlement", "widgetScope": {"widgetAffinity": "grid"}, "id": "order_settlement", "widgetType": 401}, {"apiName": "contact_member_relationship", "tenantPrivilege": {"bizConfKeys": ["contact_member_relationship_func_setting"], "enableObjects": ["AccountObj", "LeadsObj", "ContactObj"]}, "extProp": {"field_section": [], "ref_object_api_name": "ContactMemberRelationshipObj"}, "name": "人脉关系雷达", "nameI18nKey": "paas.udobj.contact_member_relationship", "id": "contact_member_relationship", "widgetType": 401}, {"apiName": "rfm_analysis", "tenantPrivilege": {"enableObjects": ["AccountObj", "LeadsObj", "ContactObj", "PartnerObj"]}, "extProp": {"field_section": [], "ref_object_api_name": "RFMAssessResultObj"}, "name": "RFM评估结果", "nameI18nKey": "paas.udobj.rfm_analysis", "id": "rfm_analysis", "widgetType": 401, "height": 700}, {"apiName": "biz_risk_brain", "tenantPrivilege": {"enableObjects": ["AccountObj"]}, "extProp": {"field_section": []}, "name": "风险画像", "nameI18nKey": "paas.udobj.biz_risk_brain", "id": "biz_risk_brain", "widgetType": 401, "height": 700}, {"apiName": "biz_enterprise_credit", "tenantPrivilege": {"enableObjects": ["AccountObj"]}, "extProp": {"field_section": []}, "name": "企业授信", "nameI18nKey": "paas.udobj.biz_enterprise_credit", "id": "biz_enterprise_credit", "widgetType": 401, "height": 700}, {"apiName": "delivery_note_detail", "extProp": {"field_section": []}, "name": "发货单明细", "unDeletable": false, "limit": 1, "nameI18nKey": "deliverynote.components.delivery_note_detail", "widgetScope": {"widgetAffinity": "grid"}, "id": "delivery_note_detail", "widgetType": 401, "height": 200}, {"apiName": "biDashboard", "tenantPrivilege": {"enableObjects": ["MarketingEventObj"]}, "extProp": {"field_section": [], "ref_object_api_name": "MarketingEventObj"}, "name": "市场活动洞察", "nameI18nKey": "paas.udobj.marketing_event_bi_dashboard", "id": "marketing_event_bi_dashboard", "widgetType": 401, "height": 700}, {"cardId": "PS_BI_CUSTOMER", "name": "图表", "limit": 0, "icon": "cs-toolsbox-icon-UIPaas_chart", "nameI18nKey": "webpage_homepage.charts", "widgetScope": {"widgetAffinity": "grid"}, "id": "bi_customer", "widgetType": 2}, {"apiName": "electronic_sign", "tenantPrivilege": {"bizConfKeys": ["electronic_sign_component"]}, "extProp": {"externalType": 1, "signFileField": "", "externalSignField": "", "signAppType": "fadada", "action_type": "COMPANY", "signFileSource": 1, "signWay": 1, "innerSignType": 1}, "name": "电子签", "limit": 1, "nameI18nKey": "", "id": "electronic_sign", "widgetType": 600, "height": 150}, {"apiName": "related_what_list", "extProp": {"ref_object_api_name": "", "field_api_name": ""}, "name": "通用关联组件", "unDeletable": false, "nameI18nKey": "paas.udobj.related_what_list", "widgetScope": {"widgetAffinity": "grid"}, "id": "related_what_list", "widgetType": 401}, {"apiName": "related_list_form", "tenantPrivilege": {"enableObjects": ["ContactObj", "AccountObj", "<PERSON><PERSON><PERSON><PERSON>", "NewOpportunityObj", "EnterpriseRelationObj", "CasesObj", "PartnerObj"]}, "extProp": {"ref_object_api_name": "", "field_api_name": ""}, "name": "通用相关组件", "unDeletable": false, "limit": 1, "nameI18nKey": "paas.udobj.related_list_form", "widgetScope": {"widgetAffinity": "grid"}, "id": "related_list_form", "grayLimit": 4, "widgetType": 401}, {"apiName": "summary_info", "extProp": {"show_header": true, "field_section": []}, "name": "关键信息", "unDeletable": false, "limit": 5, "nameI18nKey": "paas.udobj.summary_info_component", "widgetScope": {"widgetAffinity": "tabs|grid", "widgetAntiAffinity": "navigation"}, "id": "summary_info", "widgetType": 401}, {"cardId": "PS_Filter", "name": "场景", "limit": 0, "icon": "cs-toolsbox-icon-UIPaas_scene", "nameI18nKey": "webpage_homepage.senses", "id": "scenes", "widgetType": 103}, {"cardId": "PS_Tool", "name": "工具", "icon": "cs-toolsbox-icon-UIPaas_tools", "nameI18nKey": "webpage_homepage.tool", "id": "tool", "widgetType": 104}, {"cardId": "PS_Schedule", "name": "日程", "icon": "cs-toolsbox-icon-UIPaas_calendar", "nameI18nKey": "webpage_homepage.schedule", "id": "schedule", "widgetType": 101}, {"cardId": "PS_Task", "name": "任务", "nameI18nKey": "webpage_homepage.task", "id": "task", "widgetType": 102}, {"cardId": "BI_SaleReport", "name": "销售简报", "nameI18nKey": "webpage_homepage.sale_report", "widgetScope": {"widgetAffinity": "grid"}, "id": "sale_report", "widgetType": 1}, {"cardId": "BI_5a6871d037aa1b3d4865831d", "name": "回款率(回款/目标)", "nameI18nKey": "webpage_homepage.payback_rate", "widgetScope": {"widgetAffinity": "grid"}, "id": "payback_rate", "widgetType": 1}, {"cardId": "BI_5a68724c37aa1b3d48658337", "name": "预测", "nameI18nKey": "webpage_homepage.prediction", "widgetScope": {"widgetAffinity": "grid"}, "id": "prediction", "widgetType": 1}, {"cardId": "BI_5a68730437aa1b3d48658351", "name": "订单统计", "nameI18nKey": "webpage_homepage.order_statistics", "widgetScope": {"widgetAffinity": "grid"}, "id": "order_statistics", "widgetType": 1}, {"cardId": "BI_5a68738337aa1b3d4865836e", "name": "回款统计", "nameI18nKey": "webpage_homepage.payment_statistics", "widgetScope": {"widgetAffinity": "grid"}, "id": "payment_statistics", "widgetType": 1}, {"cardId": "BI_5a6873f237aa1b3d4865838b", "name": "退款统计", "nameI18nKey": "webpage_homepage.refund_statistics", "widgetScope": {"widgetAffinity": "grid"}, "id": "refund_statistics", "widgetType": 1}, {"cardId": "PS_CluesInto", "name": "线索转化", "nameI18nKey": "webpage_homepage.cluesinfo", "widgetScope": {"widgetAffinity": "grid"}, "id": "cluesinfo", "widgetType": 1}, {"cardId": "BI_595e213437aa1badec9778e6", "name": "销售阶段转化分析", "nameI18nKey": "webpage_homepage.sale_analysis", "widgetScope": {"widgetAffinity": "grid"}, "id": "sale_analysis", "widgetType": 1}, {"cardId": "BI_Rank", "name": "排行榜", "icon": "cs-toolsbox-icon-UIPaas_toplist", "nameI18nKey": "webpage_homepage.leaderboard", "widgetScope": {"widgetAffinity": "grid"}, "id": "leaderboard", "widgetType": 1}, {"cardId": "BI_SalesAssistant", "name": "销售助手", "icon": "cs-toolsbox-icon-UIPaas_asis", "nameI18nKey": "webpage_homepage.sales_assistant", "widgetScope": {"widgetAffinity": "grid"}, "id": "sales_assistant", "widgetType": 1}, {"cardId": "BI_58acfc2537aa1badf31d169a", "name": "销售漏斗（商机金额）", "icon": "cs-toolsbox-icon-UIPaas_funnel", "nameI18nKey": "webpage_homepage.sales_funnel", "widgetScope": {"widgetAffinity": "grid"}, "id": "sales_funnel", "widgetType": 1}, {"cardId": "BI_5af92d6deb8caafcfbbbc6c6", "name": "目标完成率", "icon": "cs-toolsbox-icon-UIPaas_finish", "nameI18nKey": "webpage_homepage.target_completion_rate", "widgetScope": {"widgetAffinity": "grid"}, "id": "target_completion_rate", "widgetType": 1}, {"cardId": "BI_5af92d16eb8caafcfbbbc6ab", "name": "员工目标完成率排行", "nameI18nKey": "webpage_homepage.employee_ranking", "widgetScope": {"widgetAffinity": "grid"}, "id": "employee_ranking", "widgetType": 1}, {"cardId": "BI_5af92cf0eb8caafcfbbbc690", "name": "年度目标完成情况", "nameI18nKey": "webpage_homepage.annual_target_completion", "widgetScope": {"widgetAffinity": "grid"}, "id": "annual_target_completion", "widgetType": 1}, {"cardId": "BI_5af92cd8eb8caafcfbbbc675", "name": "部门目标完成情况", "nameI18nKey": "webpage_homepage.department_goal_completion", "widgetScope": {"widgetAffinity": "grid"}, "id": "department_goal_completion", "widgetType": 1}, {"name": "轮播图", "widgetScope": {"widgetAffinity": "backgroundContainerWidget"}, "nameI18nKey": "webpage_homepage.slideImage", "id": "slideImage", "widgetType": 201, "height": 300}, {"name": "快速新建", "nameI18nKey": "webpage_homepage.objectFloatingBall", "id": "objectFloatingBall", "widgetType": 406, "height": 300}, {"name": "webView", "widgetScope": {"widgetAffinity": "backgroundContainerWidget"}, "nameI18nKey": "webpage_homepage.webView", "id": "webView", "widgetType": 201, "height": 300}, {"name": "天气", "nameI18nKey": "webpage_homepage.weather", "id": "weather", "widgetType": 201, "height": 300}, {"name": "公告", "nameI18nKey": "webpage_homepage.notice", "id": "notice", "widgetType": 201, "height": 300}, {"apiName": "work_announce_list", "name": "公告列表", "limit": 1, "nameI18nKey": "paas.udobj.work_announce_list_component", "widgetScope": {"widgetAffinity": "right"}, "id": "work_announce_list", "widgetType": 600, "height": 300}, {"cardId": "BI_5dd26b82ccbfc00001bf3a0e", "name": "客户跟进情况", "nameI18nKey": "webpage_homepage.BI_5dd26b82ccbfc00001bf3a0e", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd26b82ccbfc00001bf3a0e", "widgetType": 1}, {"cardId": "BI_5dc11bbd6eafd00001eeb880", "name": "新增客户数排行榜top10", "nameI18nKey": "webpage_homepage.BI_5dc11bbd6eafd00001eeb880", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc11bbd6eafd00001eeb880", "widgetType": 1}, {"cardId": "BI_5dd27921c910ec00011be65a", "name": "新增客户数", "nameI18nKey": "webpage_homepage.add_customer_qty", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd27921c910ec00011be65a", "widgetType": 1}, {"cardId": "BI_5dce2e8e988d3300014bc719", "name": "3天将收回客户数", "nameI18nKey": "webpage_homepage.BI_5dce2e8e988d3300014bc719", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dce2e8e988d3300014bc719", "widgetType": 1}, {"apiName": "backgroundContainerWidget", "extProp": {"components": [], "buttons": [], "tabs": []}, "name": "背景容器", "widgetScope": {"containerAffinity": "backgroundContainerWidget"}, "limit": 0, "nameI18nKey": "UIPaaS.backgroundContainerWidget.name", "id": "backgroundContainerWidget", "widgetType": 407}, {"cardId": "BI_5dc0e09b6eafd00001eeb7ca", "name": "转化率分析", "nameI18nKey": "webpage_homepage.BI_5dc0e09b6eafd00001eeb7ca", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc0e09b6eafd00001eeb7ca", "widgetType": 1}, {"cardId": "BI_5dd49f7fd44fc40001dbcb4d", "name": "线索跟进情况top10", "nameI18nKey": "webpage_homepage.BI_5dd49f7fd44fc40001dbcb4d", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd49f7fd44fc40001dbcb4d", "widgetType": 1}, {"cardId": "BI_5dd271d67d4463000101b508", "name": "线索量趋势", "nameI18nKey": "webpage_homepage.BI_5dd271d67d4463000101b508", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd271d67d4463000101b508", "widgetType": 1}, {"cardId": "BI_5dc1214c6eafd00001eeb8bd", "name": "商机金额目标达成和预测", "nameI18nKey": "webpage_homepage.BI_5dc1214c6eafd00001eeb8bd", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc1214c6eafd00001eeb8bd", "widgetType": 1}, {"cardId": "BI_5dcb747488da4e0001c2a50a", "name": "销售漏斗（商机金额)", "nameI18nKey": "webpage_homepage.sales_funnel", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dcb747488da4e0001c2a50a", "widgetType": 1}, {"cardId": "BI_5dd286aaccbfc00001bf3a58", "name": "赢单商机金额排行榜top10", "nameI18nKey": "webpage_homepage.BI_5dd286aaccbfc00001bf3a58", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd286aaccbfc00001bf3a58", "widgetType": 1}, {"cardId": "BI_5dd3cc7c99b04b00018cca97", "name": "商机简报", "nameI18nKey": "webpage_homepage.BI_5dd3cc7c99b04b00018cca97", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd3cc7c99b04b00018cca97", "widgetType": 1}, {"cardId": "BI_5dc126746eafd00001eeb95e", "name": "商机金额目标达成率", "nameI18nKey": "webpage_homepage.BI_5dc126746eafd00001eeb95e", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc126746eafd00001eeb95e", "widgetType": 1}, {"cardId": "BI_5dd0dd13c4fee90001bc4167", "name": "订单金额目标完成情况", "nameI18nKey": "webpage_homepage.BI_5dd0dd13c4fee90001bc4167", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd0dd13c4fee90001bc4167", "widgetType": 1}, {"cardId": "BI_5dc135ec6eafd00001eeb9e3", "name": "订单回款情况", "nameI18nKey": "webpage_homepage.BI_5dc135ec6eafd00001eeb9e3", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc135ec6eafd00001eeb9e3", "widgetType": 1}, {"cardId": "BI_5dc136556eafd00001eeba04", "name": "订单金额排行榜top10", "nameI18nKey": "webpage_homepage.BI_5dc136556eafd00001eeba04", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc136556eafd00001eeba04", "widgetType": 1}, {"cardId": "BI_5dc137f46eafd00001eeba61", "name": "订单金额简报", "nameI18nKey": "webpage_homepage.BI_5dc137f46eafd00001eeba61", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc137f46eafd00001eeba61", "widgetType": 1}, {"cardId": "BI_5dce6738988d3300014bd1ab", "name": "订单金额完成率", "nameI18nKey": "webpage_homepage.BI_5dce6738988d3300014bd1ab", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dce6738988d3300014bd1ab", "widgetType": 1}, {"cardId": "BI_5dc138db6eafd00001eeba82", "name": "回款目标完成情况", "nameI18nKey": "webpage_homepage.BI_5dc138db6eafd00001eeba82", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc138db6eafd00001eeba82", "widgetType": 1}, {"cardId": "BI_5dd28f0cccbfc00001bf3aa0", "name": "已回款同环比", "nameI18nKey": "webpage_homepage.BI_5dd28f0cccbfc00001bf3aa0", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dd28f0cccbfc00001bf3aa0", "widgetType": 1}, {"cardId": "BI_5dce6eef988d3300014bd364", "name": "回款排行榜top10", "nameI18nKey": "webpage_homepage.BI_5dce6eef988d3300014bd364", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dce6eef988d3300014bd364", "widgetType": 1}, {"cardId": "BI_5dce7325988d3300014bd3ab", "name": "回款简报", "nameI18nKey": "webpage_homepage.BI_5dce7325988d3300014bd3ab", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dce7325988d3300014bd3ab", "widgetType": 1}, {"cardId": "BI_5dc13b0d6eafd00001eebae5", "name": "回款完成率", "nameI18nKey": "webpage_homepage.BI_5dc13b0d6eafd00001eebae5", "widgetScope": {"widgetAffinity": "grid"}, "id": "BI_5dc13b0d6eafd00001eebae5", "widgetType": 1}, {"cardId": "filters", "name": "筛选器", "icon": "cs-toolsbox-icon-UIPaas_filter", "nameI18nKey": "webpage_homepage.filter", "id": "filters", "widgetType": 401}, {"apiName": "head_info", "tenantPrivilege": {"disEnableObjects": []}, "name": "标题和按钮", "unDeletable": true, "nameI18nKey": "paas.udobj.head_info", "widgetScope": {"widgetAffinity": "top", "widgetAntiAffinity": "left|tabs"}, "id": "head_info", "widgetType": 401}, {"fieldSectionMap": {"ContactObj": [{"field_name": "job_title"}, {"field_name": "department_contact"}, {"field_name": "primary_contact"}, {"field_name": "contact_status"}, {"field_name": "owner"}, {"field_name": "department"}], "default": [{"field_name": "owner"}, {"field_name": "owner_department"}, {"field_name": "last_modified_time"}, {"field_name": "record_type"}], "ReturnedGoodsInvoiceObj": [{"field_name": "account_id"}, {"field_name": "order_id"}, {"field_name": "returned_goods_inv_amount"}, {"field_name": "returned_goods_time"}, {"field_name": "status"}, {"field_name": "owner"}, {"field_name": "owner_department"}], "AccountObj": [{"field_name": "account_no"}, {"field_name": "account_level"}, {"field_name": "deal_status"}, {"field_name": "owner"}, {"field_name": "last_followed_time"}, {"field_name": "high_seas_name"}], "ProductObj": [{"field_name": "category"}, {"field_name": "price"}, {"field_name": "product_status"}, {"field_name": "on_shelves_time"}, {"field_name": "off_shelves_time"}, {"field_name": "owner"}, {"field_name": "owner_department"}], "LeadsObj": [{"field_name": "company"}, {"field_name": "source"}, {"field_name": "leads_status"}, {"field_name": "is_overtime"}, {"field_name": "owner"}, {"field_name": "owner_department"}], "SalesOrderObj": [{"field_name": "account_id"}, {"field_name": "order_status"}, {"field_name": "order_time"}, {"field_name": "receipt_type"}, {"field_name": "owner"}, {"field_name": "owner_department"}], "OpportunityObj": [{"field_name": "account_id"}, {"field_name": "expected_deal_amount"}, {"field_name": "status"}, {"field_name": "owner"}, {"field_name": "owner_department"}]}, "apiName": "top_info", "tenantPrivilege": {"disEnableObjects": ["JournalObj"]}, "name": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "id": "top_info", "widgetType": 401}, {"apiName": "relevant_team_component", "tenantPrivilege": {"disEnableObjects": ["JournalObj"]}, "name": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "widgetScope": {"widgetAffinity": "right"}, "id": "relevant_team_component", "widgetType": 401}, {"apiName": "sale_log", "tenantPrivilege": {"disEnableObjects": ["JournalObj"]}, "name": "跟进动态", "nameI18nKey": "paas.udobj.follow_up_dynamic", "widgetScope": {"widgetAffinity": "right"}, "id": "sale_log", "widgetType": 401}, {"apiName": "form_component", "name": "详细信息", "unDeletable": true, "nameI18nKey": "paas.udobj.detail_info", "widgetScope": {"widgetAffinity": "grid"}, "id": "form_component", "widgetType": 401}, {"apiName": "operation_log", "tenantPrivilege": {"disEnableObjects": ["JournalObj"]}, "name": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "id": "operation_log", "widgetType": 401}, {"apiName": "frame_component", "name": "嵌入页面", "limit": 0, "nameI18nKey": "paas.udobj.frame_component", "id": "frame_component", "widgetType": 404, "height": 500}, {"apiName": "BPM_related_list", "tenantPrivilege": {"disEnableObjects": ["JournalObj"], "objectApiName": "BPM"}, "name": "流程列表", "nameI18nKey": "paas.udobj.process_list", "id": "BPM_related_list", "widgetType": 401}, {"apiName": "Approval_related_list", "tenantPrivilege": {"disEnableObjects": ["JournalObj"], "objectApiName": "BPM"}, "name": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "id": "Approval_related_list", "widgetType": 401}, {"apiName": "tabs", "extProp": {"components": [], "buttons": [], "tabs": []}, "name": "页签容器", "limit": 0, "nameI18nKey": "paas.udobj.contain_tabs", "widgetScope": {"containerAntiAffinity": "tabs"}, "id": "tabs", "widgetType": 402}, {"apiName": "chart_component", "name": "图表", "limit": 10, "nameI18nKey": "paas.udobj.chart_component", "widgetScope": {"widgetAffinity": "grid"}, "id": "chart_component", "widgetType": 401, "height": 500}, {"apiName": "navigation", "name": "导航容器", "limit": 1, "nameI18nKey": "webpage_homepage.navigation", "id": "navigation", "widgetType": 402}, {"apiName": "navigate", "name": "导航组件", "limit": 1, "nameI18nKey": "webpage_homepage.navigate", "id": "navigate", "widgetType": 403}, {"apiName": "yinlu", "extProp": {"menus": []}, "name": "银鹭组件", "limit": 1, "nameI18nKey": "webpage_homepage.yinlu", "id": "yinlu", "widgetType": 600}, {"apiName": "grid", "extProp": {"components": [[], []], "showHeader": true, "nameI18nKey": "webpage_homepage.grid", "ratioType": 0, "widthRatios": [1, 1]}, "name": "栅格组件", "limit": 0, "nameI18nKey": "webpage_homepage.grid", "widgetScope": {"containerAffinity": "grid"}, "id": "grid", "widgetType": 405}, {"apiName": "iframe", "name": "嵌入页面", "limit": 0, "nameI18nKey": "paas.udobj.frame_component", "id": "iframe", "widgetType": 404, "height": 500}, {"apiName": "icnotice", "extProp": {"showType": "all", "lines": 5, "title": {"show": true, "value": "通知公告"}}, "name": "通知公告", "limit": 1, "nameI18nKey": "fs-open-material.errorinfo.MaterialController.318", "id": "icnotice", "widgetType": 600}, {"extProp": {"h5Url": "https://www.fxiaoke.com/fs-er-biz/er/auth/connect?_hash=/wx/devicelist&from=device&resourceUrl=eservice-miniprogram-home&useEM6=1&fetchOrganization=0&fsAppId=FSAID_127a3981", "header": "我的设备", "iconUrl": "https://a9.fspage.com/FSR/link/eservice/myDeviceMini.png", "showLine": 1}, "cardId": "eseviceMyDevice", "name": "我的设备", "limit": 1, "nameI18nKey": "eservce.mydevice", "id": "eseviceMyDevice", "widgetType": 600}, {"extProp": {"h5Url": "https://www.fxiaoke.com/fs-er-biz/er/auth/connect?_hash=/wxlist&apiname=CasesObj&resourceUrl=eservice-miniprogram-home&useEM6=1&fetchOrganization=0&fsAppId=FSAID_127a3981", "header": "我的工单", "iconUrl": "https://a9.fspage.com/FSR/link/eservice/WoDeGongDan2.png", "showLine": 0}, "cardId": "eseviceMyCasesObj", "name": "我的工单", "limit": 1, "nameI18nKey": "eservice.my_workorder", "id": "eseviceMyCasesObj", "widgetType": 600}, {"extProp": {"moreUrl": "https://www.fxiaoke.com/fs-er-biz/er/auth/connect?_hash=/wxnotice&resourceUrl=eservice-miniprogram-home&useEM6=1&fetchOrganization=0&fsAppId=FSAID_127a3981"}, "name": "最新通知", "limit": 1, "nameI18nKey": "eservice.lastest_notice", "id": "latestNotification", "widgetType": 600}, {"extProp": {"welcomeMsg": "尊敬的客户，您好！\n这里是自助服务大厅，希望可以帮助到您！"}, "name": "欢迎语", "limit": 1, "nameI18nKey": "eservice.welcome", "id": "welcomeHomePage", "widgetType": 600, "height": 156}, {"apiName": "list_component", "extProp": {"view_info": [{"name": "list_view", "is_default": true, "is_show": true}, {"name": "split_view", "is_default": false, "is_show": true}], "button_info": [{"exposed_button": 1, "hidden": [], "page_type": "list", "render_type": "list_normal", "order": []}, {"exposed_button": 1, "hidden": [], "page_type": "list", "render_type": "list_batch", "order": []}, {"exposed_button": 0, "hidden": [], "page_type": "list", "render_type": "list_single", "order": []}], "filter_info": [], "scene_info": [{"hidden": [], "page_type": "list", "render_type": "drop_down", "order": []}]}, "name": "列表页", "limit": 1, "unDeletable": true, "nameI18nKey": "paas.udobj.list_page", "widgetScope": {"widgetAffinity": "bottom", "widgetAntiAffinity": "top|grid"}, "id": "list_component", "widgetType": 600}, {"cardId": "biInsight", "name": "嵌入驾驶舱", "limit": 1, "nameI18nKey": "", "widgetScope": {"widgetAffinity": "grid"}, "id": "biInsight", "widgetType": 600}, {"extProp": {"bizTypeList": [], "isAll": true}, "name": "待办事项", "limit": 1, "nameI18nKey": "todo<PERSON><PERSON><PERSON>", "id": "todo<PERSON><PERSON><PERSON>", "widgetType": 600}, {"cardId": "biInsightChart", "name": "列表洞察", "limit": 0, "nameI18nKey": "", "widgetScope": {"widgetAffinity": "grid"}, "id": "biInsightChart", "widgetType": 600, "height": 500}, {"apiName": "training_partner", "name": "伙伴学堂", "limit": 1, "nameI18nKey": "eservice.tipis.outer.huobangxuetang", "id": "training_partner", "widgetType": 600}, {"apiName": "head_info", "extProp": {"button_info": [{"hidden": [], "page_type": "create", "render_type": "normal", "order": []}, {"hidden": [], "page_type": "edit", "render_type": "normal", "order": []}]}, "name": "标题和按钮", "unDeletable": true, "nameI18nKey": "paas.udobj.head_info", "widgetScope": {"widgetAffinity": "top", "widgetAntiAffinity": "bottom|tabs"}, "id": "head_info_edit", "widgetType": 401}, {"apiName": "form_component", "extProp": {"field_section": []}, "name": "表单组件", "unDeletable": true, "nameI18nKey": "paas.udobj.form_component", "widgetScope": {"widgetAffinity": "bottom", "widgetAntiAffinity": "tabs"}, "id": "form_component_edit", "widgetType": 401}, {"apiName": "shortcut", "name": "快捷定位", "limit": 1, "nameI18nKey": "", "widgetScope": {"widgetAffinity": "top", "widgetAntiAffinity": "bottom"}, "id": "shortcut", "widgetType": 600}, {"apiName": "todo<PERSON><PERSON><PERSON>", "extProp": {"bizTypeList": [], "isAll": true}, "name": "待办事项", "nameI18nKey": "webpage_homepage.todoWidget", "id": "todo<PERSON><PERSON><PERSON>", "widgetType": 600}, {"apiName": "sceneCategory", "name": "场景分类", "nameI18nKey": "sfa.form_component.scene_category", "widgetScope": {"widgetAffinity": "bottom"}, "id": "sceneCategory", "widgetType": 600}, {"apiName": "dealwith_leadsobj", "name": "处理线索", "nameI18nKey": "sfa.form_component.process_leads", "widgetScope": {"widgetAffinity": "right"}, "id": "dealwith_leadsobj", "widgetType": 600}, {"apiName": "BehaviorRecordObj_related_list", "tenantPrivilege": {"enableObjects": ["LeadsObj"], "licenseProductCodes": ["leads_deduplication_app", "strengthen_edition", "dingtalk_strengthen_edition", "wechat_strengthen_edition", "enterprise_edition"]}, "extProp": {"related_list_name": "object_behavior_record_list", "ref_object_api_name": "BehaviorRecordObj"}, "name": "行为记录", "unDeletable": false, "nameI18nKey": "BehaviorRecordObj.attribute.self.display_name", "widgetScope": {"widgetAffinity": "grid"}, "id": "BehaviorRecordObj_related_list", "widgetType": 401}, {"apiName": "SalesOrderProductObj_detail_object", "tenantPrivilege": {"enableObjects": ["SalesOrderObj"]}, "extProp": {"show_image": "product_image", "related_list_name": "order_id_list", "ref_object_api_name": "SalesOrderProductObj", "button_info": [], "master_summary_fields": [{"field_name": "product_amount"}, {"field_name": "discount"}, {"field_name": "order_amount"}], "render_type": "compact", "field_api_name": "order_id"}, "name": "订单产品", "unDeletable": false, "nameI18nKey": "SalesOrderProductObj.attribute.self.display_name", "widgetScope": {"widgetAffinity": "grid"}, "id": "SalesOrderProductObj_detail_object", "widgetType": 401}, {"apiName": "eservice_cases_related_info", "extProp": {"moreUrl": ""}, "name": "相关信息", "limit": 1, "nameI18nKey": "eservice.cases_related_info", "widgetScope": {"widgetAffinity": "right"}, "id": "eservice_cases_related_info", "widgetType": 401, "height": 500}, {"apiName": "related_rule_condition", "name": "产品条件", "nameI18nKey": "paas.udobj.related_rule_condition", "widgetScope": {"widgetAffinity": "right"}, "id": "related_rule_condition", "widgetType": 600}, {"apiName": "work", "name": "工作圈", "limit": 1, "nameI18nKey": "paas.udobj.work_component", "id": "work_circle", "widgetType": 600, "height": 500}, {"cardId": "data_overview", "name": "工单数据概览", "nameI18nKey": "eservice.work.order.data_overview", "id": "data_overview", "widgetType": 301}, {"cardId": "dht_promotion_banner", "name": "广告Banner", "nameI18nKey": "dht.component.promotion_banner", "id": "dht_promotion_banner", "widgetType": 301}, {"cardId": "dht_promotion_list", "name": "促销活动", "nameI18nKey": "dht.component.promotion_list", "id": "dht_promotion_list", "widgetType": 301}, {"cardId": "dht_promotion_product", "name": "促销商品", "nameI18nKey": "dht.app.widget.hot_selling", "id": "dht_promotion_product", "widgetType": 301}, {"cardId": "dht_common_qa", "name": "常见问题", "nameI18nKey": "dht.component.common_qa", "id": "dht_common_qa", "widgetType": 301}, {"apiName": "dht_order_approval_flow", "extProp": {"moreUrl": ""}, "name": "订单流程-下游", "limit": 1, "nameI18nKey": "dht.order_approval_flow", "widgetScope": {"widgetAffinity": "right"}, "id": "dht_order_approval_flow", "widgetType": 401, "height": 200}, {"apiName": "dht_order_customer_account", "tenantPrivilege": {"objectPlugins": ["customer_account"]}, "extProp": {"plugin_api_name": "customer_account", "is_show_name": true}, "name": "客户账户", "limit": 1, "nameI18nKey": "dht.order_customer_account", "widgetScope": {"widgetAffinity": "grid"}, "id": "dht_order_customer_account", "widgetType": 401}, {"apiName": "landing_page_path", "tenantPrivilege": {"enableObjects": ["LandingPageObj"]}, "extProp": {"related_list_name": "landing_page_list", "ref_object_api_name": "LandingPageObj"}, "name": "落地页层级", "nameI18nKey": "paas.udobj.landing_page_path", "id": "landing_page_path", "widgetType": 401}, {"apiName": "partner_path", "tenantPrivilege": {"enableObjects": ["PartnerObj"]}, "extProp": {"related_list_name": "partner_list", "ref_object_api_name": "PartnerObj"}, "name": "合作伙伴层级", "nameI18nKey": "paas.udobj.partner_path", "id": "partner_path", "widgetType": 401}, {"apiName": "new_opportunity_path", "tenantPrivilege": {"enableObjects": ["NewOpportunityObj"]}, "extProp": {"related_list_name": "parent_list", "ref_object_api_name": "NewOpportunityObj"}, "name": "商机层级", "nameI18nKey": "paas.udobj.new_opportunity_path", "id": "new_opportunity_path", "widgetType": 401}, {"apiName": "new_opportunity_contact_atlas", "tenantPrivilege": {"enableObjects": ["NewOpportunityObj"]}, "extProp": {"related_list_name": "new_opportunity_contact_list", "ref_object_api_name": "NewOpportunityContactsObj"}, "name": "商机联系人关系图谱", "nameI18nKey": "paas.udobj.new_opportunity_contact_atlas", "id": "new_opportunity_contact_atlas", "widgetType": 401, "height": 700}, {"apiName": "onlineService", "tenantPrivilege": {"licenseModuleCodes": ["customerservice_app", "customerservice_pro_app"]}, "extProp": {"adaptive": true, "defaultIconIndex": "UtilityBar-customerService"}, "name": "线上客服", "limit": 1, "nameI18nKey": "dht.app.toolbar.online_customer _service", "id": "onlineService", "widgetType": 600}, {"apiName": "shopCarPlugin", "tenantPrivilege": {"licenseModuleCodes": ["order_plus_app", "order_plus_wecom_app", "order_plus_dingtalk_app"]}, "extProp": {"adaptive": true, "defaultIconIndex": "UtilityBar-shoppingCart"}, "name": "购物车", "limit": 1, "nameI18nKey": "dht.app.toolbar.shopping_cart", "id": "shopCarPlugin", "widgetType": 600}, {"apiName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tenantPrivilege": {"licenseModuleCodes": ["order_plus_app", "order_plus_wecom_app", "order_plus_dingtalk_app"]}, "extProp": {"adaptive": true, "defaultIconIndex": "UtilityBar-31"}, "name": "快速下单", "limit": 1, "nameI18nKey": "crm.order_quickly.title", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgetType": 600}, {"apiName": "eservice_device_bom", "tenantPrivilege": {"bizConfKeys": ["device_bom_switch"]}, "extProp": {"moreUrl": ""}, "name": "设备BOM", "limit": 1, "nameI18nKey": "eservice.device_bom", "id": "eservice_device_bom", "widgetType": 401, "height": 500}, {"apiName": "eservice_cases_dynamic", "tenantPrivilege": {"bizConfKeys": ["cases_dynamic_switch"]}, "extProp": {"moreUrl": ""}, "name": "工单动态", "limit": 1, "nameI18nKey": "eservice.cases_dynamic", "widgetScope": {"widgetAffinity": "right"}, "id": "eservice_cases_dynamic", "widgetType": 401, "height": 500}, {"extProp": {"components": [], "column": 4, "map": "menuEntry"}, "cardId": "menuGroup", "name": "菜单入口", "widgetScope": {"widgetAffinity": "backgroundContainerWidget"}, "limit": 0, "nameI18nKey": "webpage.component.menuGroup", "id": "menuGroup", "widgetType": 600}, {"name": "过滤可售范围", "limit": 1, "nameI18nKey": "dht.app.toolbar.filter_available_range", "id": "SalesRangeFilter", "widgetType": 600}, {"apiName": "integralRankEmployee", "extProp": {"field_section": []}, "cardId": "integralRankEmployee", "unDeletable": false, "name": "员工积分排行", "icon": "cs-toolsbox-icon-UIPaas_toplist", "nameI18nKey": "", "widgetScope": {"widgetAffinity": "grid"}, "id": "integralRankEmployee", "widgetType": 1}, {"apiName": "integralCardEmployee", "extProp": {"field_section": []}, "cardId": "integralCardEmployee", "unDeletable": false, "name": "员工积分汇总", "icon": "cs-toolsbox-icon-UIPaas_toplist", "nameI18nKey": "", "widgetScope": {"widgetAffinity": "grid"}, "id": "integralCardEmployee", "widgetType": 1}, {"apiName": "integralRankDepartment", "extProp": {"field_section": []}, "cardId": "integralRankDepartment", "unDeletable": false, "name": "部门积分排行", "icon": "cs-toolsbox-icon-UIPaas_toplist", "nameI18nKey": "", "widgetScope": {"widgetAffinity": "grid"}, "id": "integralRankDepartment", "widgetType": 1}, {"apiName": "integralCardDepartment", "extProp": {"field_section": []}, "cardId": "integralCardDepartment", "unDeletable": false, "name": "部门积分汇总", "icon": "cs-toolsbox-icon-UIPaas_toplist", "nameI18nKey": "", "widgetScope": {"widgetAffinity": "grid"}, "id": "integralCardDepartment", "widgetType": 1}, {"apiName": "channelDataChart", "tenantPrivilege": {"appId": "FSAID_PaaS_3c81509291449"}, "extProp": {"menus": []}, "name": "经销商报表组件", "limit": 1, "nameI18nKey": "", "id": "channelDataChart", "widgetType": 600}, {"apiName": "public_employee_usage_analysis", "tenantPrivilege": {"enableObjects": ["AccountObj", "PartnerObj"], "licenseProductCodes": ["interconnect_app_basic_app"]}, "extProp": {"field_section": []}, "name": "互联用户情况", "unDeletable": false, "nameI18nKey": "eip.pe.usage_analysis", "widgetScope": {"widgetAffinity": "bottom"}, "id": "public_employee_usage_analysis", "widgetType": 401}, {"apiName": "tpm_activty", "extProp": {"field_section": []}, "name": "营销活动组件", "limit": 1, "nameI18nKey": "", "widgetScope": {"widgetAffinity": "grid", "widgetAntiAffinity": "grid"}, "id": "fmcg_tpm_activity_widget", "widgetType": 600}, {"apiName": "related_what", "extProp": {"ref_object_api_name": "", "field_api_name": ""}, "name": "通用关联组件(what)", "limit": 0, "unDeletable": false, "nameI18nKey": "paas.udobj.related_what", "widgetScope": {"widgetAffinity": "grid"}, "id": "related_what", "widgetType": 401}, {"apiName": "dht_order_delivery_address", "extProp": {"field_section": [{"show_header": true, "form_fields": [], "api_name": "dht_order_delivery_address__c", "header": "收货地址", "alwaysShow": true}], "title": {"show": true, "value": "收货地址"}}, "name": "收货地址", "limit": 1, "nameI18nKey": "dht.order_delivery_address", "widgetScope": {"widgetAffinity": "grid"}, "id": "dht_order_delivery_address", "widgetType": 401, "height": 100}, {"apiName": "dht_order_payment_summary", "extProp": {"field_section": [{"enable": true, "show_button": false, "type": "delivery"}, {"enable": false, "scenes": 2, "show_button": false, "type": "payment"}, {"enable": true, "show_button": false, "type": "invoice"}]}, "name": "回款发货摘要", "limit": 1, "nameI18nKey": "dht.order_payment_summary", "widgetScope": {"widgetAffinity": "grid"}, "id": "dht_order_payment_summary", "widgetType": 401, "height": 200}, {"apiName": "globalTopBar", "extProp": {"childComponent": [{"apiName": "searchBar", "header": "搜索框"}, {"apiName": "quickCreate", "header": "快速新建"}]}, "name": "顶部工具栏", "unDeletable": true, "limit": 1, "nameI18nKey": "webpage.widget_globalTopBar", "widgetScope": {"widgetAffinity": "top"}, "id": "globalTopBar", "widgetType": 401}, {"apiName": "promotionCategory", "name": "促销分类", "nameI18nKey": "sfa.form_component.promotion_category", "widgetScope": {"widgetAffinity": "bottom"}, "id": "promotionCategory", "widgetType": 600}, {"apiName": "table_component", "name": "移动端摘要", "unDeletable": true, "limit": 1, "nameI18nKey": "paas.udobj.abstract_component", "id": "table_component", "widgetType": 401}, {"api_name": "bpm_component", "limit": 1, "unDeletable": true, "header": "业务流组件", "nameI18nKey": "paas.udobj.bpm_component", "id": "bpm_component", "type": "bpm_component", "widgetType": 401}, {"api_name": "approval_component", "limit": 1, "unDeletable": true, "header": "审批流组件", "nameI18nKey": "paas.udobj.approval_component", "id": "approval_component", "type": "approval_component", "widgetType": 401}, {"api_name": "stage_component", "limit": 1, "unDeletable": true, "header": "阶段推进器组件", "nameI18nKey": "paas.udobj.stage_component", "id": "stage_component", "type": "stage_component", "widgetType": 401}, {"apiName": "text_component", "tenantPrivilege": {"enableObjects": ["<PERSON><PERSON><PERSON><PERSON>"]}, "extProp": {"show_header": true, "content": ""}, "name": "文本框", "unDeletable": false, "limit": 0, "nameI18nKey": "paas.udobj.text_component", "widgetScope": {"widgetAffinity": "grid", "widgetAntiAffinity": "form_table"}, "id": "text_component", "widgetType": 401}, {"apiName": "form_table", "tenantPrivilege": {"enableObjects": ["<PERSON><PERSON><PERSON><PERSON>", "AccountObj", "NewOpportunityObj"]}, "extProp": {"show_header": false, "show_border": true, "show_field_background_color": false, "field_background_color": "", "rows": [{"cells": [{"components": [], "row_span": 1, "style": {"field_background_color": ""}, "col_span": 1}]}], "col_count": 1, "row_count": 1}, "name": "表格组件", "unDeletable": false, "limit": 0, "nameI18nKey": "paas.udobj.form_table", "widgetScope": {"widgetAffinity": "bottom", "widgetAntiAffinity": "tabs"}, "id": "form_table", "widgetType": 401}, {"apiName": "eservice_knowledge_recommend", "tenantPrivilege": {"bizConfKeys": ["eservice_knowledge_recommend"]}, "extProp": {"moreUrl": ""}, "name": "知识推荐", "limit": 1, "nameI18nKey": "eservice.knowledge_recommend", "widgetScope": {"widgetAffinity": "right"}, "id": "eservice_knowledge_recommend", "widgetType": 401, "height": 500}, {"apiName": "eservice_engineer_cases", "tenantPrivilege": {"enableObjects": ["EngineerInfoObj"]}, "extProp": {"moreUrl": ""}, "name": "工程师工单", "limit": 1, "nameI18nKey": "eservice_component_engineer_cases", "widgetScope": {"widgetAffinity": "grid"}, "id": "eservice_engineer_cases", "widgetType": 401, "height": 500}, {"apiName": "select_product_order", "tenantPrivilege": {"enableObjects": ["SalesOrderObj"]}, "extProp": {"sourceApiName": "", "ruleApiName": "", "submitBtnLabel": "", "listSourceApiName": "", "targetApiName": "SalesOrderObj"}, "name": "选产品下单", "limit": 1, "nameI18nKey": "paas.udobj.select_product_order_component", "id": "select_product_order", "widgetType": 600}, {"cardId": "dht_bottomNav_me", "name": "我的", "nameI18nKey": "dht.app.widget.bottomNav_me", "id": "dht_bottomNav_me", "widgetType": 600}, {"apiName": "name_component", "tenantPrivilege": {"enableObjects": []}, "extProp": {"name": ""}, "name": "标题", "unDeletable": false, "nameI18nKey": "paas.udobj.name_component", "widgetScope": {"widgetAffinity": "", "widgetAntiAffinity": "tabs|gird"}, "id": "name_component", "widgetType": 401}, {"apiName": "dht_step_progress_bar", "extProp": {"show_header": true, "stepList": [{"_id": "1670899821774123", "name": "订单提交", "isFixed": true, "type": "biz"}, {"_id": "1670899821774124", "name": "订单审核", "type": "approval"}, {"_id": "1670899821774125", "name": "订单发货", "type": "biz", "wheres": [{"connector": "OR", "filters": [{"field_name": "logistics_status", "field_values": ["2", "3"], "operator": "HASANYOF", "value_type": 0}]}]}, {"_id": "1670899821774126", "name": "确认收货", "type": "biz", "wheres": [{"connector": "OR", "filters": [{"field_name": "logistics_status", "field_values": ["5"], "operator": "EQ", "value_type": 0}]}]}, {"_id": "1670899821774127", "name": "完成", "type": "biz", "wheres": [{"connector": "OR", "filters": [{"field_name": "logistics_status", "field_values": ["5"], "operator": "EQ", "value_type": 0}]}]}]}, "name": "步骤条", "limit": 1, "nameI18nKey": "dht.step_progress_bar", "id": "dht_step_progress_bar", "widgetType": 401}, {"apiName": "eservice_downstream_workflow_log", "tenantPrivilege": {"enableObjects": ["CasesObj"]}, "extProp": {"moreUrl": ""}, "name": "终端用户流程查询", "limit": 1, "nameI18nKey": "eservice_downstream_workflow_log", "widgetScope": {"widgetAffinity": "grid"}, "id": "eservice_downstream_workflow_log", "widgetType": 401, "height": 500}, {"apiName": "structured_project_document", "tenantPrivilege": {"enableObjects": ["ProjectObj"], "licenseModuleCodes": ["project_management_app"]}, "extProp": {"related_list_name": "structured_project_document", "ref_object_api_name": "ProjectDocumentObj"}, "name": "结构化文档", "nameI18nKey": "paas.udobj.structured_project_document", "id": "structured_project_document"}, {"apiName": "project_stage_overview", "tenantPrivilege": {"enableObjects": ["ProjectObj"], "licenseModuleCodes": ["project_management_app"]}, "extProp": {"related_list_name": "project_stage_overview", "ref_object_api_name": "ProjectStageObj"}, "name": "项目阶段概览", "nameI18nKey": "paas.udobj.project_stage_overview", "id": "project_stage_overview"}, {"apiName": "eservice_fault_self_query", "extProp": {"moreUrl": ""}, "name": "故障自查", "limit": 1, "nameI18nKey": "eservice_web_service_fault_self_query", "widgetScope": {"widgetAffinity": "grid"}, "id": "eservice_fault_self_query", "widgetType": 401, "height": 500}, {"apiName": "checkinCalendar", "extProp": {"menus": [], "unDeletable": true}, "name": "外勤日历", "unDeletable": true, "limit": 1, "nameI18nKey": "webpage_homepage.checkinCalendar", "id": "checkinCalendar", "widgetType": 600}, {"cardId": "PS_CheckinsPhotoWall", "name": "照片墙", "icon": "fx-icon-obj-app239", "nameI18nKey": "webpage_homepage.CheckinsPhotoWall", "id": "CheckinsPhotoWall", "extProp": {"unDeletable": true, "filter_fields": ["storeName", "owner", "visitDate", "checkinTypeId", "checkinActionIds"], "account_fields": [], "checkin_fields": []}, "widgetType": 104}, {"cardId": "PS_BI_CUSTOMER", "name": "图表", "limit": 2, "nameI18nKey": "paas.udobj.chart_component", "widgetScope": {"widgetAffinity": "grid"}, "id": "<PERSON><PERSON><PERSON><PERSON>", "widgetType": 1}, {"apiName": "web_im", "tenantPrivilege": {"licenseModuleCodes": ["customerservice_app", "customerservice_pro_app"]}, "name": "线上客服", "limit": 1, "nameI18nKey": "", "id": "web_im", "widgetType": 600}, {"extProp": {"showTitle": true, "subTitle": "今天也要努力工作～", "cardImg": "https://a9.fspage.com/FSR/link/eservice/uipaas/widget/employee_card_v2.png"}, "name": "人员卡片", "icon": "fx-icon-obj-contactobj", "nameI18nKey": "eservice_employee_card", "id": "eservice_employee_card-FSAID_989aa3", "widgetType": 301, "height": 315}, {"apiName": "account_main_sub_data_org_hierarchy", "tenantPrivilege": {"enableObjects": ["AccountMainDataObj"]}, "extProp": {"field_section": []}, "name": "客户子数据组织层级关系", "nameI18nKey": "paas.udobj.account_main_sub_data_org_hierarchy", "id": "account_main_sub_data_org_hierarchy", "widgetType": 401}, {"apiName": "qywx_conversion", "tenantPrivilege": {"enableObjects": ["WechatFriendsRecordObj", "WechatGroupObj"], "licenseProductCodes": ["wechat_scrm_private_500_industry", "wechat_scrm_private_100_industry", "wechat_scrm_private_500plus_industry", "wechat_scrm_private_30_industry", "wechat_scrm_private_200_industry", "scrm_wechat_marketing_service_private_industry", "scrm_wechat_marketing_private_industry"]}, "extProp": {}, "name": "企微会话", "nameI18nKey": "paas.udobj.qywx_conversion", "id": "qywx_conversion", "widgetType": 401, "height": 700}, {"apiName": "account_org_distribution", "tenantPrivilege": {"enableObjects": ["AccountObj", "AccountMainDataObj"]}, "extProp": {"field_section": [], "ref_object_api_name": "AccountObj"}, "name": "客户树组织跟进分布", "nameI18nKey": "paas.udobj.account_org_distribution", "id": "account_org_distribution", "widgetType": 401}, {"apiName": "account_relationship_tree", "tenantPrivilege": {"enableObjects": ["AccountObj", "AccountMainDataObj"]}, "extProp": {"field_section": []}, "name": "客户树", "nameI18nKey": "paas.udobj.account_relationship_tree", "id": "account_relationship_tree", "widgetType": 401}, {"cardId": "knowledge_search", "name": "知识搜索", "nameI18nKey": "knowledge.knowledge_search", "id": "knowledge_search", "widgetType": 600}]