# 需求描述
_1、如果是产品需求，贴出需求文档的链接即可。_

_2、如果是技术需求，需提供需求背景和目标。_

## 背景
_对于技术需求，描述该需求产生的背景：系统现状、存在的问题等。_  

## 目标
_对于技术需求，阐述该需求需要达成的目标。_

# 需求分析
_对于技术需求，描述该需求需要达成的目标。_

## 主要功能点
_拆解需求，列出主要功能点，对应tapd上的task。_

## 待确认
_记录需求里不明确或不合理的点，讨论之后将结论同步过来。_

# 系统架构

_对于跨团队、跨服务、多模块等中大型项目，需要提供架构图和架构说明。_

# 详细设计

## 处理流程

_1、对于涉及到多端、多服务、多模块等存在数据流转处理较复杂的功能点，需要提供时序图。_

_2、对于单一模块的业务处理逻辑较复杂的功能点，需要提供流程图。_

_3、对于存在多状态（三个及以上）变化的功能点，需要提供有限状态机图。_

_4、对于存在多实体（三个及以上）领域模型的功能点，需要提供 UML ER 实体图；_

## 数据结构

_表结构、对象描述、组件描述、MQ消息定义等。_

### 表结构设计

1、xx表

### 对象描述

1、xx对象

_将对象描述的json贴到代码块中。_

### 组件描述

1、xx组件

_将组件描述的json贴到代码块中。_

### MQ消息设计

1、xx消息

_将MQ消息的相关信息填入表格中。_

## 接口设计

_1、推荐编写统一的接口文档，将文档链接贴到此处。_

_2、接口文档需包含接口描述、接口URL、入参示例与说明、出参示例与说明。_

_3、如果在文中直接写，建议使用折叠功能_

| 
接口描述

 | 

接口URL

 | 

参数格式

 | 

返回格式

 | 

是否需要配置后台权限

 
| --- | --- | --- | --- | --- |
| ||||| 

接口描述

 |   
 | 

_建议重点强调本次改动的部分_

_（代码块尽量选择折叠模式）_



 | 

_建议重点强调本次改动的部分_

_（代码块尽量选择折叠模式）_



 | _后台管理的接口需要配置接口权限_ |

## 代码设计

_对于重构类的技术需求，需要提供类图、类描述、使用说明等信息。_

## 业务影响

_对于影响范围比较大的改动，需要提供如下信息：_

_1、对业务的影响范围：可能影响哪些服务、哪些功能。_

_2、本次改动是否向下兼容，如果不兼容，业务侧该如何修改，上线环节该如何处置。_

## 通用能力支持

_对于一些通用能力，所有需要都要支持：_

_1、国际化的支持：多语言，多时区，多区域等_

_2、沙盒，更改集的支持_

## 后台菜单配置

_如果是在后台管理新增菜单，需要通过paas管理平台刷一些配置，此处最好将配置内容和配置步骤列出来，菜单接入的参考文档：[【管理后台菜单】新增管理后台菜单接入文档](https://wiki.firstshare.cn/pages/viewpage.action?pageId=139309638)_

示例：

##   
接口路由配置

_以下场景需要修改CEP路由和apibus路由的配置。_

_场景1：新的功能模块，并且是基于Appframework的service模式开发的rest接口。_

_场景2：新的预置对象，并且是基于Appframework的Action和Controller模式开发的rest接口。_

#### CEP路由配置

#### apibus路由配置

## 外部依赖

_如果依赖其他团队，写清楚依赖团队、依赖服务、依赖接口，可附上对方的技术方案链接。_

## 上线方案

_只有独立上线的功能需要此项。_

## 灰度配置

### 灰度1

_将灰度配置的配置文件名称和配置示例与说明填入表格中。_

### 灰度2

## 上线节奏

_明确灰度到全网的节奏，一般都有独立wiki，将链接贴过来即可。_