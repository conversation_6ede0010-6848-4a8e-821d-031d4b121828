{"fields": {"object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "created_by": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "index_name": "crt_time"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "index_name": "is_del"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time"}, "task_id": {"is_index": true, "is_active": true, "description": "任务id", "is_unique": false, "label": "任务id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "task_id", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "biz_id": {"is_index": true, "is_active": true, "description": "业务id", "is_unique": false, "label": "业务id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "biz_id", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 64, "pattern": "", "label": "对象名称", "is_active": true, "api_name": "describe_api_name", "description": "describe_api_name", "status": "released"}, "biz_api_name": {"type": "text", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "业务名称", "api_name": "biz_api_name", "description": "业务名称", "status": "released"}, "record_type": {"is_required": false, "api_name": "record_type", "is_index": false, "status": "new", "is_unique": false, "description": "任务类型", "define_type": "package", "label": "任务类型", "type": "record_type", "options": [{"is_active": true, "api_name": "scheduler_task", "label": "计划任务", "description": "计划任务"}, {"is_active": true, "api_name": "count", "label": "统计字段任务", "description": "统计字段任务"}, {"is_active": true, "api_name": "formula", "label": "计算字段任务", "description": "计算字段任务"}, {"is_active": true, "api_name": "quote", "label": "引用字段任务", "description": "引用字段任务"}]}, "task_describe": {"is_index": false, "is_active": true, "description": "任务描述", "is_unique": false, "label": "任务描述", "type": "long_text", "expression_type": "string", "is_need_convert": false, "is_required": false, "api_name": "task_describe", "is_abstract": true, "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}, "task_describe_default_value": {"is_index": false, "is_active": true, "description": "任务描述默认值", "is_unique": false, "label": "任务描述默认值", "type": "long_text", "expression_type": "string", "is_need_convert": false, "is_required": false, "api_name": "task_describe_default_value", "is_abstract": true, "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}, "task_describe_i18n_key": {"type": "text", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "任务描述i18nkey", "api_name": "task_describe_i18n_key", "description": "任务描述i18nkey", "status": "released"}, "task_describe_params_i18n_key": {"is_index": false, "is_active": true, "description": "国际化key内占位参数列表", "is_unique": false, "label": "国际化key内占位参数列表", "type": "long_text", "expression_type": "json", "is_need_convert": false, "is_required": false, "api_name": "task_describe_params_i18n_key", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}, "task_describe_default_params_value": {"is_index": false, "is_active": true, "description": "国际化默认词条", "is_unique": false, "label": "国际化默认词条", "type": "long_text", "expression_type": "json", "is_need_convert": false, "is_required": false, "api_name": "task_describe_default_params_value", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}, "error_message": {"is_index": false, "is_active": true, "description": "错误信息", "is_unique": false, "label": "错误信息", "type": "long_text", "expression_type": "string", "is_need_convert": false, "is_required": false, "api_name": "error_message", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}, "task_status": {"is_required": true, "api_name": "task_status", "is_index": true, "status": "new", "is_unique": false, "description": "任务状态", "define_type": "package", "label": "任务状态", "type": "select_one", "options": [{"label": "排队中", "value": "be_queuing", "not_usable": false}, {"label": "处理中", "value": "in_process", "not_usable": false}, {"label": "已取消", "value": "canceled", "not_usable": false}, {"label": "已完成", "value": "completed", "not_usable": false}, {"label": "异常结束", "value": "abort", "not_usable": false}]}, "task_create_time": {"type": "date_time", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "任务创建时间", "api_name": "task_create_time", "description": "task_create_time", "status": "released"}, "task_estimated_execution_time": {"type": "date_time", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "任务预计执行时间", "api_name": "task_estimated_execution_time", "description": "task_execution_time", "status": "released"}, "task_execution_time": {"type": "date_time", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "任务开始执行时间", "api_name": "task_execution_time", "description": "task_execution_time", "status": "released"}, "schedule": {"expression_type": "percentile", "is_index": false, "is_active": true, "is_unique": false, "label": "任务进度", "type": "percentile", "default_to_zero": true, "is_required": false, "api_name": "schedule", "define_type": "package", "is_single": false, "is_index_field": false, "is_abstract": true, "help_text": "", "status": "released"}, "task_total_num": {"is_index": true, "length": 15, "description": "总数量", "is_unique": false, "label": "总数量", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "task_total_num", "define_type": "package", "index_name": "version", "round_mode": 4, "status": "released"}, "completed_num": {"is_index": true, "length": 15, "description": "已处理数量", "is_unique": false, "label": "已处理数量", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "completed_num", "define_type": "package", "index_name": "version", "round_mode": 4, "status": "released"}, "end_time": {"type": "date_time", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "任务完成时间", "api_name": "end_time", "description": "end_time", "status": "released"}, "submitted_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "submitted_by", "status": "released", "label": "提交人", "is_active": true}}, "tenant_id": "-100", "api_name": "AsyncTaskMonitorObj", "display_name": "异步任务监控", "package": "CRM", "is_active": true, "release_version": "6.4", "define_type": "internal", "is_deleted": false, "store_table_name": "mt_async_task_monitor"}