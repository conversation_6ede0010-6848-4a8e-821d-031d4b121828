curl -i --request POST \
  --url http://localhost/API/v1/inner/object/ContactObj/action/InsertImportData \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 816915' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 816915' \
  --header 'x-fs-userInfo: 1000' \
  --data '{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsFinalBatch":true,"IsImportPreProcessing":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"apiName":"ContactObj","fileCode":{"md5":"fff62f96042ba802072e7c209990af3d","sha256":"sAJ+8Ghvy5VeldEeBJj20OvXe+9wadUXMrvMuZQzxV8="},"headerExcelCols":[],"jobId":"6853ec157584dc0007f29557","locale":"zh-CN","objectCode":"ContactObj","oldOwnerTeamMember":{},"parameter":{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"isVerify":false,"jobId":"6853ec157584dc0007f29557","objectCode":"ContactObj","oldOwnerTeamMember":{},"operationType":"INVOKE","unionApiNames":[]},"rows":[{"rowNo":2,"邮件1":"<EMAIL>","邮件2":"","社媒联系方式":"","Whatsapp账号":"","邮件3":"","邮件4":"","负责人（必填）":"Sophy殷文杰","邮件5":"","备注":"","关键决策人":"","普通成员-只读":"","名片":"","业务类型（必填）":"预设业务类型","Linkedin账号":"","职务":"","电话1":"","电话2":"","性别":"","姓名（必填）":"NONE","客户名称":"Grupo Beca","普通成员-读写":"","手机2":"","Facebook账号":"","手机1":"","微信账号":""}],"supportFieldMapping":false,"unionApiNames":[],"user":{"ea":"shtbznkj2025","locale":"zh-CN","tenantId":"816915","userId":"1000"}}'


  curl -i --request POST \
  --url http://localhost/API/v1/inner/object/ContactObj/action/InsertImportData \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 816915' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 816915' \
  --header 'x-fs-userInfo: 1000' \
  --data '{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsFinalBatch":true,"IsImportPreProcessing":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"apiName":"ContactObj","fileCode":{"md5":"26d505c43e5fa980401b101aa5c06c9e","sha256":"DaNj3mRh8XkX0WAcj4DXWC2k1ILJePbC8NMZgWxlCrM="},"headerExcelCols":[],"jobId":"685512257584dc0007f4a27d","locale":"zh-CN","objectCode":"ContactObj","oldOwnerTeamMember":{},"parameter":{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"isVerify":false,"jobId":"685512257584dc0007f4a27d","objectCode":"ContactObj","oldOwnerTeamMember":{},"operationType":"INVOKE","unionApiNames":[]},"rows":[{"rowNo":2,"邮件1":"<EMAIL>","邮件2":"","社媒联系方式":"","Whatsapp账号":"","邮件3":"","邮件4":"","负责人（必填）":"Angel黄可昕","邮件5":"","备注":"","关键决策人":"","普通成员-只读":"","名片":"","业务类型（必填）":"预设业务类型","Linkedin账号":"","职务":"Purchasing Department","电话1":"","电话2":"","性别":"","姓名（必填）":"Filip Kalas","客户名称":"Komputronik S.A.","普通成员-读写":"","手机2":"","Facebook账号":"","手机1":"","微信账号":""}],"supportFieldMapping":false,"unionApiNames":[],"user":{"ea":"shtbznkj2025","locale":"zh-CN","tenantId":"816915","userId":"1003"}}'



  curl -i --request POST \
  --url http://localhost/API/v1/rest/object/object_f4i8g__c/action/InsertImportData \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 700609' \
  --header 'x-fs-userInfo: 1001' \
  --data '{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsFinalBatch":true,"IsImportPreProcessing":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"apiName":"object_f4i8g__c","detailInfo":[],"fileCode":{"md5":"e8351a51f02ffcd73c0089df76670d05","sha256":"A/hmO0V8ApObDu8cMhvsi/5wDZt4qjqrIL6fM7ZY33I="},"headerExcelCols":[{"colIndex":"0","colName":"主属性"},{"colIndex":"1","colName":"数字"},{"colIndex":"2","colName":"金额-增量更新"},{"colIndex":"3","colName":"导入V2-不映射该字段"},{"colIndex":"4","colName":"负责人"},{"colIndex":"5","colName":"人员-普通成员-只读"},{"colIndex":"6","colName":"人员-普通成员-读写"},{"colIndex":"7","colName":"部门-普通成员-只读"},{"colIndex":"8","colName":"部门-普通成员-读写"},{"colIndex":"9","colName":"用户组-普通成员-只读"},{"colIndex":"10","colName":"用户组-普通成员-读写"},{"colIndex":"11","colName":"角色-普通成员-只读"},{"colIndex":"12","colName":"角色-普通成员-读写"},{"colIndex":"13","colName":"富文本"},{"colIndex":"14","colName":"协同富文本"},{"colIndex":"15","colName":"长文本1"},{"colIndex":"16","colName":"长文本2"},{"colIndex":"17","colName":"多选-子"},{"colIndex":"18","colName":"国家"},{"colIndex":"19","colName":"省"},{"colIndex":"20","colName":"市"},{"colIndex":"21","colName":"区"},{"colIndex":"22","colName":"乡镇"},{"colIndex":"23","colName":"详细地址"},{"colIndex":"24","colName":"定位"},{"colIndex":"25","colName":"关联-客户地址"},{"colIndex":"26","colName":"外部人员"},{"colIndex":"27","colName":"外部部门"},{"colIndex":"28","colName":"日期时间（多时区无关）"},{"colIndex":"29","colName":"日期时间（多时区有关）"}],"jobId":"nkoy6sx2kcsm1zkits8u0oa7","locale":"zh-CN","masterInfo":{"apiName":"object_f4i8g__c","fieldMapping":[{"apiName":"name","colIndex":"0"},{"apiName":"field_da1ZI__c","colIndex":"1"},{"apiName":"field_4963m__c","colIndex":"2"},{"apiName":"owner","colIndex":"4"},{"apiName":"0_4_1","colIndex":"5","importFieldMark":"TEAM_MEMBER"},{"apiName":"0_4_2","colIndex":"6","importFieldMark":"TEAM_MEMBER"},{"apiName":"field_Sps59__c","colIndex":"13"},{"apiName":"field_nj8Y9__c","colIndex":"14"},{"apiName":"field_1KqOG__c","colIndex":"15"},{"apiName":"field_RhemN__c","colIndex":"16"},{"apiName":"field_Vg3O5__c","colIndex":"17"},{"apiName":"field_44l9P__c","colIndex":"18"},{"apiName":"field_Jb55k__c","colIndex":"19"},{"apiName":"field_38r55__c","colIndex":"20"},{"apiName":"field_QffFK__c","colIndex":"21"},{"apiName":"field_Jbb3k__c","colIndex":"22"},{"apiName":"field_gu1q0__c","colIndex":"23"},{"apiName":"field_fB4kM__c","colIndex":"24"},{"apiName":"field_9xR1s__c","colIndex":"25"},{"apiName":"field_A61Rj__c","colIndex":"26"},{"apiName":"field_l5qtG__c","colIndex":"27"},{"apiName":"date_time__c","colIndex":"28"},{"apiName":"date_time_multi_timezone__c","colIndex":"29"}]},"objectCode":"object_f4i8g__c","parameter":{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"isVerify":false,"jobId":"nkoy6sx2kcsm1zkits8u0oa7","objectCode":"object_f4i8g__c","operationType":"INVOKE"},"rows":[{"0":"导入V2-01.voeef","1":"","2":"","3":"","4":"zhangman","5":"fanby","6":"crmadmin","7":"","8":"","9":"","13":"我是新建导入的普通富文本1","14":"我是新建导入的协同\"\"富文本","15":"","16":"","17":"子-2.1|子-2.2|子-3.3","18":"中国","19":"黑龙江省","20":"哈尔滨市","21":"道里区","22":"榆树镇","23":"黑龙江省哈尔滨市道里区铁路街1号哈尔滨站","24":"黑龙江省哈尔滨市道里区铁路街1号哈尔滨站","25":"Addr.2025-06-23_003010","26":"fktest299(590274).admin01","27":"fktest299(590274).fktest299(590274)","rowNo":2},{"0":"导入V2-02.qlb3w","1":"","2":"","3":"","4":"zhangman","5":"","6":"","7":"","8":"","9":"","13":"","14":"","15":"我是新建导入的长文本1","16":"我是新建导入的长文本2","17":"子-1.1|子-2.2|子-3.1","26":"fktest299(590274).bi","27":"fktest299(590274).停用的互联部门","28":"2025-05-30 16:40","29":"2025-05-31 17:50","rowNo":3}],"supportFieldMapping":true}'



  curl -i --location --request POST 'http://localhost/API/v1/rest/object_solitude__c/controller/FindBySearchTemplateQuery?traceId=E-E.74255.1000-1750688896' \
--header 'x-fs-userInfo: 1000' \
--header 'x-fs-ei: 74255' \
--header 'content-type: application/json' \
--data-raw '{
    "custom": false,
    "describeVersionMap": null,
    "include_layout": true,
    "search_query_info": "{\"filters\":[{\"connector\":\"AND\",\"fieldName\":\"_id\",\"fieldNum\":0,\"fieldValues\":[\"6836fe5823e7ea00076d4937\"],\"includeVariable\":false,\"objectReference\":false,\"operator\":\"IN\",\"valueType10\":false,\"valueType11\":false,\"valueType12\":false,\"valueType14\":false,\"valueType15\":false,\"valueType16\":false,\"valueType17\":false,\"valueType18\":false,\"valueType19\":false,\"valueType20\":false,\"valueType21\":false,\"valueType22\":false,\"valueType23\":false,\"valueType24\":false,\"valueType27\":false}],\"limit\":6,\"offset\":0,\"orders\":[],\"permissionType\":0,\"specialSearchAfterFlag\":false,\"whatFieldApiNames\":[],\"wheres\":[]}",
    "object_describe_api_name": "object_solitude__c",
    "search_template_id": null,
    "include_org_info": false,
    "keyword": null,
    "id_list": null,
    "field_projection": null,
    "ignore_scene_filter": null,
    "find_explicit_total_num": null,
    "replace_option_value": false,
    "check_permission": true,
    "get_data_only": true,
    "include_invalid_data": false,
    "include_describe": true,
    "include_button": true,
    "search_template_type": null,
    "tag_operator": null,
    "tags": null,
    "search_template_api_name": null,
    "ignore_scene_record_type": false,
    "need_return_count_num": true,
    "filter_product_ids": null,
    "need_tag": false,
    "skip_international_init_context": false,
    "search_rich_text_extra": true
}'



curl -i --request POST \
  --url http://localhost/API/v1/rest/object/object_f4i8g__c/action/InsertImportData \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 700609' \
  --header 'x-fs-userInfo: 1001' \
  --data '{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsFinalBatch":true,"IsImportPreProcessing":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"apiName":"object_f4i8g__c","detailInfo":[],"fileCode":{"md5":"e8351a51f02ffcd73c0089df76670d05","sha256":"A/hmO0V8ApObDu8cMhvsi/5wDZt4qjqrIL6fM7ZY33I="},"headerExcelCols":[{"colIndex":"0","colName":"主属性"},{"colIndex":"1","colName":"数字"},{"colIndex":"2","colName":"金额-增量更新"},{"colIndex":"3","colName":"导入V2-不映射该字段"},{"colIndex":"4","colName":"负责人"},{"colIndex":"5","colName":"人员-普通成员-只读"},{"colIndex":"6","colName":"人员-普通成员-读写"},{"colIndex":"7","colName":"部门-普通成员-只读"},{"colIndex":"8","colName":"部门-普通成员-读写"},{"colIndex":"9","colName":"用户组-普通成员-只读"},{"colIndex":"10","colName":"用户组-普通成员-读写"},{"colIndex":"11","colName":"角色-普通成员-只读"},{"colIndex":"12","colName":"角色-普通成员-读写"},{"colIndex":"13","colName":"富文本"},{"colIndex":"14","colName":"协同富文本"},{"colIndex":"15","colName":"长文本1"},{"colIndex":"16","colName":"长文本2"},{"colIndex":"17","colName":"多选-子"},{"colIndex":"18","colName":"国家"},{"colIndex":"19","colName":"省"},{"colIndex":"20","colName":"市"},{"colIndex":"21","colName":"区"},{"colIndex":"22","colName":"乡镇"},{"colIndex":"23","colName":"详细地址"},{"colIndex":"24","colName":"定位"},{"colIndex":"25","colName":"关联-客户地址"},{"colIndex":"26","colName":"外部人员"},{"colIndex":"27","colName":"外部部门"},{"colIndex":"28","colName":"日期时间（多时区无关）"},{"colIndex":"29","colName":"日期时间（多时区有关）"}],"jobId":"ibjnzgwal0n8j7sew4f36r9a","locale":"zh-CN","masterInfo":{"apiName":"object_f4i8g__c","fieldMapping":[{"apiName":"name","colIndex":"0"},{"apiName":"field_da1ZI__c","colIndex":"1"},{"apiName":"field_4963m__c","colIndex":"2"},{"apiName":"owner","colIndex":"4"},{"apiName":"0_4_1","colIndex":"5","importFieldMark":"TEAM_MEMBER"},{"apiName":"0_4_2","colIndex":"6","importFieldMark":"TEAM_MEMBER"},{"apiName":"field_Sps59__c","colIndex":"13"},{"apiName":"field_nj8Y9__c","colIndex":"14"},{"apiName":"field_1KqOG__c","colIndex":"15"},{"apiName":"field_RhemN__c","colIndex":"16"},{"apiName":"field_Vg3O5__c","colIndex":"17"},{"apiName":"field_44l9P__c","colIndex":"18"},{"apiName":"field_Jb55k__c","colIndex":"19"},{"apiName":"field_38r55__c","colIndex":"20"},{"apiName":"field_QffFK__c","colIndex":"21"},{"apiName":"field_Jbb3k__c","colIndex":"22"},{"apiName":"field_gu1q0__c","colIndex":"23"},{"apiName":"field_fB4kM__c","colIndex":"24"},{"apiName":"field_9xR1s__c","colIndex":"25"},{"apiName":"field_A61Rj__c","colIndex":"26"},{"apiName":"field_l5qtG__c","colIndex":"27"},{"apiName":"date_time__c","colIndex":"28"},{"apiName":"date_time_multi_timezone__c","colIndex":"29"}]},"objectCode":"object_f4i8g__c","parameter":{"ImportType":0,"IsApprovalFlowEnabled":false,"IsCheckOutOwner":false,"IsEmptyValueToUpdate":false,"IsRemoveOutTeamMember":false,"IsUnionDuplicateChecking":false,"IsUpdateOwner":false,"IsWorkFlowEnabled":false,"MatchingType":2,"isVerify":false,"jobId":"ibjnzgwal0n8j7sew4f36r9a","objectCode":"object_f4i8g__c","operationType":"INVOKE"},"rows":[{"0":"导入V2-01.fttod","1":"","2":"","3":"","4":"zhangman","5":"fanby","6":"crmadmin","7":"","8":"","9":"","13":"我是新建导入的普通富文本1","14":"我是新建导入的协同\"\"富文本","15":"","16":"","17":"子-2.1|子-2.2|子-3.3","18":"中国","19":"黑龙江省","20":"哈尔滨市","21":"道里区","22":"榆树镇","23":"黑龙江省哈尔滨市道里区铁路街1号哈尔滨站","24":"黑龙江省哈尔滨市道里区铁路街1号哈尔滨站","25":"Addr.2025-06-30_003049","26":"fktest299(590274).admin01","27":"fktest299(590274).fktest299(590274)","rowNo":2},{"0":"导入V2-02.ks2c0","1":"","2":"","3":"","4":"zhangman","5":"","6":"","7":"","8":"","9":"","13":"","14":"","15":"我是新建导入的长文本1","16":"我是新建导入的长文本2","17":"子-1.1|子-2.2|子-3.1","26":"fktest299(590274).bi","27":"fktest299(590274).停用的互联部门","28":"2025-05-30 16:40","29":"2025-05-31 17:50","rowNo":3}],"supportFieldMapping":true}'