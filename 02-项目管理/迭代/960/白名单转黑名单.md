# 白名单转黑名单机制优化

## 1. 背景

产品委员会正在推进【预置对象适配平台能力】专项。历史上因特殊原因，部分能力采用白名单机制（指定哪些对象放开特定能力），导致适配过程繁琐。为了减轻开发负担并提升适配效率，平台将统一将这些能力从白名单机制改为黑名单机制。

我们需要各团队配合梳理需放入黑名单的对象（即不支持/暂不适配此能力的对象）。

## 2. 设计方案

### 2.1 以UI事件为例

**现状（白名单机制）：**
```
# ui事件隐藏从对象灰度开关控制
ui_event_hidden_gray_object={
  "udobj":["ALL"],
  "CasesObj":["ALL"],
  "NewOpportunityObj":["ALL"],
  "PreventiveMaintenanceObj":${variables_eservice.service_plan_ui_event_hidden_obj_ei_list},
  "SalesOrderObj":["ALL"],
  "MarketingEventObj":["ALL"],
  "TPMActivityBudgetObj":["ALL"],
  "TPMActivityBudgetAdjustObj":["ALL"],
  "AccountObj":["ALL"],
  "ReceiveMaterialBillObj":["ALL"],
  "RefundMaterialBillObj":["ALL"],
  "InvoiceApplicationLinesObj":["ALL"],
  "NewOpportunityObj":["ALL"],
  "SalesOrderProductObj":["ALL"],
  "DeliveryNoteProductObj":["ALL"],
  "TPMDealerActivityCostObj":["ALL"],
  "LeadsObj":["ALL"],
  "QuoteObj":["ALL"],
  "SaleContractObj":["ALL"],
  "JournalObj":["753006"],
  "TPMActivityObj":["ALL"],
  "ServiceAgreementObj":["ALL"],
  "TPMActivityUnifiedCaseObj":["ALL"],
  "ActiveRecordObj":["ALL"],
  "LeaveApplicationObj":["781715","779366"],
  "TimeSheetObj":["ALL"]
}
```

**优化方案（引入黑名单机制）：**
增加黑名单配置：
```
ui_event_black_object=SalesOrderObj,QuoteObj,DeliveryNoteObj,StockCheckNoteObj,GoodsReceivedNoteObj,PurchaseOrderObj,PurchaseReturnNoteObj,FAccountAuthorizationObj,SalesInvoiceObj,MatchNoteObj,PaymentObj,ActiveRecordObj,TelesalesRecordObj,ServiceLogObj,JournalObj,ScheduleObj,BlogObj,AnnounceObj,ApprovalFormOb,LeaveApplicationObj,OvertimeApplicationObj,ReimbursementObj,TravelApplicationObj,TravelReimbursementObj,TPMBudgetCarryForwardObj,TPMBudgetDisassemblyObj
```

### 2.2 决策流程

```mermaid
flowchart TD
    A[开始] --> B{对象是否在黑名单中?}
    B -->|否| C[支持此功能]
    B -->|是| D{对象是否在白名单中?}
    D -->|否| E[不支持此功能]
    D -->|是| F{符合白名单配置?}
    F -->|是| G[支持此功能]
    F -->|否| H[不支持此功能]
```

### 2.3 处理逻辑

1. 首先判断对象是否在黑名单中：
   - 不在黑名单 → 直接支持此功能
   - 在黑名单 → 继续判断

2. 判断是否在白名单中：
   - 不在白名单 → 不支持此功能
   - 在白名单 → 继续判断

3. 根据白名单配置判断企业是否支持：
   - 符合配置条件 → 支持此功能
   - 不符合配置条件 → 不支持此功能

## 3. 实现