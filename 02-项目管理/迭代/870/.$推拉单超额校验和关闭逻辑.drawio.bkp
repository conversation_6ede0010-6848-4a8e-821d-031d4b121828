<mxfile host="Electron" modified="2023-06-30T06:00:40.932Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.2.8 Chrome/112.0.5615.165 Electron/24.2.0 Safari/537.36" etag="i40zaPZ58Ard4VbAbUlr" version="21.2.8" type="device">
  <diagram name="Page-1" id="13e1069c-82ec-6db2-03f1-153e76fe0fe0">
    <mxGraphModel dx="849" dy="1593" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="UqCqAOurar4fwznB28a1-22" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-23" target="UqCqAOurar4fwznB28a1-28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-23" value="转换数据新建保存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="460" y="-680" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-24" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-28" target="UqCqAOurar4fwznB28a1-30" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-25" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-24" vertex="1" connectable="0">
          <mxGeometry x="-0.4889" y="-4" relative="1" as="geometry">
            <mxPoint x="4" y="9" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-26" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-28" target="UqCqAOurar4fwznB28a1-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-27" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-26" vertex="1" connectable="0">
          <mxGeometry x="-0.3472" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-28" value="新建init方法中判断是否为转换来的数据" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="480" y="-570" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-30" target="UqCqAOurar4fwznB28a1-39" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-30" value="通过optioninfo参数中数据id及转换规则初始化源对象主从数据及转换规则" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="460" y="-445" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-31" value="走原逻辑" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="620" y="-560" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-33" target="UqCqAOurar4fwznB28a1-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7hVdz4U6ljP2hgBLB-gY-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="UqCqAOurar4fwznB28a1-33" target="7hVdz4U6ljP2hgBLB-gY-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-33" value="根据转换规则条件回写源单数据关闭字段值" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="460" y="-110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-34" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-39" target="UqCqAOurar4fwznB28a1-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-35" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-34" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-36" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-39" target="UqCqAOurar4fwznB28a1-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="520" y="-230" />
              <mxPoint x="520" y="-230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-37" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-36" vertex="1" connectable="0">
          <mxGeometry x="-0.52" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-38" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-39" target="UqCqAOurar4fwznB28a1-43" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-39" value="validate中超额检查是否通过" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="480" y="-340" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-40" value="超额提示" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="620" y="-330" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-42" target="UqCqAOurar4fwznB28a1-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-42" value="doSaveData()保存主从数据" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="460" y="-210" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-43" value="新增超额检查具体实现参考ValidationRuleCalculator实现" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="250" y="-345" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-45" target="UqCqAOurar4fwznB28a1-47" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-45" value="after逻辑" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="460" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-47" target="UqCqAOurar4fwznB28a1-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-47" value="记修改记录（需标识为转换数据）" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="460" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-48" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;" parent="1" vertex="1">
          <mxGeometry x="505" y="200" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7hVdz4U6ljP2hgBLB-gY-1" value="使用ObjectDataFilter工具类根据转换规则关闭逻辑条件过滤数据，满足条件的数据更新其关闭字段标识" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="650" y="-125" width="160" height="90" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
