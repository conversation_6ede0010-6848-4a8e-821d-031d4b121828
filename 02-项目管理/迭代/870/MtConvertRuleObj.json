{"fields": {"name": {"is_index": true, "is_active": true, "description": "转换规则主属性", "is_unique": false, "label": "转换规则主属性", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 200, "status": "released"}, "api_name": {"is_index": true, "is_active": true, "description": "转换规则apiName", "is_unique": false, "label": "转换规则apiName", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "api_name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 100, "status": "released"}, "rule_api_name": {"is_index": true, "is_active": true, "description": "规则的apiName", "is_unique": false, "label": "规则的apiName", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "rule_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 100, "status": "released"}, "source_object_describe_api_name": {"is_index": true, "is_active": true, "description": "源对象apiName", "is_unique": false, "label": "源对象apiName", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "source_object_describe_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "target_object_describe_api_name": {"is_index": true, "is_active": true, "description": "目标对象apiName", "is_unique": false, "label": "目标对象apiName", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "target_object_describe_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "define_type": {"is_index": true, "is_active": true, "description": "定义类型", "is_unique": false, "label": "定义类型", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "define_type", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 20, "status": "released"}, "master_convert_rule_api_name": {"is_index": true, "is_active": true, "description": "主对象转换规则", "is_unique": false, "label": "主对象转换规则", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "master_convert_rule_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "md_type": {"is_index": false, "create_time": 1636980620734, "length": 2, "description": "主从关系类型", "is_unique": false, "label": "主从关系类型", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": true, "api_name": "md_type", "define_type": "package", "index_name": "version", "round_mode": 4, "status": "released"}, "associated_field_api_name": {"is_index": true, "is_active": true, "description": "关联字段apiName", "is_unique": false, "label": "关联字段apiName", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "associated_field_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 100, "status": "released"}, "wheres": {"is_index": true, "is_active": true, "description": "转换规则适用范围", "is_unique": false, "label": "转换规则适用范围", "type": "long_text", "expression_type": "json", "is_need_convert": false, "is_required": false, "api_name": "wheres", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}, "where_type": {"is_index": true, "is_active": true, "description": "范围类型", "is_unique": false, "label": "范围类型", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "where_type", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "message": {"is_index": true, "is_active": true, "description": "不满足条件时提示用户", "is_unique": false, "label": "不满足条件时提示用户", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "message", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 1000, "status": "released"}, "is_back_write": {"type": "true_or_false", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "是否设置回写规则", "api_name": "is_back_write", "description": "是否设置回写规则", "default_value": false, "status": "released"}, "back_write_model": {"is_index": true, "is_active": true, "description": "回写模式", "is_unique": false, "label": "回写模式", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "back_write_model", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 20, "status": "released"}, "is_close_logic": {"type": "true_or_false", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "是否设置关闭逻辑", "api_name": "is_close_logic", "description": "是否设置关闭逻辑", "default_value": false, "status": "released"}, "close_strategy": {"is_index": false, "create_time": 1636980620734, "length": 2, "description": "关闭策略", "is_unique": false, "label": "关闭策略", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "close_strategy", "define_type": "package", "index_name": "version", "round_mode": 4, "status": "released"}, "close_field_api_name": {"is_index": true, "is_active": true, "description": "关闭标识字段apiName", "is_unique": false, "label": "关闭标识字段apiName", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "close_field_api_name", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 100, "status": "released"}, "close_field_value": {"is_index": true, "is_active": true, "description": "关闭字段值", "is_unique": false, "label": "关闭字段值", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "close_field_value", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 500, "status": "released"}, "close_wheres": {"is_index": true, "is_active": true, "description": "关闭条件", "is_unique": false, "label": "关闭条件", "type": "long_text", "expression_type": "json", "is_need_convert": false, "is_required": false, "api_name": "close_wheres", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}, "excess_check_mode": {"is_index": true, "is_active": true, "description": "超额检查模式", "is_unique": false, "label": "超额检查模式", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "excess_check_mode", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 20, "status": "released"}, "excess_condition": {"is_index": true, "is_active": true, "description": "超额条件", "is_unique": false, "label": "超额条件", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "excess_condition", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 1000, "status": "released"}, "default_to_zero": {"type": "true_or_false", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "默认是否为零", "api_name": "default_to_zero", "description": "默认是否为零", "default_value": false, "status": "released"}, "excess_prompt": {"is_index": true, "is_active": true, "description": "超额提醒", "is_unique": false, "label": "超额提醒", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "excess_prompt", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 1000, "status": "released"}, "remark": {"is_index": true, "is_active": true, "description": "备注", "is_unique": false, "label": "备注", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "remark", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 500, "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name"}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "index_name": "crt_time"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "index_name": "is_del"}, "is_active": {"type": "true_or_false", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "是否启用", "api_name": "is_active", "description": "是否启用", "default_value": true, "status": "released"}}, "tenant_id": "-100", "is_udef": null, "api_name": "MtConvertRuleObj", "display_name": "转换规则配置", "package": "CRM", "record_type": null, "is_active": true, "release_version": "6.4", "plural_name": null, "define_type": "internal", "is_deleted": false, "store_table_name": "mt_convert_rule", "module": null, "icon_index": null, "description": null, "visible_scope": null, "is_auto_increment": null}