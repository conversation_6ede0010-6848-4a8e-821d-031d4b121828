<mxfile host="Electron" modified="2023-07-18T11:25:33.930Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.1 Chrome/112.0.5615.204 Electron/24.6.1 Safari/537.36" etag="dgYy0dqbiP7kBPs6a0jL" version="21.6.1" type="device">
  <diagram name="Page-1" id="13e1069c-82ec-6db2-03f1-153e76fe0fe0">
    <mxGraphModel dx="2126" dy="743" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-22" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="Ub_QTpmHPdV00I0JZ1s9-18" target="Ub_QTpmHPdV00I0JZ1s9-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-18" value="" style="rounded=0;whiteSpace=wrap;html=1;opacity=50;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="-160" y="905" width="180" height="215" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-4" value="" style="rounded=0;whiteSpace=wrap;html=1;opacity=50;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="-160" y="640" width="180" height="205" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-22" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-23" target="UqCqAOurar4fwznB28a1-28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-23" value="转换数据新建保存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="-130" y="60" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-24" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-28" target="UqCqAOurar4fwznB28a1-30" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-25" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-24" vertex="1" connectable="0">
          <mxGeometry x="-0.4889" y="-4" relative="1" as="geometry">
            <mxPoint x="4" y="9" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-26" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-28" target="UqCqAOurar4fwznB28a1-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-27" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-26" vertex="1" connectable="0">
          <mxGeometry x="-0.3472" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-28" value="通过optionInfo中参数判断是否为转换来的数据" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="-110" y="170" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-30" target="Ub_QTpmHPdV00I0JZ1s9-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-30" value="新建init方法中增加初始化转换规则逻辑" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="-130" y="295" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-31" value="走原逻辑" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="30" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Ub_QTpmHPdV00I0JZ1s9-4" target="Ub_QTpmHPdV00I0JZ1s9-18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="UqCqAOurar4fwznB28a1-33" target="Ub_QTpmHPdV00I0JZ1s9-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-33" value="根据转换规则条件回写源单数据关闭字段值" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="-130" y="756.25" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-34" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-39" target="UqCqAOurar4fwznB28a1-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-35" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-34" vertex="1" connectable="0">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="14" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-36" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="UqCqAOurar4fwznB28a1-39" target="Ub_QTpmHPdV00I0JZ1s9-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
            <mxPoint x="-50" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-37" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UqCqAOurar4fwznB28a1-36" vertex="1" connectable="0">
          <mxGeometry x="-0.52" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-38" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="UqCqAOurar4fwznB28a1-39" target="UqCqAOurar4fwznB28a1-43" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-160" y="550.0000000000002" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="UqCqAOurar4fwznB28a1-39" target="Ub_QTpmHPdV00I0JZ1s9-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint y="550" />
              <mxPoint y="410" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-39" value="validate中超额检查是否通过(放在验证规则前)" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="-110" y="510" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-40" value="超额提示" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="50" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-42" target="UqCqAOurar4fwznB28a1-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-38" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-42" target="Ub_QTpmHPdV00I0JZ1s9-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-42" value="doSaveData()保存主从数据" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="-130" y="668.75" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-43" value="新增超额检查具体实现「ConvertRuleCalculator」参考ValidationRuleCalculator实现" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-320" y="505" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-45" value="触发审批&lt;br&gt;触发相关对象审批&lt;br&gt;...等" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="-130" y="1027.5" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="Ub_QTpmHPdV00I0JZ1s9-18" target="UqCqAOurar4fwznB28a1-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-90" y="1135" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-23" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UqCqAOurar4fwznB28a1-47" target="UqCqAOurar4fwznB28a1-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-47" value="记修改记录（需标识为转换数据）" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="-130" y="937.5" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UqCqAOurar4fwznB28a1-48" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;" parent="1" vertex="1">
          <mxGeometry x="-85" y="1175" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="Ub_QTpmHPdV00I0JZ1s9-4" target="Ub_QTpmHPdV00I0JZ1s9-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-7" value="doAct()方法" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;rounded=0;opacity=50;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="-340" y="702.5" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-13" value="在同一个事务中" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.annotation_2;align=left;labelPosition=right;pointerEvents=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="692.5" width="50" height="100" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-21" value="after()方法" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=0;opacity=50;" parent="1" vertex="1">
          <mxGeometry x="-340" y="972.5" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Ub_QTpmHPdV00I0JZ1s9-24" target="UqCqAOurar4fwznB28a1-39" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-70" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-24" value="初始化规则中涉及的源对象主从描述以及数据" style="whiteSpace=wrap;html=1;fillColor=#ffcd28;strokeColor=#d79b00;rounded=1;shadow=1;gradientColor=#ffa500;" parent="1" vertex="1">
          <mxGeometry x="-130" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Ub_QTpmHPdV00I0JZ1s9-30" value="&lt;br&gt;源对象主对象数据作为主，保存的目标对象及数据（主从）（&lt;b style=&quot;border-color: var(--border-color);&quot;&gt;&lt;font style=&quot;border-color: var(--border-color);&quot; color=&quot;#ff6680&quot;&gt;需要和&lt;/font&gt;&lt;/b&gt;&lt;br&gt;&lt;b style=&quot;border-color: var(--border-color);&quot;&gt;&lt;font style=&quot;border-color: var(--border-color);&quot; color=&quot;#ff6680&quot;&gt;数据库里的数据进行合并&lt;/font&gt;&lt;/b&gt;）调用&lt;br&gt;MetaDataComputeService#calculateCountFieldsInMemory方法&lt;br&gt;在内存中计算源对象的统计字段。" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.annotation_2;align=left;labelPosition=right;pointerEvents=1;" parent="1" vertex="1">
          <mxGeometry x="20" y="360" width="50" height="100" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
