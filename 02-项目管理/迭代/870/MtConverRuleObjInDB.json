{"fields": {"remark": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "备注", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "t_18", "max_length": 500, "is_index": false, "is_active": true, "create_time": 1689143790463, "is_encrypted": false, "label": "备注", "is_need_convert": false, "api_name": "remark", "_id": "64ae49ee59adce0001439e27", "is_index_field": false, "status": "released"}, "excess_condition": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "超额条件", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_1", "max_length": 1000, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "超额条件", "is_need_convert": false, "api_name": "excess_condition", "_id": "64ad141b9166820001188758", "is_index_field": false, "status": "released"}, "associated_field_api_name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "关联字段apiName", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "t_3", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "关联字段apiName", "is_need_convert": false, "api_name": "associated_field_api_name", "_id": "64ad141b916682000118875a", "is_index_field": false, "status": "released"}, "source_object_describe_api_name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "源对象apiName", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "t_4", "max_length": 50, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "源对象apiName", "is_need_convert": false, "api_name": "source_object_describe_api_name", "_id": "64ad141b916682000118875b", "is_index_field": false, "status": "released"}, "default_to_zero": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "默认是否为零", "is_unique": false, "type": "true_or_false", "is_required": false, "options": [], "define_type": "package", "is_single": false, "index_name": "b_1", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "default_value": false, "label": "默认是否为零", "is_need_convert": false, "api_name": "default_to_zero", "_id": "64ad141b916682000118875d", "is_index_field": false, "status": "released"}, "is_deleted": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "is_deleted", "is_unique": false, "type": "true_or_false", "is_required": false, "options": [], "define_type": "system", "is_single": false, "index_name": "is_del", "is_index": false, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "default_value": false, "label": "is_deleted", "is_need_convert": false, "api_name": "is_deleted", "_id": "64ad141b916682000118875e", "is_index_field": false, "status": "released"}, "wheres": {"expression_type": "json", "describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "转换规则适用范围", "is_unique": false, "type": "long_text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_6", "max_length": 10000, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "转换规则适用范围", "is_need_convert": false, "api_name": "wheres", "_id": "64ad141b916682000118875f", "is_index_field": false, "status": "released"}, "is_close_logic": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "是否设置关闭逻辑", "is_unique": false, "type": "true_or_false", "is_required": false, "options": [], "define_type": "package", "is_single": false, "index_name": "b_2", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "default_value": false, "label": "是否设置关闭逻辑", "is_need_convert": false, "api_name": "is_close_logic", "_id": "64ad141b9166820001188760", "is_index_field": false, "status": "released"}, "close_wheres": {"expression_type": "json", "describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "关闭条件", "is_unique": false, "type": "long_text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_7", "max_length": 10000, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "关闭条件", "is_need_convert": false, "api_name": "close_wheres", "_id": "64ad141b9166820001188761", "is_index_field": false, "status": "released"}, "define_type": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "定义类型", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "t_8", "max_length": 20, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "定义类型", "is_need_convert": false, "api_name": "define_type", "_id": "64ad141b9166820001188763", "is_index_field": false, "status": "released"}, "master_convert_rule_api_name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "主对象转换规则", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_9", "max_length": 50, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "主对象转换规则", "is_need_convert": false, "api_name": "master_convert_rule_api_name", "_id": "64ad141b9166820001188764", "is_index_field": false, "status": "released"}, "where_type": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "范围类型", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_5", "max_length": 50, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "范围类型", "is_need_convert": false, "api_name": "where_type", "_id": "64ad141b916682000118875c", "is_index_field": false, "status": "released"}, "excess_check_mode": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "超额检查模式", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_10", "max_length": 20, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "超额检查模式", "is_need_convert": false, "api_name": "excess_check_mode", "_id": "64ad141b9166820001188765", "is_index_field": false, "status": "released"}, "is_back_write": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "是否设置回写规则", "is_unique": false, "type": "true_or_false", "is_required": false, "options": [], "define_type": "package", "is_single": false, "index_name": "b_3", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "default_value": false, "label": "是否设置回写规则", "is_need_convert": false, "api_name": "is_back_write", "_id": "64ad141b9166820001188766", "is_index_field": false, "status": "released"}, "last_modified_time": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "last_modified_time", "is_unique": false, "type": "date_time", "is_required": false, "define_type": "system", "is_single": false, "index_name": "md_time", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "最后修改时间", "time_zone": "", "is_need_convert": false, "api_name": "last_modified_time", "date_format": "yyyy-MM-dd HH:mm:ss", "_id": "64ad141b9166820001188767", "is_index_field": false, "status": "released"}, "is_active": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "是否启用", "is_unique": false, "type": "true_or_false", "is_required": true, "options": [], "define_type": "package", "is_single": false, "index_name": "b_4", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "default_value": true, "label": "是否启用", "is_need_convert": false, "api_name": "is_active", "_id": "64ad141b9166820001188768", "is_index_field": false, "status": "released"}, "create_time": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "create_time", "is_unique": false, "type": "date_time", "is_required": false, "define_type": "system", "is_single": false, "index_name": "crt_time", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "创建时间", "time_zone": "", "is_need_convert": false, "api_name": "create_time", "date_format": "yyyy-MM-dd HH:mm:ss", "_id": "64ad141b9166820001188769", "is_index_field": false, "status": "released"}, "close_strategy": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "关闭策略", "is_unique": false, "type": "number", "decimal_places": 0, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_1", "is_index": false, "is_active": true, "create_time": 1636980620734, "is_encrypted": false, "length": 2, "label": "关闭策略", "is_need_convert": false, "api_name": "close_strategy", "_id": "64ad141b916682000118876a", "is_index_field": false, "round_mode": 4, "status": "released"}, "last_modified_by": {"describe_api_name": "MtConvertRuleObj", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "_id": "64ad141b916682000118876b", "is_index_field": false, "is_single": true, "index_name": "md_by", "status": "released"}, "created_by": {"describe_api_name": "MtConvertRuleObj", "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "_id": "64ad141b916682000118876d", "is_index_field": false, "is_single": true, "index_name": "crt_by", "status": "released"}, "close_field_api_name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "关闭标识字段apiName", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_12", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "关闭标识字段apiName", "is_need_convert": false, "api_name": "close_field_api_name", "_id": "64ad141b916682000118876e", "is_index_field": false, "status": "released"}, "api_name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "转换规则apiName", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "t_13", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "转换规则apiName", "is_need_convert": false, "api_name": "api_name", "_id": "64ad141b916682000118876f", "is_index_field": false, "status": "released"}, "close_field_value": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "关闭字段值", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_14", "max_length": 500, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "关闭字段值", "is_need_convert": false, "api_name": "close_field_value", "_id": "64ad141b9166820001188770", "is_index_field": false, "status": "released"}, "name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "转换规则主属性", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "name", "max_length": 200, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "转换规则主属性", "is_need_convert": false, "api_name": "name", "_id": "64ad141b9166820001188771", "is_index_field": false, "status": "released"}, "rule_api_name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "规则的apiName", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "t_15", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "规则的apiName", "is_need_convert": false, "api_name": "rule_api_name", "_id": "64ad141b9166820001188772", "is_index_field": false, "status": "released"}, "target_object_describe_api_name": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "目标对象apiName", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "index_name": "t_16", "max_length": 50, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "目标对象apiName", "is_need_convert": false, "api_name": "target_object_describe_api_name", "_id": "64ad141b9166820001188773", "is_index_field": false, "status": "released"}, "excess_prompt": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "超额提醒", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_17", "max_length": 1000, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "超额提醒", "is_need_convert": false, "api_name": "excess_prompt", "_id": "64ad141b9166820001188774", "is_index_field": false, "status": "released"}, "md_type": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "description": "主从关系类型", "is_unique": false, "type": "number", "decimal_places": 0, "is_required": true, "define_type": "package", "is_single": false, "index_name": "d_2", "is_index": false, "is_active": true, "create_time": 1636980620734, "is_encrypted": false, "length": 2, "label": "主从关系类型", "is_need_convert": false, "api_name": "md_type", "_id": "64ad141b9166820001188775", "is_index_field": false, "round_mode": 4, "status": "released"}, "message": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "不满足条件时提示用户", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_11", "max_length": 1000, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "不满足条件时提示用户", "is_need_convert": false, "api_name": "message", "_id": "64ad141b916682000118876c", "is_index_field": false, "status": "released"}, "back_write_model": {"describe_api_name": "MtConvertRuleObj", "auto_adapt_places": false, "pattern": "", "description": "回写模式", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_2", "max_length": 20, "is_index": true, "is_active": true, "create_time": 1689064475439, "is_encrypted": false, "label": "回写模式", "is_need_convert": false, "api_name": "back_write_model", "_id": "64ad141b9166820001188759", "is_index_field": false, "status": "released"}, "_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "is_active": true, "api_name": "_id", "description": "_id", "status": "released", "index_name": "_id", "create_time": 1689064475438}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "is_active": true, "api_name": "tenant_id", "description": "tenant_id", "status": "released", "create_time": 1689064475438, "index_name": "ei"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name", "create_time": 1689064475438}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "index_name": "version", "create_time": 1689064475438}}, "actions": {}, "_id": "64ad141b9166820001188776", "tenant_id": "-100", "api_name": "MtConvertRuleObj", "display_name": "转换规则配置", "package": "CRM", "is_active": true, "version": 3, "release_version": "6.4", "define_type": "internal", "is_deleted": false, "last_modified_time": 1689064475439, "create_time": 1689064475438, "store_table_name": "mt_convert_rule", "short_name": "jjJ"}