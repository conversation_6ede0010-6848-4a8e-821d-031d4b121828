<mxfile host="Electron" modified="2023-11-28T03:39:29.116Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/22.0.3 Chrome/114.0.5735.289 Electron/25.8.4 Safari/537.36" etag="-RhUOf1I4DYx3RpgzNUP" version="22.0.3" type="device">
  <diagram name="第 1 页" id="MBOqHqNXuHoEtVWyU-IB">
    <mxGraphModel dx="892" dy="646" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-235" value="" style="rounded=1;whiteSpace=wrap;html=1;gradientColor=none;strokeColor=default;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="145" y="630" width="310" height="460" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-236" value="" style="rounded=1;whiteSpace=wrap;html=1;gradientColor=none;strokeColor=default;fillColor=none;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="140" y="60" width="310" height="420" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-237" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-238" target="tBI_t4s7CRVH5zATsHyZ-240">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-238" value="初始化转换规则" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="70" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-239" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-240" target="tBI_t4s7CRVH5zATsHyZ-242">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-240" value="初始化源主对象数据" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-241" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-242" target="tBI_t4s7CRVH5zATsHyZ-244">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-242" value="初始化源对象描述" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-243" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-244" target="tBI_t4s7CRVH5zATsHyZ-250">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-244" value="初始化转换规则中源从对象描述" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-245" value="&lt;h3&gt;&lt;b&gt;初始化&lt;/b&gt;&lt;/h3&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="145" y="70" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-246" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-250" target="tBI_t4s7CRVH5zATsHyZ-252">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-247" value="满足" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="tBI_t4s7CRVH5zATsHyZ-246">
          <mxGeometry x="-0.2705" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-248" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-250" target="tBI_t4s7CRVH5zATsHyZ-254">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-249" value="不满足" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="tBI_t4s7CRVH5zATsHyZ-248">
          <mxGeometry x="0.2942" y="-2" relative="1" as="geometry">
            <mxPoint x="-19" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-250" value="根据规则中源主对象的范围规则检验数据" style="rhombus;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="255" y="510" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-251" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-252" target="tBI_t4s7CRVH5zATsHyZ-260">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="295" y="760" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-252" value="初始化源从对象数据" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="650" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-253" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-254" target="tBI_t4s7CRVH5zATsHyZ-255">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-254" value="报错提示" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="405" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-255" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;" vertex="1" parent="1">
          <mxGeometry x="565" y="535" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-256" value="&lt;h3&gt;&lt;b&gt;执行doAct&lt;/b&gt;&lt;/h3&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="155" y="665" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-257" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-258" target="tBI_t4s7CRVH5zATsHyZ-262">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-258" value="根据转换规则中源从对象的范围过滤过滤数据" style="whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-259" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-260" target="tBI_t4s7CRVH5zATsHyZ-258">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-260" value="从对象数据过滤掉从对象转换规则未配置的业务类型数据" style="whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="750" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-261" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-262" target="tBI_t4s7CRVH5zATsHyZ-264">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-262" value="掩码字段过滤" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="930" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-263" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="tBI_t4s7CRVH5zATsHyZ-264" target="tBI_t4s7CRVH5zATsHyZ-265">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="295" y="1180.0000000000005" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-264" value="执行转换" style="whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="235" y="1010" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tBI_t4s7CRVH5zATsHyZ-265" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;" vertex="1" parent="1">
          <mxGeometry x="280" y="1130" width="30" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
