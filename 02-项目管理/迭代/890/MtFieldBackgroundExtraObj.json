{"fields": {"field_api_name": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "字段apiName", "api_name": "field_api_name", "description": "字段apiName", "default_value": true, "status": "released"}, "describe_api_name": {"type": "text", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "label": "对象apiName", "api_name": "describe_api_name", "description": "对象apiName", "default_value": true, "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "index_name": "crt_time"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time"}, "remark": {"is_index": true, "is_active": true, "description": "备注", "is_unique": false, "label": "备注", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "remark", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 500, "status": "released"}, "option_remark": {"type": "long_text", "expression_type": "json", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "选项备注", "api_name": "option_remark", "description": "选项备注", "default_value": true, "status": "released", "max_length": 100000}}, "tenant_id": "-101", "api_name": "MtFieldBackgroundExtraObj", "display_name": "字段后端扩展对象", "package": "CRM", "is_active": true, "release_version": "6.4", "define_type": "internal", "is_deleted": false, "store_table_name": "mt_field_background_extra"}