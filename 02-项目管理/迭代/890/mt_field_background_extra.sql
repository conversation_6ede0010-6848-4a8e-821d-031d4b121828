create table if not exists mt_field_background_extra (
    id                       varchar(32)  not null,
    tenant_id                varchar(10)  not null,
    object_describe_api_name varchar(200) not null,
    create_time              bigint       not null,
    created_by               var<PERSON>r(50)  not null,
    last_modified_time       bigint       not null,
    last_modified_by         varchar(50)  not null,
    version                  integer      not null,
    field_api_name           varchar(100) not null,
    describe_api_name        varchar(100) not null,
    remark                   varchar(500),
    option_remark            jsonb,
    primary key (id, tenant_id)
);

create unique index if not exists uk_mt_field_background_extra_ti_describe_api_name_api_name
    on mt_field_background_extra (tenant_id, describe_api_name, field_api_name);

drop trigger if exists x_audit_changes on mt_field_background_extra;
create trigger x_audit_changes
    after insert or update or delete
    on mt_field_background_extra
    for each row
execute procedure f_change_detail('id', 'tenant_id', 'object_describe_api_name');