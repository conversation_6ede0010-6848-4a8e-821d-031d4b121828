# 需求文档
【【940】【Bug转需求】【目标从对象被删除了】订单上的映射按钮显示对象描述不存在】
https://www.tapd.cn/54330609/prong/stories/view/1154330609001313299

# 需求分析
1. 映射规则删除从对象映射中的目标对象后，点击映射按钮会报错『对象不存在』
2. 转换规则不存在此问题，转换前会将从对象规则中非正常状态的对应的源对象数据过滤，转换时不会存在此问题
   
## 待确认
   1. 转换规则不存在此问题，转换前会将从对象规则中非正常状态的对应的源对象数据过滤，转换时不会存在此问题。映射规则是否按照转换规则的处理流程？ 按转换规则处理

# 详细设计
## 处理流程
在 com.facishare.paas.appframework.metadata.ObjectMappingServiceImpl 类的 mappingData 方法里，需实现对源从对象数据的过滤逻辑，具体步骤如下：
1. 批量查询所有从对象映射规则目标对象描述。
2. 不存在的目标对象，需要将其对应的源对象数据移除。



