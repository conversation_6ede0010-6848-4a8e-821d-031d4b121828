# 需求文档
【【业务方测试】【SFA技术依赖-张雅丽】【映射规则】提供底层支持，业务的【回款映射】放开源头对象中的【计算字段】与目标对象中的字段进行映射@冯津】
https://www.tapd.cn/54330609/prong/stories/view/1154330609001327702

# 需求分析
## 现状
![image](./image/纷享20250213110547-48.png)
目前业务侧映射规则生成逻辑如下：
1. 规则API名称（ruleApiName）的生成逻辑：
   - 对于订单支付（OrderPaymentObj）：生成格式为 "rule_p_%s2op__c"
   - 对于结算明细（SettlementDetailObj）：生成格式为 "rule_p_%s2sd__c"
   - 对于发票申请行（InvoiceApplicationLinesObj）：生成格式为 "rule_%s2ial__c"

2. 映射规则目标对象目前有：OrderPaymentObj,SettlementDetailObj,InvoiceApplicationLinesObj
3. define_type为system

## 目标
1. 映射规则支持业务侧按需过滤

## 详细设计
### 方案一
通过下面配置规则过滤：
```json
[
    {
        "targetObjects": "OrderPaymentObj",
        "ruleApiNamePattern": "rule_p_%s2op__c",
        "defineType": "system"
    },
    {
        "targetObjects": "SettlementDetailObj",
        "ruleApiNamePattern": "rule_p_%s2sd__c",
        "defineType": "system"
    },
    {
        "targetObjects": "InvoiceApplicationLinesObj",
        "ruleApiNamePattern": "rule_%s2ial__c",
        "defineType": "system"
    }
]
```
### 方案二 ✅
1. mt_object_mapping_rule表新增biz_type列，用于区分业务侧映射规则。如：refund_mapping 回款映射
2. /API/v1/object/object_mapping/service/findRuleList接口过滤biz_type为refund_mapping的规则（可根据配置过滤）
3. 业务侧历史数据需要刷库biz_type为refund_mapping

## 刷库接口




