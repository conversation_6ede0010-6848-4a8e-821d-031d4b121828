

{"serializeEmpty":false,"extractExtendInfo":true,"object_describe_api_name":"ActiveRecordObj","search_template_id":"5d0c806a7cfed91f3e95ba8e","include_describe":true,"search_template_type":"default","ignore_scene_record_type":true,"search_query_info":"{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"related_object_data\",\"field_values\":[\"测试无负责人客户\"],\"operator\":\"EQ\"},{\"field_name\":\"related_api_names\",\"field_values\":[\"AccountObj\"],\"operator\":\"EQ\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}","pageSizeOption":[20,50,100,200]}



"search_query_info":"{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"related_object_data\",\"field_values\":[\"测试无负责人客户\"],\"operator\":\"EQ\"},{\"field_name\":\"related_api_names\",\"field_values\":[\"AccountObj\"],\"operator\":\"EQ\"}]


what组件结构

"related_object": {
    "describe_api_name": "ActiveRecordObj",
    "is_index": false,
    "is_active": true,
    "create_time": *************,
    "description": "关联对象,包含对象和对象数据",
    "is_unique": false,
    "group_type": "what_list",
    "label": "关联业务模块",
    "type": "group",
    "relation_table": "feed_relation",
    "api_name": "related_object",
    "define_type": "package",
    "_id": "5f6b0eabb59f6f000111b6b2",
    "is_single": false,
    "label_r": "关联业务模块",
    "is_index_field": false,
    "fields": {
        "id_field": "related_object_data",
        "api_name_field": "related_api_names"
    },
    "extend_field_api_name": "feed_id",
    "index_name": "s_2",
    "status": "released"
},
"related_object_data": {
    "describe_api_name": "ActiveRecordObj",
    "is_index": true,
    "is_active": true,
    "create_time": *************,
    "description": "关联业务数据",
    "is_unique": false,
    "label": "关联业务数据",
    "type": "what_list_data",
    "is_abstract": true,
    "used_in": "component",
    "is_required": false,
    "api_name": "related_object_data",
    "define_type": "package",
    "_id": "5f6b0eabb59f6f000111b6af",
    "is_single": false,
    "label_r": "关联业务数据",
    "is_index_field": false,
    "index_name": "s_7",
    "max_length": 10000,
    "status": "released"
},
"related_api_names": {
    "describe_api_name": "ActiveRecordObj",
    "is_index": true,
    "is_active": true,
    "create_time": *************,
    "description": "关联业务对象",
    "is_unique": false,
    "label": "关联业务对象",
    "type": "select_many",
    "used_in": "component",
    "is_required": false,
    "api_name": "related_api_names",
    "options": [
        {
            "value": "AccountAddrObj",
            "label": "客户地址"
        },
        {
            "value": "AccountFinInfoObj",
            "label": "客户财务信息"
        },
        {
            "value": "AccountObj",
            "label": "客户"
        },
        {
            "value": "CasesObj",
            "label": "工单"
        },
        {
            "value": "ContactObj",
            "label": "联系人"
        },
        {
            "value": "ContractObj",
            "label": "合同"
        },
        {
            "value": "LeadsObj",
            "label": "销售线索"
        },
        {
            "value": "MarketingEventObj",
            "label": "市场活动"
        },
        {
            "value": "object_009Jp__c",
            "label": "prm主M"
        },
        {
            "value": "object_0u48F__c",
            "label": "zz 测试"
        },
        {
            "value": "object_1g18n__c",
            "label": "hh主对象"
        },
        {
            "value": "object_2mmga__c",
            "label": "zj-自定义对象"
        },
        {
            "value": "object_58332__c",
            "label": "fyq主对象"
        },
        {
            "value": "object_5Hpz8__c",
            "label": "prm对象A"
        },
        {
            "value": "object_8n9iB__c",
            "label": "hh对象B(多关联)"
        },
        {
            "value": "object_e1v84__c",
            "label": "fyq从对象"
        },
        {
            "value": "object_eW72p__c",
            "label": "zm-自定义订单"
        },
        {
            "value": "object_G139E__c",
            "label": "zm-自定义客户"
        },
        {
            "value": "object_HiMu2__c",
            "label": "prm从D"
        },
        {
            "value": "object_hM32x__c",
            "label": "zm-对象A"
        },
        {
            "value": "object_IYKfJ__c",
            "label": "工单的从对象"
        },
        {
            "value": "object_M9B1t__c",
            "label": "zm-所有字段"
        },
        {
            "value": "object_q927k__c",
            "label": "zm-主对象"
        },
        {
            "value": "object_sbmmI__c",
            "label": "UIAction 测试对象"
        },
        {
            "value": "object_tG0pY__c",
            "label": "zyx-主"
        },
        {
            "value": "object_V993N__c",
            "label": "B"
        },
        {
            "value": "object_xdw4q__c",
            "label": "wj-a"
        },
        {
            "value": "object_zcwS9__c",
            "label": "M对象"
        },
        {
            "value": "object_zl_whatlist__c",
            "label": "zl-whatlist"
        },
        {
            "value": "OpportunityObj",
            "label": "商机"
        },
        {
            "value": "PartnerObj",
            "label": "合作伙伴"
        },
        {
            "value": "PersonnelObj",
            "label": "人员"
        },
        {
            "value": "QuoteObj",
            "label": "报价单"
        },
        {
            "value": "SalesOrderObj",
            "label": "销售订单"
        },
        {
            "value": "SalesOrderProductObj",
            "label": "订单产品"
        }
    ],
    "is_dynamic": true,
    "define_type": "package",
    "_id": "5f6b0eabb59f6f000111b6aa",
    "is_single": false,
    "label_r": "关联业务对象",
    "is_index_field": false,
    "index_name": "a_2",
    "config": {},
    "status": "released"
}