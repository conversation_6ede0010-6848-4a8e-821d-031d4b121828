 Map batch = [
                '5fd6dcafa6f41f0001c2aea5': [
                        'name': 'carmelo1'
                ],
                '5fd6dd08a6f41f0001c2b1c2': [
                        'name': 'george1'
                ],
                '5fd72703ff12e9000143d5d4': [
                        'name': '00031'
                ]
        ]
        def closure  = {List list ->
            Fx.log('test page ------------------')
            //循环聚合所有数据
            Map updateData = [:]
            list.each { item ->
                Map objectData = item as Map
                updateData.put(objectData._id,batch.get(objectData._id))
            }

            def update = Fx.object.batchUpdate("object_eWv3k__c",updateData)
            Fx.log(update)
            Fx.log('test page ------------------')
        }
    def ret = Fx.object.query("object_eWv3k__c",[["data_own_department":"1000"]], closure)
    
    Fx.log(ret) 
    
       
       Map batch = [
                '5fd6dcafa6f41f0001c2aea5': [
                        'name': 'carmelo1'
                ],
                '5fd6dd08a6f41f0001c2b1c2': [
                        'name': 'george1'
                ],
                '5fd72703ff12e9000143d5d4': [
                        'name': '00031'
                ]
        ] as Map
        def closure  = {List list ->
            Fx.log.info('test page ------------------')
            //循环聚合所有数据
            Map updateData = [:]
            list.each { item ->
                Map objectData = item as Map
                updateData.put(objectData._id,batch.get(objectData._id))
            }

            def update = Fx.object.batchUpdate("object_eWv3k__c",updateData)
            Fx.log.info(update)
            Fx.log.info('test page ------------------')
        }
    def ret = Fx.object.query("object_eWv3k__c",[["data_own_department":"1000"]], closure)
    
    Fx.log.info(ret)


    def ret = Fx.object.find("object_eWv3k__c",[["_id":"5fd6dcafa6f41f0001c2aea5"],["_id":"5fd6dd08a6f41f0001c2b1c2"],["_id":"5fd72703ff12e9000143d5d4"]], 100,0)
    Fx.log.info(ret)