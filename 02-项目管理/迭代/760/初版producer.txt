package com.facishare.function.rocketmq;

import com.alibaba.fastjson.JSON;
import com.facishare.function.FunctionMessage;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.util.DialectUtil;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

public class AsyncFunctionByDbProducer {
    private static final Logger logger = LoggerFactory.getLogger(AsyncFunctionByDbProducer.class);
    private AutoConfMQProducer sender;
    private static final String TOPIC = "ASYNC_FUNCTION_PARTITION_TOPIC";
    public static final String TAG = "WORKFLOW";
    private List<String> dbInstanceList = Lists.newArrayList();
    private Map<Integer,Integer> tenantLimits = Maps.newConcurrentMap();
    @Setter
    private String configName;
    private int defaultLimitThreshold;
    @Autowired
    private DbRouterClient dbRouterClient;
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, iConfig -> {
            defaultLimitThreshold = iConfig.getInt("defaultTenantLimitThreshold",2);
            String dbInstanceStr = iConfig.get("dbInstancelist");
            if (!Strings.isNullOrEmpty(dbInstanceStr)) {
                Set<String> dbInstanceSet = JSON.parseArray(dbInstanceStr,String.class).stream().map(s -> s.trim()).collect(Collectors.toSet());
                dbInstanceList = Lists.newArrayList(dbInstanceSet);
            }
        });
        sender = new AutoConfMQProducer(configName);
        logger.info("AsyncFunctionByDbProducer init over, configName:{}", configName);
    }

    private MessageQueueSelector selector = (mqs, msg, arg) -> {
        String tenantId = (String) arg;
        int size = mqs.size();
        String jdbcUrl = queryJdbcUrl(tenantId, "CRM", DialectUtil.POSTGRESQL);
        int dbInstanceIndex = dbInstanceList.indexOf(jdbcUrl);
        if (jdbcUrl == null || dbInstanceList.size() == 0 || dbInstanceIndex == -1) {
            int index = Math.abs(tenantId.hashCode()) % size;
            logger.warn("get dbUrl from podApiClient,tenantId:{},dbUrl:{},dbInstanceList size:{},dbInstanceIndex:{}",
                    tenantId, jdbcUrl, dbInstanceList.size(), dbInstanceIndex);
            return mqs.get(index);
        }

        int mod = size % dbInstanceList.size();
        int averageSize = size <= dbInstanceList.size() ? 1 : (mod > 0 && dbInstanceIndex < mod ? size / dbInstanceList.size()
                        + 1 : size / dbInstanceList.size());
        int startIndex = (mod > 0 && dbInstanceIndex < mod) ? dbInstanceIndex * averageSize : dbInstanceIndex * averageSize + mod;
        int range = Math.min(averageSize, size - startIndex);
        List<Integer> list = Lists.newArrayList();
        int origin = Math.abs(tenantId.hashCode()) % range;
        for (int i = 0; i < defaultLimitThreshold; i++) {
            list.add((origin + i) % range);
        }
        int index = (startIndex + new Random().nextInt(list.size())) % size;
        return mqs.get(index);
    };

    public void sendAsyncFunctionPartitionMessage(FunctionMessage functionMessage, RequestContext context) {
        Message message = new Message();
        message.setTopic(TOPIC);
        message.setTags(TAG);
        message.setBody(JSON.toJSONString(functionMessage).getBytes(Charsets.UTF_8));
        sendBySelector(message,selector,context.getTenantId());
    }

    public void sendBySelector(Message msg, MessageQueueSelector selector, Object obj) {
        try {
            SendResult sendResult = sender.send(msg, selector, obj);
            SendStatus status = sendResult.getSendStatus();
            if (status.equals(SendStatus.SEND_OK)) {
                logger.debug("msgId={}, status={}", sendResult.getMsgId(), status);
            } else {
                logger.error("msgId={}, status={}", sendResult.getMsgId(), status);
            }
        } catch (Exception e) {
            logger.error("SendError,message={}", msg, e);
            throw new FunctionException("sendBySelector exception" + e);
        }
    }
    private String queryJdbcUrl(String tenantId, String biz, String dialect){
        try {
            String jdbcUrl = dbRouterClient.queryJdbcUrl(tenantId, biz, dialect);
            return StringUtils.substringBefore(StringUtils.substringAfter(jdbcUrl, "//"), ":");
        } catch (Exception e) {
            logger.error("AsyncFunctionByDbProducer queryJdbcUrl error,tenantId:{}", tenantId, e);
            return null;
        }
    }

    @PreDestroy
    public void shutDown() {
        sender.close();
    }
}
