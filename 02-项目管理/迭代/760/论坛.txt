官网：https://www.fxiaoke.com/mob/guide/crmdoc/src/8%E8%87%AA%E5%AE%9A%E4%B9%89%E5%87%BD%E6%95%B0.html
论坛：https://www.fxiaoke.com/forum/forum.php?mod=forumdisplay&fid=38&page=1
ReatEasy 框架
Groovy语言，语法，动态编译等
http://wiki.firstshare.cn/pages/viewpage.action?pageId=19666137 这里元数据模型 相关的内容
群测：http://wiki.firstshare.cn/pages/viewpage.action?pageId=152183182

函数需求：http://wiki.firstshare.cn/pages/viewpage.action?pageId=148373120




rocketmq server：10.112.41.9:9876;10.112.41.10:9876
                


异步定时函数mq，申请新的topic和group
1. 新增消费者的topic、group
2. 环境类型：112，线上，各个专属云
3. MQ类型：rocketmq
4. 集群地址：
112：10.112.41.9:9876;10.112.41.10:9876
线上：172.17.41.55:9876;172.17.41.56:9876
专属云地址参考各个专属云
5. Groups: GROUP_ASYNC_PARTITION_FUNCTION
6. Topic： ASYNC_FUNCTION_PARTITION_TOPIC
7. Queues：400
8. 部门：@PaaS业务通讯组 
9. 相关负责人（告警接收人）：@王毅 @佘城 @黎贵昂\

