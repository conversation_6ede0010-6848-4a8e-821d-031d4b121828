


工作流函数并发高导致pg负载高的问题

涉及查询数据库操作：
    1.通过udefFunctionService.findFunctionByApiName(user.getTenantId(), apiName, bindingObjectAPIName)查询函数信息  表名：mt_udef_function 
    2.根据bindingObjectDataId（绑定对象id），User对象，binding_object_api_name字段查询绑定对象信息。metaDataFindService.findObjectDataIncludeDeleted(context.getUser(), objectId, objectAPIName)
    3.通过describeLogicService.findDetailDescribes(context.getTenantId(), objectAPIName)查询对象描述集合。
    4.通过tenantId和apiName查询对象IObjectDescriber。describeLogicService.findObject(context.getTenantId(), objectAPIName)  --有缓存
    5.通过metaDataFindService.findRelatedObjectData(relatedListQueryBuilder.getRelatedListQuery(context.getUser(), count), context.getUser())查询关联对象List

解决方案：
    一、加入缓存---优点：解决并发高导致pg负载高的问题。缺点：引入后数据一致性问题，如何解决？是否影响较大？上面步骤1-4是否适合加入缓存？  
    二、将限流阈值调小。目前生产方和消费方使用的默认限流策略 QPS=1000。优点：只需要新增限流参数即可，代码无需修改。缺点：降低了工作流函数的并发。


    




