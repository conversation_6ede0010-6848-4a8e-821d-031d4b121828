<mxfile host="Electron" modified="2023-06-02T10:55:35.762Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.2.8 Chrome/112.0.5615.165 Electron/24.2.0 Safari/537.36" etag="NL-AhkPdg8n6VsGiFBDe" version="21.2.8" type="device">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel dx="1674" dy="854" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-0" target="v1mWEZ-1BSxbe5TJE1Kv-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-0" value="函数提交任务" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="250" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-1" target="v1mWEZ-1BSxbe5TJE1Kv-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-1" value="调用appframework接口" style="whiteSpace=wrap;html=1;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;rounded=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="250" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-5" target="v1mWEZ-1BSxbe5TJE1Kv-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-19" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-5" target="v1mWEZ-1BSxbe5TJE1Kv-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-21" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#143642;" parent="v1mWEZ-1BSxbe5TJE1Kv-19" vertex="1" connectable="0">
          <mxGeometry x="-0.1333" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-23" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-5" target="v1mWEZ-1BSxbe5TJE1Kv-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-24" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#143642;" parent="v1mWEZ-1BSxbe5TJE1Kv-23" vertex="1" connectable="0">
          <mxGeometry x="-0.3" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-5" value="企业小于10" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;rounded=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="270" y="350" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;entryX=0.5;entryY=0.5;entryDx=0;entryDy=-15;entryPerimeter=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="310" y="610" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-7" value="异步执行刷库操作" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="250" y="490" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-26" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;exitX=0.45;exitY=1.033;exitDx=0;exitDy=0;exitPerimeter=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-15" target="v1mWEZ-1BSxbe5TJE1Kv-38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="350" y="625" as="targetPoint" />
            <Array as="points">
              <mxPoint x="464" y="625" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-15" value="更新成功失败企业到redis中" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="410" y="490" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-22" target="v1mWEZ-1BSxbe5TJE1Kv-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-22" value="for循环同步执行刷库操作" style="whiteSpace=wrap;html=1;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;rounded=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="410" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-30" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-27" target="v1mWEZ-1BSxbe5TJE1Kv-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-27" value="函数轮训查询进度" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="620" y="130" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-29" target="v1mWEZ-1BSxbe5TJE1Kv-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-29" value="调用查询进度接口" style="whiteSpace=wrap;html=1;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;rounded=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="620" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-34" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-31" target="v1mWEZ-1BSxbe5TJE1Kv-35" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="680" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-31" value="查询redis中成功失败企业，错误日志？" style="whiteSpace=wrap;html=1;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;rounded=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="620" y="340" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-35" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;fontColor=#393C56;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="665" y="450" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-39" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontColor=#143642;fillColor=#FAE5C7;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="v1mWEZ-1BSxbe5TJE1Kv-38" target="v1mWEZ-1BSxbe5TJE1Kv-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v1mWEZ-1BSxbe5TJE1Kv-38" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;fontColor=#393C56;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="295" y="610" width="30" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
