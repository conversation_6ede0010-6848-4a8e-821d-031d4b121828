{"basicFilterCondition": [], "deleteOriginalData": false, "fieldDescribe": [{"fieldType": "String", "name": "tenant_id", "order": 1}, {"fieldType": "String", "name": "describe_api_name", "order": 2}, {"fieldBizType": "ApiName", "fieldType": "String", "name": "api_name", "order": 3}, {"fieldType": "String", "name": "actions", "order": 4}], "inBoundCheck": {"carefulChecks": [], "checkTableDescriptors": [], "dependencyTableDescriptors": [], "unchangeableField": [], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": 1, "principalIds": [7898], "processorDescribe": {"inBoundTag": "IGNORE", "outBoundTag": "ACTION", "selectHandlerType": {"configName": "tenant-sandbox-base", "handlerType": "USER_DEFINE"}}, "slaveTableDescribe": [{"filters": [], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "id", "operator": "IN", "refFieldName": "id"}], "tableId": "paas_metadata_mt_entity_reference"}], "tableId": "paas_button_reference_validate", "tableName": "button_reference_validate", "uniqueKeys": ["tenant_id", "describe_api_name", "api_name", "actions"]}