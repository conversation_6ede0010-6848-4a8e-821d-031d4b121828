## 需求描述
【Bug转需求】函数出站会带引用关系，但未做检查是否有source，需优化】
https://www.tapd.cn/54330609/prong/stories/view/1154330609001332190
## 背景
由于按钮的引用关系结构为：
```json
{
    "source_value": "按钮apiName",
    "source_type": "button",
    "target_value": "函数apiName",
    "target_type": "function"
}
```
由于`source_value` 为按钮的 `apiName`，但由于 `apiName` 不是唯一标识符，无法单独通过 `paas_metadata_mt_udef_button` 更改集描述或 `paas_metadata_mt_udef_action` 更改集描述直接确定唯一的引用关系`paas_metadata_mt_entity_reference`。
### **现状**
按钮的函数引用关系： `paas_metadata_mt_udef_button` 依赖 `paas_metadata_mt_udef_function`，按钮类型的函数 `paas_metadata_mt_udef_function` 依赖引用关系`paas_metadata_mt_entity_reference`，来确定按钮的函数引用关系。

```puml
@startuml
package "按钮引用关系" {
    [paas_metadata_mt_udef_button] --> [paas_metadata_mt_udef_function]
    [paas_metadata_mt_udef_function] --> [paas_metadata_mt_entity_reference]
}
@enduml
```
用户出站按钮会带出函数及函数引用关系。
### 存在问题
用户只出站函数未出站按钮，导致按钮引用关系被带出，导致函数无法删除。
### 如何解决
1. 引入虚拟表
2. 在 `paas_metadata_mt_udef_button` slaveTableDescribe中增加依赖虚拟表 `paas_button_reference_validate` 
3. 在 `paas_metadata_mt_udef_function` slaveTableDescribe中移除 `paas_metadata_mt_entity_reference` 的依赖。



## 目标
1. 确保按钮出站后函数及函数引用关系出站正确
2. 确保只出站函数未出站按钮时不会将函数引用关系出站
## 需求分析
### 主要功能点
调整paas_metadata_mt_udef_button、paas_metadata_mt_udef_function更改集描述

## 技术方案
1. 引入虚拟表
```json
{
	"basicFilterCondition": [],
	"deleteOriginalData": false,
	"fieldDescribe": [
		{
			"fieldType": "String",
			"name": "tenant_id",
			"order": 1
		},
		{
			"fieldType": "String",
			"name": "describe_api_name",
			"order": 2
		},
        {
            "fieldBizType": "ApiName",
            "fieldType": "String",
            "name": "api_name",
            "order": 3
        },
        {
            "fieldType": "String",
            "name": "actions",
            "order": 4
        }
	],
	"inBoundCheck": {
		"carefulChecks": [],
		"checkTableDescriptors": [],
		"dependencyTableDescriptors": [],
		"unchangeableField": [],
		"uniqueIndices": []
	},
	"logicDeleteFieldValue": {},
	"order": 1,
	"principalIds": [
		7898
	],
	"processorDescribe": {
		"inBoundTag": "IGNORE",
		"outBoundTag": "ACTION",
		"selectHandlerType": {
			"configName": "tenant-sandbox-base",
			"handlerType": "USER_DEFINE"
		}
	},
	"slaveTableDescribe": [
		{
			"filters": [],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "id",
					"operator": "IN",
					"refFieldName": "id"
				}
			],
			"tableId": "paas_metadata_mt_entity_reference"
		}
	],
	"tableId": "paas_button_reference_validate",
	"tableName": "button_reference_validate",
	"uniqueKeys": [
		"tenant_id",
        "describe_api_name",
		"api_name",
        "actions"
	]
}
```
2. `paas_metadata_mt_udef_button` slaveTableDescribe中增加依赖虚拟表 `paas_button_reference_validate` 
```json
        {
			"filters": [],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"refFieldName": "describe_api_name"
				},
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"refFieldName": "api_name"
				},
				{
					"fieldName": "actions",
					"operator": "IN",
					"refFieldName": "actions"
				}
			],
			"tableId": "paas_button_reference_validate"
		}
```
3. 在 `paas_metadata_mt_udef_function` slaveTableDescribe中移除 `paas_metadata_mt_entity_reference` 的依赖。
## 实现效果



