# 需求分析

**whatlist查询关联业务对象接口**

```shell
curl --location --request POST 'http://localhost:8082/API/v1/rest/object/object_zl_whatlist__c/controller/CRMObjectList' \
--header 'content-type: application/json' \
--header 'x-fs-userInfo: 1000' \
--header 'x-fs-ei: 74255' \
--data-raw '{"isIncludeFieldDescribe":false,"isIncludeSystemObj":true,"isIncludeUnActived":false,"packageName":"CRM","objectData":""}'
```

**原来配置函数后数据结构**

```json
{
    "wheres": [
        {
            "func_api_name": "func_EP6eb__c"
        }
    ]
}
```

## 问题

1、配置到那？——配置到related_object字段中
2、如何过滤apiname

```
{
    "wheres": [
        {
            "func_api_name": "func_EP6eb__c"
        }
    ]
}
```
3、


| 字段 | 类型 | 说明 | |
| ------------------------ | ------- | ------------------------------------------------------------ | ---- |
| **associationCrmObject** | | \- 业务对象 app-involved-object 和数据权限 data-setting 这两部分依赖于 app 的 associationCrmObject 属性，为 true 才展示。如通知公告、互联网盘这种不需要依赖 crm 的 object 的该属性为 false。这两部分在下游是不展示的。\- 功能权限的设置部分 function-setting 除了以上的 associationCrmObject 之外，在企业互联的自主管理的下游也是展示的，只是只有人员分配的功能。740 增加角色权限，当前企业下的功能权限被移除，但功能权限若开通了游客角色，【功能权限】tab 保留，但底下对角色的操作已经移到【互联角色】下。 | |
| **dataAuthType** | number | 当值为1时有”添加权限配置“ | |
| **name** | String | 应用名称 | |
| **showPaasPage** | Number | _0:全不展示 1:全展示 2:只展示 web 页签 3:只展示 App 页签_ | |
| **status** | Number | 是否展示“设置”按钮, 1: 已启用， 上游没 status，默认启用 | |
| **supportExtendObject** | Boolean | 是否支持添加对象 | |
| **supportIdentityTypes** | String | 应用类型：'1': 企业，'2': 个人，'1,2': ‘企业，个人’ | |
| supportNoFsAccount | Boolean | _应用可以被 无租户 使用_ | |
| **unableSetManagerText** | String | 是否允许设置应用管理员, | |

- 进入应用-本企业
- status = 1 && webManageUrl -> 展示“进入应用”
- hasLinkAppAdmin = false -> 没权限
- hasLinkAppAdmin = true -> 跳转 webManagerUrl
- 进入应用-上游
- Upstream && webUrl -> 展示“进入应用”
- webUrl -> 跳转
- 设置-本企业
- !!status -> 展示“设置”
- 设置-上游
- manageMode = 2 -> 展示“设置”，_1 是企业代管 2 是自主管理，自主管理的有设置功能_
- 已启用
- status =1 || !!openStatus || !!upstreamEa
- 不满足上述条件为禁用
- 基本信息：设置按钮
- 不是上游 && (type ===2 || !unableSetManagerText) -> 展示设置按钮
17:18
朱春永chunyong
少了字段
showPaasPage： 控制视图是否展示
sourceType + associationCrmObject 控制外部数据权限和业务对象
| 编辑框字段           | 是否可编辑 | 是否必填 | 字段类型 | 默认值             | 字段描述                      |
| :------------------- | :--------- | :------- | :------- | :----------------- | ----------------------------- |
| 字段名称             | 是         | 是       | 文本     | 动态关联           | label                         |
| API Name             | 否         | 是       | 文本     | related_object     | api_name                      |
| 提示信息             | 是         | 否       | 文本     | 无                 | help_text                     |
| ~~相关列表标题~~     | ~~是~~     | ~~是~~   | ~~文本~~ | ~~动态关联~~       | ~~target_related_list_label~~ |
| ~~相关列表API Name~~ | ~~是~~     | ~~是~~   | ~~文本~~ | ~~系统自动生成~~   | ~~target_related_list_name~~  |
| 关联对象范围         | 是         | 是       | 单选     | 默认全部           | wheres                        |
| 是否允许重复         | 否         | 否       | 单选     | 允许重复           | is_unique                     |
| 是否必填             | 是         | 否       | 单选     | 否                 | is_required                   |
| 布局                 | 是         | 否       | 复选     | 默认所有布局可展示 |                               |


1. 可在whatlist字段中配置函数来过滤对象返回及对象排序——List<String>有序

2. 只能配置基于APL代码的函数

   {

   ​    "isIncludeFieldDescribe": false,

   ​    "isIncludeSystemObj": true,

   ​    "isIncludeUnActived": false,

   ​    "packageName": "CRM",

   ​    "objectData": ""

   }

## whatlist字段支持更改集
`{
	"basicFilterCondition": [
		{
			"fieldName": "status",
			"operator": "NIN",
			"order": 1,
			"values": [
				"deleted"
			]
		},
		{
			"fieldName": "define_type",
			"operator": "NIN",
			"order": 2,
			"values": [
				"deleted"
			]
		}
	],
	"deleteOriginalData": false,
	"fieldDescribe": [
		{
			"fieldType": "String",
			"name": "define_type",
			"order": 2147483647
		},
		{
			"fieldBizType": "ObjectApiName",
			"fieldType": "String",
			"name": "describe_api_name",
			"order": 2147483647
		},
		{
			"fieldBizType": "Name",
			"fieldType": "String",
			"name": "field_label",
			"order": 2147483647
		},
		{
			"fieldType": "String",
			"name": "status",
			"order": 4
		},
		{
			"fieldBizType": "ApiName",
			"fieldType": "String",
			"name": "api_name",
			"order": 2
		},
		{
			"fieldType": "String",
			"name": "describe_id",
			"order": 3
		},
		{
			"fieldBizType": "TenantId",
			"fieldType": "String",
			"name": "tenant_id",
			"order": 1
		},
		{
			"fieldBizType": "IdGenerator",
			"fieldType": "String",
			"name": "field_id",
			"order": 2147483647
		},
		{
			"fieldType": "String",
			"name": "cascade_parent_api_name",
			"order": 2147483647
		},
		{
			"fieldType": "String",
			"name": "target_api_name",
			"order": 2147483647
		},
		{
			"fieldType": "String",
			"name": "type",
			"order": 2147483647
		},
		{
			"fieldType": "String",
			"name": "group_type",
			"order": 2147483647
		}
	],
	"inBoundCheck": {
		"checkTableDescriptors": [],
		"dependencyTableDescriptors": [
			{
				"filters": [],
				"foreignKeyDescribe": [
					{
						"fieldName": "describe_api_name",
						"operator": "IN",
						"refFieldName": "describe_api_name"
					},
					{
						"fieldName": "tenant_id",
						"operator": "IN",
						"refFieldName": "tenant_id"
					}
				],
				"paramFields": [],
				"processLack": "exception",
				"tableId": "paas_metadata_mt_describe"
			},
			{
				"filters": [],
				"foreignKeyDescribe": [
					{
						"fieldName": "describe_id",
						"operator": "IN",
						"refFieldName": "describe_id"
					},
					{
						"fieldName": "tenant_id",
						"operator": "IN",
						"refFieldName": "tenant_id"
					}
				],
				"paramFields": [],
				"processLack": "exception",
				"tableId": "paas_metadata_mt_describe"
			},
			{
				"filters": [],
				"foreignKeyDescribe": [
					{
						"fieldName": "describe_api_name",
						"operator": "IN",
						"refFieldName": "target_api_name"
					},
					{
						"fieldName": "tenant_id",
						"operator": "IN",
						"refFieldName": "tenant_id"
					}
				],
				"paramFields": [],
				"processLack": "exception",
				"tableId": "paas_metadata_mt_describe"
			},
			{
				"filters": [],
				"foreignKeyDescribe": [
					{
						"fieldName": "api_name",
						"operator": "IN",
						"refFieldName": "cascade_parent_api_name"
					},
					{
						"fieldName": "describe_api_name",
						"operator": "IN",
						"refFieldName": "describe_api_name"
					},
					{
						"fieldName": "tenant_id",
						"operator": "IN",
						"refFieldName": "tenant_id"
					}
				],
				"paramFields": [],
				"processLack": "exception",
				"tableId": "paas_metadata_mt_field"
			}
		],
		"unchangeableField": [
			{
				"fieldName": "type"
			}
		],
		"uniqueIndices": []
	},
	"logicDeleteFieldValue": {},
	"order": 0,
	"principalIds": [],
	"processorDescribe": {
		"inBoundTag": "PG",
		"outBoundTag": "PG",
		"selectHandlerType": {
			"configName": "PG",
			"handlerType": "PG"
		}
	},
	"slaveTableDescribe": [
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"refFieldName": "describe_api_name"
				}
			],
			"tableId": "bi_after_action_mt_field"
		},
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"refFieldName": "describe_api_name"
				}
			],
			"tableId": "after_action_mt_describe_clear_cache"
		},
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"refFieldName": "describe_api_name"
				}
			],
			"tableId": "after_action_mt_describe"
		},
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"refFieldName": "describe_api_name"
				}
			],
			"tableId": "after_action_mt_describe_clear_cache"
		},
		{
			"filters": [
				{
					"fieldName": "type",
					"operator": "IN",
					"order": 1,
					"values": [
						"formula",
						"count",
						"quote"
					]
				}
			],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"refFieldName": "describe_api_name"
				},
				{
					"fieldName": "api_name",
					"operator": "IN",
					"refFieldName": "api_name"
				}
			],
			"tableId": "recalculate_after_action_mt_field"
		},
		{
			"filters": [
				{
					"fieldName": "describe_api_name",
					"operator": "IN",
					"order": 1,
					"values": [
						"CheckinsObj"
					]
				}
			],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "api_name",
					"operator": "IN",
					"refFieldName": "describe_api_name"
				}
			],
			"tableId": "checkins_after_action_mt_field"
		},
		{
			"filters": [
				{
					"fieldName": "type",
					"operator": "IN",
					"order": 1,
					"values": [
						"auto_number",
						"object_reference",
						"object_reference_many",
						"employee",
						"employee_many",
						"department",
						"department_many"
					]
				}
			],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "source_value",
					"operator": "IN",
					"refFieldName": "api_name"
				}
			],
			"tableId": "paas_metadata_mt_entity_reference"
		},
		{
			"filters": [
				{
					"fieldName": "type",
					"operator": "IN",
					"order": 1,
					"values": [
						"select_one",
						"select_many"
					]
				}
			],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "source_value",
					"operator": "IN",
					"refFieldName": "describe_api_name,api_name",
					"refReader": "JOIN:{0}.{1}"
				}
			],
			"tableId": "paas_metadata_mt_entity_reference"
		},
		{
			"filters": [
				{
					"fieldName": "type",
					"operator": "IN",
					"order": 1,
					"values": [
						"select_one",
						"select_many"
					]
				}
			],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "source_value",
					"operator": "IN",
					"refFieldName": "describe_api_name,api_name",
					"refReader": "JOIN:{0}.{1}"
				}
			],
			"tableId": "paas_metadata_mt_entity_reference"
		},
		{
			"filters": [
				{
					"fieldName": "type",
					"operator": "IN",
					"order": 1,
					"values": [
						"group"
					]
				},
				{
					"fieldName": "group_type",
					"operator": "IN",
					"order": 2,
					"values": [
						"what_list"
					]
				}
			],
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "source_value",
					"operator": "IN",
					"refFieldName": "api_name"
				}
			],
			"tableId": "paas_metadata_mt_entity_reference"
		},
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenantId",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "key",
					"operator": "IN",
					"refFieldName": "describe_api_name,api_name",
					"refReader": "JOIN:{0}.{1}"
				}
			],
			"tableId": "I18N"
		},
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenantId",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "key",
					"operator": "IN",
					"refFieldName": "describe_api_name,api_name",
					"refReader": "JOIN:{0}.field.{1}"
				}
			],
			"tableId": "I18N"
		}
	],
	"tableId": "paas_metadata_mt_field",
	"tableName": "mt_field",
	"uniqueKeys": [
		"tenant_id",
		"describe_api_name",
		"api_name"
	]
}
`
### 存在问题
- **whatlist字段配置数据范围区域时，whatlist字段非自定义字段，组件中不支持**——
  - 1、自定义对象可通过选择对象组件。
  - 2、预置对象则无法配置。（暂无解决方案）
**解决：**暂且加上配置，更改集组件支持预制字段后自动可支持。

## UI事件函数更改集
  `{
	"basicFilterCondition": [
		{
			"fieldName": "is_deleted",
			"operator": "IN",
			"order": 2,
			"values": [
				false
			]
		}
	],
	"deleteOriginalData": true,
	"fieldDescribe": [
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "id",
			"order": 1
		},
		{
			"fieldBizType": "TenantId",
			"fieldType": "String",
			"name": "tenant_id",
			"order": 2
		},
		{
			"fieldBizType": "ObjectApiName",
			"fieldType": "String",
			"name": "describe_api_name",
			"order": 3
		},
		{
			"fieldBizType": "ObjectApiName",
			"fieldType": "String",
			"name": "trigger_describe_api_name",
			"order": 4
		},
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "trigger_field_api_names",
			"order": 5
		},
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "triggers",
			"order": 6
		},
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "func_api_name",
			"order": 7
		},
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "created_by",
			"order": 8
		},
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "create_time",
			"order": 9
		},
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "last_modified_time",
			"order": 10
		},
		{
			"fieldBizType": "String",
			"fieldType": "String",
			"name": "is_deleted",
			"order": 11
		}
	],
	"inBoundCheck": {
		"checkTableDescriptors": [],
		"dependencyTableDescriptors": [],
		"unchangeableField": [],
		"uniqueIndices": []
	},
	"logicDeleteFieldValue": {},
	"order": 0,
	"principalIds": [],
	"processorDescribe": {
		"inBoundTag": "PG",
		"outBoundTag": "PG",
		"selectHandlerType": {
			"configName": "PG",
			"handlerType": "PG"
		}
	},
	"slaveTableDescribe": [
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "api_name",
					"operator": "IN",
					"refFieldName": "func_api_name"
				}
			],
			"tableId": "paas_metadata_mt_udef_function"
		},
		{
			"foreignKeyDescribe": [
				{
					"fieldName": "tenant_id",
					"operator": "IN",
					"refFieldName": "tenant_id"
				},
				{
					"fieldName": "target_value",
					"operator": "IN",
					"refFieldName": "func_api_name"
				},
				{
					"fieldName": "source_value",
					"operator": "IN",
					"refFieldName": "id"
				}
			],
			"tableId": "paas_metadata_mt_entity_reference"
		}
	],
	"tableId": "paas_metadata_mt_ui_event",
	"tableName": "mt_ui_event",
	"uniqueKeys": [
		"tenant_id",
		"id"
	]
}`


# 大文件附件





# 通过函数获取单选级联关系

格式：

`{
    "field_Xc0dX__c": {
        "describe_api_name": "object_n4zWv__c",
        "auto_adapt_places": false,
        "description": "",
        "is_unique": false,
        "type": "select_one",
        "is_required": false,
        "options": [
            {
                "not_usable": false,
                "child_options": [
                    {
                        "field_9Pkay__c": [
                            "uu7pJqLF2",
                            "ADHvz1Gm0"
                        ]
                    }
                ],
                "label": "a",
                "font_color": "#2a304d",
                "value": "aBw83Xq53"
            },
            {
                "not_usable": false,
                "child_options": [
                    {
                        "field_9Pkay__c": [
                            "option1"
                        ]
                    }
                ],
                "label": "b",
                "font_color": "#2a304d",
                "value": "p56164sG4"
            },
            {
                "not_usable": false,
                "child_options": [
                    {
                        "field_9Pkay__c": [
                            "ADHvz1Gm0"
                        ]
                    }
                ],
                "label": "c",
                "font_color": "#2a304d",
                "value": "wdrWeliC7"
            },
            {
                "not_usable": true,
                "label": "其他",
                "font_color": "#2a304d",
                "value": "other"
            }
        ],
        "define_type": "custom",
        "is_single": false,
        "cascade_parent_api_name": "record_type",
        "index_name": "s_19",
        "is_index": true,
        "is_active": true,
        "create_time": 1627372701538,
        "is_encrypted": false,
        "default_value": "",
        "label": "单选-b",
        "field_num": 45,
        "api_name": "field_Xc0dX__c",
        "_id": "60ffbc9d20b61d00016bfb80",
        "is_index_field": false,
        "config": {},
        "help_text": "",
        "status": "new"
    },
    "record_type": {
        "describe_api_name": "object_n4zWv__c",
        "is_index": true,
        "is_active": true,
        "create_time": 1620394263291,
        "is_encrypted": false,
        "auto_adapt_places": false,
        "description": "record_type",
        "is_unique": false,
        "label": "业务类型",
        "type": "record_type",
        "is_need_convert": false,
        "is_required": false,
        "api_name": "record_type",
        "options": [
            {
                "is_active": true,
                "font_color": "#181C25",
                "api_name": "default__c",
                "description": "预设业务类型",
                "child_options": [
                    {
                        "field_x1e7B__c": [
                            "6U38ir82j",
                            "ujSShQPRs",
                            "W22we9eoF"
                        ],
                        "field_Xc0dX__c": [
                            "aBw83Xq53",
                            "p56164sG4"
                        ]
                    }
                ],
                "label": "预设业务类型",
                "value": "default__c"
            }
        ],
        "define_type": "package",
        "_id": "60954117812b190001eb7ac4",
        "is_single": false,
        "label_r": "业务类型",
        "is_index_field": false,
        "index_name": "r_type",
        "config": {},
        "help_text": "",
        "status": "released"
    }
}`

`[
    {
        "child_options": [
            {
                "field_1uR3x__c": [
                    "8W9bG226K",
                    "ae40LeKU2"
                ],
                "field_aorbk__c": [
                    "1FX8kBvb1",
                    "2om18ACSv",
                    "option1",
                    "A3fx10XQ4"
                ]
            }
        ],
        "label": "辅助",
        "value": "vbQKTQ169"
    },
    {
        "child_options": [
            {
                "field_1uR3x__c": [
                    "E4fa88jz2",
                    "w32iV0hc2"
                ],
                "field_aorbk__c": [
                    "A3fx10XQ4",
                    "d6md415kI",
                    "gDvjyMSxq",
                    "Aw8L4Hzfw"
                ]
            }
        ],
        "label": "打野",
        "value": "U1Ms5aKM4"
    },
    {
        "child_options": [
            {
                "field_1uR3x__c": [
                    "ae40LeKU2",
                    "E4fa88jz2"
                ],
                "field_aorbk__c": [
                    "26e1wzry1",
                    "60Uwb52e9"
                ]
            }
        ],
        "label": "射手",
        "value": "276tebcDS"
    },
    {
        "child_options": [
            {
                "field_1uR3x__c": [
                    "w32iV0hc2",
                    "E4fa88jz2",
                    "option1"
                ],
                "field_aorbk__c": [
                    "14w6q0dsb",
                    "d6md415kI",
                    "35frh44C1"
                ]
            }
        ],
        "label": "中路",
        "value": "C3mG8ou09"
    },
    {
        "child_options": [
            {
                "field_1uR3x__c": [
                    "option1",
                    "w32iV0hc2"
                ],
                "field_aorbk__c": [
                    "gDvjyMSxq",
                    "60Uwb52e9",
                    "8lOQS7ynx"
                ]
            }
        ],
        "label": "上路",
        "value": "option1"
    }
]
`



## 需求：

​	**根据父选项**

![image-20220215175550046](/Users/<USER>/Library/Application Support/typora-user-images/image-20220215175550046.png)

磊哥，这两个字段在【业务方案】（自定义对象）上，是两个独立的单选字段，但是这两个字段做了字段依赖，例如我【作物】分类选了玉米，那么【作物细分类】就只能选春玉米跟夏玉米。这个【业务方案】对象会被采购合同 查找关联引用，采购合同上有从对象，选择产品对象（自定义对象），我们需要针对产品可以选择的数据进行数据范围控制，只能选择跟【作物】这个大分类的对应的产品。产品上只有一个【作物细分类】字段，没有【作物】字段，但是【作物细分类】跟【作物】字段是做了字段依赖关系的，所以我们希望能拿到这个字段的依赖关系，那么我们就可以通过【玉米】去找到【春玉米】跟【夏玉米】的分类，然后作为产品档案的筛选的条件。

- 根据单选字段apiname获取级联关系。
- 根据单选字段及选项值获取此选项的级联关系。
- 获取对象下面所有单选字段的级联关系。

1. 函数定义

   1. Fx.object.getOptionCascade(String apiName, String fieldAPIName)
   2. Fx.object.getOptionCascade(String apiName, String fieldAPIName, String optionValue)
   3. Fx.object.getOptionCascade(String apiName)

   - 

2. 请求参数确定：

3. 返回值确定

   

   











