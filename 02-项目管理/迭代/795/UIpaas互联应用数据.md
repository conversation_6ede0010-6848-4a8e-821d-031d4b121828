| 字段 | 类型 | 说明 | |
| ------------------------ | ------- | ------------------------------------------------------------ | ---- |
| **associationCrmObject** | | \- 业务对象 app-involved-object 和数据权限 data-setting 这两部分依赖于 app 的 associationCrmObject 属性，为 true 才展示。如通知公告、互联网盘这种不需要依赖 crm 的 object 的该属性为 false。这两部分在下游是不展示的。\- 功能权限的设置部分 function-setting 除了以上的 associationCrmObject 之外，在企业互联的自主管理的下游也是展示的，只是只有人员分配的功能。740 增加角色权限，当前企业下的功能权限被移除，但功能权限若开通了游客角色，【功能权限】tab 保留，但底下对角色的操作已经移到【互联角色】下。 | |
| **dataAuthType** | number | 当值为1时有”添加权限配置“ | |
| **name** | String | 应用名称 | |
| **showPaasPage** | Number | _0:全不展示 1:全展示 2:只展示 web 页签 3:只展示 App 页签_ | |
| **status** | Number | 是否展示“设置”按钮, 1: 已启用， 上游没 status，默认启用 | |
| **supportExtendObject** | Boolean | 是否支持添加对象 | |
| **supportIdentityTypes** | String | 应用类型：'1': 企业，'2': 个人，'1,2': ‘企业，个人’ | |
| supportNoFsAccount | Boolean | _应用可以被 无租户 使用_ | |
| **unableSetManagerText** | String | 是否允许设置应用管理员, | |

- 进入应用-本企业
- status = 1 && webManageUrl -> 展示“进入应用”
- hasLinkAppAdmin = false -> 没权限
- hasLinkAppAdmin = true -> 跳转 webManagerUrl
- 进入应用-上游
- Upstream && webUrl -> 展示“进入应用”
- webUrl -> 跳转
- 设置-本企业
- !!status -> 展示“设置”
- 设置-上游
- manageMode = 2 -> 展示“设置”，_1 是企业代管 2 是自主管理，自主管理的有设置功能_
- 已启用
- status =1 || !!openStatus || !!upstreamEa
- 不满足上述条件为禁用
- 基本信息：设置按钮
- 不是上游 && (type ===2 || !unableSetManagerText) -> 展示设置按钮
17:18
朱春永chunyong
少了字段
showPaasPage： 控制视图是否展示
sourceType + associationCrmObject 控制外部数据权限和业务对象