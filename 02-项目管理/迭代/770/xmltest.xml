<?xml version="1.0" encoding="utf-8"?>
<doclet>

    <package>
        <outpath>
            <name>packagehtml</name>
            <value>2.0.html</value>
        </outpath>
        <viewpath>
            <name>packagehtml</name>
            <value>D:/model/index.ftl</value>
        </viewpath>
        <basepath>d:/api2</basepath>
    </package>

    <class>
        <tag>
            <name>uri</name>
            <type>string</type>
        </tag>
        <tag>
            <name>description</name>
            <type>string</type>
        </tag>
    </class>

    <method>
        <tag>
            <name>param</name>
            <type>split</type>
            <symbol>~</symbol>
            <item>name</item>
            <item>select</item>
            <item>type</item>
            <item>explain</item>
        </tag>
        <tag>
            <name>returnparam</name>
            <type>split</type>
            <symbol>~</symbol>
            <item>name</item>
            <item>type</item>
            <item>explain</item>
        </tag>
        <tag>
            <name>uri</name>
            <type>string</type>
        </tag>
        <tag>
            <name>description</name>
            <type>string</type>
        </tag>
        <tag>
            <name>type</name>
            <type>string</type>
        </tag>
        <tag>
            <name>returnjson</name>
            <type>string</type>
        </tag>
        <tag>
            <name>path</name>
            <type>string</type>
        </tag>
        <tag>
            <name>methodname</name>
            <type>string</type>
        </tag>
        <tag>
            <name>html</name>
            <type>string</type>
        </tag>

        <tag>
            <name>java</name>
            <type>string</type>
        </tag>
        <viewpath>
            <name>html</name>
            <value>D:/model/Method.ftl</value>
        </viewpath>
        <viewpath>
            <name>java</name>
            <value>D:/model/javaTemplate.ftl</value>
        </viewpath>
    </method>
</doclet>