# Fx.object


Fx.object：和对象操作有关的API    

#### 1、创建业务对象-create

- **对象创建同时新建从对象**：Fx.object.create(&lt;String apiName&gt;,&lt;Map&lt;String,Map&gt; objectData&gt;,&lt;Map details&gt;,&lt;boolean withBizLogic&gt;)

&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

  参数 |说明
-|-
apiName | 对象的api名称
objectData | 主对象数据即字段值
details | 从对象数据
withBizLogic | 是否执行业务逻辑，默认为true（该属性已经废除，无法生效。如果需要执行业务逻辑，请使用ActionAttribute参数） 

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

​	举例：

 >      def(Boolean error,Map data,String errorMessage) = Fx.object.create("object_2fJ1o__c",["name":"主从同时新建主1"],["object_Ssm46__c":[["name":"张三1"]]],true)

- **注意**：

  主对象创建同时新建从对象，从对象可以传空值。

  此函数不会触发新建对象业务逻辑（withBizLogic参数已废弃无效）。

----------

  - **对象创建同时新建从对象（ActionAttribute）**：Fx.object.create(&lt;String apiName&gt;,&lt;Map&lt;String,Map&gt; objectData&gt;,&lt;Map details&gt;,&lt;ActionAttribute attribute&gt;)

&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

| 参数       | 说明                                                         |
| ---------- | ------------------------------------------------------------ |
| apiName    | 对象的api名称                                                |
| objectData | 主对象数据即字段值                                           |
| details    | 从对象数据                                                   |
| attribute  | triggerApprovalFlow:是否触发审批流<br/>triggerWorkflow:是否触发工作流<br/>skipFunctionAction:是否跳过前后动作函数，这个参数设置为true，前后动作的函数都会跳过<br/>specifyCreatedBy:默认的创建人是系统，通过参数指定在objectData中设置指定的key:&lt;created_by:List&lt;String&gt;&gt;传入人员的id可以设置创建人<br/>specifyTime:指定创建时间，默认的创建时间是当前时间，通过指定参数可以在objectData中设置指定的key:create_time，可以设置创建时间，时间的格式是毫秒时间戳<br/>使用方法详见下方示例 |

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

&nbsp;&nbsp;&nbsp;&nbsp; 举例：

 >      ActionAttribute attribute = ActionAttribute.build {
 >                  triggerApprovalFlow = false
 >                  triggerWorkflow = false
 >                  skipFunctionAction = true
 >                  specifyCreatedBy = true
 >                  specifyTime = true
 >      }
 >      def ret = Fx.object.create("AccountObj",["name":"1234",created_by:["1000"],create_time:*************],null,attribute)
 >      Fx.log.info(ret)

- **注意**：

  主对象创建同时新建从对象，从对象可以传空值。

------



#### 2、批量新建-batchCreate

 - **批量新建**：

   Fx.object.batchCreate(&lt;String apiName&gt;,&lt;List&lt;Map&gt; objectData&gt;)

   Fx.object.batchCreate(&lt;String apiName&gt;,&lt;List&lt;Map&gt; objectData&gt;,&lt;boolean triggerWorkflow&gt;)

&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

参数 |说明
-|-
apiName | 对象的api名称
objectData | 对象数据即字段值
triggerWorkflow | 是否触发工作流，默认为false 

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：List

&nbsp;&nbsp;&nbsp;&nbsp; 举例：

 >      def(Boolean error,List data,String errorMessage) = Fx.object.batchCreate("AccountObj",[["name":"客户1"],["name":"客户2"]],true)

- **注意**：

  预设对象不推荐使用这个API批量创建。

  batch类函数不会不会触发某些对象业务逻辑（判断权限、触发审批流等），是否触发工作流由参数控制。
----------

#### 3、更新业务对象-update

 - **定义**：

   Fx.object.update(&lt;String apiName&gt;,&lt;String objectDataId&gt;,&lt;Map objectData&gt;)

   Fx.object.update(&lt;String apiName&gt;,&lt;String objectDataId&gt;,&lt;Map objectData&gt;, &lt;boolean triggerWorkflow&gt;)

 - **参数说明**：

参数 |说明
 -|-
apiName | 对象的api名称
objectDataId | 对象实例的ID
objectData | 对象数据即字段值
triggerWorkflow | 是否触发工作流，默认为true 

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) =  Fx.object.update("AccountObj","id123456",["name":"纷享销客"],false)
 - **注意**：此函数不会触发部分更新对象业务逻辑（判断权限、触发审批流等），是否触发工作流由参数控制。

----------
#### 4、批量更新业务对象-batchUpdate

 - **全量更新（更新字段map中没有的字段也会取当前数据的字段值进行覆盖更新）**：Fx.object.batchUpdate(&lt;String apiName&gt;,&lt;Map&lt;String,Map&gt; objectData&gt;)

 - **参数说明**：

参数 |说明
 -|-
apiName | 对象的api名称
objectData | 对象数据即字段值（key值为对象ID）

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：List

 - **举例：**


 >     def (Boolean error,List data,String errorMessage) =  object.batchUpdate("AccountObj",["e6a338ae8a944cdfb2bae737db1aa12f":["name":"客户1"],"4cd5a9f902af4f66a34df35a53630237":["name":"客户2"]])



 - **增量更新（只会更新map中存在的字段，每条数据的更新字段必须一致）**：Fx.object.batchUpdate(String apiName, Map objects, List fields)

 - **参数说明**：

| 参数    | 说明                                                  |
| ------- | ----------------------------------------------------- |
| apiName | 对象的api名称                                         |
| objects | 批量更新的数据信息（每条数据的更新字段必须一致）      |
| fields  | 批量更新的字段里列表（必选和objects中设置的字段一致） |

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：List

 - **举例：**


 >     ```
 >     Map batch = [
 >       '60acc4a2d040a70001886739': [
 >         'field_5h77a__c': 'test1'
 >       ],
 >       '60acc482d040a70001886582': [
 >         'field_5h77a__c': 'test2'
 >       ]
 >     ]
 >     List fields = ['field_5h77a__c']
 >     def (Boolean error,List data,String errorMessage) = Fx.object.batchUpdate('object_8N0H2__c', batch, fields)
 >     if (error) {
 >       log.info(errorMessage)
 >     } else {
 >       log.info(data)
 >     }
 >     ```

&nbsp;&nbsp;&nbsp;&nbsp; 注：batch类函数不会不会触发对象业务逻辑（判断权限、触发审批流工作流等）。

----------

#### 5、按业务对象Id查询业务对象数据象-findById

 - **定义**：Fx.object.findById(&lt;String apiName&gt;,&lt;String objectDataId&gt;)
 - **参数说明**：

参数 |说明
 -|-
apiName | 对象的api名称
objectDataId | 对象实例的ID

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) =  Fx.object.findById("AccountObj","e6a338ae8a944cdfb2bae737db1aa12f") 

----------

#### 6、批量按业务对象Id查询业务对象数据-findByIds

 - **定义**：Fx.object.findByIds(&lt;String apiName&gt;,&lt;List objectDataIds&gt;)
 - **参数说明**：

参数 |说明
 -|-
apiName | 对象的api名称
objectDataIds | 对象实例的ID的List

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：List

 - **举例：**


 >     def (Boolean error,List data,String errorMessage) =  Fx.object.findByIds("AccountObj",["e6a338ae8a944cdfb2bae737db1aa12f","4cd5a9f902af4f66a34df35a53630237"]) 

----------

#### 7、按查询条件查询业务对象-find

 - **普通查询**：Fx.object.find(&lt;String apiName&gt;,&lt;List&lt;Map&gt; criteria&gt;,&lt;BigDecimal limit&gt;,&lt;BigDecimal skip&gt;)
参数说明：

参数 |说明
 -|-
apiName | 对象的api名称
criteria | 查询条件 
limit | 限制查询条数，最大100条，如超过返回100条，使用skip参数分批查询
skip | 跳过多少数据

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：QueryResult

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：

 >     def (Boolean error,QueryResult data,String errorMessage) =  Fx.object.find("AccountObj",[["name":"纷享销客"],["create_time": Operator.GT(*************)]],10,0); 

 - **查询并排序**：Fx.object.find(&lt;String apiName&gt;,&lt;List&lt;Map&gt; criteria&gt;,&lt;Map orderBy&gt;,&lt;BigDecimal limit&gt;,&lt;BigDecimal skip&gt;)
参数说明：

参数 |说明
 -|-
apiName | 对象的api名称
criteria | 查询条件
limit | 限制查询条数，最大100条，如超过返回100条，使用skip参数分批查询
orderBy | 排序规则  key:按哪个字段排序，字段名称 ；value：1 - 升序，-1 - 降序
skip | 跳过多少数据

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：QueryResult

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：

 >     def (Boolean error,QueryResult data,String errorMessage) =  Fx.object.find("AccountObj",[["name":"分享逍客"],["create_time": Operator.GT(*************)]],["name":1],10,0);

  - **使用用户身份查询数据**：Fx.object.find(&lt;String apiName&gt;,&lt;List&lt;Map&gt; criteria&gt;,&lt;Map orderBy&gt;,&lt;BigDecimal limit&gt;,&lt;BigDecimal skip&gt;,&lt;Boolean useCurrentIdentity&gt;)
 参数说明：

 参数 |说明
  -|-
 apiName | 对象的api名称
 criteria | 查询条件
 limit | 限制查询条数，最大100条，如超过返回100条，使用skip参数分批查询
 orderBy | 排序规则  key:按哪个字段排序，字段名称 ；value：1 - 升序，-1 - 降序
 skip | 跳过多少数据
 useCurrentIdentity | 是否使用用户身份。注意流程后动作执行的函数,用户的身份依旧是系统。其他函数使用场景，例如按钮点击，可以获取用户的身份。

 &nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：QueryResult

 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：

  >     def (Boolean error,QueryResult data,String errorMessage) =  Fx.object.find("AccountObj",[["name":"分享逍客"],["create_time": Operator.GT(*************)]],["name":1],10,0,true);

Fx.object.find方法中的条件语句（使用Operator.调用，如 Operator.GT(*************)）如下：

说明 |代码格式
 -|-
判断相等 | EQ(&lt;Object str&gt;) 文本查询条件区分大小写 
判断不相等 | NE(&lt;Object str&gt;)
判断大于 | GT(&lt;Object str&gt;)
判断小于 | LT(&lt;Object str&gt;)
判断大于等于 | GTE(&lt;Object str&gt;)
判断小于等于 | LTE(&lt;Object str&gt;)
判断是否包含 | LIKE(&lt;String str&gt;) 文本查询条件不区分大小写 
判断不包含 | NLIKE(&lt;String str&gt;)
判断属于其中一个 | IN(&lt;List str&gt;)
判断不属于其中 | NIN(&lt;List list&gt;)
判断字段是否有值 | EXISTS(&lt;boolean ex&gt;)

- 注意

  条件中的日期格式使用毫秒时间戳

----------

#### 8、作废业务对象-remove

 - **定义**：Fx.object.remove(&lt;String apiName&gt;,&lt;String objectDataId&gt;)
 - **参数说明**：

参数 |说明
 -|-
apiName | 对象的api名称
objectDataId | 对象实例的ID

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) =  Fx.object.remove("AccountObj","ed47841898054749a2ec9be9e6e5d728")

----------
#### 9、更换负责人-changeOwner

 - **定义**：Fx.object.changeOwner(&lt;String objectAPIName&gt;,&lt;String ObjectDataId&gt;,&lt;String OwnerId&gt;)
 - **参数说明**：

参数 |说明
 -|-
objectAPIName | 对象的api名称
objectDataId | 对象实例的ID
owner | 要变更的负责人的用户ID

&nbsp;&nbsp;&nbsp;&nbsp; 无返回值


 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) = Fx.object.changeOwner("AccountObj","ed47841898054749a2ec9be9e6e5d728","1001")

----------
 - **定义(ActionAttribute参数)**：Fx.object.changeOwner(&lt;String objectAPIName&gt;,&lt;String objectDataId&gt;,&lt;String ownerId&gt;,&lt;ActionAttribute attribute&gt;)
 - **参数说明**：

| 参数          | 说明                   |
| ------------- | ---------------------- |
| objectAPIName | 对象的api名称          |
| objectDataId  | 对象实例的ID           |
| ownerId       | 要变更的负责人的用户ID |
| attribute     | 控制触发行为           |

```
ActionAttribute attribute = ActionAttribute.build{
  //跳过审批流
  triggerApprovalFlow = false
  //跳过工作流
  triggerWorkflow = false
  //跳过前后动作函数
  skipFunctionAction = true
}
Fx.object.changeOwner("APIName","objectId","ownerId",attribute)
```

------

#### 10、添加团队成员-addTeamMember

 - **定义**：Fx.object.addTeamMember(&lt;String ObjectAPIName&gt;,&lt;String ObjectDataId&gt;,&lt;List UserIdList&gt;,&lt;Integer Role&gt;,&lt;Integer Permission&gt;)
 - **参数说明**：

参数 |说明
 -|-
ObjectAPIName | 对象的api名称
ObjectDataId | 对象实例的ID
UserIdList | 添加的团队成员的用户ID的List
Role | 添加的团队成员的角色：1-负责人，2-联合跟进人，3-售后服务人员，4-普通成员
Permission | 添加的团队成员的权限：1-只读，2-读写

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) = Fx.object.addTeamMember("AccountObj","83cf73d957924284a96e9c44ebb333ec",["1001"],4,1)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：不能添加负责人；如果添加的成员包括负责人，则不会修改负责人数据；如果添加的成员在原系统中有重复的则更新该成员

----------
#### 11、删除团队成员-deleteTeamMember

 - **定义**：Fx.object.deleteTeamMember(&lt;String ObjectAPIName&gt;,&lt;String ObjectDataId&gt;,&lt;List UserIdList&gt;)
 - **参数说明**：

参数 |说明
 -|-
ObjectAPIName | 对象的api名称
ObjectDataId | 对象实例的ID
UserIdList | 删除的团队成员的用户ID的List

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) = Fx.object.deleteTeamMember("AccountObj","83cf73d957924284a96e9c44ebb333ec",["1001"]) 

----------
#### 12、编辑团队成员-editTeamMember

 - **定义**：Fx.object.editTeamMember(&lt;String ObjectAPIName&gt;,&lt;String ObjectDataId&gt;,&lt;List&lt;Map&gt; TeamMemberList&gt;)

 - **参数说明**：

参数 |说明
 -|-
ObjectAPIName | 对象的api名称
ObjectDataId | 对象实例的ID
TeamMemberList | 要编辑团队成员的信息的List（key值包括：userID：用户ID；role：用户角色；permisson：用户权限，具体参考7）

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) = Fx.object.editTeamMember("AccountObj","36fd270a986842529445bf3d252cca9b",[["userId":"1058","role":4,"permission":1],["userId":"1057","role":3,"permission":2]]) 

----------
#### 13、获取团队成员-getTeamMember

 - **定义**：Fx.object.getTeamMember(&lt;String objectAPIName&gt;,&lt;String objectId&gt;)
 - **参数说明**：

参数 |说明
 -|-
objectAPIName | 对象的api名称
objectId | 对象实例的ID

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) = Fx.object.getTeamMember("AccountObj","83cf73d957924284a96e9c44ebb333ec")

----------
#### 14、添加外部团队成员-addOutTeamMember

 - **定义**：Fx.object.addOutTeamMember(String apiName,String objectId,int permission,List&lt;Map&gt; employee) 

- **参数说明**：

参数 |说明
 -|-
apiName | 对象的apiname
objectId | 对象实例的ID
permission | 外部团队成员权限 1：只读  2：读写
employee | 员工信息，其中Map包括【 userId：员工Id ； outTenantId：外部企业id】

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：String

 - **举例：**


 >     def (Boolean error,String data,String errorMessage) = Fx.object.addOutTeamMember('AccountObj',id,1,[['userId':'1001','outTenantId':'590057']])

----------
#### 15、获取单选/多选业务名称/选项名称-getOptionName

 - **定义**：Fx.object.getOptionName(&lt;String objectAPIName&gt;,&lt;String filedAPIName&gt;,&lt;String value&gt;)
 - **参数说明**：

参数 |说明
 -|-
objectAPIName | 对象的api名称
filedAPIName | 字段的api名称
value | 单选/多选的值

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,String data,String errorMessage) = Fx.object.getOptionName("AccountObj","lock_status","0")

----------
#### 16、根据映射规则创建数据-copyByRule

 - **根据映射规则新建（不能添加额外的字段）**：Fx.object.copyByRule(&lt;String sourceApiName&gt;,&lt;String sourceId&gt;,&lt;String ruleApiName&gt;)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

参数 |说明
 -|-
sourceApiName | 被映射的对象Api Name
sourceId | 被映射的对象实例的ID
ruleApiName | 映射规则API Name

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：


 >     def (Boolean error,Map data,String errorMessage) = Fx.object.copyByRule('object_ejyW2__c','5d308dc0b5a2bf0001b0bfc2','map_btp50__c')

- **根据映射规则直新建（同时新建从对象）**：Fx.object.copyByRule(&lt;String sourceApiName&gt;,&lt;String sourceId&gt;,&lt;String ruleApiName&gt;,&lt;Map plus&gt;,  &lt;Map detailPlus&gt;)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

参数 |说明
 -|-
sourceApiName | 被映射的对象Api Name
sourceId | 被映射的对象实例的ID
ruleApiName | 映射规则API Name
plus | 主对象数据参数
detailPlus | 从对象数据参数

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：

 >     Map plus = ["field_ZszsOc": "18800154471"];
 >     Map detailValues1 = ["field_z5AI0c": "data1填充内容"]; 
 >     Map detailValues2 = ["field_z5AI0c": "data2填充内容"];
 >     List  detailFillValueList= [];
 >     detailFillValueList.add( detailValues1);
 >     detailFillValueList.add( detailValues2);
 >     Map detailPlus = ["object_6hN1ic": detailFillValueList]
 >     def (Boolean error,Map data,String errorMessage) = Fx.object.copyByRule('object_ob2G0__c','5cedf0137cfed9b33b75ddaa','map_797K4__c',plus,detailPlus)

----------
#### 17、数据锁定/解锁-lock/unlock

 - **数据锁定**：Fx.object.lock(String apiName , String objectId , boolean cascadeDetail)
 - **数据解锁**：Fx.object.unlock(String apiName , String objectId , boolean cascadeDetail)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

参数 |说明
 -|-
apiName | 对象的apiname
objectId | 对象实例id
cascadeDetail | 是否锁定/解锁从对象

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：null

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：


 >     def (Boolean error,data,String errorMessage) =Fx.object.lock('AccountObj' , 'e6a338ae8a944cdfb2bae737db1aa12f' , true)

----------
#### 18、聚合计算-aggregate

 - **定义**：Fx.object.aggregate(String apiName,Aggregate type,int decimalScale,List criteria)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

参数 |说明
 -|-
apiName | 对象的apiname
Aggregate | 计算类型
decimalScale | 小数位数
criteria | 查询条件（和find查询条件使用一样）

其中计算类型：

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**Aggregate.SUM(String fieldApiName)**   求和  

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**Aggregate.COUNT()**                    计算数量

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**Aggregate.MAX(String fieldApiName)**   最大值

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**Aggregate.MIN(String fieldApiName)**   最小值

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;**Aggregate.AVG(String fieldApiName)**   平均值

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：String

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：


 >     def (Boolean error,String data,String errorMessage) =Fx.object.aggregate("object_rqa45__c",Aggregate.AVG("field_VE1by__c"),2,[["name":Operator.LIKE("name")])


----------

#### 19、查询包括作废数据的API	- findIncludeInvalided

 - **定义**：Fx.object.findIncludeInvalided(String apiName, List criteria, int limit, int skip);
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fx.object.findIncludeInvalided(String apiName,List criteria,Map orderBy ,int limit ,int skip);
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;传参、返回值和调用方法参考find函数。

----------

#### 20、单多选/业务类型查询	- getOptionInfo

 - **定义**：Fx.object.getOptionInfo(String apiName,String fieldAPIName);

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

参数 |说明
 -|-
apiName | 对象的apiname
fieldAPIName | 单多选/业务类型的API名称

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：map(label -> value , value -> label 的双向映射)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：
 >     def(boolean error,Map result,String errorMessage) = Fx.object.getOptionInfo("object_i66LR__c","record_type")

----------

#### 21、批量锁定/解锁- batchLock/batchUnlock

 - **锁定**：Fx.object.batchLock(String apiName,List objectIds,boolean cascadeDetail);
 - **解锁**：Fx.object.batchUnlock(String apiName,List objectIds,boolean cascadeDetail);

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

参数 |说明
 -|-
apiName | 对象APIName
objectIds | 数据id
cascadeDetail | 是否递归锁定从对象数据

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：无

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：
 >     def (Boolean error,data,String errorMessage) =Fx.object.batchLock('AccountObj' , ["e6a338ae8a944cdfb2bae737db1aa12f"] , true)
 >     def (Boolean error,data,String errorMessage) =Fx.object.batchUnlock('AccountObj' , ["e6a338ae8a944cdfb2bae737db1aa12f"] , true)

------

#### 22、批量作废- bulkRemove

 - **定义**

   Fx.object.bulkRemove(String apiName,List&lt;String&gt; objectIds)

   Fx.object.bulkRemove(String apiName,List&lt;String&gt; objectIds, ActionAttribute attribute)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参数说明：

| 参数            | 说明                                                         |
| --------------- | ------------------------------------------------------------ |
| apiName         | 对象APIName                                                  |
| objectIds       | 数据id                                                       |
| ActionAttribute | triggerApprovalFlow:是否触发审批流(这个参数true才有效，因为批量作废是异步的，不管有没有配置审批流都不会触发审批流)<br/>triggerWorkflow:是否触发工作流<br/>skipFunctionAction:是否跳过前后动作函数，这个参数设置为true，前后动作的函数都会跳过<br/>使用方法详见下方示例 |

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map,包含objectDataList,里面是删除的对象数据

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;举例：
 >     ```
 >     ActionAttribute attribute = ActionAttribute.build{
 >       triggerApprovalFlow = false
 >       triggerWorkflow = f
 >       skipFunctionAction = true
 >     }
 >     def ret = Fx.object.bulkRemove("object_oMTq9__c",objectIds,attribute)
 >     Fx.log.info(ret)object.batchUnlock('AccountObj' , ["e6a338ae8a944cdfb2bae737db1aa12f"] , true)
  >     ```
------

#### 23、批量更换负责人-batchChangeOwner

 - **定义**：Fx.object.batchChangeOwner(&lt;String objectAPIName&gt;,&lt;List&lt;Map&gt; datas&gt;,&lt;ActionAttribute attribute&gt;)
 - **参数说明**：

| 参数          | 说明                           |
| ------------- | ------------------------------ |
| objectAPIName | 对象的api名称                  |
| datas         | 要变更的负责人的数据ID和用户ID |
| attribute     | 控制触发行为                   |

&nbsp;&nbsp;&nbsp;&nbsp; 无返回值


 - **举例：**


 >     ```
 >     Map changeData1 = [
 >       "objectId":"5f86b47b1bdac00001f2c300",
 >       "ownerId":["-10000"]
 >     ]
 >     Map changeData2 = [
 >       "objectId":"5f86b4a71bdac00001f2d232",
 >       "ownerId":["-10000"]
 >     ]
 >     List arg = [changeData1,changeData2]
 >     def ret = Fx.object.batchChangeOwner("object_i66LR__c",arg,	attribute)
 >     Fx.log.info(ret)
 >     ```

------

#### 24、物理删除数据-delete,bulkDelete

 - **定义**：

   Fx.object.delete(String objectAPIName, String objectId)

   Fx.object.bulkDelete(String objectAPIName , List&lt;String&gt; objectIds)

 - **参数说明**：

| 参数               | 说明             |
| ------------------ | ---------------- |
| objectAPIName      | 对象的api名称    |
| objectId,objectIds | 物理删除的数据id |

&nbsp;&nbsp;&nbsp;&nbsp; 无返回值


 - **举例：**


 >     ```
 >     //这个是一个十分高危的操作，并且是物理删除
 >     
 >     String objectAPIName = "AccountObj"
 >     String objectId = "60057c76a383690001243bb4"
 >     
 >     def ret = Fx.object.delete(objectAPIName,objectId)
 >     if( ret[0] ){
 >       Fx.log.info("删除数据出错  ： " + ret[2])
 >     }
 >     
 >     String objectAPIName = "AccountObj"
 >     String objectIds = ["60057c76a383690001243bb4"]
 >     
 >     def ret = Fx.object.delete(objectAPIName,objectIds)
 >     if( ret[0] ){
 >       Fx.log.info("删除数据出错  ： " + ret[2])
 >     }
 >     ```

------

#### 25、使用QueryTemplate调用find函数

- 可以用来做或查询，使用此函数需要开通列表页或查询功能

 - **定义**：

   Fx.object.find(String apiName, QueryTemplate queryTemplate, Map orderBy, int limit, int skip)

 - **参数说明**：

| 参数          | 说明                                                         |
| ------------- | ------------------------------------------------------------ |
| apiName       | 对象的api名称                                                |
| queryTemplate | 查询条件queryTemplate                                        |
| orderBy       | 排序规则  key:按哪个字段排序，字段名称 ；value：1 - 升序，-1 - 降序 |
| limit         | 限制查询条数，最大100条，如超过返回100条，使用skip参数分批查询 |
| skip          | 跳过多少数据                                                 |

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：QueryResult


 - **举例：**


 >     ```
 >     QueryTemplate template1 = QueryTemplate.AND(
 >       ["name": Operator.LIKE("测试")],
 >     )
 >     QueryTemplate template2 = QueryTemplate.AND(
 >       ["field_99psr__c":Operator.EQ("78057")]
 >     )
 >     
 >     //QueryTemplate使用OR条件是灰度功能
 >     QueryTemplate template = QueryTemplate.OR(template1,template2)
 >     def (Boolean error,QueryResult data,String errorMessage) = Fx.object.find("object_6177t__c", template, ["name":1], 10, 0);
 >     if (error) {
 >       log.info(errorMessage)
 >     } else {
 >       log.info(data)
 >     }
 >     ```

------

#### 26、关联查询（只支持子查主）- findWithRelated

- 通过传入关联的字段名实现一条查询把主子或查找关联数据同时查出来

 - **定义**：

   Fx.object.findWithRelated(String apiName, String relatedField, List criteria, Map orderBy, int limit, int skip)

 - **参数说明**：

| 参数         | 说明                                                         |
| ------------ | ------------------------------------------------------------ |
| apiName      | 对象的api名称                                                |
| relatedField | 关联关系字段的API名称                                        |
| criteria     | 查找条件                                                     |
| orderBy      | 排序规则  key:按哪个字段排序，字段名称 ；value：1 - 升序，-1 - 降序 |
| limit        | 限制查询条数，最大100条，如超过返回100条，使用skip参数分批查询 |
| skip         | 跳过多少数据                                                 |

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：QueryResult


 - **举例：**


 >     ```
 >     def (Boolean error,QueryResult data,String errorMessage) = Fx.object.findWithRelated('object_0uyAd__c', 'field_YjJ6d__c', [["_id":"60868215965b1900014c0d35"]], [:], 1, 0)
 >     if (error) {
 >       log.info(errorMessage)
 >     } else {
 >       log.info(data)
 >     }
 >     List dataList = data['dataList'] as List
 >     dataList.each { item->
 >       log.info(item['object_0uyAd__c'])
 >       log.info(item['AccountObj'])
 >     }
 >     ```

