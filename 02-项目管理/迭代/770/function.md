# ${functionClass}

 ${functionClassDesc}

 #### 3、更新业务对象-update
 #### ${commentText}-${methodName}
 - **定义**：
    ${example}
   Fx.object.update(&lt;String apiName&gt;,&lt;String objectDataId&gt;,&lt;Map objectData&gt;)

   Fx.object.update(&lt;String apiName&gt;,&lt;String objectDataId&gt;,&lt;Map objectData&gt;, &lt;boolean triggerWorkflow&gt;)

 - **参数说明**：

参数 |说明
 -|-
apiName | 对象的api名称
objectDataId | 对象实例的ID
objectData | 对象数据即字段值
triggerWorkflow | 是否触发工作流，默认为true 

&nbsp;&nbsp;&nbsp;&nbsp; data返回值类型：Map

 - **举例：**


 >     def (Boolean error,Map data,String errorMessage) =  Fx.object.update("AccountObj","id123456",["name":"纷享销客"],false)
 - **注意**：此函数不会触发部分更新对象业务逻辑（判断权限、触发审批流等），是否触发工作流由参数控制。

----------

