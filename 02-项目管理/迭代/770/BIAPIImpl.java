package com.fxiaoke.functions;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.common.entities.*;
import com.facishare.bi.common.entities.stat.FilterList;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.functions.dto.BIChartConfig;
import com.fxiaoke.functions.dto.BIDataQuery;
import com.fxiaoke.functions.dto.BIFilters;
import com.fxiaoke.functions.exception.FunctionAPIException;
import com.fxiaoke.functions.remote.resource.BIRestResource;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BIAPIImpl extends AbstractFunctionProvider implements BIAPI {

    @Resource
    private BIRestResource biRestResource;
    @Override
    public APIResult getRules(String id) {
        if (Strings.isNullOrEmpty(id)) {
            throw new FunctionAPIException("Illegal arguments, id should not be null");
        }
        BIFilters.Result filtersResult = getFiltersResult(id);
        Map<String, Object> map = Maps.newHashMap();
        wrapFilterAndRule(filtersResult, map);
        map.put("measureFields", queryChartConfig(id));
        return APIResult.ok(map);
    }

    private BIFilters.Result getFiltersResult(String id) {
        BIDataQuery.Arg arg = BIDataQuery.Arg.builder()
                .id(id)
                .isView(1)
                .build();
        BIFilters.Result filtersResult = biRestResource.getFiltersResult(getContext().getEa(),
                getContext().getTenantId(),
                getCurrentUser().getUserId(),
                arg);
        return filtersResult;
    }

    private void wrapFilterAndRule(BIFilters.Result filtersResult, Map<String, Object> map) {
        if (Objects.nonNull(filtersResult)) {
            List<Map<String, Object>> userFilters = Lists.newArrayList();
            List<FilterList> filterList = filtersResult.getFilterLists();
            if (CollectionUtils.notEmpty(filterList)) {
                filterList.forEach(filters -> {
                    filters.getFilters().stream()
                            .forEach(filter -> {
                                if (Objects.equals("rule_id", filter.getFieldLocation())) {
                                    FieldTypeInfo fieldTypeInfo = filter.getUi();
                                    if (Objects.nonNull(fieldTypeInfo)){
                                        List<EnumItem> data = fieldTypeInfo.getData();
                                        List<Map> mapList = JSON.parseArray(JSON.toJSONString(data), Map.class);
                                        map.put("rules",mapList);
                                    }
                                } else {
                                    Map<String, Object> filterMap = Maps.newHashMap();
                                    filterMap.put("fieldId", filter.getFieldId());
                                    filterMap.put("fieldName", filter.getFieldName());
                                    userFilters.add(filterMap);
                                }
                            });

                });
                map.put("userFilters",userFilters);
            }
            List<BIFilters.LabelAndOption> labelAndOptions = filtersResult.getLabelAndOptions();
            if (CollectionUtils.notEmpty(labelAndOptions)) {
                labelAndOptions.stream().forEach(labelAndOption -> {
                    List<BIFilters.FilterOption> defaultFilterOptions = labelAndOption.getDefaultFilterOptions();
                    if (CollectionUtils.notEmpty(defaultFilterOptions)) {
                        List<Map> mapList = JSON.parseArray(JSON.toJSONString(defaultFilterOptions), Map.class);
                        map.put("defaultFilterOptions", mapList);
                    }
                });
            }
        }
    }


    private List<Map> queryChartConfig(String id) {
        BIChartConfig.Result result = getChartConfig(id);
        if (Objects.nonNull(result) && CollectionUtils.notEmpty(result.getMeasureFields())) {
            String measureFields = JSON.toJSONString(result.getMeasureFields());
            return JSON.parseArray(measureFields, Map.class);
        }
        return Lists.newArrayList();
    }

    private BIChartConfig.Result getChartConfig(String id) {
        BIChartConfig.Arg arg = BIChartConfig.Arg.builder()
                .isView(1)
                .id(id)
                .build();
        BIChartConfig.Result result = biRestResource.queryChartConfig(getContext().getEa(),
                getContext().getTenantId(),
                getCurrentUser().getUserId(),
                arg);
        return result;
    }


    /**
     * {
     *     "userFilters": [
     *         {
     *             "fieldId": "BI_5d9ff3331b9ad40001873778",
     *             "targetType": 0,
     *             "value": [
     *                 1000,
     *                 1200
     *             ]
     *         },
     *         {
     *             "fieldId": "BI_5d9ff3331b9ad40001873778",
     *             "targetType": 1,
     *             "value": [
     *                 1000,
     *                 1200
     *             ]
     *         }
     *     ],
     *     "dateFilters": {
     *         "fieldId": "BI_5d9ff3331b9ad40001873778",
     *         "startDateTime": "2017-07-09 00:00:00",
     *         "endDateTime": "2022-07-12 23:59:59"
     *     },
     *     "ruleFilters": {
     *         "optionCode": [
     *             "603325bf9ccad50001cd06ef"
     *         ],
     *         "isChild": 0
     *     }
     * }
     * }
     * @param id
     * @param optionID
     * @param filters
     * @param measureFieldIds
     * @return
     */
    @Override
    public APIResult getTargetCompletion(String id, String optionID, Map filters, List measureFieldIds) {
        if (Strings.isNullOrEmpty(id) || Strings.isNullOrEmpty(optionID)) {
            throw new FunctionAPIException("Illegal arguments, id or optionID should not be null");
        }
        if (!CollectionUtils.notEmpty(filters) || !CollectionUtils.notEmpty(measureFieldIds)) {
            throw new FunctionAPIException("Illegal arguments, filters or measureFieldIds should not be null or empty");
        }
        BIFilters.Result filtersResult = getFiltersResult(id);
        // 校验optionID待补充

        BIChartConfig.Result chartConfig = getChartConfig(id);
        List<MeasureFieldDto> measureFieldList = null;
        if (Objects.nonNull(chartConfig) && CollectionUtils.notEmpty(chartConfig.getMeasureFields())){
            measureFieldList = chartConfig.getMeasureFields().stream()
                    .filter(measureField -> measureFieldIds.contains(measureField.getFieldID()))
                    .collect(Collectors.toList());
        } else {
            return APIResult.error("please check your chart config");
        }
        List<FilterList> filterLists = null;
        if (Objects.nonNull(filtersResult) && CollectionUtils.notEmpty(filtersResult.getFilterLists())) {
            Map<String,String> idValueMap = Maps.newHashMap();
            fillValue(filters, idValueMap);
            filterLists = filtersResult.getFilterLists();
            filterLists.stream().forEach(f -> f.getFilters().stream().forEach(filter -> {
                String value = idValueMap.get(filter.getFieldId());
                if (!Strings.isNullOrEmpty(value)) {
                    if (value.contains("\\|")) {
                        String[] split = value.split("\\|");
                        filter.setValue1(split[0]);
                        filter.setValue1(split[1]);
                    } else {
                        filter.setValue1(value);
                    }
                }
            }));
        }
        BIDataQuery.Arg arg = BIDataQuery.Arg.builder()
                .id(id)
                .isView(1)
                .defaultFilterOptionIDs(Lists.newArrayList(optionID))
                .filterLists(filterLists)
                .measureFields(measureFieldList)
                .build();
        BIDataQuery.Result result = biRestResource.query(getContext().getEa(),
                getContext().getTenantId(),
                getCurrentUser().getUserId(),
                arg);
        List<Map<String,Object>> list = Lists.newArrayList();
        if (CollectionUtils.notEmpty(result.getDataSet())) {
            result.getDataSet().stream().forEach(statColumnDataDto -> {
                Map<String,Object> map = Maps.newHashMap();
                List<Object> valueList = Lists.newArrayList();
                map.put("fieldID",statColumnDataDto.getFieldID());
                map.put("fieldName",statColumnDataDto.getFieldName());
//                map.put("fieldType",statColumnDataDto.getFieldType());
                map.put("value",valueList);
                statColumnDataDto.getValue().stream().forEach(cell -> {
                    Map<String,Object> cellMap = Maps.newHashMap();
                    cellMap.put("formattedValue",cell.getFormattedValue());
                    cellMap.put("formattedShowValue",cell.getFormattedShowValue());
                    valueList.add(cellMap);
                });
                list.add(map);
            });
        }
        return APIResult.ok(list);
    }

    private void fillValue(Map filters, Map<String, String> idValueMap) {
        if (CollectionUtils.notEmpty((List)(filters.get("userFilters")))) {
            List<Map<String,Object>> userFilters = (List)(filters.get("userFilters"));
            userFilters.stream().forEach(f -> {
                String targetType = String.valueOf(f.get("targetType"));
                String fieldId = String.valueOf(f.get("fieldId"));
                List<Integer> value = (List)(f.get("value"));
                if (!Strings.isNullOrEmpty(targetType) && !Strings.isNullOrEmpty(fieldId) && CollectionUtils.notEmpty(value)) {
                    List<UserOwner> userOwnerList = Lists.newArrayList();
                    value.stream().forEach(v -> {
                        UserOwner userOwner = new UserOwner();
                        userOwner.setId(String.valueOf(v));
                        userOwner.setType(Objects.equals("0",targetType) ? "p" : "g");
                        userOwnerList.add(userOwner);
                    });
                    idValueMap.put(String.valueOf(f.get("fieldId")),JSON.toJSONString(userOwnerList));
                }
            });
        }
        Map<String,String> dateFilters = (Map) (filters.get("dateFilters"));
        if (CollectionUtils.notEmpty(dateFilters)) {
            String startDateTime = dateFilters.get("startDateTime");
            String endDateTime = dateFilters.get("endDateTime");
            if (StringUtils.isNotEmpty(startDateTime) && StringUtils.isNotEmpty(endDateTime)) {
                StringJoiner joiner = new StringJoiner("\\|");
                joiner.add(startDateTime);
                joiner.add(endDateTime);
                idValueMap.put(dateFilters.get("fieldId"), joiner.toString());
            }
        }
    }
}
