# 对象可修改显示字段

### 需求文档

[【平台】07. 对象可修改显示字段 - CRM - 纷享wiki (firstshare.cn)](http://wiki.firstshare.cn/pages/viewpage.action?pageId=152182848)

### 需求分析

1、对象主属性字段可修改字段类型--------前端放开限制修改即可。

2、新建自定义对象时新增api_name为open_display_name的预制字段，默认不开启，用户可开启，开启后不能关闭，不可禁用。

3、新建自定义对象时新增api_name为display_name的预制字段，通过api_name为open_display_name的预制字段判断是否启用。



### 技术方案

## List接口

| URL      | /API/v1/object/${objectApiName}/controller/List              | ${objectApiName}需要替换成对应的对象apiName                  |
| :------- | :----------------------------------------------------------- | ------------------------------------------------------------ |
| 请求方式 | POST                                                         |                                                              |
| 参数格式 | {"object_describe_api_name":"object_50Cjk__c","search_template_id":"60d0461236ba2b000111871a","include_describe":false,"search_template_type":"custom","ignore_scene_filter":false,"ignore_scene_record_type":false,"search_query_info":"{\"limit\":100,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}","pageSizeOption":[20,50,100]} |                                                              |
| 返回格式 | {"layout":{},"describe":{},"data":{},"isHaveDetailObj":true,"objectDescribeExt":{},"isInApprovalWhiteList":false,"supportTag":true,"isSupportGdpr":false} | 对于配置了显示对象1、根据数据的生命状态过滤详情页按钮的时候，如果数据是in_change状态并且当前操作人是审批例外人，则下发编辑按钮。 |

## ListHeader接口

| URL      | /API/v1/object/${objectApiName}/controller/ListHeader        | ${objectApiName}需要替换成对应的对象apiName                  |
| :------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 请求方式 | POST                                                         |                                                              |
| 参数格式 | {"include_layout":true,"apiname":"object_0r1Tl__c","layout_type":"list","layout_by_template":true,"check_edit_permission":true,"list_type":"list"} | object_id：数据idbizInfo：业务信息`biz：业务代码``otherBizId：业务主id（审批流程实例id）`includeDetail：是否返回从对象数据 |
| 返回格式 | {"layout":{},"objectDescribe":{},"templates":[],"baseScenes":[],"isChildObj":true,"supportTag":true,"supportOrFilter":true,"visibleFields":[],"visibleFieldsWidth":[],"buttons":[],"isInApprovalWhiteList":false,"hasEditPermission":true,"filterFields":[],"supportGeoQuery":false,"quickFilterField":[],"viewInfo":[],"summaryInfo":[],"listSingleExposed":0,"sceneRenderType":"drop_down","allPageSummaryInfo":[]} | objectDescribe：主对象的数据details：从对象的数据（审批中的数据） |



````
