String siji  = context.data["field_izXEg__c"] as String


if( !siji.isEmpty() ){
  
  
  def (Boolean error1,QueryResult data1,String errorMessage1) =  Fx.object.find("object_7t4ju__c",[["field_QH1p8__c":siji]],1,0);
  
  log.info(data1.size);
  
  if( data1.size == 1 ){
    
    String yj = data1.dataList[0]["field_v3j19__c"] as String
    String ej = data1.dataList[0]["field_81KLA__c"] as String
    String sj = data1.dataList[0]["field_1i3xy__c"] as String

  Map map = [:]
  map.put("field_2oC8t__c",  yj)
  map.put("field_NyKd4__c",  ej)
  map.put("field_JxtEz__c",  sj)

  def res = Fx.object.update("object_UO28V__c", context.data["_id"] as String,map)
  log.info(res)
    
  }
  
}