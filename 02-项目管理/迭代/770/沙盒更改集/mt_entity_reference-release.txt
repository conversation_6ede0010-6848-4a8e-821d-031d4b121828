{"basicFilterCondition": [], "deleteOriginalData": false, "fieldDescribe": [{"fieldBizType": "TenantId", "fieldType": "String", "name": "tenant_id", "order": 1}, {"fieldBizType": "IdGenerator", "fieldType": "String", "name": "id", "order": 2147483647}, {"fieldType": "String", "name": "source_type", "order": 2147483647}, {"fieldType": "String", "name": "source_value", "order": 2147483647}, {"fieldType": "String", "name": "target_type", "order": 2147483647}, {"fieldType": "String", "name": "target_value", "order": 2147483647}], "inBoundCheck": {"checkTableDescriptors": [], "dependencyTableDescriptors": [], "unchangeableField": [], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": 0, "principalIds": [], "processorDescribe": {"inBoundTag": "PG", "outBoundTag": "PG", "selectHandlerType": {"configName": "PG", "handlerType": "PG"}}, "slaveTableDescribe": [], "tableId": "paas_metadata_mt_entity_reference", "tableName": "mt_entity_reference", "uniqueKeys": ["tenant_id", "source_type", "source_value", "target_type", "target_value"]}