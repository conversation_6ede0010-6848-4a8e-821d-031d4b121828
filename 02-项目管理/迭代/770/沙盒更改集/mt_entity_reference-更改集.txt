{"component": "REFERENCE", "tableId": "paas_metadata_mt_entity_reference", "filters": [{"fieldName": "source_type", "operator": "IN", "order": 1, "values": ["Describe.import.version"]}], "layoutDescribe": [{"displayConvert": "Text", "fieldName": "source_type", "filterType": "TEXT", "i18nLabel": "paas.udobj.reference.source.type", "defaultLabel": "引用关系类型", "order": 1}, {"displayConvert": "Text", "fieldName": "source_label", "filterType": "TEXT", "i18nLabel": "paas.udobj.reference.source.label", "defaultLabel": "引用名称", "order": 2}, {"displayConvert": "Text", "fieldName": "source_value", "filterType": "TEXT", "i18nLabel": "paas.udobj.reference.source.value", "defaultLabel": "引用目标数据", "order": 3}, {"displayConvert": "Text", "fieldName": "target_value", "filterType": "TEXT", "i18nLabel": "paas.udobj.reference.target.value", "defaultLabel": "引用目标名称", "order": 4}]}