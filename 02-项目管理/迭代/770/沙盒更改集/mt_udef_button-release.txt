{"basicFilterCondition": [], "deleteOriginalData": false, "fieldDescribe": [{"fieldBizType": "ObjectApiName", "fieldType": "String", "name": "describe_api_name", "order": 2147483647}, {"fieldBizType": "IdGenerator", "fieldType": "String", "name": "id", "order": 2147483647}, {"fieldBizType": "ApiName", "fieldType": "String", "name": "api_name", "order": 2}, {"fieldBizType": "Name", "fieldType": "String", "name": "label", "order": 2147483647}, {"fieldType": "String", "name": "actions", "order": 2147483647}, {"fieldBizType": "TenantId", "fieldType": "String", "name": "tenant_id", "order": 1}], "inBoundCheck": {"checkTableDescriptors": [], "dependencyTableDescriptors": [{"filters": [], "foreignKeyDescribe": [{"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}, {"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}], "paramFields": [], "processLack": "exception", "tableId": "paas_metadata_mt_describe"}], "unchangeableField": [], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": 0, "principalIds": [], "processorDescribe": {"inBoundTag": "PG", "outBoundTag": "PG", "selectHandlerType": {"configName": "PG", "handlerType": "PG"}}, "slaveTableDescribe": [{"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "id", "operator": "IN", "refFieldName": "actions", "refReader": "JsonPath:$..*"}], "tableId": "paas_metadata_mt_udef_action"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "func_code", "operator": "LIKE", "refFieldName": "describe_api_name,api_name", "refReader": "JOIN:{0}||{1}"}], "tableId": "paas_metadata_fc_func"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "func_code", "operator": "LIKE", "refFieldName": "describe_api_name,api_name", "refReader": "JOIN:{0}||{1}"}], "tableId": "paas_metadata_fc_func_access"}], "tableId": "paas_metadata_mt_udef_button", "tableName": "mt_udef_button", "uniqueKeys": ["tenant_id", "describe_api_name", "api_name"]}