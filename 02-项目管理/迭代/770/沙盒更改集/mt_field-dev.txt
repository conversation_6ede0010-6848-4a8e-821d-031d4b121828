{"basicFilterCondition": [{"fieldName": "status", "operator": "NIN", "order": 1, "values": ["deleted"]}, {"fieldName": "define_type", "operator": "NIN", "order": 2, "values": ["deleted"]}], "deleteOriginalData": false, "fieldDescribe": [{"fieldType": "String", "name": "define_type", "order": 2147483647}, {"fieldBizType": "ObjectApiName", "fieldType": "String", "name": "describe_api_name", "order": 2147483647}, {"fieldBizType": "Name", "fieldType": "String", "name": "field_label", "order": 2147483647}, {"fieldType": "String", "name": "status", "order": 4}, {"fieldBizType": "ApiName", "fieldType": "String", "name": "api_name", "order": 2}, {"fieldType": "String", "name": "describe_id", "order": 3}, {"fieldBizType": "TenantId", "fieldType": "String", "name": "tenant_id", "order": 1}, {"fieldBizType": "IdGenerator", "fieldType": "String", "name": "field_id", "order": 2147483647}, {"fieldType": "String", "name": "cascade_parent_api_name", "order": 2147483647}, {"fieldType": "String", "name": "target_api_name", "order": 2147483647}, {"fieldType": "String", "name": "type", "order": 2147483647}], "inBoundCheck": {"checkTableDescriptors": [], "dependencyTableDescriptors": [{"filters": [], "foreignKeyDescribe": [{"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}, {"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}], "paramFields": [], "processLack": "exception", "tableId": "paas_metadata_mt_describe"}, {"filters": [], "foreignKeyDescribe": [{"fieldName": "describe_id", "operator": "IN", "refFieldName": "describe_id"}, {"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}], "paramFields": [], "processLack": "exception", "tableId": "paas_metadata_mt_describe"}, {"filters": [], "foreignKeyDescribe": [{"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "target_api_name"}, {"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}], "paramFields": [], "processLack": "exception", "tableId": "paas_metadata_mt_describe"}, {"filters": [], "foreignKeyDescribe": [{"fieldName": "api_name", "operator": "IN", "refFieldName": "cascade_parent_api_name"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}, {"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}], "paramFields": [], "processLack": "exception", "tableId": "paas_metadata_mt_field"}], "unchangeableField": [{"fieldName": "type"}], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": 0, "principalIds": [], "processorDescribe": {"inBoundTag": "PG", "outBoundTag": "PG", "selectHandlerType": {"configName": "PG", "handlerType": "PG"}}, "slaveTableDescribe": [{"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "bi_after_action_mt_field"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "after_action_mt_describe"}, {"filters": [{"fieldName": "type", "operator": "IN", "order": 1, "values": ["formula"]}], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}, {"fieldName": "api_name", "operator": "IN", "refFieldName": "api_name"}], "tableId": "recalculate_after_action_mt_field"}, {"filters": [{"fieldName": "describe_api_name", "operator": "IN", "order": 1, "values": ["CheckinsObj"]}], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "api_name", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "checkins_after_action_mt_field"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "source_value", "operator": "IN", "refFieldName": "api_name"}], "tableId": "paas_metadata_mt_entity_reference"}], "tableId": "paas_metadata_mt_field", "tableName": "mt_field", "uniqueKeys": ["tenant_id", "describe_api_name", "api_name"]}