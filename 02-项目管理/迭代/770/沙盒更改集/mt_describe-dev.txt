{"basicFilterCondition": [{"fieldName": "is_deleted", "operator": "IN", "order": 1, "values": [false]}], "deleteOriginalData": false, "fieldDescribe": [{"fieldBizType": "TenantId", "fieldType": "String", "name": "tenant_id", "order": 1}, {"fieldBizType": "ObjectApiName", "fieldType": "String", "name": "describe_api_name", "order": 2147483647}, {"fieldBizType": "Name", "fieldType": "String", "name": "display_name", "order": 2147483647}, {"fieldBizType": "IdGenerator", "fieldType": "String", "name": "describe_id", "order": 2147483647}], "inBoundCheck": {"checkTableDescriptors": [], "dependencyTableDescriptors": [], "unchangeableField": [], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": -2, "principalIds": [], "processorDescribe": {"inBoundTag": "PG", "outBoundTag": "PG", "selectHandlerType": {"configName": "PG", "handlerType": "PG"}}, "slaveTableDescribe": [{"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "func_code", "operator": "LIKE", "refFieldName": "describe_api_name"}], "tableId": "paas_metadata_fc_func_access"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "paas_metadata_mt_field"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "entity_id", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "paas_metadata_fc_record_type_access"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "ref_object_api_name", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "paas_metadata_mt_ui_component"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "paas_metadata_mt_rules"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "func_code", "operator": "LIKE", "refFieldName": "describe_api_name"}], "tableId": "paas_metadata_fc_func"}, {"foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "source_value", "operator": "LIKE", "refFieldName": "api_name"}], "tableId": "paas_metadata_mt_entity_reference"}], "tableId": "paas_metadata_mt_describe", "tableName": "mt_describe", "uniqueKeys": ["tenant_id", "describe_api_name"]}