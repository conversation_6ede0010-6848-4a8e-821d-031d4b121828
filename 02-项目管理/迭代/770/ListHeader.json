{"layout": {"buttons": [{"action_type": "default", "api_name": "Add_button_default", "action": "Add", "label": "新建", "is_exposed": true}, {"action_type": "default", "api_name": "IntelligentForm_button_default", "action": "IntelligentForm", "label": "智能表单"}, {"action_type": "default", "api_name": "Import_button_default", "action": "Import", "label": "导入"}, {"action_type": "default", "api_name": "Export_button_default", "action": "Export", "label": "导出"}, {"action_type": "default", "api_name": "ExportFile_button_default", "action": "ExportFile", "label": "导出图片/附件"}], "components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "country", "field_name": "field_zY1xN__c"}, {"is_readonly": false, "is_required": false, "render_type": "province", "field_name": "field_g4zeu__c"}, {"is_readonly": false, "is_required": false, "render_type": "city", "field_name": "field_yHAy0__c"}, {"is_readonly": false, "is_required": false, "render_type": "district", "field_name": "field_3mPOp__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_zqXG3__c"}, {"is_readonly": false, "is_required": false, "render_type": "location", "field_name": "field_1M86d__c"}, {"is_required": false, "field_name": "mc_exchange_rate", "render_type": "number"}, {"is_required": false, "field_name": "field_caKX4__c", "render_type": "date_time"}, {"is_required": false, "field_name": "out_owner", "render_type": "employee"}, {"is_required": false, "field_name": "mc_currency", "render_type": "select_one"}], "api_name": "field_Hpo8k__c", "tab_index": "ltr", "column": 2, "header": "地区定位"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "country", "field_name": "field_51jwc__c"}, {"is_readonly": false, "is_required": false, "render_type": "province", "field_name": "field_5bZOS__c"}, {"is_readonly": false, "is_required": false, "render_type": "city", "field_name": "field_51iwF__c"}, {"is_readonly": false, "is_required": false, "render_type": "district", "field_name": "field_UCZk4__c"}, {"is_readonly": false, "is_required": false, "render_type": "town", "field_name": "field_Uw35F__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_D2rNz__c"}, {"is_readonly": false, "is_required": false, "render_type": "location", "field_name": "field_3q2MS__c"}], "api_name": "field_koXLb__c", "tab_index": "ltr", "column": 2, "header": "地区定位"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_many", "field_name": "field_Wzg9S__c"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "field_PdXnI__c"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "lock_status"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_ioM6p__c", "target_display_name": "客户"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_x03I6__c", "target_display_name": "lihh-对象A"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_z3mRp__c", "target_display_name": "fj-可用显示"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_y9a6j__c", "target_display_name": "查重测试"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_61g8v__c", "target_display_name": "lihh-对象A"}, {"is_readonly": false, "is_required": true, "render_type": "number", "field_name": "field_u7w22__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_49g51__c", "target_display_name": "wj-测试主属性自定义"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_FT84f__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "field_VtmhT__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_82rIe__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "field_z76oi__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_24Zkp__c", "target_display_name": "lihh-对象B"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_kk4Eq__c"}, {"is_readonly": false, "is_required": false, "render_type": "html_rich_text", "field_name": "field_alsZi__c"}, {"is_readonly": false, "is_required": false, "render_type": "long_text", "field_name": "field_dbu5g__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_n0Atc__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_BbFwU__c", "target_display_name": "市场活动"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_9Pkay__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_Xc0dX__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_x1e7B__c"}, {"field_name": "relevant_team", "is_readonly": true, "is_required": false, "render_type": "relevant_team"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_LagU0__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "_id": "form_component", "type": "form", "order": 6}], "last_modified_time": 1627372731630, "is_deleted": false, "version": 54, "create_time": 1620394264186, "_id": "60954118812b190001eb7ad8", "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "layout_x1kgc__c", "what_api_name": null, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "tabs_xi1JP__c"], ["relevant_team_component", "sale_log"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "created_by": "1000", "display_name": "默认布局", "is_default": true, "last_modified_by": "1000", "layout_type": "detail", "package": "CRM", "ref_object_api_name": "object_n4zWv__c", "tenant_id": "74255", "ui_event_ids": ["609664bbc8b9310001d2d7e2", "609664bbc8b9310001d2d7e3", "60f513fab1985100011a9c28"], "hidden_buttons": [], "hidden_components": ["component_4fEPz__c", "component_SDWSM__c", "component_pJEzE__c"], "enable_mobile_layout": false, "events": [{"_id": "609664bbc8b9310001d2d7e2", "tenant_id": "74255", "describe_api_name": "object_n4zWv__c", "func_api_name": "func_52a1c__c", "trigger_describe_api_name": "object_n4zWv__c", "created_by": "1000", "create_time": 1620468923019, "is_deleted": false, "last_modified_by": "1000", "last_modified_time": 1626685070330, "triggers": [1], "trigger_field_api_names": ["field_3q2MS__c"], "type": 1}, {"_id": "609664bbc8b9310001d2d7e3", "tenant_id": "74255", "describe_api_name": "object_n4zWv__c", "func_api_name": "func_52a1c__c", "trigger_describe_api_name": "object_n4zWv__c", "created_by": "1000", "create_time": 1620468923019, "is_deleted": false, "last_modified_by": "1000", "last_modified_time": 1626685070330, "triggers": [1], "trigger_field_api_names": ["field_1M86d__c"], "type": 1}, {"_id": "60f513fab1985100011a9c28", "tenant_id": "74255", "describe_api_name": "object_n4zWv__c", "func_api_name": "func_UbVub__c", "trigger_describe_api_name": "object_n4zWv__c", "created_by": "1000", "create_time": 1626674170397, "is_deleted": false, "last_modified_by": "1000", "last_modified_time": 1626685070330, "triggers": [1], "trigger_field_api_names": ["name"], "type": 1}]}, "objectDescribe": {"tenant_id": "74255", "package": "CRM", "is_active": true, "last_modified_time": 1627372788717, "create_time": *************, "description": "", "last_modified_by": "1000", "display_name": "wj-多选", "created_by": "1000", "version": 84, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "object_n4zWv__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "EBB", "_id": "60954117812b190001eb7ad0", "fields": {"lock_rule": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1627372788378, "is_encrypted": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "_id": "60ffbcf420b61d00016bfca1", "is_single": false, "is_index_field": false, "index_name": "s_1", "status": "new"}, "field_PdXnI__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620439904419, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "单选", "type": "select_one", "field_num": 11, "is_required": false, "api_name": "field_PdXnI__c", "options": [{"font_color": "#2a304d", "label": "示例选项", "value": "GdcMaZg1s", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#181c25", "label": "示例选项1", "value": "vV2pE1J0y", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#ff8000", "label": "示例选项2", "value": "n343tmu26", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#ff522a", "label": "示例选项3", "value": "option1", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#30c776", "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "custom", "_id": "6095f3605397a20001075a2b", "is_single": false, "is_index_field": false, "index_name": "s_6", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_Xc0dX__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1627372701538, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "单选-b", "type": "select_one", "field_num": 45, "is_required": false, "api_name": "field_Xc0dX__c", "options": [{"font_color": "#2a304d", "not_usable": false, "child_options": [{"field_9Pkay__c": ["uu7pJqLF2", "ADHvz1Gm0"]}], "label": "a", "value": "aBw83Xq53", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": false, "child_options": [{"field_9Pkay__c": ["option1"]}], "label": "b", "value": "p56164sG4", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": false, "child_options": [{"field_9Pkay__c": ["ADHvz1Gm0"]}], "label": "c", "value": "wdrWeliC7", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "custom", "_id": "60ffbc9d20b61d00016bfb80", "is_single": false, "cascade_parent_api_name": "record_type", "is_index_field": false, "index_name": "s_25", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_UCZk4__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424813, "is_encrypted": false, "description": "", "is_unique": false, "label": "区1", "type": "district", "field_num": 21, "used_in": "component", "is_required": false, "api_name": "field_UCZk4__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc9c", "is_single": false, "cascade_parent_api_name": "field_51iwF__c", "is_index_field": false, "index_name": "s_8", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_82rIe__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_5", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1626836823465, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 12, "default_value": "", "label": "数字但是幅度萨芬订单合计", "field_num": 38, "api_name": "field_82rIe__c", "_id": "60f78f572525e400011f875b", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1, "decimal_places": 1}}, "round_mode": 4, "help_text": "", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_1", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1620394263296, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "汇率", "field_num": 7, "api_name": "mc_exchange_rate", "_id": "60954117812b190001eb7acd", "is_index_field": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 0, "label": 0, "help_text": 0, "max_length": 0, "decimal_places": 0}}, "round_mode": 4, "help_text": "", "status": "new"}, "field_n0Atc__c": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1626945361182, "is_encrypted": false, "quote_field_type": "date_time", "description": "", "is_unique": false, "label": "引用字段", "type": "quote", "quote_field": "field_24Zkp__c__r.field_orW22__c", "is_required": false, "api_name": "field_n0Atc__c", "define_type": "custom", "_id": "60f93751d79a2900010d6cf6", "is_single": false, "is_index_field": false, "index_name": "l_2", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"is_index": 1, "api_name": 1, "quote_field_type": 1, "label": 1, "help_text": 1, "quote_field": 1}}, "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1627372788378, "is_encrypted": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 8, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "_id": "60ffbcf420b61d00016bfca2", "is_single": false, "is_index_field": false, "index_name": "t_1", "max_length": 256, "status": "new"}, "field_zY1xN__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424803, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "国家", "type": "country", "field_num": 12, "used_in": "component", "is_required": false, "api_name": "field_zY1xN__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc92", "is_single": false, "is_index_field": false, "index_name": "s_10", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1620394263285, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "_id": "60954117812b190001eb7abe", "is_index_field": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "label": 0}}, "help_text": "", "status": "new"}, "field_3q2MS__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424815, "is_encrypted": false, "description": "", "is_unique": false, "label": "定位1", "is_geo_index": false, "type": "location", "field_num": 23, "used_in": "component", "is_required": false, "api_name": "field_3q2MS__c", "define_type": "custom", "_id": "60965710c8b9310001d2cc9e", "is_single": false, "is_index_field": false, "index_name": "t_3", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_BbFwU__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_23", "is_index": true, "is_active": true, "create_time": 1627022655899, "is_encrypted": false, "label": "查找关联22", "target_api_name": "MarketingEventObj", "target_related_list_name": "target_related_list_TGN9d__c", "field_num": 43, "target_related_list_label": "wj-多选", "action_on_target_delete": "set_null", "api_name": "field_BbFwU__c", "_id": "60fa65408462e20001f2a09b", "is_index_field": true, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"target_related_list_label": 1, "wheres": 1, "api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "target_api_name": 1, "target_related_list_name": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_51iwF__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424812, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "市1", "type": "city", "field_num": 20, "used_in": "component", "is_required": false, "api_name": "field_51iwF__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc9b", "is_single": false, "cascade_parent_api_name": "field_5bZOS__c", "is_index_field": false, "index_name": "s_11", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_dbu5g__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "t_7", "max_length": 2000, "is_index": true, "is_active": true, "create_time": 1626934905962, "is_encrypted": false, "default_value": "", "label": "多行文本", "field_num": 42, "api_name": "field_dbu5g__c", "_id": "60f90e7a5bcb7f0001c5b565", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1}}, "help_text": "", "status": "new"}, "field_9Pkay__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1627372677222, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "单选-a", "type": "select_one", "field_num": 44, "is_required": false, "api_name": "field_9Pkay__c", "options": [{"font_color": "#2a304d", "not_usable": false, "label": "a", "value": "uu7pJqLF2", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": false, "label": "c", "value": "ADHvz1Gm0", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": false, "label": "b", "value": "option1", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "custom", "_id": "60ffbc8520b61d00016bfb76", "is_single": false, "cascade_parent_api_name": "field_Xc0dX__c", "is_index_field": false, "index_name": "s_24", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620394263190, "is_encrypted": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "field_num": 2, "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"font_color": "#936de3", "label": "未锁定", "value": "0", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#30c776", "label": "锁定", "value": "1", "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "_id": "60954117812b190001eb7ac9", "is_single": false, "is_index_field": false, "index_name": "s_2", "config": {"attrs": {"is_readonly": 0, "is_required": 0}}, "help_text": "", "status": "new"}, "package": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": *************, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "create_time": {"describe_api_name": "object_n4zWv__c", "is_index": true, "create_time": *************, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "field_zqXG3__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_4", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1620465424807, "is_encrypted": false, "default_value": "", "label": "详细地址", "field_num": 16, "api_name": "field_zqXG3__c", "_id": "60965710c8b9310001d2cc96", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_z76oi__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_6", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1626918879939, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额-掩码", "currency_unit": "￥", "field_num": 39, "api_name": "field_z76oi__c", "_id": "60f8cfe05bcb7f0001c501fe", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1, "decimal_places": 1}}, "is_show_mask": true, "round_mode": 4, "help_text": "", "status": "new"}, "field_Hpo8k__c": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1620465424809, "is_encrypted": false, "is_support_town": false, "description": "", "is_unique": false, "group_type": "area", "label": "地区定位", "type": "group", "is_required": false, "api_name": "field_Hpo8k__c", "define_type": "custom", "_id": "60965710c8b9310001d2cc98", "is_single": false, "is_index_field": false, "fields": {"area_country": "field_zY1xN__c", "area_location": "field_1M86d__c", "area_detail_address": "field_zqXG3__c", "area_city": "field_yHAy0__c", "area_province": "field_g4zeu__c", "area_district": "field_3mPOp__c"}, "index_name": "s_15", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "header": 1}}, "help_text": "", "status": "new"}, "field_1M86d__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424808, "is_encrypted": false, "description": "", "is_unique": false, "label": "定位", "is_geo_index": false, "type": "location", "field_num": 17, "used_in": "component", "is_required": false, "api_name": "field_1M86d__c", "define_type": "custom", "_id": "60965710c8b9310001d2cc97", "is_single": false, "is_index_field": false, "index_name": "t_5", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "version": {"describe_api_name": "object_n4zWv__c", "is_index": false, "create_time": *************, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": *************, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "relevant_team": {"describe_api_name": "object_n4zWv__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1627372788379, "is_encrypted": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "60ffbcf420b61d00016bfca3", "is_single": false, "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "field_5bZOS__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424811, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "省2", "type": "province", "field_num": 19, "used_in": "component", "is_required": false, "api_name": "field_5bZOS__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc9a", "is_single": false, "cascade_parent_api_name": "field_51jwc__c", "is_index_field": false, "index_name": "s_16", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_FT84f__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": true, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_3", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1626766536038, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 12, "default_value": "$field_u7w22__c$", "label": "数字-默认值", "field_num": 35, "api_name": "field_FT84f__c", "_id": "60f67ccbb01e61000117707a", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1, "decimal_places": 1}}, "round_mode": 4, "help_text": "", "status": "new"}, "data_own_department": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": *************, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "index_name": "data_owner_dept_id", "status": "released"}, "name": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "pattern": "", "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1620394263397, "is_encrypted": false, "default_value": "", "label": "主属性", "api_name": "name", "_id": "60954117812b190001eb7abc", "is_index_field": false, "config": {"edit": 1, "display": 1, "attrs": {"api_name": 1, "is_unique": 1, "label": 1, "type": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "_id": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": *************, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}, "field_g4zeu__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424804, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "省", "type": "province", "field_num": 13, "used_in": "component", "is_required": false, "api_name": "field_g4zeu__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc93", "is_single": false, "cascade_parent_api_name": "field_zY1xN__c", "is_index_field": false, "index_name": "s_17", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_caKX4__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "type": "date_time", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "l_1", "is_index": true, "is_active": true, "create_time": 1626772004450, "is_encrypted": false, "default_value": "", "label": "日期时间", "time_zone": "GMT+8", "field_num": 36, "api_name": "field_caKX4__c", "date_format": "yyyy-MM-dd HH:mm", "_id": "60f69224b01e610001179c4e", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_u7w22__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 3, "default_to_zero": true, "is_required": true, "define_type": "custom", "is_single": false, "index_name": "d_2", "max_length": 7, "is_index": true, "is_active": true, "create_time": 1626172662369, "is_encrypted": false, "display_style": "step", "step_value": -1000, "length": 4, "default_value": "", "label": "数字名称特特特哔哔哔哔哔长的的时候的", "field_num": 30, "api_name": "field_u7w22__c", "_id": "60ed6cf6309df6000199aff0", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1, "decimal_places": 1}}, "round_mode": 4, "help_text": "", "status": "new"}, "tenant_id": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": *************, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "field_x03I6__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": true, "index_name": "a_6", "is_index": true, "is_active": true, "create_time": 1626160612604, "is_encrypted": false, "label": "查找关联(多选)-对象", "target_api_name": "object_Zpv1K__c", "target_related_list_name": "target_related_list_a841G__c", "field_num": 28, "target_related_list_label": "wj-多选", "action_on_target_delete": "set_null", "api_name": "field_x03I6__c", "_id": "60ed3de47d53dc000190e6e1", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_51jwc__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424810, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "国家1", "type": "country", "field_num": 18, "used_in": "component", "is_required": false, "api_name": "field_51jwc__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc99", "is_single": false, "is_index_field": false, "index_name": "s_7", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_49g51__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_19", "is_index": true, "is_active": true, "create_time": 1626316945278, "is_encrypted": false, "label": "查找关联-wj主属性自定义", "target_api_name": "object_Ju6l7__c", "target_related_list_name": "target_related_list_22En4__c", "field_num": 32, "target_related_list_label": "wj-主属性自定义", "action_on_target_delete": "set_null", "api_name": "field_49g51__c", "_id": "60efa09123cb7c000131aa80", "is_index_field": true, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"target_related_list_label": 1, "wheres": 1, "api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "target_api_name": 1, "target_related_list_name": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_alsZi__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "html_rich_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "s_22", "max_length": 131072, "is_index": false, "is_active": true, "create_time": 1626934559573, "is_encrypted": false, "default_value": "", "label": "富文本", "field_num": 41, "api_name": "field_alsZi__c", "_id": "60f90d1f5bcb7f0001c5a374", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1627372788378, "is_encrypted": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 5, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "_id": "60ffbcf420b61d00016bfca4", "is_single": true, "is_index_field": false, "index_name": "a_2", "status": "new"}, "field_kk4Eq__c": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1626926579383, "is_encrypted": false, "quote_field_type": "object_reference", "description": "", "is_unique": false, "label": "引用字段-wj自定义属性", "type": "quote", "quote_field": "field_24Zkp__c__r.field_uT0e5__c", "is_required": false, "api_name": "field_kk4Eq__c", "define_type": "custom", "_id": "60f8edf35bcb7f0001c5880e", "is_single": false, "is_index_field": false, "index_name": "s_21", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"is_index": 1, "api_name": 1, "quote_field_type": 1, "label": 1, "help_text": 1, "quote_field": 1}}, "help_text": "", "status": "new"}, "field_VtmhT__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_4", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1626836809571, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额名称特特特特长更多嘎嘎", "currency_unit": "￥", "field_num": 37, "api_name": "field_VtmhT__c", "_id": "60f78f492525e400011f86a8", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1, "decimal_places": 1}}, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "object_n4zWv__c", "is_index": false, "create_time": *************, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "index_name": "is_del", "status": "released"}, "object_describe_api_name": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": *************, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "field_Uw35F__c": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1620465424817, "is_encrypted": false, "description": "", "is_unique": false, "label": "乡镇2", "type": "town", "field_num": 24, "used_in": "component", "is_required": false, "api_name": "field_Uw35F__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cca0", "is_single": false, "cascade_parent_api_name": "field_UCZk4__c", "is_index_field": false, "index_name": "s_9", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": *************, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released"}, "field_z3mRp__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": true, "index_name": "a_9", "is_index": true, "is_active": true, "create_time": 1626685031003, "is_encrypted": false, "label": "fj-查找关联(多选)", "target_api_name": "object_o6elx__c", "target_related_list_name": "target_related_list_4n4u3__c", "field_num": 34, "target_related_list_label": "wj-多选", "action_on_target_delete": "set_null", "api_name": "field_z3mRp__c", "_id": "60f53e674563f80001eb1368", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620394263284, "is_encrypted": false, "description": "", "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "_id": "60954117812b190001eb7abd", "is_single": true, "is_index_field": false, "index_name": "owner", "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "label": 0}}, "help_text": "", "status": "new"}, "field_yHAy0__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424805, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "市", "type": "city", "field_num": 14, "used_in": "component", "is_required": false, "api_name": "field_yHAy0__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc94", "is_single": false, "cascade_parent_api_name": "field_g4zeu__c", "is_index_field": false, "index_name": "s_12", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_ioM6p__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": true, "index_name": "a_5", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "查找关联(多选)-客户", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_45uDi__c", "field_num": 27, "target_related_list_label": "wj-多选", "action_on_target_delete": "set_null", "api_name": "field_ioM6p__c", "_id": "60ed3dc97d53dc000190e6d2", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "last_modified_time": {"describe_api_name": "object_n4zWv__c", "is_index": true, "create_time": *************, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "field_24Zkp__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_20", "is_index": true, "is_active": true, "create_time": 1626926504308, "is_encrypted": false, "label": "查找关联-lihh对象B", "target_api_name": "object_96mtw__c", "target_related_list_name": "target_related_list_eq7Uq__c", "field_num": 40, "target_related_list_label": "wj-lihh对象B", "action_on_target_delete": "set_null", "api_name": "field_24Zkp__c", "_id": "60f8eda95bcb7f0001c587a7", "is_index_field": true, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"target_related_list_label": 1, "wheres": 1, "api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "target_api_name": 1, "target_related_list_name": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_koXLb__c": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": 1620465424816, "is_encrypted": false, "is_support_town": true, "description": "", "is_unique": false, "group_type": "area", "label": "地区定位-乡镇", "type": "group", "is_required": false, "api_name": "field_koXLb__c", "define_type": "custom", "_id": "60965710c8b9310001d2cc9f", "is_single": false, "is_index_field": false, "fields": {"area_country": "field_51jwc__c", "area_location": "field_3q2MS__c", "area_town": "field_Uw35F__c", "area_detail_address": "field_D2rNz__c", "area_city": "field_51iwF__c", "area_province": "field_5bZOS__c", "area_district": "field_UCZk4__c"}, "index_name": "s_13", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "header": 1}}, "help_text": "", "status": "new"}, "field_Wzg9S__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620394263293, "is_encrypted": false, "description": "", "is_unique": false, "default_value": [], "label": "多选", "type": "select_many", "field_num": 3, "is_required": true, "api_name": "field_Wzg9S__c", "options": [{"font_color": "#ff522a", "label": "示例选项", "value": "option1", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "label": "ffffffffffffff2134234123432", "value": "cbO2g6f2c", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "label": "dsaffffffffffffff324", "value": "vm2B9QK88", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "label": "dsfsfsfsfsfsfsfsfsfsfsfsfsf324", "value": "K720Mdte2", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "label": "43211111111111fedfwd", "value": "rc1o8VkKz", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "label": "2344444444ffds", "value": "z4Lw4v2l9", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "label": "234444444444444444fdsaf", "value": "f5sUol1LR", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "label": "sdfffffffffffffffffff", "value": "4931you9s", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#30c776", "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "custom", "_id": "60954117812b190001eb7ac6", "is_single": false, "is_index_field": false, "index_name": "a_1", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_3mPOp__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620465424806, "is_encrypted": false, "description": "", "is_unique": false, "label": "区", "type": "district", "field_num": 15, "used_in": "component", "is_required": false, "api_name": "field_3mPOp__c", "options": [], "define_type": "custom", "_id": "60965710c8b9310001d2cc95", "is_single": false, "cascade_parent_api_name": "field_yHAy0__c", "is_index_field": false, "index_name": "s_14", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620394263292, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "field_num": 4, "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"font_color": "#30c776", "label": "未生效", "value": "ineffective", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#181C25", "label": "审核中", "value": "under_review", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#936de3", "label": "正常", "value": "normal", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#ff8000", "label": "变更中", "value": "in_change", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#ff522a", "label": "作废", "value": "invalid", "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "_id": "60954117812b190001eb7ac5", "is_single": false, "is_index_field": false, "index_name": "s_3", "config": {"attrs": {"is_readonly": 0, "is_required": 0}}, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": *************, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "out_tenant_id": {"describe_api_name": "object_n4zWv__c", "is_index": false, "is_active": true, "create_time": *************, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620394263295, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "币种", "type": "select_one", "field_num": 6, "is_required": false, "api_name": "mc_currency", "options": [{"font_color": "#2a304d", "not_usable": true, "label": "AED - 阿联酋迪拉姆", "value": "AED", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#2a304d", "label": "ALL - 阿尔巴尼亚列克", "value": "ALL", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#2a304d", "label": "AOA - 安哥拉宽扎", "value": "AOA", "config": {"edit": 0, "enable": 0, "remove": 0}}, {"font_color": "#2a304d", "label": "CNY - 人民币", "value": "CNY", "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "_id": "60954117812b190001eb7acc", "is_single": false, "is_index_field": false, "index_name": "s_4", "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "options": 0, "is_unique": 0, "default_value": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "record_type": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620394263291, "is_encrypted": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "font_color": "#181C25", "api_name": "default__c", "description": "预设业务类型", "child_options": [{"field_x1e7B__c": ["6U38ir82j", "ujSShQPRs", "W22we9eoF"], "field_Xc0dX__c": ["aBw83Xq53", "p56164sG4"]}], "label": "预设业务类型", "value": "default__c", "config": {"edit": 1}}], "define_type": "package", "_id": "60954117812b190001eb7ac4", "is_single": false, "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "field_D2rNz__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_6", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1620465424814, "is_encrypted": false, "default_value": "", "label": "详细地址1", "field_num": 22, "api_name": "field_D2rNz__c", "_id": "60965710c8b9310001d2cc9d", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_x1e7B__c": {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1627372731149, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "单选-d", "type": "select_one", "field_num": 46, "is_required": false, "api_name": "field_x1e7B__c", "options": [{"font_color": "#2a304d", "not_usable": false, "label": "a", "value": "6U38ir82j", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": false, "label": "b", "value": "0rd1ezc48", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": false, "label": "c", "value": "ujSShQPRs", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": false, "label": "d", "value": "W22we9eoF", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "custom", "_id": "60ffbcbb20b61d00016bfb86", "is_single": false, "cascade_parent_api_name": "record_type", "is_index_field": false, "index_name": "s_26", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_y9a6j__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": true, "index_name": "a_7", "is_index": true, "is_active": true, "create_time": 1626251947311, "is_encrypted": false, "label": "new多选", "target_api_name": "object_byk3m__c", "target_related_list_name": "target_related_list_7ToTb__c", "field_num": 31, "target_related_list_label": "wj-多选", "action_on_target_delete": "set_null", "api_name": "field_y9a6j__c", "_id": "60eea2ad7249bc0001c45cad", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "object_n4zWv__c", "is_index": false, "create_time": *************, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "field_61g8v__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_18", "is_index": true, "is_active": true, "create_time": 1626171100307, "is_encrypted": false, "label": "查找关联", "target_api_name": "object_Zpv1K__c", "target_related_list_name": "target_related_list_nrz20__c", "field_num": 29, "target_related_list_label": "wj-dddd", "action_on_target_delete": "set_null", "api_name": "field_61g8v__c", "_id": "60ed66dd309df60001999e88", "is_index_field": true, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"target_related_list_label": 1, "wheres": 1, "api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "target_api_name": 1, "target_related_list_name": 1, "help_text": 1}}, "help_text": "", "status": "new"}}, "release_version": "6.4", "actions": {}}, "templates": [{"type": "default", "_id": "5d0c806a7cfed91f3e95ba8e", "last_modified_time": 1627271537024, "create_time": 1561100394821, "extend_attribute": null, "created_by": "1000", "object_describe_api_name": "object_n4zWv__c", "object_describe_id": null, "wheres": [], "api_name": "All", "is_default": true, "is_hidden": false, "label": "全部", "last_modified_by": "1000", "order": 0, "package": "CRM", "tenant_id": "74255", "user_id": "1000", "version": 1, "record_type": null, "field_list_type": 0, "field_list": [{"is_show": true, "field_name": "name"}, {"is_show": true, "field_name": "field_VtmhT__c"}, {"is_show": true, "field_name": "field_x03I6__c"}, {"is_show": true, "field_name": "field_61g8v__c"}, {"is_show": true, "field_name": "field_49g51__c"}, {"is_show": true, "field_name": "field_zY1xN__c"}, {"is_show": true, "field_name": "field_g4zeu__c"}, {"is_show": true, "field_name": "field_yHAy0__c"}, {"is_show": true, "field_name": "field_3mPOp__c"}, {"is_show": true, "field_name": "field_zqXG3__c"}, {"is_show": true, "field_name": "field_1M86d__c"}, {"is_show": true, "field_name": "mc_exchange_rate"}, {"is_show": true, "field_name": "out_owner"}, {"is_show": true, "field_name": "mc_currency"}, {"is_show": true, "field_name": "field_51jwc__c"}, {"is_show": true, "field_name": "field_5bZOS__c"}, {"is_show": true, "field_name": "field_51iwF__c"}, {"is_show": true, "field_name": "field_UCZk4__c"}, {"is_show": true, "field_name": "field_Uw35F__c"}, {"is_show": true, "field_name": "field_D2rNz__c"}, {"is_show": true, "field_name": "field_3q2MS__c"}, {"is_show": true, "field_name": "owner"}, {"is_show": true, "field_name": "owner_department"}, {"is_show": true, "field_name": "record_type"}, {"is_show": true, "field_name": "field_Wzg9S__c"}, {"is_show": true, "field_name": "life_status"}, {"is_show": true, "field_name": "data_own_department"}, {"is_show": true, "field_name": "field_PdXnI__c"}, {"is_show": true, "field_name": "lock_status"}, {"is_show": true, "field_name": "relevant_team"}, {"is_show": true, "field_name": "created_by"}, {"is_show": true, "field_name": "create_time"}, {"is_show": true, "field_name": "last_modified_by"}, {"is_show": true, "field_name": "last_modified_time"}, {"is_show": true, "field_name": "field_ioM6p__c"}, {"is_show": true, "field_name": "field_u7w22__c"}, {"field_name": "field_caKX4__c", "is_show": false}, {"field_name": "field_z3mRp__c", "is_show": false}, {"field_name": "field_y9a6j__c", "is_show": false}, {"field_name": "field_FT84f__c", "is_show": false}, {"field_name": "field_82rIe__c", "is_show": false}, {"field_name": "field_z76oi__c", "is_show": false}, {"field_name": "field_24Zkp__c", "is_show": false}, {"field_name": "field_kk4Eq__c", "is_show": false}, {"field_name": "field_alsZi__c", "is_show": false}, {"field_name": "field_dbu5g__c", "is_show": false}, {"field_name": "field_n0Atc__c", "is_show": false}, {"field_name": "field_BbFwU__c", "is_show": false}, {"field_name": "field_9Pkay__c", "is_show": false}, {"field_name": "field_Xc0dX__c", "is_show": false}, {"field_name": "field_x1e7B__c", "is_show": false}], "is_new_scene": false, "tenant_search_id": null, "is_available": true, "base_scene_api_name": "All", "default_scene_priority": null, "out_tenant_id": null, "out_user_id": null, "filters": [{"field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user}||${current_user_subordinates}||${current_user_dept_users}||${current_user_shared_users}"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}]}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": [{"field_name": "field_zY1xN__c", "is_show": true}, {"field_name": "field_g4zeu__c", "is_show": true}, {"field_name": "field_yHAy0__c", "is_show": true}, {"field_name": "field_3mPOp__c", "is_show": true}, {"field_name": "field_zqXG3__c", "is_show": true}, {"field_name": "field_1M86d__c", "is_show": true}, {"field_name": "mc_exchange_rate", "is_show": true}, {"field_name": "field_caKX4__c", "is_show": true}, {"field_name": "out_owner", "is_show": true}, {"field_name": "mc_currency", "is_show": true}, {"field_name": "field_51jwc__c", "is_show": true}, {"field_name": "field_5bZOS__c", "is_show": true}, {"field_name": "field_51iwF__c", "is_show": true}, {"field_name": "field_UCZk4__c", "is_show": true}, {"field_name": "field_Uw35F__c", "is_show": true}, {"field_name": "field_D2rNz__c", "is_show": true}, {"field_name": "field_3q2MS__c", "is_show": true}, {"field_name": "name", "is_show": true}, {"field_name": "owner", "is_show": true}, {"field_name": "owner_department", "is_show": true}, {"field_name": "record_type", "is_show": true}, {"field_name": "field_Wzg9S__c", "is_show": true}, {"field_name": "life_status", "is_show": true}, {"field_name": "data_own_department", "is_show": true}, {"field_name": "field_PdXnI__c", "is_show": true}, {"field_name": "lock_status", "is_show": true}, {"field_name": "field_ioM6p__c", "is_show": true}, {"field_name": "field_x03I6__c", "is_show": true}, {"field_name": "field_z3mRp__c", "is_show": true}, {"field_name": "field_y9a6j__c", "is_show": true}, {"field_name": "field_61g8v__c", "is_show": true}, {"field_name": "field_u7w22__c", "is_show": true}, {"field_name": "field_49g51__c", "is_show": true}, {"field_name": "field_FT84f__c", "is_show": true}, {"field_name": "field_VtmhT__c", "is_show": true}, {"field_name": "field_82rIe__c", "is_show": true}, {"field_name": "field_z76oi__c", "is_show": true}, {"field_name": "field_24Zkp__c", "is_show": true}, {"field_name": "field_kk4Eq__c", "is_show": true}, {"field_name": "field_alsZi__c", "is_show": true}, {"field_name": "field_dbu5g__c", "is_show": true}, {"field_name": "field_n0Atc__c", "is_show": true}, {"field_name": "field_BbFwU__c", "is_show": true}, {"field_name": "field_9Pkay__c", "is_show": true}, {"field_name": "field_Xc0dX__c", "is_show": true}, {"field_name": "field_x1e7B__c", "is_show": true}, {"field_name": "relevant_team", "is_show": true}, {"field_name": "created_by", "is_show": true}, {"field_name": "create_time", "is_show": true}, {"field_name": "last_modified_by", "is_show": true}, {"field_name": "last_modified_time", "is_show": true}], "field_list_type": 0, "order": 1, "is_all": true, "last_modified_time": 1561100394822, "package": "CRM", "create_time": 1561100394822, "base_scene_api_name": "Participate", "is_new_scene": false, "filters": [{"field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user}"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "owner", "field_values": ["${current_user}"], "operator": "N", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "label": "我参与的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "Participate", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba90", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": [{"field_name": "field_zY1xN__c", "is_show": true}, {"field_name": "field_g4zeu__c", "is_show": true}, {"field_name": "field_yHAy0__c", "is_show": true}, {"field_name": "field_3mPOp__c", "is_show": true}, {"field_name": "field_zqXG3__c", "is_show": true}, {"field_name": "field_1M86d__c", "is_show": true}, {"field_name": "mc_exchange_rate", "is_show": true}, {"field_name": "field_caKX4__c", "is_show": true}, {"field_name": "out_owner", "is_show": true}, {"field_name": "mc_currency", "is_show": true}, {"field_name": "field_51jwc__c", "is_show": true}, {"field_name": "field_5bZOS__c", "is_show": true}, {"field_name": "field_51iwF__c", "is_show": true}, {"field_name": "field_UCZk4__c", "is_show": true}, {"field_name": "field_Uw35F__c", "is_show": true}, {"field_name": "field_D2rNz__c", "is_show": true}, {"field_name": "field_3q2MS__c", "is_show": true}, {"field_name": "name", "is_show": true}, {"field_name": "owner", "is_show": true}, {"field_name": "owner_department", "is_show": true}, {"field_name": "record_type", "is_show": true}, {"field_name": "field_Wzg9S__c", "is_show": true}, {"field_name": "life_status", "is_show": true}, {"field_name": "data_own_department", "is_show": true}, {"field_name": "field_PdXnI__c", "is_show": true}, {"field_name": "lock_status", "is_show": true}, {"field_name": "field_ioM6p__c", "is_show": true}, {"field_name": "field_x03I6__c", "is_show": true}, {"field_name": "field_z3mRp__c", "is_show": true}, {"field_name": "field_y9a6j__c", "is_show": true}, {"field_name": "field_61g8v__c", "is_show": true}, {"field_name": "field_u7w22__c", "is_show": true}, {"field_name": "field_49g51__c", "is_show": true}, {"field_name": "field_FT84f__c", "is_show": true}, {"field_name": "field_VtmhT__c", "is_show": true}, {"field_name": "field_82rIe__c", "is_show": true}, {"field_name": "field_z76oi__c", "is_show": true}, {"field_name": "field_24Zkp__c", "is_show": true}, {"field_name": "field_kk4Eq__c", "is_show": true}, {"field_name": "field_alsZi__c", "is_show": true}, {"field_name": "field_dbu5g__c", "is_show": true}, {"field_name": "field_n0Atc__c", "is_show": true}, {"field_name": "field_BbFwU__c", "is_show": true}, {"field_name": "field_9Pkay__c", "is_show": true}, {"field_name": "field_Xc0dX__c", "is_show": true}, {"field_name": "field_x1e7B__c", "is_show": true}, {"field_name": "relevant_team", "is_show": true}, {"field_name": "created_by", "is_show": true}, {"field_name": "create_time", "is_show": true}, {"field_name": "last_modified_by", "is_show": true}, {"field_name": "last_modified_time", "is_show": true}], "field_list_type": 0, "order": 2, "is_all": true, "last_modified_time": 1561100394822, "package": "CRM", "create_time": 1561100394822, "base_scene_api_name": "InCharge", "is_new_scene": false, "filters": [{"field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "owner", "field_values": ["${current_user}"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "label": "我负责的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "InCharge", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba8f", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": [{"field_name": "field_zY1xN__c", "is_show": true}, {"field_name": "field_g4zeu__c", "is_show": true}, {"field_name": "field_yHAy0__c", "is_show": true}, {"field_name": "field_3mPOp__c", "is_show": true}, {"field_name": "field_zqXG3__c", "is_show": true}, {"field_name": "field_1M86d__c", "is_show": true}, {"field_name": "mc_exchange_rate", "is_show": true}, {"field_name": "field_caKX4__c", "is_show": true}, {"field_name": "out_owner", "is_show": true}, {"field_name": "mc_currency", "is_show": true}, {"field_name": "field_51jwc__c", "is_show": true}, {"field_name": "field_5bZOS__c", "is_show": true}, {"field_name": "field_51iwF__c", "is_show": true}, {"field_name": "field_UCZk4__c", "is_show": true}, {"field_name": "field_Uw35F__c", "is_show": true}, {"field_name": "field_D2rNz__c", "is_show": true}, {"field_name": "field_3q2MS__c", "is_show": true}, {"field_name": "name", "is_show": true}, {"field_name": "owner", "is_show": true}, {"field_name": "owner_department", "is_show": true}, {"field_name": "record_type", "is_show": true}, {"field_name": "field_Wzg9S__c", "is_show": true}, {"field_name": "life_status", "is_show": true}, {"field_name": "data_own_department", "is_show": true}, {"field_name": "field_PdXnI__c", "is_show": true}, {"field_name": "lock_status", "is_show": true}, {"field_name": "field_ioM6p__c", "is_show": true}, {"field_name": "field_x03I6__c", "is_show": true}, {"field_name": "field_z3mRp__c", "is_show": true}, {"field_name": "field_y9a6j__c", "is_show": true}, {"field_name": "field_61g8v__c", "is_show": true}, {"field_name": "field_u7w22__c", "is_show": true}, {"field_name": "field_49g51__c", "is_show": true}, {"field_name": "field_FT84f__c", "is_show": true}, {"field_name": "field_VtmhT__c", "is_show": true}, {"field_name": "field_82rIe__c", "is_show": true}, {"field_name": "field_z76oi__c", "is_show": true}, {"field_name": "field_24Zkp__c", "is_show": true}, {"field_name": "field_kk4Eq__c", "is_show": true}, {"field_name": "field_alsZi__c", "is_show": true}, {"field_name": "field_dbu5g__c", "is_show": true}, {"field_name": "field_n0Atc__c", "is_show": true}, {"field_name": "field_BbFwU__c", "is_show": true}, {"field_name": "field_9Pkay__c", "is_show": true}, {"field_name": "field_Xc0dX__c", "is_show": true}, {"field_name": "field_x1e7B__c", "is_show": true}, {"field_name": "relevant_team", "is_show": true}, {"field_name": "created_by", "is_show": true}, {"field_name": "create_time", "is_show": true}, {"field_name": "last_modified_by", "is_show": true}, {"field_name": "last_modified_time", "is_show": true}], "field_list_type": 0, "order": 3, "is_all": true, "last_modified_time": 1561100394823, "package": "CRM", "create_time": 1561100394823, "base_scene_api_name": "SubInCharge", "is_new_scene": false, "filters": [{"field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_subordinates}"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "label": "我下属负责的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "SubInCharge", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba92", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": [{"field_name": "field_zY1xN__c", "is_show": true}, {"field_name": "field_g4zeu__c", "is_show": true}, {"field_name": "field_yHAy0__c", "is_show": true}, {"field_name": "field_3mPOp__c", "is_show": true}, {"field_name": "field_zqXG3__c", "is_show": true}, {"field_name": "field_1M86d__c", "is_show": true}, {"field_name": "mc_exchange_rate", "is_show": true}, {"field_name": "field_caKX4__c", "is_show": true}, {"field_name": "out_owner", "is_show": true}, {"field_name": "mc_currency", "is_show": true}, {"field_name": "field_51jwc__c", "is_show": true}, {"field_name": "field_5bZOS__c", "is_show": true}, {"field_name": "field_51iwF__c", "is_show": true}, {"field_name": "field_UCZk4__c", "is_show": true}, {"field_name": "field_Uw35F__c", "is_show": true}, {"field_name": "field_D2rNz__c", "is_show": true}, {"field_name": "field_3q2MS__c", "is_show": true}, {"field_name": "name", "is_show": true}, {"field_name": "owner", "is_show": true}, {"field_name": "owner_department", "is_show": true}, {"field_name": "record_type", "is_show": true}, {"field_name": "field_Wzg9S__c", "is_show": true}, {"field_name": "life_status", "is_show": true}, {"field_name": "data_own_department", "is_show": true}, {"field_name": "field_PdXnI__c", "is_show": true}, {"field_name": "lock_status", "is_show": true}, {"field_name": "field_ioM6p__c", "is_show": true}, {"field_name": "field_x03I6__c", "is_show": true}, {"field_name": "field_z3mRp__c", "is_show": true}, {"field_name": "field_y9a6j__c", "is_show": true}, {"field_name": "field_61g8v__c", "is_show": true}, {"field_name": "field_u7w22__c", "is_show": true}, {"field_name": "field_49g51__c", "is_show": true}, {"field_name": "field_FT84f__c", "is_show": true}, {"field_name": "field_VtmhT__c", "is_show": true}, {"field_name": "field_82rIe__c", "is_show": true}, {"field_name": "field_z76oi__c", "is_show": true}, {"field_name": "field_24Zkp__c", "is_show": true}, {"field_name": "field_kk4Eq__c", "is_show": true}, {"field_name": "field_alsZi__c", "is_show": true}, {"field_name": "field_dbu5g__c", "is_show": true}, {"field_name": "field_n0Atc__c", "is_show": true}, {"field_name": "field_BbFwU__c", "is_show": true}, {"field_name": "field_9Pkay__c", "is_show": true}, {"field_name": "field_Xc0dX__c", "is_show": true}, {"field_name": "field_x1e7B__c", "is_show": true}, {"field_name": "relevant_team", "is_show": true}, {"field_name": "created_by", "is_show": true}, {"field_name": "create_time", "is_show": true}, {"field_name": "last_modified_by", "is_show": true}, {"field_name": "last_modified_time", "is_show": true}], "field_list_type": 0, "order": 4, "is_all": true, "last_modified_time": 1561100394823, "package": "CRM", "create_time": 1561100394823, "base_scene_api_name": "InChargeDept", "is_new_scene": false, "filters": [{"field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_dept_users}"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "label": "我负责部门的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "InChargeDept", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba91", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": [{"field_name": "field_zY1xN__c", "is_show": true}, {"field_name": "field_g4zeu__c", "is_show": true}, {"field_name": "field_yHAy0__c", "is_show": true}, {"field_name": "field_3mPOp__c", "is_show": true}, {"field_name": "field_zqXG3__c", "is_show": true}, {"field_name": "field_1M86d__c", "is_show": true}, {"field_name": "mc_exchange_rate", "is_show": true}, {"field_name": "field_caKX4__c", "is_show": true}, {"field_name": "out_owner", "is_show": true}, {"field_name": "mc_currency", "is_show": true}, {"field_name": "field_51jwc__c", "is_show": true}, {"field_name": "field_5bZOS__c", "is_show": true}, {"field_name": "field_51iwF__c", "is_show": true}, {"field_name": "field_UCZk4__c", "is_show": true}, {"field_name": "field_Uw35F__c", "is_show": true}, {"field_name": "field_D2rNz__c", "is_show": true}, {"field_name": "field_3q2MS__c", "is_show": true}, {"field_name": "name", "is_show": true}, {"field_name": "owner", "is_show": true}, {"field_name": "owner_department", "is_show": true}, {"field_name": "record_type", "is_show": true}, {"field_name": "field_Wzg9S__c", "is_show": true}, {"field_name": "life_status", "is_show": true}, {"field_name": "data_own_department", "is_show": true}, {"field_name": "field_PdXnI__c", "is_show": true}, {"field_name": "lock_status", "is_show": true}, {"field_name": "field_ioM6p__c", "is_show": true}, {"field_name": "field_x03I6__c", "is_show": true}, {"field_name": "field_z3mRp__c", "is_show": true}, {"field_name": "field_y9a6j__c", "is_show": true}, {"field_name": "field_61g8v__c", "is_show": true}, {"field_name": "field_u7w22__c", "is_show": true}, {"field_name": "field_49g51__c", "is_show": true}, {"field_name": "field_FT84f__c", "is_show": true}, {"field_name": "field_VtmhT__c", "is_show": true}, {"field_name": "field_82rIe__c", "is_show": true}, {"field_name": "field_z76oi__c", "is_show": true}, {"field_name": "field_24Zkp__c", "is_show": true}, {"field_name": "field_kk4Eq__c", "is_show": true}, {"field_name": "field_alsZi__c", "is_show": true}, {"field_name": "field_dbu5g__c", "is_show": true}, {"field_name": "field_n0Atc__c", "is_show": true}, {"field_name": "field_BbFwU__c", "is_show": true}, {"field_name": "field_9Pkay__c", "is_show": true}, {"field_name": "field_Xc0dX__c", "is_show": true}, {"field_name": "field_x1e7B__c", "is_show": true}, {"field_name": "relevant_team", "is_show": true}, {"field_name": "created_by", "is_show": true}, {"field_name": "create_time", "is_show": true}, {"field_name": "last_modified_by", "is_show": true}, {"field_name": "last_modified_time", "is_show": true}], "field_list_type": 0, "order": 5, "is_all": true, "last_modified_time": 1561100394824, "package": "CRM", "create_time": 1561100394824, "base_scene_api_name": "Shared", "is_new_scene": false, "filters": [{"field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "owner", "field_values": ["${current_user_shared_users}"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "label": "共享给我的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "Shared", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba94", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": [{"field_name": "field_zY1xN__c", "is_show": true}, {"field_name": "field_g4zeu__c", "is_show": true}, {"field_name": "field_yHAy0__c", "is_show": true}, {"field_name": "field_3mPOp__c", "is_show": true}, {"field_name": "field_zqXG3__c", "is_show": true}, {"field_name": "field_1M86d__c", "is_show": true}, {"field_name": "mc_exchange_rate", "is_show": true}, {"field_name": "field_caKX4__c", "is_show": true}, {"field_name": "out_owner", "is_show": true}, {"field_name": "mc_currency", "is_show": true}, {"field_name": "field_51jwc__c", "is_show": true}, {"field_name": "field_5bZOS__c", "is_show": true}, {"field_name": "field_51iwF__c", "is_show": true}, {"field_name": "field_UCZk4__c", "is_show": true}, {"field_name": "field_Uw35F__c", "is_show": true}, {"field_name": "field_D2rNz__c", "is_show": true}, {"field_name": "field_3q2MS__c", "is_show": true}, {"field_name": "name", "is_show": true}, {"field_name": "owner", "is_show": true}, {"field_name": "owner_department", "is_show": true}, {"field_name": "record_type", "is_show": true}, {"field_name": "field_Wzg9S__c", "is_show": true}, {"field_name": "life_status", "is_show": true}, {"field_name": "data_own_department", "is_show": true}, {"field_name": "field_PdXnI__c", "is_show": true}, {"field_name": "lock_status", "is_show": true}, {"field_name": "field_ioM6p__c", "is_show": true}, {"field_name": "field_x03I6__c", "is_show": true}, {"field_name": "field_z3mRp__c", "is_show": true}, {"field_name": "field_y9a6j__c", "is_show": true}, {"field_name": "field_61g8v__c", "is_show": true}, {"field_name": "field_u7w22__c", "is_show": true}, {"field_name": "field_49g51__c", "is_show": true}, {"field_name": "field_FT84f__c", "is_show": true}, {"field_name": "field_VtmhT__c", "is_show": true}, {"field_name": "field_82rIe__c", "is_show": true}, {"field_name": "field_z76oi__c", "is_show": true}, {"field_name": "field_24Zkp__c", "is_show": true}, {"field_name": "field_kk4Eq__c", "is_show": true}, {"field_name": "field_alsZi__c", "is_show": true}, {"field_name": "field_dbu5g__c", "is_show": true}, {"field_name": "field_n0Atc__c", "is_show": true}, {"field_name": "field_BbFwU__c", "is_show": true}, {"field_name": "field_9Pkay__c", "is_show": true}, {"field_name": "field_Xc0dX__c", "is_show": true}, {"field_name": "field_x1e7B__c", "is_show": true}, {"field_name": "relevant_team", "is_show": true}, {"field_name": "created_by", "is_show": true}, {"field_name": "create_time", "is_show": true}, {"field_name": "last_modified_by", "is_show": true}, {"field_name": "last_modified_time", "is_show": true}], "field_list_type": 0, "order": 6, "is_all": true, "last_modified_time": 1561100394824, "package": "CRM", "create_time": 1561100394824, "base_scene_api_name": "SubParticipate", "is_new_scene": false, "filters": [{"field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"], "operator": "EQ", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_subordinates}"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "owner", "field_values": ["${current_user_subordinates}"], "operator": "NIN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "label": "我下属参与的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "SubParticipate", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba93", "extend_attribute": null, "default_scene_priority": null}, {"type": "custom", "_id": "60efd0564b23700001340947", "last_modified_time": 1626329174284, "create_time": 1626329174284, "extend_attribute": null, "created_by": "1000", "object_describe_api_name": "object_n4zWv__c", "object_describe_id": null, "wheres": [], "filters": [{"field_name": "field_49g51__c.name", "field_values": ["123213"], "operator": "LIKE", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "api_name": null, "is_default": false, "is_hidden": false, "label": "11111111111", "last_modified_by": "1000", "order": 7, "package": "CRM", "tenant_id": "74255", "user_id": "1000", "version": 1, "record_type": "", "field_list_type": 0, "field_list": [{"is_show": true, "field_name": "field_zY1xN__c"}, {"is_show": true, "field_name": "field_g4zeu__c"}, {"is_show": true, "field_name": "field_yHAy0__c"}, {"is_show": true, "field_name": "field_3mPOp__c"}, {"is_show": true, "field_name": "field_zqXG3__c"}, {"is_show": true, "field_name": "field_1M86d__c"}, {"is_show": true, "field_name": "mc_exchange_rate"}, {"is_show": true, "field_name": "out_owner"}, {"is_show": true, "field_name": "mc_currency"}, {"is_show": true, "field_name": "field_51jwc__c"}, {"is_show": true, "field_name": "field_5bZOS__c"}, {"is_show": true, "field_name": "field_51iwF__c"}, {"is_show": true, "field_name": "field_UCZk4__c"}, {"is_show": true, "field_name": "field_Uw35F__c"}, {"is_show": true, "field_name": "field_D2rNz__c"}, {"is_show": true, "field_name": "field_3q2MS__c"}, {"is_show": true, "field_name": "name"}, {"is_show": true, "field_name": "owner"}, {"is_show": true, "field_name": "owner_department"}, {"is_show": true, "field_name": "record_type"}, {"is_show": true, "field_name": "field_Wzg9S__c"}, {"is_show": true, "field_name": "life_status"}, {"is_show": true, "field_name": "data_own_department"}, {"is_show": true, "field_name": "field_PdXnI__c"}, {"is_show": true, "field_name": "lock_status"}, {"is_show": true, "field_name": "field_ioM6p__c"}, {"is_show": true, "field_name": "field_x03I6__c"}, {"is_show": true, "field_name": "field_y9a6j__c"}, {"is_show": true, "field_name": "field_61g8v__c"}, {"is_show": true, "field_name": "field_u7w22__c"}, {"is_show": true, "field_name": "field_49g51__c"}, {"is_show": true, "field_name": "relevant_team"}, {"is_show": true, "field_name": "created_by"}, {"is_show": true, "field_name": "create_time"}, {"is_show": true, "field_name": "last_modified_by"}, {"is_show": true, "field_name": "last_modified_time"}, {"field_name": "field_caKX4__c", "is_show": false}, {"field_name": "field_z3mRp__c", "is_show": false}, {"field_name": "field_FT84f__c", "is_show": false}, {"field_name": "field_VtmhT__c", "is_show": false}, {"field_name": "field_82rIe__c", "is_show": false}, {"field_name": "field_z76oi__c", "is_show": false}, {"field_name": "field_24Zkp__c", "is_show": false}, {"field_name": "field_kk4Eq__c", "is_show": false}, {"field_name": "field_alsZi__c", "is_show": false}, {"field_name": "field_dbu5g__c", "is_show": false}, {"field_name": "field_n0Atc__c", "is_show": false}, {"field_name": "field_BbFwU__c", "is_show": false}, {"field_name": "field_9Pkay__c", "is_show": false}, {"field_name": "field_Xc0dX__c", "is_show": false}, {"field_name": "field_x1e7B__c", "is_show": false}], "orders": [{"fieldName": "last_modified_time", "isAsc": false, "isReference": false}], "is_new_scene": false, "tenant_search_id": null, "is_available": true, "base_scene_api_name": "All", "default_scene_priority": null, "out_tenant_id": null, "out_user_id": null}, {"type": "custom", "_id": "60eff17f105ed30001133ba2", "last_modified_time": 1626337862571, "create_time": 1626337663865, "extend_attribute": null, "created_by": "1000", "object_describe_api_name": "object_n4zWv__c", "object_describe_id": null, "wheres": [], "filters": [{"field_name": "field_Wzg9S__c", "field_values": ["option1", "other"], "operator": "LIKE", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}, {"field_name": "owner", "field_values": ["{\"employee\":[\"1000\"],\"department\":[\"1007\"]}"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "value_type": 5, "filterGroup": "1"}], "api_name": null, "is_default": false, "is_hidden": false, "label": "wer324321", "last_modified_by": "1000", "order": 8, "package": "CRM", "tenant_id": "74255", "user_id": "1000", "version": 1, "record_type": "", "field_list_type": 0, "field_list": [{"is_show": true, "field_name": "field_zY1xN__c"}, {"is_show": true, "field_name": "field_g4zeu__c"}, {"is_show": true, "field_name": "field_yHAy0__c"}, {"is_show": true, "field_name": "field_3mPOp__c"}, {"is_show": true, "field_name": "field_zqXG3__c"}, {"is_show": true, "field_name": "field_1M86d__c"}, {"is_show": true, "field_name": "mc_exchange_rate"}, {"is_show": true, "field_name": "out_owner"}, {"is_show": true, "field_name": "mc_currency"}, {"is_show": true, "field_name": "field_51jwc__c"}, {"is_show": true, "field_name": "field_5bZOS__c"}, {"is_show": true, "field_name": "field_51iwF__c"}, {"is_show": true, "field_name": "field_UCZk4__c"}, {"is_show": true, "field_name": "field_Uw35F__c"}, {"is_show": true, "field_name": "field_D2rNz__c"}, {"is_show": true, "field_name": "field_3q2MS__c"}, {"is_show": true, "field_name": "name"}, {"is_show": true, "field_name": "owner"}, {"is_show": true, "field_name": "owner_department"}, {"is_show": true, "field_name": "record_type"}, {"is_show": true, "field_name": "field_Wzg9S__c"}, {"is_show": true, "field_name": "life_status"}, {"is_show": true, "field_name": "data_own_department"}, {"is_show": true, "field_name": "field_PdXnI__c"}, {"is_show": true, "field_name": "lock_status"}, {"is_show": true, "field_name": "field_ioM6p__c"}, {"is_show": true, "field_name": "field_x03I6__c"}, {"is_show": true, "field_name": "field_y9a6j__c"}, {"is_show": true, "field_name": "field_61g8v__c"}, {"is_show": true, "field_name": "field_u7w22__c"}, {"is_show": true, "field_name": "field_49g51__c"}, {"is_show": true, "field_name": "relevant_team"}, {"is_show": true, "field_name": "created_by"}, {"is_show": true, "field_name": "create_time"}, {"is_show": true, "field_name": "last_modified_by"}, {"is_show": true, "field_name": "last_modified_time"}, {"field_name": "field_caKX4__c", "is_show": false}, {"field_name": "field_z3mRp__c", "is_show": false}, {"field_name": "field_FT84f__c", "is_show": false}, {"field_name": "field_VtmhT__c", "is_show": false}, {"field_name": "field_82rIe__c", "is_show": false}, {"field_name": "field_z76oi__c", "is_show": false}, {"field_name": "field_24Zkp__c", "is_show": false}, {"field_name": "field_kk4Eq__c", "is_show": false}, {"field_name": "field_alsZi__c", "is_show": false}, {"field_name": "field_dbu5g__c", "is_show": false}, {"field_name": "field_n0Atc__c", "is_show": false}, {"field_name": "field_BbFwU__c", "is_show": false}, {"field_name": "field_9Pkay__c", "is_show": false}, {"field_name": "field_Xc0dX__c", "is_show": false}, {"field_name": "field_x1e7B__c", "is_show": false}], "orders": [{"fieldName": "last_modified_time", "isAsc": false, "isReference": false}], "is_new_scene": false, "tenant_search_id": null, "is_available": true, "base_scene_api_name": "All", "default_scene_priority": null, "out_tenant_id": null, "out_user_id": null}, {"type": "custom", "_id": "60f01aca2f6c660001164107", "last_modified_time": 1626348234854, "create_time": 1626348234854, "extend_attribute": null, "created_by": "1000", "object_describe_api_name": "object_n4zWv__c", "object_describe_id": null, "wheres": [], "filters": [{"field_name": "field_49g51__c.name", "field_values": ["c", "d"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "api_name": null, "is_default": false, "is_hidden": false, "label": "fsf", "last_modified_by": "1000", "order": null, "package": "CRM", "tenant_id": "74255", "user_id": "1000", "version": 1, "record_type": "", "field_list_type": 0, "field_list": [{"is_show": true, "field_name": "name"}, {"is_show": true, "field_name": "field_x03I6__c"}, {"is_show": true, "field_name": "field_61g8v__c"}, {"is_show": true, "field_name": "field_49g51__c"}, {"is_show": true, "field_name": "field_zY1xN__c"}, {"is_show": true, "field_name": "field_g4zeu__c"}, {"is_show": true, "field_name": "field_yHAy0__c"}, {"is_show": true, "field_name": "field_3mPOp__c"}, {"is_show": true, "field_name": "field_zqXG3__c"}, {"is_show": true, "field_name": "field_1M86d__c"}, {"is_show": true, "field_name": "mc_exchange_rate"}, {"is_show": true, "field_name": "out_owner"}, {"is_show": true, "field_name": "mc_currency"}, {"is_show": true, "field_name": "field_51jwc__c"}, {"is_show": true, "field_name": "field_5bZOS__c"}, {"is_show": true, "field_name": "field_51iwF__c"}, {"is_show": true, "field_name": "field_UCZk4__c"}, {"is_show": true, "field_name": "field_Uw35F__c"}, {"is_show": true, "field_name": "field_D2rNz__c"}, {"is_show": true, "field_name": "field_3q2MS__c"}, {"is_show": true, "field_name": "owner"}, {"is_show": true, "field_name": "owner_department"}, {"is_show": true, "field_name": "record_type"}, {"is_show": true, "field_name": "field_Wzg9S__c"}, {"is_show": true, "field_name": "life_status"}, {"is_show": true, "field_name": "data_own_department"}, {"is_show": true, "field_name": "field_PdXnI__c"}, {"is_show": true, "field_name": "lock_status"}, {"is_show": true, "field_name": "relevant_team"}, {"is_show": true, "field_name": "created_by"}, {"is_show": true, "field_name": "create_time"}, {"is_show": true, "field_name": "last_modified_by"}, {"is_show": true, "field_name": "last_modified_time"}, {"is_show": true, "field_name": "field_ioM6p__c"}, {"is_show": true, "field_name": "field_y9a6j__c"}, {"field_name": "field_caKX4__c", "is_show": false}, {"field_name": "field_z3mRp__c", "is_show": false}, {"field_name": "field_u7w22__c", "is_show": false}, {"field_name": "field_FT84f__c", "is_show": false}, {"field_name": "field_VtmhT__c", "is_show": false}, {"field_name": "field_82rIe__c", "is_show": false}, {"field_name": "field_z76oi__c", "is_show": false}, {"field_name": "field_24Zkp__c", "is_show": false}, {"field_name": "field_kk4Eq__c", "is_show": false}, {"field_name": "field_alsZi__c", "is_show": false}, {"field_name": "field_dbu5g__c", "is_show": false}, {"field_name": "field_n0Atc__c", "is_show": false}, {"field_name": "field_BbFwU__c", "is_show": false}, {"field_name": "field_9Pkay__c", "is_show": false}, {"field_name": "field_Xc0dX__c", "is_show": false}, {"field_name": "field_x1e7B__c", "is_show": false}], "orders": [{"fieldName": "last_modified_time", "isAsc": false, "isReference": false}], "is_new_scene": false, "tenant_search_id": null, "is_available": true, "base_scene_api_name": "All", "default_scene_priority": null, "out_tenant_id": null, "out_user_id": null}, {"type": "custom", "_id": "60f101b00abc4e0001b27e3c", "last_modified_time": 1626407344283, "create_time": 1626407344283, "extend_attribute": null, "created_by": "1000", "object_describe_api_name": "object_n4zWv__c", "object_describe_id": null, "wheres": [], "filters": [{"field_name": "field_49g51__c.name", "field_values": ["fdf", "sd"], "operator": "IN", "connector": "AND", "fieldNum": 0, "isObjectReference": false, "isIndex": false, "filterGroup": "1"}], "api_name": null, "is_default": false, "is_hidden": false, "label": "ds", "last_modified_by": "1000", "order": null, "package": "CRM", "tenant_id": "74255", "user_id": "1000", "version": 1, "record_type": "", "field_list_type": 0, "field_list": [{"is_show": true, "field_name": "name"}, {"is_show": true, "field_name": "field_x03I6__c"}, {"is_show": true, "field_name": "field_61g8v__c"}, {"is_show": true, "field_name": "field_49g51__c"}, {"is_show": true, "field_name": "field_zY1xN__c"}, {"is_show": true, "field_name": "field_g4zeu__c"}, {"is_show": true, "field_name": "field_yHAy0__c"}, {"is_show": true, "field_name": "field_3mPOp__c"}, {"is_show": true, "field_name": "field_zqXG3__c"}, {"is_show": true, "field_name": "field_1M86d__c"}, {"is_show": true, "field_name": "mc_exchange_rate"}, {"is_show": true, "field_name": "out_owner"}, {"is_show": true, "field_name": "mc_currency"}, {"is_show": true, "field_name": "field_51jwc__c"}, {"is_show": true, "field_name": "field_5bZOS__c"}, {"is_show": true, "field_name": "field_51iwF__c"}, {"is_show": true, "field_name": "field_UCZk4__c"}, {"is_show": true, "field_name": "field_Uw35F__c"}, {"is_show": true, "field_name": "field_D2rNz__c"}, {"is_show": true, "field_name": "field_3q2MS__c"}, {"is_show": true, "field_name": "owner"}, {"is_show": true, "field_name": "owner_department"}, {"is_show": true, "field_name": "record_type"}, {"is_show": true, "field_name": "field_Wzg9S__c"}, {"is_show": true, "field_name": "life_status"}, {"is_show": true, "field_name": "data_own_department"}, {"is_show": true, "field_name": "field_PdXnI__c"}, {"is_show": true, "field_name": "lock_status"}, {"is_show": true, "field_name": "relevant_team"}, {"is_show": true, "field_name": "created_by"}, {"is_show": true, "field_name": "create_time"}, {"is_show": true, "field_name": "last_modified_by"}, {"is_show": true, "field_name": "last_modified_time"}, {"is_show": true, "field_name": "field_ioM6p__c"}, {"field_name": "field_caKX4__c", "is_show": false}, {"field_name": "field_z3mRp__c", "is_show": false}, {"field_name": "field_y9a6j__c", "is_show": false}, {"field_name": "field_u7w22__c", "is_show": false}, {"field_name": "field_FT84f__c", "is_show": false}, {"field_name": "field_VtmhT__c", "is_show": false}, {"field_name": "field_82rIe__c", "is_show": false}, {"field_name": "field_z76oi__c", "is_show": false}, {"field_name": "field_24Zkp__c", "is_show": false}, {"field_name": "field_kk4Eq__c", "is_show": false}, {"field_name": "field_alsZi__c", "is_show": false}, {"field_name": "field_dbu5g__c", "is_show": false}, {"field_name": "field_n0Atc__c", "is_show": false}, {"field_name": "field_BbFwU__c", "is_show": false}, {"field_name": "field_9Pkay__c", "is_show": false}, {"field_name": "field_Xc0dX__c", "is_show": false}, {"field_name": "field_x1e7B__c", "is_show": false}], "orders": [{"fieldName": "last_modified_time", "isAsc": false, "isReference": false}], "is_new_scene": false, "tenant_search_id": null, "is_available": true, "base_scene_api_name": "All", "default_scene_priority": null, "out_tenant_id": null, "out_user_id": null}], "baseScenes": [{"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [{"connector": "OR", "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user}||${current_user_subordinates}||${current_user_dept_users}||${current_user_shared_users}"]}]}], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": null, "field_list_type": null, "order": 1, "last_modified_time": 1561100394821, "package": "CRM", "create_time": 1561100394821, "base_scene_api_name": null, "is_new_scene": null, "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user}||${current_user_subordinates}||${current_user_dept_users}||${current_user_shared_users}"]}], "label": "全部", "last_modified_by": null, "is_default": true, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "All", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba8e", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [{"connector": "OR", "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user}"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "N", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user}"]}]}], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": null, "field_list_type": null, "order": 3, "last_modified_time": 1561100394822, "package": "CRM", "create_time": 1561100394822, "base_scene_api_name": null, "is_new_scene": null, "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user}"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "N", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user}"]}], "label": "我参与的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "Participate", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba90", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [{"connector": "OR", "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user}"]}]}], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": null, "field_list_type": null, "order": 2, "last_modified_time": 1561100394822, "package": "CRM", "create_time": 1561100394822, "base_scene_api_name": null, "is_new_scene": null, "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user}"]}], "label": "我负责的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "InCharge", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba8f", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [{"connector": "OR", "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_subordinates}"]}]}], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": null, "field_list_type": null, "order": 5, "last_modified_time": 1561100394823, "package": "CRM", "create_time": 1561100394823, "base_scene_api_name": null, "is_new_scene": null, "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_subordinates}"]}], "label": "我下属负责的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "SubInCharge", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba92", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [{"connector": "OR", "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_dept_users}"]}]}], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": null, "field_list_type": null, "order": 4, "last_modified_time": 1561100394823, "package": "CRM", "create_time": 1561100394823, "base_scene_api_name": null, "is_new_scene": null, "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_dept_users}"]}], "label": "我负责部门的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "InChargeDept", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba91", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [{"connector": "OR", "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user_shared_users}"]}]}], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": null, "field_list_type": null, "order": 7, "last_modified_time": 1561100394824, "package": "CRM", "create_time": 1561100394824, "base_scene_api_name": null, "is_new_scene": null, "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user_shared_users}"]}], "label": "共享给我的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "Shared", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba94", "extend_attribute": null, "default_scene_priority": null}, {"tenant_id": "74255", "is_hidden": false, "type": "default", "wheres": [{"connector": "OR", "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_subordinates}"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "NIN", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user_subordinates}"]}]}], "object_describe_api_name": "object_n4zWv__c", "out_user_id": null, "field_list": null, "field_list_type": null, "order": 6, "last_modified_time": 1561100394824, "package": "CRM", "create_time": 1561100394824, "base_scene_api_name": null, "is_new_scene": null, "filters": [{"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "EQ", "isObjectReference": false, "field_name": "object_describe_api_name", "field_values": ["object_faJ94__c"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "IN", "isObjectReference": false, "field_name": "relevant_team.teamMemberEmployee", "field_values": ["${current_user_subordinates}"]}, {"connector": "AND", "isIndex": false, "fieldNum": 0, "operator": "NIN", "isObjectReference": false, "field_name": "owner", "field_values": ["${current_user_subordinates}"]}], "label": "我下属参与的", "last_modified_by": null, "is_default": false, "out_tenant_id": null, "created_by": "system", "version": 1, "record_type": null, "is_available": null, "tenant_search_id": null, "user_id": "1000", "api_name": "SubParticipate", "object_describe_id": null, "_id": "5d0c806a7cfed91f3e95ba93", "extend_attribute": null, "default_scene_priority": null}], "isChildObj": false, "objectDescribeExt": {"fields": {"field_x03I6__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": true, "index_name": "a_6", "is_index": true, "is_active": true, "create_time": 1626160612604, "is_encrypted": false, "label": "查找关联(多选)-对象", "target_api_name": "object_Zpv1K__c", "target_related_list_name": "target_related_list_a841G__c", "field_num": 28, "is_open_display_name": true, "target_related_list_label": "wj-多选", "action_on_target_delete": "set_null", "api_name": "field_x03I6__c", "_id": "60ed3de47d53dc000190e6e1", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_49g51__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_19", "is_index": true, "is_active": true, "create_time": 1626316945278, "is_encrypted": false, "label": "查找关联-wj主属性自定义", "target_api_name": "object_Ju6l7__c", "target_related_list_name": "target_related_list_22En4__c", "field_num": 32, "is_open_display_name": true, "target_related_list_label": "wj-主属性自定义", "action_on_target_delete": "set_null", "api_name": "field_49g51__c", "_id": "60efa09123cb7c000131aa80", "is_index_field": true, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"target_related_list_label": 1, "wheres": 1, "api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "target_api_name": 1, "target_related_list_name": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_61g8v__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_18", "is_index": true, "is_active": true, "create_time": 1626171100307, "is_encrypted": false, "label": "查找关联", "target_api_name": "object_Zpv1K__c", "target_related_list_name": "target_related_list_nrz20__c", "field_num": 29, "is_open_display_name": true, "target_related_list_label": "wj-dddd", "action_on_target_delete": "set_null", "api_name": "field_61g8v__c", "_id": "60ed66dd309df60001999e88", "is_index_field": true, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"target_related_list_label": 1, "wheres": 1, "api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "target_api_name": 1, "target_related_list_name": 1, "help_text": 1}}, "help_text": "", "status": "new"}, "field_z3mRp__c": {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": true, "index_name": "a_9", "is_index": true, "is_active": true, "create_time": 1626685031003, "is_encrypted": false, "label": "fj-查找关联(多选)", "target_api_name": "object_o6elx__c", "target_related_list_name": "target_related_list_4n4u3__c", "field_num": 34, "is_open_display_name": true, "target_related_list_label": "wj-多选", "action_on_target_delete": "set_null", "api_name": "field_z3mRp__c", "_id": "60f53e674563f80001eb1368", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}}}, "supportTag": true, "supportOrFilter": false, "visibleFields": ["field_zY1xN__c", "field_g4zeu__c", "field_yHAy0__c", "field_3mPOp__c", "field_zqXG3__c", "field_1M86d__c", "mc_exchange_rate", "field_caKX4__c", "out_owner", "mc_currency", "field_51jwc__c", "field_5bZOS__c", "field_51iwF__c", "field_UCZk4__c", "field_Uw35F__c", "field_D2rNz__c", "field_3q2MS__c", "name", "owner", "owner_department", "record_type", "field_Wzg9S__c", "life_status", "data_own_department", "field_PdXnI__c", "lock_status", "field_ioM6p__c", "field_x03I6__c", "field_z3mRp__c", "field_y9a6j__c", "field_61g8v__c", "field_u7w22__c", "field_49g51__c", "field_FT84f__c", "field_VtmhT__c", "field_82rIe__c", "field_z76oi__c", "field_24Zkp__c", "field_kk4Eq__c", "field_alsZi__c", "field_dbu5g__c", "field_n0Atc__c", "field_BbFwU__c", "field_9Pkay__c", "field_Xc0dX__c", "field_x1e7B__c", "relevant_team", "created_by", "create_time", "last_modified_by", "last_modified_time"], "visibleFieldsWidth": [{"field_name": "field_zY1xN__c", "width": null}, {"field_name": "field_g4zeu__c", "width": null}, {"field_name": "field_yHAy0__c", "width": null}, {"field_name": "field_3mPOp__c", "width": null}, {"field_name": "field_zqXG3__c", "width": null}, {"field_name": "field_1M86d__c", "width": null}, {"field_name": "mc_exchange_rate", "width": null}, {"field_name": "field_caKX4__c", "width": null}, {"field_name": "out_owner", "width": null}, {"field_name": "mc_currency", "width": null}, {"field_name": "field_51jwc__c", "width": null}, {"field_name": "field_5bZOS__c", "width": null}, {"field_name": "field_51iwF__c", "width": null}, {"field_name": "field_UCZk4__c", "width": null}, {"field_name": "field_Uw35F__c", "width": null}, {"field_name": "field_D2rNz__c", "width": null}, {"field_name": "field_3q2MS__c", "width": null}, {"field_name": "name", "width": null}, {"field_name": "owner", "width": null}, {"field_name": "owner_department", "width": null}, {"field_name": "record_type", "width": null}, {"field_name": "field_Wzg9S__c", "width": null}, {"field_name": "life_status", "width": null}, {"field_name": "data_own_department", "width": null}, {"field_name": "field_PdXnI__c", "width": null}, {"field_name": "lock_status", "width": null}, {"field_name": "field_ioM6p__c", "width": null}, {"field_name": "field_x03I6__c", "width": 327}, {"field_name": "field_z3mRp__c", "width": null}, {"field_name": "field_y9a6j__c", "width": null}, {"field_name": "field_61g8v__c", "width": null}, {"field_name": "field_u7w22__c", "width": null}, {"field_name": "field_49g51__c", "width": null}, {"field_name": "field_FT84f__c", "width": null}, {"field_name": "field_VtmhT__c", "width": null}, {"field_name": "field_82rIe__c", "width": null}, {"field_name": "field_z76oi__c", "width": null}, {"field_name": "field_24Zkp__c", "width": null}, {"field_name": "field_kk4Eq__c", "width": null}, {"field_name": "field_alsZi__c", "width": null}, {"field_name": "field_dbu5g__c", "width": null}, {"field_name": "field_n0Atc__c", "width": null}, {"field_name": "field_BbFwU__c", "width": null}, {"field_name": "field_9Pkay__c", "width": null}, {"field_name": "field_Xc0dX__c", "width": null}, {"field_name": "field_x1e7B__c", "width": null}, {"field_name": "relevant_team", "width": null}, {"field_name": "created_by", "width": null}, {"field_name": "create_time", "width": 80}, {"field_name": "last_modified_by", "width": 80}, {"field_name": "last_modified_time", "width": 80}], "buttons": [{"action_type": "system", "api_name": "ChangeOwner_button_default", "label": "更换负责人", "action": "AsyncBulkChangeOwner"}, {"action_type": "system", "api_name": "Abolish_button_default", "label": "作废", "action": "AsyncBulkInvalid"}, {"action_type": "system", "api_name": "AddTeamMember_button_default", "label": "添加相关团队成员", "action": "AsyncBulkAddTeamMember"}, {"action_type": "system", "api_name": "DeleteTeamMember_button_default", "label": "移除相关团队成员", "action": "AsyncBulkDeleteTeamMember"}, {"action_type": "system", "api_name": "Lock_button_default", "label": "锁定", "action": "AsyncBulkLock"}, {"action_type": "system", "api_name": "Unlock_button_default", "label": "解锁", "action": "AsyncBulkUnlock"}, {"action_type": "default", "api_name": "Export_button_default", "action": "Export", "label": "导出"}, {"action_type": "default", "api_name": "ExportFile_button_default", "action": "ExportFile", "label": "导出图片/附件"}, {"action_type": "system", "api_name": "Print_button_default", "label": "打印", "action": "AsyncBulkPrint"}, {"wheres": [], "param_form": [{"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 3, "default_to_zero": true, "is_required": true, "define_type": "custom", "is_single": false, "index_name": "d_2", "max_length": 7, "is_index": true, "is_active": true, "create_time": 1626172662369, "is_encrypted": false, "display_style": "step", "step_value": -1000, "length": 4, "default_value": "", "label": "数字名称特特特哔哔哔哔哔长的的时候的", "field_num": 30, "is_readonly": false, "object_api_name": "object_n4zWv__c", "api_name": "form_field_u7w22__c", "_id": "60ed6cf6309df6000199aff0", "is_index_field": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1, "decimal_places": 1}}, "round_mode": 4, "help_text": "", "status": "new"}, {"describe_api_name": "object_n4zWv__c", "is_index": true, "is_active": true, "create_time": 1620439904419, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "", "label": "单选", "type": "select_one", "field_num": 11, "is_readonly": false, "is_required": false, "object_api_name": "object_n4zWv__c", "api_name": "form_field_PdXnI__c", "options": [{"font_color": "#2a304d", "label": "示例选项", "value": "GdcMaZg1s", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#181c25", "label": "示例选项1", "value": "vV2pE1J0y", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#ff8000", "label": "示例选项2", "value": "n343tmu26", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#ff522a", "label": "示例选项3", "value": "option1", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"font_color": "#30c776", "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "custom", "_id": "6095f3605397a20001075a2b", "is_single": false, "is_index_field": false, "index_name": "s_6", "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "new"}, {"describe_api_name": "object_n4zWv__c", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_19", "is_index": true, "is_active": true, "create_time": 1626316945278, "is_encrypted": false, "label": "查找关联-wj主属性自定义", "target_api_name": "object_Ju6l7__c", "target_related_list_name": "target_related_list_22En4__c", "field_num": 32, "is_open_display_name": true, "target_related_list_label": "wj-主属性自定义", "is_readonly": false, "action_on_target_delete": "set_null", "object_api_name": "object_n4zWv__c", "api_name": "form_field_49g51__c", "_id": "60efa09123cb7c000131aa80", "is_index_field": true, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"target_related_list_label": 1, "wheres": 1, "api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "target_api_name": 1, "target_related_list_name": 1, "help_text": 1}}, "help_text": "", "status": "new"}], "actions": ["60efd18c4b23700001340c4b"], "_id": "60efa46223cb7c000131b500", "api_name": "button_w9s49__c", "tenant_id": "74255", "describe_api_name": "object_n4zWv__c", "description": "", "label": "按钮名称(示例)", "created_by": "1000", "create_time": 1626317922818, "last_modified_time": 1626329484439, "button_type": "common", "use_pages": ["detail", "list", "list_batch"], "jump_url": "", "define_type": "custom", "last_modified_by": "1000", "is_active": true, "is_deleted": false, "version": 3, "redirect_type": "", "lock_data_show_button": false, "action_type": "custom", "action": "BulkCustomButton"}], "isInApprovalWhiteList": false, "hasEditPermission": true, "supportGeoQuery": false, "quickFilterField": [], "viewInfo": [{"name": "list_view", "is_default": true, "is_show": true}, {"name": "split_view", "is_default": false, "is_show": true}, {"name": "map_view", "bubble_info": {"field": "lock_status", "options": [{"color": "rgba(255,0,0,1)", "label": "未锁定", "value": "0"}, {"color": "rgba(255,0,0,1)", "label": "锁定", "value": "1"}]}, "fields": ["name", "owner", "field_1M86d__c", "field_z3mRp__c", "field_82rIe__c", "mc_exchange_rate", "field_zY1xN__c", "owner_department", "field_51iwF__c", "field_3q2MS__c", "lock_status", "field_FT84f__c", "field_5bZOS__c", "created_by", "field_zqXG3__c", "data_own_department"], "is_default": false, "is_show": true, "location_field": "field_1M86d__c"}, {"display_type": "list", "time_dimension": ["create_time", "create_time"], "name": "calendar_view", "fields": ["name", "field_PdXnI__c"], "is_default": false, "is_show": false}], "summaryInfo": [{"type": "sum", "field_name": "field_82rIe__c"}, {"type": "sum", "field_name": "field_FT84f__c"}, {"type": "sum", "field_name": "field_u7w22__c"}, {"type": "sum", "field_name": "field_VtmhT__c"}, {"type": "sum", "field_name": "field_z76oi__c"}], "listSingleExposed": 0, "sceneRenderType": "drop_down", "allPageSummaryInfo": [{"api_name": "sum_field_82rIe__c", "type": "sum", "field_name": "field_82rIe__c"}, {"api_name": "sum_field_FT84f__c", "type": "sum", "field_name": "field_FT84f__c"}, {"api_name": "sum_field_u7w22__c", "type": "sum", "field_name": "field_u7w22__c"}, {"api_name": "sum_field_VtmhT__c", "type": "sum", "field_name": "field_VtmhT__c"}, {"api_name": "sum_field_z76oi__c", "type": "sum", "field_name": "field_z76oi__c"}]}