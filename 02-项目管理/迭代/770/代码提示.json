{
    "Fx": {
        "require": false, //标识这个key（Fx）可以省略   仅支持省略第一个
        "statics": {
            "object": {
                "statics": {
                    "create": {
                        "args": [ //args 属性非null、undefined、false时当属性处理， args为数组时（空数组也可）当函数处理
                            {
                                "dataType": "String",//参数类型
                                "name": "apiName",//参数名称
                                "mandatory": true,//是否必填参数
                                "modules": []//参数默认值
                            },
                            {
                                "dataType": "Map",
                                "name": "objectData",
                                "mandatory": true,
                                "modules": []
                            }
                        ],
                        "functionName": "create",//方法名称  为空时默认为对应key的“.”最后一个
                        "definition": "",//方法括号体 为空时会自动按照args里的内容构造出来
                        "returnType": "List", //返回类型
                        "description": "创建业务对象",
                        "example": "def (error, data, errorMessage) = Fx.object.create(\"AccountObj\",[\"name\":\"客户\"])"
                    },
                    "update": {
                        "args": [
                            {
                                "dataType": "String",
                                "name": "apiName",
                                "mandatory": true,
                                "modules": []
                            },
                            {
                                "dataType": "String",
                                "name": "objectDataId",
                                "mandatory": true,
                                "modules": []
                            },
                            {
                                "dataType": "Map",
                                "name": "objectData",
                                "mandatory": true,
                                "modules": []
                              }
                        ],
                        "functionName": "update",
                        "definition": "",
                        "returnType": "List",
                        "description": "更新业务对象字段",
                        "example": "def (error, data, errorMessage) = Fx.object.update(\"AccountObj\",\"id123456\",[\"name\":\"客户\"])"
                    },
                    "findById": {
                        "args": [
                            {
                                "dataType": "String",
                                "name": "apiName",
                                "mandatory": true,
                                "modules": []
                            },
                            {
                                "dataType": "String",
                                "name": "objectDataId",
                                "mandatory": true,
                                "modules": []
                            }
                        ],
                        "functionName": "findById",
                        "definition": "",
                        "returnType": "List",
                        "description": "按业务对象Id查询业务对象数据",
                        "example": "def (error, data, errorMessage) = Fx.object.findById(\"AccountObj\",\"id123456\")"
                    }
                }
            }
        }
    }
}