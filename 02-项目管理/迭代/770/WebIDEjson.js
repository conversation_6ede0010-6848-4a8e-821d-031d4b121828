define(function (require, exports, module) {

  let condition = '${1:<expression>}';
  /**
   * 编辑器配置
   */
  module.exports = {
    basic_config: {
      fontSize: 14,    //设置编辑器里字体的大小
      tabSize: 2,
      firstLineNumber: 1,
      theme: 'ace/theme/xcode',
      printMargin: false,     //是否显示打印边距
      foldStyle: 'markbegin', //显示收起箭头
      enableBasicAutocompletion: true,
      enableSnippets: true,
      enableLiveAutocompletion: true
    },

    snippets: [       //代码片段配置
      {
        groupName: $t("基础"),
        elements: [
          {
            name: 'set_variable',
            displayName: 'set variable',
            content: '${1:<type>} ${2:<variable>} = ${3:<expression>}'
          }, {
            name: 'return',
            content: 'return ${1:<result>}'
          }, {
            name: 'log_info',
            displayName: 'log info',
            content: 'log.info(${1:<text>})'
          }, {
            name: 'log_debug',
            displayName: 'log debug',
            content: 'log.debug(${1:<text>})'
          }
        ]
      },
      {
        groupName: $t("条件"),
        elements: [{
          name: 'if',
          content: `if( ${condition} ){\n}`,
          description: $t("如果表达式的值为true则执行if语句中的代码块"),
          example: `if(mark < 39){\n\tinfo 'Reappear';\n}`
        }, {
          name: 'else_if',
          displayName: 'else if',
          content: `else if( ${condition} ){\n}`,
          description: `${$t("跟在if语句后面")},${$t("如果表达式的值为true则执行if语句中的代码块")}`
        }, {
          name: 'else',
          content: 'else{\n}',
          description: `${$t("跟在if语句后面")},${$t("如果if语句的表达式的值为false")},${$t("则执行else语句中的代码块")}`
        }, {
          name: 'switch',
          content: `switch(\${1:<key>}) {\n\tcase \${2:<value>}:\n\n\t\tbreak;\n\tdefault:\n\n}`,
          description: $t("switch语句是if语句的兄弟语句可以用switch语句为表达式提供一系列的情况case")
        }]
      },
      {
        groupName: $t("变量类型"),
        elements: [{
          name: 'List',
          // displayName: 'creat list',       //用于显示的名称   不存在时使用name
          content: 'List ${1:<variable>} = [${2}]'
        }, {
          name: 'Map',
          content: 'Map ${1:<variable>} = ["${2:<key>}": ${3:<value>}]'
        }, {
          name: 'String',
          content: 'String ${1:<variable>} = "${2}"'
        }, {
          name: 'BigDecimal',
          content: 'BigDecimal ${1:<variable>} = ${2:1}'
        }, {
          name: 'Date',
          content: 'Date ${1:<variable>} = "${2:<yyyy-MM-dd>}"'
        }, {
          name: 'Time',
          content: 'Time ${1:<variable>} = "${2:<HH:mm>}"'
        }, {
          name: 'DateTime',
          content: 'DateTime ${1:<variable>} = "${2:<yyyy-MM-dd HH:mm>}"'
        }]
      }
    ],

    completions: {    //支持的completion
      Fx: {
        require: false,      //标识这个key（Fx）可以省略   仅支持省略第一个
        statics: {
          object: {
            statics: {
              create: {
                args: [{     //args 属性非null、undefined、false时当属性处理， args为数组时（空数组也可）当函数处理
                  dataType: 'String', //参数类型
                  name: 'apiName', //参数名称
                  mandatory: true, //是否必填参数
                  modules: [] //参数默认值
                },
                {
                  dataType: 'Map', //参数类型
                  name: 'objectData', //参数名称
                  mandatory: true, //是否必填参数
                  modules: [] //参数默认值
                }
                ],
                functionName: '', //方法名称  为空时默认为对应key的“.”最后一个
                definition: '', //方法括号体 为空时会自动按照args里的内容构造出来
                returnType: 'List', //返回类型
                description: $t("创建业务对象"),
                example: `def (error, data, errorMessage) = Fx.object.create("AccountObj",["name":${$t("客户")}])`
              },
              update: {
                args: [{
                  dataType: 'String', //参数类型
                  name: 'apiName', //参数名称
                  mandatory: true, //是否必填参数
                  modules: [] //参数默认值
                },
                {
                  dataType: 'String', //参数类型
                  name: 'objectDataId', //参数名称
                  mandatory: true, //是否必填参数
                  modules: [] //参数默认值
                },
                {
                  dataType: 'Map',
                  name: 'objectData',
                  mandatory: true,
                  modules: []
                }
                ],
                functionName: 'update',
                returnType: 'List',
                description: $t("更新业务对象字段"),
                example: `def (error, data, errorMessage) = Fx.object.update("AccountObj","id123456",["name":"${$t("纷享销客")}"])`
              },
              findById: {
                args: [{
                  dataType: 'String', //参数类型
                  name: 'apiName', //参数名称
                  mandatory: true, //是否必填参数
                  modules: [] //参数默认值
                },
                {
                  dataType: 'String',
                  name: 'objectDataId',
                  mandatory: true,
                  modules: []
                }
                ],
                functionName: '',
                returnType: 'List',
                description: $t("按业务对象Id查询业务对象数据"),
                example: 'def (error, data, errorMessage) = Fx.object.findById("AccountObj","id123456")'
              },
              find: {
                args: [{
                    dataType: 'String', //参数类型
                    name: 'apiName', //参数名称
                    mandatory: true, //是否必填参数
                    modules: [] //参数默认值
                  },
                  {
                    dataType: 'List<Map>',
                    name: 'criteria',
                    mandatory: true,
                    modules: []
                  },
                  {
                    dataType: 'BigDecimal',
                    name: 'limit',
                    mandatory: true,
                    modules: []
                  },
                  {
                    dataType: 'BigDecimal',
                    name: 'skip',
                    mandatory: true,
                    modules: []
                  },
                ],
                functionName: '',
                returnType: 'List',
                description: $t("按查询条件查询业务对象"),
                example: `def (error, data, errorMessage) = Fx.object.find("AccountObj",[\n\t["name":"${$t("纷享销客")}"],\n\t["create_time": Operator.GT(********)]\n], 10, 0);`
              },
              remove: {
                args: [{
                  dataType: 'String', //参数类型
                  name: 'apiName', //参数名称
                  mandatory: true, //是否必填参数
                  modules: [] //参数默认值
                },
                {
                  dataType: 'String',
                  name: 'objectDataId',
                  mandatory: true,
                  modules: []
                }
                ],
                functionName: 'remove',
                returnType: 'List',
                description: $t("作废业务对象"),
                example: 'def (error, data, errorMessage) = Fx.object.remove("AccountObj","id123456")'
              },
              changeOwner: {
                args: [{
                  dataType: 'String',
                  name: 'objectApiName',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'String',
                  name: 'objectDataId',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'String',
                  name: 'ownerId',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'changeOwner',
                description: $t('更换负责人'),
                example: 'Fx.object.changeOwner("AccountObj","ed47841898054749a2ec9be9e6e5d728","1001")'
              },
              addTeamMember: {
                args: [{
                  dataType: 'String',
                  name: 'objectApiName',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'String',
                  name: 'objectDataId',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'List',
                  name: 'userList',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Integer',
                  name: 'role',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Integer',
                  name: 'premission',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'addTeamMember',
                description: $t('添加团队成员'),
                example: `Fx.object.addTeamMember("AccountObj","83cf73d957924284a96e9c44ebb333ec",["1001"],4,1)\n枚举值\nRole 1:${$t("负责人")}2:${$t("联合跟进人")}3:${$t("售后服务人员")}4:${$t("普通成员")}; Permission (1:${$t("只读")}2:${$t("读写")})`
              },
              deleteTeamMember: {
                args: [{
                  dataType: 'String',
                  name: 'objectApiName',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'String',
                  name: 'objectDataId',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'List',
                  name: 'userList',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: '',
                  name: 'role',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: '',
                  name: 'premission',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'deleteTeamMember',
                description: $t('移除团队成员'),
                example: 'Fx.object.deleteTeamMember("AccountObj","83cf73d957924284a96e9c44ebb333ec",["1001"])'
              },
              editTeamMember: {
                args: [{
                  dataType: 'String',
                  name: 'objectApiName',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'String',
                  name: 'objectDataId',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'List',
                  name: 'teamMemberList',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'editTeamMember',
                description: '编辑团队成员',
                example: 'Fx.object.editTeamMember("AccountObj","83cf73d957924284a96e9c44ebb333ec",["1002"])'
              },
              getTeamMember: {
                args: [{
                  dataType: 'String',
                  name: 'objectApiName',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'String',
                  name: 'objectDataId',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'getTeamMember',
                description: '获取团队成员',
                example: 'Fx.object.getTeamMember("AccountObj","83cf73d957924284a96e9c44ebb333ec")'
              }
            }
          },
          org: {
            statics: {
              findUserById: {
                args: [{
                  dataType: 'String',
                  name: 'userId',
                  mandatory: true,
                  modules: []
                }],
                returnType: 'Map',
                description: $t("按用户Id查询用户信息"),
                example: 'def (error, data, errorMessage) = org.findUserById("1000")'
              },
              findByUserIds: {
                args: [{
                  dataType: 'List',
                  name: 'userId',
                  mandatory: true,
                  modules: []
                }],
                returnType: 'Map<String,Map>',
                description: $t("按用户Id列表查询若干用户信息"),
                example: 'def (error, data, errorMessage) = org.findByUserIds(["1000","1001"])'
              }
            }
          },
          http: {
            statics: {
              get: {
                args: [{
                  dataType: 'String',
                  name: 'url',
                  mandatory: true,
                  modules: []
                },
                {
                  dataType: 'Map',
                  name: 'headers',
                  mandatory: false,
                  modules: []
                }
                ],
                functionName: 'get',
                returnType: 'List',
                description: $t("HTTPGET请求"),
                example: 'def (error, data, errorMessage) = Fx.http.get("http://www.fxiaoke.com",["X-token":"myToken"])'
              },
              post: {
                args: [{
                  dataType: 'String',
                  name: 'url',
                  mandatory: true,
                  modules: []
                },
                {
                  dataType: 'Map',
                  name: 'headers',
                  mandatory: false,
                  modules: []
                },
                {
                  dataType: 'Map/String',
                  name: 'data',
                  mandatory: false,
                  modules: []
                }
                ],
                functionName: 'post',
                returnType: 'List',
                description: $t("HTTPPOST请求"),
                example: 'def (error, data, errorMessage) = Fx.http.post("http://www.fxiaoke.com",["X-token":"myToken"],["id":1])'
              }
            }
          },
          log: {
            statics: {
              info: {
                args: [{
                  dataType: 'Object',
                  name: 'obj',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'info',
                returnType: '',
                description: $t("运行日志，服务器最多记录20条"),
                example: 'Fx.log.info("Hello fxiaoke");'
              },
              debug: {
                args: [{
                  dataType: 'Object',
                  name: 'obj',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'debug',
                returnType: '',
                description: $t("调试日志只在调试窗口输出，不记录在运行日志中"),
                example: 'Fx.log.debug("Hello fxiaoke");'
              }
            }
          },
          crm: {
            statics: {
              product: {
                statics: {
                  shelf: {
                    args: [{
                      dataType: 'String',
                      name: '产品Id',
                      mandatory: true,
                      modules: []
                    }, {
                      dataType: 'Integer',
                      name: '上架/下架 int value',
                      mandatory: true,
                      modules: []
                    }],
                    functionName: 'shelf',
                    returnType: '',
                    description: '产品上架下架',
                    example: 'Fx.crm.product.shelf("e3f99215209c4a5ba9587c1cdb9692b3",1)\nFx.crm.product.shelf("e3f99215209c4a5ba9587c1cdb9692b3",2)\n 1 上架, 2 下架'
                  }
                }
              },
              leads: {
                statics: {
                  giveBack: {
                    args: [{
                      dataType: 'String',
                      name: '线索Id',
                      mandatory: true,
                      modules: []
                    }, {
                      dataType: 'String',
                      name: '线索池Id',
                      mandatory: true,
                      modules: []
                    }],
                    description: '线索退回',
                    functionName: 'giveBack',
                    returnType: '',
                    example: 'Fx.crm.leads.giveBack("99a198dbe6614b8e9c93cd6c46bcb8b0","84ce6f0c66254bb497c2832dd3317373")'
                  },
                  move: {
                    args: [{
                      dataType: 'String',
                      name: '线索Id',
                      mandatory: true,
                      modules: []
                    }, {
                      dataType: 'String',
                      name: '线索池Id',
                      mandatory: true,
                      modules: []
                    }],
                    functionName: 'move',
                    returnType: '',
                    description: '线索转移',
                    example: 'Fx.crm.leads.move("99a198dbe6614b8e9c93cd6c46bcb8b0","b2446a2a4d6c46758d0e029670bfc2f5")'
                  },
                }
              }
            }
          },
          work: {
            statics: {
              createTask: {
                args: [{
                  dataType: 'String',
                  name: 'title',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'String',
                  name: 'content',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'DateTime',
                  name: 'deadLine',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Map<List>',
                  name: 'executeUsers',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Map<List>',
                  name: 'atUsers',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'createTask',
                returnType: '',
                description: '发任务',
                example: 'DateTime deadLine = DateTime.now + 1.days\nFx.work.createTask("hello", "world", deadLine, [users: ["1000"]], [users: ["1000"]])\n\nexecuteUsers支持的key : "users" 用户列表 , "departments" 部门列表\natUsers支持的key : "users" 用户列表 , "departments" 部门列表'
              },
              createSchedule: {
                args: [{
                  dataType: 'String',
                  name: 'title',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'DateTime',
                  name: 'beginTime',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'DateTime',
                  name: 'endTime',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Boolean',
                  name: 'isFullDate',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Boolean',
                  name: 'needReceipt',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'List',
                  name: 'remindTimes',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Map<List>',
                  name: 'atUsers',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'createSchedule',
                returnType: '',
                description: '发日程',
                example: 'DateTime endTime = DateTime.now + 1.days\nFx.work.createSchedule("hello", DateTime.now(), endTime , false, false, [RemindTime.BEGIN()], [users: ["1000"]])\n\nRemindTime类型枚举值：\nRemindTime.BEGIN RemindTime.FIVE_MINUTES_AGO RemindTime.FIFTEEN_MINUTES_AGO RemindTime.THIRTY_MINUTES_AGO , RemindTime.ONE_HOURS_AGO RemindTime.TWO_HOURS_AGO\natUsers支持的key : "users" 用户列表 , "departments" 部门列表'
              },
              createSalesRecord: {
                args: [{
                  dataType: 'String',
                  name: 'content',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Map',
                  name: 'objects',
                  mandatory: true,
                  modules: []
                }, {
                  dataType: 'Map<List>',
                  name: 'atUsers',
                  mandatory: true,
                  modules: []
                }],
                functionName: 'createSalesRecord',
                returnType: '',
                description: '发销售记录',
                example: 'Fx.work.createSalesRecord("hello", [[object_api_name: "AccountObj", id: "c14e1e2bef8d494185ead3f843df6163"]], [users: ["1000"]])\n\nobjects支持的key:  "object_api_name" 对象APIName ,  "id" 对象Id\natUsers支持的key : "users" 用户列表 , "departments" 部门列表'
              }
            }
          },
          random: {
            statics: {
              nextInt: {
                args: [{
                  dataType: 'Integer',
                  name: 'bound',
                  modules: [], //参数默认值
                  mandatory: false
                }],
                functionName: 'nextInt',
                returnType: '',
                description: '生成随机数',
                example: 'Fx.random.nextInt(int bound)'
              }
            }
          },
          json: {
            statics: {
              toJson: {
                args: [{
                  dataType: 'Map',
                  name: 'map',
                  modules: [], //参数默认值
                  mandatory: true
                }],
                returnType: 'String',
                description: 'Map转json字符串',
                example: 'def json = Fx.json.toJson([a: "1"])'
              },
              parse: {
                args: [{
                  dataType: 'String',
                  name: 'jsonstr',
                  modules: [], //参数默认值
                  mandatory: true
                }],
                returnType: 'Map',
                description: '解析json字符串为Map对象',
                example: 'def map = Fx.json.parse("{a: 1}")'
              }
            }
          }
        }
      },
      context: {
        statics: {
          tenantId: {
            returnType: "String",
            description: $t("获取登陆账号的企业ID")
          },
          userId: {
            returnType: 'String',
            description: $t("获取登陆账号的用户ID")
          },
          details: {
            returnType: 'Map',
            description: $t('获取绑定对象的明细数据')
          },
          data: {
            returnType: 'Map',
            description: $t('获取绑定对象的全部数据包含明细数据')
          },
          // data: null
        }
      },
      Operator: {
        require: true, //标识这个key（Fx）可以省略   仅支持省略第一个
        // map: 'self',      //标识映射是completions 还是 self   仅当requrie为false时有效
        statics: {
          EQ: {
            args: [{
              dataType: 'Object',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `判断相等,${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          },
          NE: {
            args: [{
              dataType: 'Object',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `判断不相等,${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          },
          GT: {
            args: [{
              dataType: 'Object',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `判断大于,${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`

          },
          LT: {
            args: [{
              dataType: 'Object',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `${$t("判断小于")},${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          },
          GTE: {
            args: [{
              dataType: 'Object',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `${$t("判断大于等于")},${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          },
          LTE: {
            args: [{
              dataType: 'Object',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `${$t("判断小于等于")},${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          },
          LIKE: {
            args: [{
              dataType: 'String',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `${$t("判断是否包含")},${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          },
          NLIKE: {
            args: [{
              dataType: 'String',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description:  `${$t("判断不包含")},${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          },
          IN: {
            args: [{
              dataType: 'List',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `${$t("判断属于其中一个")},${$t("用于设置{{method}}方法中的条件语句"), {method: 'object.find'}}`
          },
          NIN: {
            args: [{
              dataType: 'List',
              name: 'list',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: `${$t("判断不属于其中")},${$t("用于设置{{method}}方法中的条件语句", {method: 'object.find'})}`
          }
        }
      },
      //以下类型（String、List、Map）只设置对应方法
      String: {
        props: {
          contains: {
            args: [{
              dataType: 'String',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: $t("如果字符串包含特定序列或字符返回true"),
            example: `"fx is great".contains("fx") // ${$t("返回")}: true`
          },
          startsWith: {
            args: [{
              dataType: 'String',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: $t("检测是否以特定字符串开头"),
            example: `"fx is great".startsWith("fx") // ${$t("返回")}: true`
          },
          endsWith: {
            args: [{
              dataType: 'String',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: $t("检测是否以特定字符串结尾"),
            example: `"fx is great".endsWith("fx") // ${$t("返回")}: true`
          },
          concat: {
            args: [{
              dataType: 'String',
              name: 'str',
              mandatory: true
            }],
            returnType: 'Boolean', //返回类型
            description: $t("将指定的字符串加在此字符串的末尾"),
            example: `"fx is great".concat("fx") // ${$t("返回")}: true`
          },
          isEmpty: {
            args: [],
            returnType: 'Boolean', //返回类型
            description: $t("判断是否为空"),
            example: `"fx".isEmpty(); // ${$t("返回")}: true`
          },
          trim: {
            args: [],
            returnType: 'String', //返回类型
            description: $t("返回字符串的副本忽略前导空白和尾部空白"),
            example: `"   Welcome to fxiaoke Creator  ".trim(); // ${$t("返回")}: "Welcome to fxiaoke Creator"`
          },
          replace: {
            args: [{
              dataType: 'String', //参数类型
              name: 'searchString', //参数名称
              mandatory: true //是否必填参数
            },
            {
              dataType: 'String', //参数类型
              name: 'replacement', //参数名称
              mandatory: true //是否必填参数
            }
            ],
            returnType: 'String', //返回类型
            description: $t("使用给定的参数replacement替换字符串所有匹配给定的searchString的子字符串"),
            example: `"Red ,Green ,Green".replace("Green","Blue"); // ${$t("返回")}: "Red ,Blue ,Blue"`
          },
          replaceAll: {
            args: [{
              dataType: 'String', //参数类型
              name: 'regex', //参数名称
              mandatory: true //是否必填参数
            },
            {
              dataType: 'String', //参数类型
              name: 'replacement', //参数名称
              mandatory: true //是否必填参数
            }
            ],
            returnType: 'String', //返回类型
            description: $t("使用给定的参数replacement替换字符串所有匹配给定的regex正则表达式的子字符串"),
            example: `"Red ,Green ,Green".replaceAll("Green","Blue"); // ${$t("返回")}: "Red ,Blue ,Blue"`
          },
          substring: {
            args: [{
              dataType: 'BigDecimal', //参数类型
              name: 'startIndex', //参数名称
              mandatory: true //是否必填参数
            },
            {
              dataType: 'BigDecimal', //参数类型
              name: 'endIndex', //参数名称
              mandatory: false //是否必填参数
            }
            ],
            returnType: 'String', //返回类型
            description: $t("返回一个新字符串它是此字符串的一个子字符串"),
            example: `"www.fxiaoke.com".substring(4, 11) // ${$t("返回")}: "fxiaoke"`
          },
          split: {
            args: [{
              dataType: 'String', //参数类型
              name: 'regex', //参数名称
              mandatory: true //是否必填参数
            },
            {
              dataType: 'BigDecimal', //参数类型
              name: 'limit', //参数名称
              mandatory: false //是否必填参数
            }
            ],
            returnType: 'List', //返回类型
            description: $t("根据匹配给定的正则表达式来拆分字符串"),
            example: `"Welcome-to-fxiaoke".split("-"); // ${$t("返回")}: {"Welcome", "to", "fxiaoke"}\nWelcome-to-fxiaoke".split("-", 2); // ${$t("返回")}: {"Welcome", "to-fxiaoke"}`
          },
          length: {
            args: [],
            returnType: 'BigDecimal', //返回类型
            description: $t("返回此字符串的长度"),
            example: `"www.fxiaoke.com".length(); // ${$t("返回")}: 15`
          },
          indexOf: {
            args: [{
              dataType: 'String', //参数类型
              name: 'subString', //参数名称
              mandatory: true //是否必填参数
            },
            {
              dataType: 'BigDecimal', //参数类型
              name: 'fromIndex', //参数名称
              mandatory: false //是否必填参数
            }
            ],
            returnType: 'BigDecimal', //返回类型
            description: $t("返回指定子字符串在此字符串中第一次出现处的索引从指定的索引开始默认从0开始"),
            example: `"www.fxiaoke.com".indexOf('o'); // ${$t("返回")}: 8`
          },
          lastIndexOf: {
            args: [{
              dataType: 'String', //参数类型
              name: 'subString', //参数名称
              mandatory: true //是否必填参数
            },
            {
              dataType: 'BigDecimal', //参数类型
              name: 'fromIndex', //参数名称
              mandatory: false //是否必填参数
            }
            ],
            returnType: 'BigDecimal', //返回类型
            description: $t("返回指定字符在此字符串中最后一次出现处的索引从指定的索引处开始进行反向搜索"),
            example: `"www.fxiaoke.com".lastIndexOf('o'); // ${$t("返回")}: 13`
          }
        }
      },
      List: {
        props: {
          add: {
            args: [{
              dataType: 'Object', //参数类型
              name: 'any_type', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("向列表添加元素"),
            example: `colors = {"red", "blue", "green"};\ncolors.add("yellow");`
          },
          addAll: {
            args: [{
              dataType: 'List', //参数类型
              name: 'list', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("向列表添加多个元素"),
            example: `colors = {"red", "blue", };\ncolors.add({"green", "yellow"});`
          },
          clear: {
            args: [],
            returnType: '', //返回类型
            description: $t("清空列表"),
            example: `colors = {"red", "blue", };\ncolors.clear();`
          },
          contains: {
            args: [{
              dataType: 'Object', //参数类型
              name: 'any_type', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("判断列表中是否存在指定元素如果列表中存在元素则返回true"),
            example: `colors = {"red", "blue", "green", "yellow"};\nresult = colors.contains("red"); // ${$t("返回")}: true`
          },
          containsAll: {
            args: [{
              dataType: 'List', //参数类型
              name: 'list', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("判断列表中是否存在指定元素如果列表中存在元素则返回true"),
            example: `colors = {"red", "blue", "green"};\nresult = colors.containsAll({"red", "yellow"}); // ${$t("返回")}: false`
          },
          indexOf: {
            args: [{
              dataType: 'Object', //参数类型
              name: 'any_type', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'BigDecimal', //返回类型
            description: $t("返回给定元素在列表中的位置第一个元素的位置为0"),
            example: `colors = {"red", "blue", "green", "yellow"};\nresult = colors.indexOf("green"); // ${$t("返回")}: 2`
          },
          get: {
            args: [{
              dataType: 'BigDecimal', //参数类型
              name: 'indexNumber', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Object', //返回类型
            description: $t("返回给定元素在列表中的位置第一个元素的位置为0"),
            example: `colors = {"red", "blue", "green", "yellow"};\nresult = colors.get(1); // ${$t("返回")}: "blue"`
          },
          size: {
            args: [],
            returnType: 'BigDecimal', //返回类型
            description: $t("返回列表中元素的数目"),
            example: `colors = {"red", "blue", "green", "yellow"};\nresult = colors.size(); // ${$t("返回")}: 4`
          },
          // distinct: {
          //   args: [],
          //   returnType: 'List',                     //返回类型
          //   description: '返回列表去重后的副本',
          //   example: `colors = {"red", "green", "green"};\nresult = colors.distinct(); // ${$t("返回")}: {"red", "green"}`
          // },
          sort: {
            args: [{
              dataType: 'Boolean', //参数类型
              name: 'bool', //参数名称
              mandatory: false, //是否必填参数
            }],
            returnType: 'List', //返回类型
            description: $t("列表排序可选布尔值指定升序true降序false"),
            example: `colors = {"red", "blue", "green", "yellow"};\ncolors.sort(); // ${$t("返回")}: {"blue","green","red","yellow"}`
          },
          subList: {
            args: [{
              dataType: 'BigDecimal', //参数类型
              name: 'startIndex', //参数名称
              mandatory: true, //是否必填参数
            },
            {
              dataType: 'BigDecimal', //参数类型
              name: 'endIndex', //参数名称
              mandatory: false, //是否必填参数
            }
            ],
            returnType: 'List', //返回类型
            description: $t("根据指定的开始和结束索引包含指定的索引从给定列表中返回一组元素"),
            example: `colors = {"red", "blue", "green", "yellow"};\nresult = colors.sublist(1, 3); // ${$t("返回")}: {"blue","green","yellow"}`
          },
          remove: {
            args: [{
              dataType: 'BigDecimal', //参数类型
              name: 'index', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Object', //返回类型
            description: $t("移除并返回指定索引处的元素第一个元素的索引为{{num}}", {
              num: 0
            }),
            example: `colors = {"red", "blue", "green", "yellow"};\ncolors.remove(2); // ${$t("返回")}: {"red", "blue", "green"}`
          },
          isEmpty: {
            args: [],
            returnType: 'Boolean', //返回类型
            description: $t("判断列表是否为空返回一个布尔值-如果列表中不包含任何值则为true否则为-如果列表中包含值则为false"),
            example: `colors = {"red", "blue", "green", "yellow"};\nresult = colors.isEmpty(); // ${$t("返回")}: false`
          },

          intersect: {
            args: [{
              dataType: 'List', //参数类型
              name: 'list', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("返回指定列表与当前列表的交集"),
            example: `colors = {"red", "blue", "green", "orange"};\nfruits = {"apple","orange","banana"};\nresult = colors.intersect(fruits); // ${$t("返回")}: {"orange"}`
          },
          lastIndexOf: {
            args: [{
              dataType: 'Object', //参数类型
              name: 'any_type', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("返回指定元素在列表中的最后一个匹配项的位置"),
            example: `colors = {"red", "blue", "green", "yellow", "green"};\nresult = colors.lastIndexOf("green"); // ${$t("返回")}: 4`
          },
          eachWithIndex: {
            args: [{
              dataType: 'Closure', // 闭包
              name: 'closure',
              mandatory: true
            }],
            returnType: 'List',
            description: $t("遍历列表中的数据闭包中传入item和index"),
            example: `List list = ["a", "c"]\nlist.eachWithIndex { item, int i -> \n\n}`
          },
          each: {
            definition: ` { item -> \n\n}`,
            returnType: 'List',
            description: $t("遍历列表中的数据闭包中传入item"),
            example: `List list = ["a", "c"]\nlist.each { item -> \n\n}`
          }

        }
      },
      Map: {
        props: {
          keys: {
            args: [],
            returnType: 'List', //返回类型
            description: $t("获取字典所有的属性名称"),
            example: `map = { "a": 1, "b": 2};\nresult = map.keys(); // ${$t("返回")}: {"a", "b"}`
          },
          size: {
            args: [],
            returnType: 'BigDecimal', //返回类型
            description: $t("返回字典中元素的数目"),
            example: `map = { "a": 1, "b": 2};\nresult = map.size(); // ${$t("返回")}: 2`
          },
          isEmpty: {
            args: [],
            returnType: 'Boolean', //返回类型
            description: $t("判断字典是否为空如果不包含键值映射则返回布尔值true如果包含键值映射则为false"),
            example: `map = { "a": 1, "b": 2};\nresult = map.isEmpty(); // ${$t("返回")}: false`
          },
          remove: {
            args: [{
              dataType: 'String', //参数类型
              name: 'key', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Object', //返回类型
            description: $t("移除并返回指定键的元素"),
            example: `map = { "a": 1, "b": 2};\nmap.remove("a"); // ${$t("返回")}: 1`
          },
          clear: {
            args: [],
            returnType: 'Boolean', //返回类型
            description: $t("从字典中移除所有键值对"),
            example: `map = { "a": 1, "b": 2};\nresult = map.clear(); // ${$t("返回")}: {}`
          },
          put: {
            args: [{
              dataType: 'String', //参数类型
              name: 'key', //参数名称
              mandatory: true, //是否必填参数
            }, {
              dataType: 'Object', //参数类型
              name: 'value', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Object', //返回类型
            description: $t("存放键值对"),
            example: `map = { "a": 1, "b": 2};\nmap.put('c', 3);`
          },
          putIfAbsent: {
            args: [{
              dataType: 'String', //参数类型
              name: 'key', //参数名称
              mandatory: true, //是否必填参数
            }, {
              dataType: 'Object', //参数类型
              name: 'value', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Object', //返回类型
            description: $t("存放键值对如果key存在的情况下在putIfAbsent下不会修改"),
            example: `map = { "a": 1, "b": 2};\nmap.putIfAbsent('a', 2); //此时键“a”的值还是1`
          },
          containsKey: {
            args: [{
              dataType: 'String', //参数类型
              name: 'key', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("是否包含key"),
            example: `map = { "a": 1, "b": 2};\nmap.containsKey("a"); // ${$t("返回")}: true`
          },
          containsValue: {
            args: [{
              dataType: 'Object', //参数类型
              name: 'value', //参数名称
              mandatory: true, //是否必填参数
            }],
            returnType: 'Boolean', //返回类型
            description: $t("是否包含value"),
            example: `map = { "a": 1, "b": 2};\nmap.containsValue(2); // ${$t("返回")}: true`
          },
          values: {
            args: [],
            returnType: 'Boolean', //返回类型
            description: $t("返回所有值的集合"),
            example: `map = { "a": 1, "b": 2};\nmap.values(); // ${$t("返回")}: {1, 2}`
          },
          each: {
            args: [{
              dataType: 'Closure',
              name: 'closure',
              mandatory: true
            }],
            returnType: 'Map',
            description: $t("遍历字典中的数据闭包中传入key和value"),
            example: `Map map = ["a": 1, "b": 2]\nmap.each {String key,value -> \n\n}`
          }
        }
      },
      BigDecimal: {
        props: {
          years: null,
          months: null,
          days: null,
          hours: null,
          minutes: null
        },
        statics: {
          of: {
            args: [{
              dataType: 'String',
              name: 'value',
              mandatory: true
            }],
            functionName: 'of',
            returnType: 'BigDecimal',
            example: 'BigDecimal a = BigDecimal.of("1.25")'
          }
        }
      },
      Date: {
        statics: {
          now: {
            args: [],
            returnType: 'Date',
            description: $t("当前日期"),
          }
        },
        props: {
          withYear: {
            args: [{
              dataType: 'Integer',
              name: 'year',
              mandatory: true
            }],
            returnType: 'Date',
            description: $t("设置日期的年返回新的日期"),
          },
          withMonth: {
            args: [{
              dataType: 'Integer',
              name: 'month',
              mandatory: true
            }],
            returnType: 'Date',
            description: $t("设置日期的月返回新的日期")
          },
          withDay: {
            args: [{
              dataType: 'Integer',
              name: 'day',
              mandatory: true
            }],
            returnType: 'Date',
            description: $t("设置日期的日返回新的日期")
          },
          toTimestamp: {
            args: [],
            returnType: 'BigDecimal',
            description: $t('日期转时间戳')
          }
        }
      },
      DateTime: {
        statics: {
          now: {
            args: [],
            returnType: 'DateTime',
            description: $t("当前日期时间"),
          }
        },
        props: {
          withYear: {
            args: [{
              dataType: 'Integer',
              name: 'year',
              mandatory: true
            }],
            returnType: 'DateTime',
            description: $t("设置日期时间的年返回新的日期时间")
          },
          withMonth: {
            args: [{
              dataType: 'Integer',
              name: 'month',
              mandatory: true
            }],
            returnType: 'DateTime',
            description: $t("设置日期时间的月返回新的日期时间")
          },
          withDay: {
            args: [{
              dataType: 'Integer',
              name: 'day',
              mandatory: true
            }],
            returnType: 'DateTime',
            description: $t("设置日期时间的日返回新的日期时间")
          },
          withHour: {
            args: [{
              dataType: 'Integer',
              name: 'hour',
              mandatory: true
            }],
            returnType: 'DateTime',
            description: $t("设置日期时间的小时返回新的日期时间")
          },
          withMinute: {
            args: [{
              dataType: 'Integer',
              name: 'minute',
              mandatory: true
            }],
            returnType: 'DateTime',
            description: $t("设置日期时间的分钟返回新的日期时间")
          },
          toTimestamp: {
            args: [],
            returnType: 'BigDecimal',
            description: $t('日期时间转时间戳')
          }
        },
      },
      Time: {
        statics: {
          now: {
            args: [],
            returnType: 'Time',
            description: $t("当前时间"),
          }
        },
        props: {
          withHour: {
            args: [{
              dataType: 'Integer',
              name: 'hour',
              mandatory: true
            }],
            returnType: 'Time',
            description: $t("设置时间的小时返回新的时间")
          },
          withMinute: {
            args: [{
              dataType: 'Integer',
              name: 'minute',
              mandatory: true
            }],
            returnType: 'Time',
            description: $t("设置时间的分钟返回新的时间")
          },
          toTimestamp: {
            args: [],
            returnType: 'BigDecimal',
            description: '时间转时间戳'
          }
        },
      },
      HttpResult: {
        props: {
          statusCode: {
            args: null,
            returnType: 'Integer',
            description: $t("HTTP响应状态码"),
          },
          content: {
            args: null,
            returnType: 'String',
            description: $t("HTTP响应的Payload内容")
          }
        }
      },
      QueryResult: {
        props: {
          size: {
            args: null,
            returnType: 'Integer',
            description: $t("查询的数据结果的条数"),
          },
          total: {
            args: null,
            returnType: 'Integer',
            description: $t("总数据条数"),
          },
          dataList: {
            args: null,
            returnType: 'List',
            description: $t("查询返回的数据列表")
          }
        }
      },
      Range: {
        props: {
          each: {
            args: [],
            definition: ` { i -> \n\t\${1}\n}`,
            description: $t('遍历Range中的数据闭包中传入下标'),
            example: `Range range\nrange.each { i -> \n\n}`,
            returnType: ''
          }
        }
      },
      Ranges: {
        statics: {
          of: {
            args: [{
              dataType: 'Integer',
              name: 'from',
              mandatory: true
            }, {
              dataType: 'Integer',
              name: 'to',
              mandatory: true
            }],
            description: $t('创建一个{{name}}对象', { name: 'Range' }),
            example: 'Range r = Ranges.of(1, 2)'
          }
        }
      },
      UIEvent: {
        statics: {
          of: {
            args: [{
              dataType: 'Context',
              name: 'context',
              mandatory: true
            }],
            returnType: 'UIEvent',
            description: $t('创建一个{{name}}对象', { name: 'UIEvent' }),
            example: 'UIEvent event = UIEvent.of(context)'
          }
        },
        props: {
          addData: {
            args: [
              {
                dataType: 'String',
                name: 'fieldApiName',
                mandatory: true
              },
              {
                dataType: 'Map',
                name: 'value',
                mandatory: true
              }
            ],
            returnType: '',
            description: $t("添加主对象数据")
          },
          removeData: {
            args: [
              {
                dataType: 'String',
                name: 'fieldApiName',
                mandatory: true
              }
            ],
            returnType: '',
            description: $t("删除主对象数据")
          },
          updateData: {
            args: [
              {
                dataType: 'String',
                name: 'fieldApiName',
                mandatory: true
              },
              {
                dataType: 'Object',
                name: 'value',
                mandatory: true
              }
            ],
            returnType: '',
            description: $t("修改主对象数据")
          },
          getDetailList: {
            args: [
              {
                dataType: 'String',
                name: 'detailAPIName',
                mandatory: true
              }
            ],
            returnType: 'List',
            description: $t("根据APIName获取从对象数")
          },
          addDetail: {
            args: [
              {
                dataType: 'String',
                name: 'detailApiName',
                mandatory: true
              },
              {
                dataType: 'Map',
                name: 'value',
                mandatory: true
              }
            ],
            returnType: '',
            description: $t("添加从对象数据")
          },
          getCurrentDetail: {
            args: [],
            returnType: 'Map',
            description: $t("获取当前编辑的从对象的数据")
          }
        },
      },
      Remind: {
        statics: {
          Text: {
            args: [{
              dataType: 'String',
              name: 'content',
              mandatory: true
            }],
            returnType: 'Remind',
            description: $t('红字提醒'),
            example: `Remind remind = Remind.Text("${$t('红字提醒')}")`
          },
          // Toast: {
          //   args: [{
          //     dataType: 'String',
          //     name: 'content',
          //     mandatory: true
          //   }],
          //   returnType: 'Remind',
          //   description: $t('Toast提醒'),
          //   example: `Remind remind = Remind.Toast("${$t('Toast提醒')}")`
          // },
          Alert: {
            args: [{
              dataType: 'String',
              name: 'content',
              mandatory: true
            }],
            returnType: 'Remind',
            description: $t('弹窗提醒'),
            example: `Remind remind = Remind.Alert("${$t('弹窗提醒')}")`
          }
        }
      }
    }
  }
});