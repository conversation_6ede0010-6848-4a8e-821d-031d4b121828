{"relatedObject": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134912, "description": "所属对象,包含对象类型和对象数据", "is_unique": true, "group_type": "what", "label": "所属对象", "type": "group", "is_required": false, "api_name": "relatedObject", "define_type": "package", "_id": "60bee8148d2591000176401c", "fields": {"id_field": "objectDataId", "api_name_field": "objectApiName"}, "is_index_field": false, "is_single": false, "index_name": "s_3", "help_text": "", "status": "new"}, "objectDataId": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1546430134917, "pattern": "", "is_unique": false, "label": "所属业务记录", "type": "text", "is_required": false, "api_name": "objectDataId", "define_type": "package", "_id": "60bee8148d25910001764016", "is_index_field": false, "is_single": false, "index_name": "t_9", "max_length": 256, "status": "new"}, "objectApiName": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "pattern": "", "is_unique": false, "label": "所属业务对象", "type": "text", "is_required": false, "api_name": "objectApiName", "define_type": "package", "_id": "60bee8148d25910001764020", "is_index_field": false, "is_single": false, "index_name": "t_6", "max_length": 256, "status": "new"}}