{"related_object": {"describe_api_name": "object_zl_whatlist__c", "is_index": false, "is_active": true, "create_time": 1627976765277, "is_encrypted": false, "auto_adapt_places": false, "description": "动态关联", "is_unique": false, "group_type": "what_list", "label": "动态关联", "type": "group", "relation_table": "biz_behavior_record_relation", "is_required": false, "api_name": "related_object", "define_type": "package", "_id": "616572dd983987000180e4f6", "is_single": false, "is_index_field": false, "fields": {"id_field": "related_object_data", "api_name_field": "related_api_names"}, "index_name": "s_2", "help_text": "", "status": "released"}, "related_api_names": {"describe_api_name": "object_zl_whatlist__c", "auto_adapt_places": false, "description": "关联业务对象", "is_unique": false, "type": "select_many", "is_required": false, "options": [{"value": "AccountAddrObj", "label": "客户地址"}, {"value": "AccountObj", "label": "客户"}, {"value": "ContactObj", "label": "联系人"}, {"value": "LeadsObj", "label": "销售线索"}, {"value": "MarketingEventObj", "label": "市场活动1"}, {"value": "NewOpportunityLinesObj", "label": "商机2.0明细"}, {"value": "NewOpportunityObj", "label": "商机2.0"}, {"value": "object_4s712__c", "label": "xkk-detail1"}, {"value": "object_AP2Z9__c", "label": "750乡镇映射"}, {"value": "object_fcB1t__c", "label": "sk2测试"}, {"value": "object_fs58r__c", "label": "zz测试从"}, {"value": "object_Ju6l7__c", "label": "wj-测试主属性自定义"}, {"value": "object_ZkmdG__c", "label": "wj-从人员部门"}, {"value": "SalesOrderProductObj", "label": "订单产品"}, {"value": "SpecificationObj", "label": "规格"}], "define_type": "package", "is_single": false, "index_name": "a_2", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "default_value": [], "label": "关联业务对象", "field_num": 10, "api_name": "related_api_names", "is_dynamic": true, "_id": "616572dd983987000180e4f4", "is_index_field": false, "config": {}, "help_text": "", "status": "released"}, "related_object_data": {"describe_api_name": "object_zl_whatlist__c", "is_index": true, "is_active": true, "create_time": 1627976765265, "is_encrypted": false, "auto_adapt_places": false, "description": "关联业务数据", "is_unique": false, "label": "关联业务数据", "type": "what_list_data", "is_abstract": true, "field_num": 11, "is_required": false, "api_name": "related_object_data", "define_type": "package", "_id": "616572dd983987000180e4f5", "is_single": false, "is_index_field": false, "index_name": "s_8", "max_length": 10000, "help_text": "", "status": "released"}}