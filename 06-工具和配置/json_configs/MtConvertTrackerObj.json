{"fields": {"event_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "event_id", "is_active": true, "api_name": "event_id", "description": "event_id", "status": "released"}, "source_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "source_api_name", "is_active": true, "api_name": "source_api_name", "description": "source_api_name", "status": "released"}, "target_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "target_api_name", "is_active": true, "api_name": "target_api_name", "description": "target_api_name", "status": "released"}, "source_master_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 64, "pattern": "", "label": "source_master_api_name", "is_active": true, "api_name": "source_master_api_name", "description": "source_master_api_name", "status": "released"}, "target_master_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 64, "pattern": "", "label": "target_master_api_name", "is_active": true, "api_name": "target_master_api_name", "description": "target_master_api_name", "status": "released"}, "source_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "source_id", "is_active": true, "api_name": "source_id", "description": "source_id", "status": "released"}, "source_master_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 64, "pattern": "", "label": "source_master_id", "is_active": true, "api_name": "source_master_id", "description": "source_master_id", "status": "released"}, "target_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "target_id", "is_active": true, "api_name": "target_id", "description": "target_id", "status": "released"}, "target_master_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 64, "pattern": "", "label": "target_master_id", "is_active": true, "api_name": "target_master_id", "description": "target_master_id", "status": "released"}, "rule_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "rule_api_name", "is_active": true, "api_name": "rule_api_name", "description": "rule_api_name", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 64, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name"}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "index_name": "crt_time"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "index_name": "is_del"}}, "tenant_id": "-100", "is_udef": null, "api_name": "MtConvertTrackerObj", "display_name": "转换跟踪表", "package": "CRM", "record_type": null, "is_active": true, "release_version": "6.4", "plural_name": null, "define_type": "internal", "is_deleted": false, "store_table_name": "mt_convert_tracker", "module": null, "icon_index": null, "description": null, "visible_scope": null, "is_auto_increment": null}