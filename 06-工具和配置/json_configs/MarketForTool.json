{"tenant_id": "74255", "store_table_name": "biz_marketing_event", "package": "CRM", "is_active": true, "last_modified_time": 1663831190749, "create_time": 1557917706913, "description": "", "last_modified_by": "1000", "display_name": "「工具新建」市场活动", "version": 164, "is_open_display_name": false, "index_version": 200, "icon_index": 19, "is_deleted": false, "api_name": "MarketingEventToolCreateObj", "icon_path": "A_201907_02_fc43b9aace584a63bd6a05548166a014.png", "is_udef": true, "define_type": "package", "display_name_r": "市场活动", "fields": {"lock_rule": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1581584691612, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_4", "help_text": "", "status": "new"}, "field_21llU__c": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "a_1", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "查找关联(多选)1", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_m5hHl__c", "field_num": 3, "target_related_list_label": "市场活动", "lookup_roles": [], "action_on_target_delete": "set_null", "api_name": "field_21llU__c", "is_index_field": false, "help_text": "", "status": "new"}, "base_expected_income": {"expression_type": "js", "return_type": "currency", "describe_api_name": "MarketingEventToolCreateObj", "auto_adapt_places": false, "description": "预计收入(本位币)", "is_unique": false, "type": "formula", "decimal_places": 4, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "预计收入(元)(本位币)", "index_name": "d_1", "is_index": true, "is_active": true, "expression": "$expected_income$*$mc_exchange_rate$", "create_time": *************, "is_encrypted": false, "length": 20, "label": "预计收入(元)(本位币)", "is_need_convert": false, "api_name": "base_expected_income", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "execution_desc": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "执行描述", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "label_r": "执行描述", "index_name": "t_2", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322407, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "执行描述", "is_need_convert": false, "api_name": "execution_desc", "is_index_field": false, "help_text": "", "status": "released"}, "mc_exchange_rate": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_6", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1609299178352, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "汇率", "api_name": "mc_exchange_rate", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "extend_obj_data_id", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "system", "input_mode": "", "is_single": false, "is_extend": false, "label_r": "extend_obj_data_id", "index_name": "t_7", "max_length": 100, "is_index": false, "is_active": true, "create_time": 1558352322407, "is_encrypted": false, "default_value": "", "label": "extend_obj_data_id", "api_name": "extend_obj_data_id", "is_index_field": false, "help_text": "", "status": "released"}, "event_type": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1558352322414, "is_encrypted": false, "auto_adapt_places": false, "description": "活动类型", "is_unique": false, "default_value": "", "label": "活动类型", "type": "select_one", "is_need_convert": false, "is_required": true, "api_name": "event_type", "options": [{"font_color": "#2a304d", "label": "展会活动", "value": "1"}, {"font_color": "#2a304d", "label": "品牌活动", "value": "2"}, {"font_color": "#2a304d", "label": "会议营销", "value": "3"}, {"font_color": "#2a304d", "label": "搜索引擎", "value": "4"}, {"font_color": "#2a304d", "label": "互联网广告", "value": "5"}, {"font_color": "#2a304d", "label": "平面媒体广告", "value": "6"}, {"font_color": "#2a304d", "label": "电视媒体广告", "value": "7"}, {"font_color": "#2a304d", "label": "关系公关", "value": "8"}, {"font_color": "#2a304d", "label": "电话营销", "value": "9"}, {"font_color": "#2a304d", "label": "短信营销", "value": "10"}, {"font_color": "#2a304d", "label": "邮件营销", "value": "11"}, {"font_color": "#2a304d", "label": "1", "value": "eW6Ti6m6i"}, {"font_color": "#2a304d", "label": "2", "value": "7G6N79zjz"}, {"font_color": "#2a304d", "label": "3", "value": "g52814r6s"}, {"font_color": "#2a304d", "label": "4", "value": "jZlE74kuQ"}, {"font_color": "#2a304d", "is_required": false, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "label_r": "活动类型", "is_index_field": false, "index_name": "s_5", "config": {}, "help_text": "", "status": "released"}, "expected_cost": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "预计成本(元)", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "预计成本(元)", "index_name": "d_5", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1558352322414, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "预计成本(元)", "is_need_convert": false, "api_name": "expected_cost", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "label_r": "作废前生命状态", "index_name": "t_8", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1581584691612, "is_encrypted": false, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "is_index_field": false, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "负责人主属部门", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322414, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "help_text": "", "status": "released"}, "marketing_plan": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "计划", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "label_r": "计划", "index_name": "t_3", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322414, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "计划", "is_need_convert": false, "api_name": "marketing_plan", "is_index_field": false, "help_text": "", "status": "released"}, "biz_status": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1559187029834, "is_encrypted": false, "auto_adapt_places": false, "description": "状态", "is_unique": false, "default_value": "un_billed", "label": "状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "biz_status", "options": [{"font_color": "#2a304d", "label": "已计划", "value": "scheduled"}, {"font_color": "#2a304d", "label": "进行中", "value": "ongoing"}, {"font_color": "#2a304d", "label": "已结束", "value": "complete"}, {"font_color": "#2a304d", "label": "终止", "value": "finish"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "label_r": "状态", "is_index_field": false, "index_name": "s_6", "config": {}, "help_text": "", "status": "released"}, "lock_status": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1558352322428, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_required": false, "api_name": "lock_status", "options": [{"font_color": "#2a304d", "label": "未锁定", "value": "0"}, {"font_color": "#2a304d", "label": "锁定", "value": "1"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "is_index_field": false, "index_name": "s_7", "config": {}, "help_text": "", "status": "new"}, "package": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1557917706913, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "belong_to_supplier": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "scan", "is_single": false, "is_extend": false, "index_name": "s_13", "is_index": true, "is_active": true, "create_time": 1662458504321, "is_encrypted": false, "label": "所属供应商", "target_api_name": "SupplierObj", "target_related_list_name": "supplier_marketingevent_list", "target_related_list_label": "市场活动1", "action_on_target_delete": "set_null", "is_need_convert": false, "related_wheres": [], "api_name": "belong_to_supplier", "is_index_field": true, "help_text": "", "status": "released"}, "create_time": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "create_time": 1557917706913, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "actual_income": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "实际收入(元)", "is_unique": false, "type": "number", "decimal_places": 4, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "实际收入", "index_name": "d_8", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1558352322434, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "实际收入", "is_need_convert": false, "api_name": "actual_income", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "version": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "create_time": 1557917706913, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1557917706913, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released", "description": ""}, "relevant_team": {"describe_api_name": "MarketingEventToolCreateObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1581584691872, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "data_own_department": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1557917706913, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_department", "define_type": "package", "is_single": true, "label_r": "归属部门", "is_index_field": false, "index_name": "data_owner_dept_id", "help_text": "", "status": "released"}, "actual_cost": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "实际成本(元)", "is_unique": false, "type": "number", "decimal_places": 4, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "实际成本", "index_name": "d_9", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1558352322444, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "实际成本", "is_need_convert": false, "api_name": "actual_cost", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "effect": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "效果", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "label_r": "效果", "index_name": "t_5", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322444, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "效果", "is_need_convert": false, "api_name": "effect", "is_index_field": false, "help_text": "", "status": "released"}, "name": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "市场活动名称", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "市场活动名称", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322444, "is_encrypted": false, "default_value": "", "label": "市场活动名称", "is_need_convert": false, "api_name": "name", "is_index_field": false, "help_text": "主属性怎么提示信息", "status": "released"}, "_id": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1557917706913, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}, "field_rA1ck__c": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": true, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": true, "index_name": "t_10", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1629186634512, "is_encrypted": false, "default_value": "$event_type$", "label": "单行文本", "field_num": 7, "api_name": "field_rA1ck__c", "is_index_field": false, "help_text": "", "status": "new"}, "status": {"describe_api_name": "MarketingEventToolCreateObj", "is_use_value": true, "auto_adapt_places": false, "description": "状态(原)", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"font_color": "#2a304d", "label": "已计划", "value": "1"}, {"font_color": "#2a304d", "label": "进行中", "value": "2"}, {"font_color": "#2a304d", "label": "已结束", "value": "3"}, {"font_color": "#2a304d", "label": "中止", "value": "4"}, {"font_color": "#2a304d", "label": "未生效", "value": "5"}, {"font_color": "#2a304d", "label": "审核中", "value": "6"}, {"font_color": "#2a304d", "label": "已作废", "value": "99"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "label_r": "状态（原）", "index_name": "s_9", "is_index": true, "is_active": true, "expression": "CASE($life_status$, 'invalid', '99', 'ineffective','5','under_review','6',CASE($biz_status$,'scheduled','1','ongoing','2','complete','3','finish','4','1'))", "create_time": 1558352322451, "is_encrypted": false, "default_value": "", "label": "状态（原）", "is_need_convert": false, "is_need_calculate": true, "api_name": "status", "is_index_field": false, "config": {}, "help_text": "", "status": "released"}, "tenant_id": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1557917706913, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "data_own_organization": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1647329827458, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属组织", "type": "department", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "data_own_organization", "define_type": "package", "is_single": true, "label_r": "归属组织", "is_index_field": false, "index_name": "a_8", "help_text": "", "status": "released"}, "description": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "描述", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "label_r": "描述", "index_name": "t_1", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322407, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "描述", "is_need_convert": false, "api_name": "description", "is_index_field": false, "help_text": "", "status": "released"}, "lock_user": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1581584691612, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "where_type": "field", "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_5", "help_text": "", "status": "new"}, "marketing_event_path": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1615967687682, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "市场活动层级", "is_unique": false, "label": "市场活动层级", "type": "tree_path", "is_need_convert": false, "is_required": false, "api_name": "marketing_event_path", "define_type": "package", "is_single": false, "label_r": "市场活动层级", "is_index_field": false, "index_name": "s_12", "help_text": "", "status": "released"}, "is_deleted": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "create_time": 1557917706913, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "base_actual_cost": {"expression_type": "js", "return_type": "currency", "describe_api_name": "MarketingEventToolCreateObj", "auto_adapt_places": false, "description": "实际成本(本位币)", "is_unique": false, "type": "formula", "decimal_places": 4, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "实际成本(本位币)", "index_name": "d_2", "is_index": true, "is_active": true, "expression": "$actual_cost$*$mc_exchange_rate$", "create_time": 1607081475528, "is_encrypted": false, "length": 20, "label": "实际成本(本位币)", "is_need_convert": false, "api_name": "base_actual_cost", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "object_describe_api_name": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1557917706913, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "out_owner": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1557917706913, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released", "description": ""}, "field_w2liu__c": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "category", "field_values": ["1"]}]}, {"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "owner", "field_values": ["1021"]}]}], "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": true, "index_name": "s_3", "is_index": true, "is_active": true, "create_time": 1564371996247, "is_encrypted": false, "default_value": "", "label": "查找关联", "target_api_name": "SPUObj", "target_related_list_name": "target_related_list_USo1z__c", "field_num": 1, "target_related_list_label": "市场活动1", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_w2liu__c", "is_index_field": true, "help_text": "", "status": "new"}, "field_9qv6n__c": {"describe_api_name": "MarketingEventToolCreateObj", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "type": "image", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "a_7", "support_file_types": ["jpg", "gif", "jpeg", "png"], "is_index": true, "file_amount_limit": 1, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "create_time": 1642489748112, "is_encrypted": false, "label": "图片", "is_watermark": false, "field_num": 2, "file_size_limit": 20971520, "is_ocr_recognition": false, "api_name": "field_9qv6n__c", "is_need_cdn": false, "is_index_field": false, "identify_type": "", "help_text": "单个图片不得超过20M", "status": "new"}, "mc_functional_currency": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1609299178369, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "本位币", "type": "select_one", "is_required": false, "api_name": "mc_functional_currency", "options": [{"label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "本位币", "is_index_field": false, "index_name": "s_10", "config": {}, "help_text": "", "status": "new"}, "field_62oeS__c": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "d_12", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1642490707304, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额", "currency_unit": "￥", "field_num": 10, "api_name": "field_62oeS__c", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_v2Egf__c": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1632382265588, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": [], "label": "多选", "type": "select_many", "field_num": 8, "is_required": false, "api_name": "field_v2Egf__c", "options": [{"font_color": "#2a304d", "label": "示例选项", "value": "option1"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "a_6", "config": {}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "MarketingEventToolCreateObj", "auto_adapt_places": false, "description": "负责人", "is_unique": false, "where_type": "field", "type": "employee", "decimal_places": 0, "is_required": true, "wheres": [], "define_type": "package", "is_single": true, "label_r": "负责人", "index_name": "owner", "is_index": true, "is_active": true, "create_time": 1558352322414, "is_encrypted": false, "length": 20, "label": "负责人", "is_need_convert": false, "api_name": "owner", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "summary": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "总结", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "label_r": "总结", "index_name": "t_4", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322414, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "总结", "is_need_convert": false, "api_name": "summary", "is_index_field": false, "help_text": "", "status": "released"}, "field_1oF9b__c": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "a_2", "is_index": true, "is_active": true, "create_time": 1629171306990, "is_encrypted": false, "label": "查找关联(多选)2", "target_api_name": "ContactObj", "target_related_list_name": "target_related_list_31nZF__c", "field_num": 4, "target_related_list_label": "市场活动", "action_on_target_delete": "set_null", "api_name": "field_1oF9b__c", "is_index_field": false, "help_text": "", "status": "new"}, "last_modified_time": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "create_time": 1557917706913, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "field_xp2q5__c": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": true, "index_name": "s_2", "is_index": true, "is_active": true, "create_time": 1632475442175, "is_encrypted": false, "label": "查找关联111", "target_api_name": "PartnerObj", "target_related_list_name": "target_related_list_28WlA__c", "field_num": 9, "target_related_list_label": "市场活动", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_xp2q5__c", "is_index_field": true, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1559187029881, "is_encrypted": false, "auto_adapt_places": false, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"font_color": "#2a304d", "label": "未生效", "value": "ineffective"}, {"font_color": "#2a304d", "label": "审核中", "value": "under_review"}, {"font_color": "#2a304d", "label": "正常", "value": "normal"}, {"font_color": "#2a304d", "label": "变更中", "value": "in_change"}, {"font_color": "#2a304d", "label": "作废", "value": "invalid"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "is_index_field": false, "index_name": "s_8", "config": {}, "help_text": "", "status": "released"}, "base_actual_income": {"expression_type": "js", "return_type": "currency", "describe_api_name": "MarketingEventToolCreateObj", "auto_adapt_places": false, "description": "实际收入(本位币)", "is_unique": false, "type": "formula", "decimal_places": 4, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "实际收入(本位币)", "index_name": "d_3", "is_index": true, "is_active": true, "expression": "$actual_income$*$mc_exchange_rate$", "create_time": 1607081475732, "is_encrypted": false, "length": 20, "label": "实际收入(本位币)", "is_need_convert": false, "api_name": "base_actual_income", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "expected_income": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "预计收入(元)", "is_unique": false, "type": "number", "decimal_places": 4, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "预计收入(元)", "index_name": "d_7", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1558352322434, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "预计收入(元)", "is_need_convert": false, "api_name": "expected_income", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "end_time": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "结束日期", "is_unique": false, "type": "date", "default_to_zero": false, "is_required": true, "define_type": "package", "is_single": false, "label_r": "结束日期", "index_name": "l_1", "is_index": true, "is_active": true, "create_time": 1558352322434, "is_encrypted": false, "default_value": "", "label": "结束日期", "time_zone": "GMT+8", "is_need_convert": false, "api_name": "end_time", "date_format": "yyyy-MM-dd", "is_index_field": false, "help_text": "", "status": "released"}, "begin_time": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "开始日期", "is_unique": false, "type": "date", "default_to_zero": false, "is_required": true, "define_type": "package", "is_single": false, "label_r": "开始日期", "index_name": "l_2", "is_index": true, "is_active": true, "create_time": 1558352322434, "is_encrypted": false, "default_value": "", "label": "开始日期", "time_zone": "GMT+8", "is_need_convert": false, "api_name": "begin_time", "date_format": "yyyy-MM-dd", "is_index_field": false, "help_text": "", "status": "released"}, "last_modified_by": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1557917706913, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released", "description": ""}, "out_tenant_id": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": false, "is_active": true, "create_time": 1557917706913, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1609299178352, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "币种", "type": "select_one", "is_required": false, "api_name": "mc_currency", "options": [{"label": "BHD - <PERSON><PERSON>", "value": "BHD"}, {"not_usable": true, "label": "BDT - 孟加拉国塔卡", "value": "BDT"}, {"not_usable": true, "label": "BGN - Bulgarian Lev", "value": "BGN"}, {"label": "AWG - 阿鲁巴岛弗罗林", "value": "AWG"}, {"label": "USD - U.S. Dollar", "value": "USD"}, {"label": "AED - UAE Dirham", "value": "AED"}, {"label": "ALL - 阿尔巴尼亚列克", "value": "ALL"}, {"label": "AOA - 安哥拉宽扎", "value": "AOA"}, {"label": "BAM - 自由兑换马克", "value": "BAM"}, {"label": "ARS - 阿根廷比索", "value": "ARS"}, {"label": "AMD - 亚美尼亚打兰", "value": "AMD"}, {"label": "AUD - Australian Dollar", "value": "AUD"}, {"label": "ANG - 荷属安地列斯盾", "value": "ANG"}, {"label": "BBD - 巴巴多斯元", "value": "BBD"}, {"label": "AFN - Afghanistan Afghani (New)", "value": "AFN"}, {"label": "AZN - 阿塞拜疆马纳特", "value": "AZN"}, {"label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "币种", "is_index_field": false, "index_name": "s_11", "config": {}, "help_text": "", "status": "new"}, "record_type": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1559187029897, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": true, "api_name": "record_type", "options": [{"is_active": true, "font_color": "#2a304d", "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}, {"is_active": true, "api_name": "record_dr4Of__c", "label": "测试无字段"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "base_expected_cost": {"expression_type": "js", "return_type": "currency", "describe_api_name": "MarketingEventToolCreateObj", "auto_adapt_places": false, "description": "预计成本(本位币)", "is_unique": false, "type": "formula", "decimal_places": 4, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "预计成本(元)(本位币)", "index_name": "d_4", "is_index": true, "is_active": true, "expression": "$expected_cost$*$mc_exchange_rate$", "create_time": 1607081475958, "is_encrypted": false, "length": 20, "label": "预计成本(元)(本位币)", "is_need_convert": false, "api_name": "base_expected_cost", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "released"}, "location": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "地点", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "label_r": "地点", "index_name": "t_6", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1558352322444, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "地点", "is_need_convert": false, "api_name": "location", "is_index_field": false, "help_text": "", "status": "released"}, "mc_exchange_rate_version": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "label_r": "汇率版本", "index_name": "t_9", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1609299178369, "is_encrypted": false, "default_value": "", "label": "汇率版本", "api_name": "mc_exchange_rate_version", "is_index_field": false, "help_text": "", "status": "new"}, "field_oq6L1__c": {"describe_api_name": "MarketingEventToolCreateObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "a_3", "is_index": true, "is_active": true, "create_time": 1629175505092, "is_encrypted": false, "label": "查找关联(多选)", "target_api_name": "LeadsObj", "target_related_list_name": "target_related_list_m02nS__c", "field_num": 6, "target_related_list_label": "市场活动", "action_on_target_delete": "set_null", "api_name": "field_oq6L1__c", "is_index_field": false, "help_text": "", "status": "new"}, "field_ve61W__c": {"describe_api_name": "MarketingEventToolCreateObj", "is_index": true, "is_active": true, "create_time": 1629171522997, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "default_value": "", "label": "部门(多选)", "type": "department_many", "field_num": 5, "is_required": false, "wheres": [], "api_name": "field_ve61W__c", "define_type": "custom", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "a_4", "help_text": "", "status": "new"}}, "release_version": "6.4", "actions": {}}