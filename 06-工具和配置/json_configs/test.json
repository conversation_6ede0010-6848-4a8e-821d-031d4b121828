{"_id": "", "api_name": "object_test__c", "display_name": "工具创建测试", "description": "", "define_type": "custom", "icon_path": "", "icon_index": 0, "fields": {"name": {"type": "text", "define_type": "system", "api_name": "name", "label": "主属性", "help_text": "", "is_required": true, "is_unique": true, "is_active": true, "is_index": true, "status": "new", "default_value": "", "default_is_expression": false, "default_to_zero": false, "max_length": 100, "input_mode": "", "is_show_mask": false, "remove_mask_roles": {}}, "owner": {"type": "employee", "define_type": "package", "api_name": "owner", "label": "负责人", "help_text": "", "is_required": true, "is_unique": false, "is_active": true, "is_index": true, "status": "new", "is_single": true, "department_list": [], "wheres": [], "where_type": "field", "employee_list": [], "is_need_convert": false}, "owner_department": {"type": "text", "define_type": "package", "api_name": "owner_department", "label": "负责人主属部门", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "new", "default_value": "", "default_is_expression": false, "default_to_zero": false, "max_length": 100, "input_mode": "", "is_show_mask": false, "remove_mask_roles": {}, "department_list": [], "is_need_convert": false, "is_single": true, "page_acitve": true}, "data_own_department": {"type": "department", "define_type": "package", "api_name": "data_own_department", "label": "归属部门", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "new", "is_single": true, "department_list": [], "wheres": [], "where_type": "field", "is_need_convert": false, "page_acitve": true}, "created_by": {"type": "text", "define_type": "system", "api_name": "created_by", "label": "创建人", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "released", "default_value": "", "default_is_expression": false, "default_to_zero": false, "max_length": 200, "input_mode": "", "is_show_mask": false, "remove_mask_roles": {}, "is_need_convert": false, "pattern": ""}, "create_time": {"type": "date_time", "define_type": "system", "api_name": "create_time", "label": "创建时间", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "released", "default_value": "", "default_is_expression": false, "default_to_zero": false, "not_use_multitime_zone": false, "time_zone": "GMT+8", "date_format": "yyyy-MM-dd HH:mm:ss", "is_need_convert": false, "max_length": 200, "pattern": ""}, "last_modified_by": {"type": "text", "define_type": "system", "api_name": "last_modified_by", "label": "最后修改人", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "released", "default_value": "", "default_is_expression": false, "default_to_zero": false, "max_length": 200, "input_mode": "", "is_show_mask": false, "remove_mask_roles": {}, "is_need_convert": false, "pattern": ""}, "last_modified_time": {"type": "date_time", "define_type": "system", "api_name": "last_modified_time", "label": "最后修改时间", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "released", "default_value": "", "default_is_expression": false, "default_to_zero": false, "not_use_multitime_zone": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "is_need_convert": false, "max_length": 200, "pattern": ""}, "record_type": {"type": "record_type", "define_type": "package", "api_name": "record_type", "label": "业务类型", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "released", "is_need_convert": false, "is_single": false, "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}]}, "life_status": {"type": "select_one", "define_type": "package", "api_name": "life_status", "label": "生命状态", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "new", "default_value": "normal", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "is_need_convert": false, "is_single": false}}, "is_active": true, "is_open_display_name": false, "version": 1, "index_version": 1, "package": "CRM"}