{"tenant_id": "771080", "store_table_name": "handler_brush__c", "package": "CRM", "is_active": true, "last_modified_time": 1706154792563, "create_time": 1705549113417, "description": "", "last_modified_by": "8786", "display_name": "hander刷库工具", "created_by": "8786", "version": 8, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "handler_brush__c", "icon_path": "", "is_udef": true, "define_type": "custom", "fields": {"tenant_id": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113417, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113329, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "is_extend": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_0", "status": "new"}, "origin_source": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113417, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "index_name": "s_os", "config": {"display": 0}, "status": "released", "description": ""}, "lock_user": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113329, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 7, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "is_extend": false, "label_r": "加锁人", "is_index_field": false, "index_name": "a_1", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "is_extend": false, "label_r": "汇率", "index_name": "d_0", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1705549113377, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "汇率", "field_num": 9, "api_name": "mc_exchange_rate", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "handler_brush__c", "is_index": false, "create_time": 1705549113417, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113329, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 10, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "is_extend": false, "label_r": "作废前生命状态", "is_index_field": false, "index_name": "t_1", "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113417, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "interface_code__c": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "Add", "value": "Add"}, {"label": "Edit", "value": "Edit"}, {"label": "FlowCompleted", "value": "FlowCompleted"}, {"label": "FlowStartCallback", "value": "FlowStartCallback"}, {"label": "IncrementUpdate", "value": "IncrementUpdate"}, {"label": "Invalid", "value": "Invalid"}, {"label": "BulkInvalid", "value": "BulkInvalid"}, {"not_usable": true, "label": "common", "value": "common"}, {"not_usable": true, "label": "common_action", "value": "commonAction"}, {"label": "其他方法", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "s_5", "is_index": true, "is_active": true, "create_time": 1705549113374, "is_encrypted": false, "default_value": "", "label": "接口方法", "field_num": 11, "api_name": "interface_code__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705549113361, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "handler_brush__c", "is_index": true, "is_active": true, "create_time": 1705549113417, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released", "description": ""}, "execution_status__c": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "执行成功", "value": "success"}, {"label": "执行中", "value": "running"}, {"label": "部分执行失败", "value": "failure"}, {"label": "终止任务", "value": "terminate"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "s_7", "is_index": true, "is_active": true, "create_time": 1705549113371, "is_encrypted": false, "default_value": "", "label": "执行状态", "field_num": 15, "api_name": "execution_status__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "employee", "is_required": true, "wheres": [], "define_type": "package", "is_single": true, "label_r": "负责人", "index_name": "owner", "is_index": true, "is_active": true, "create_time": 1705549113360, "is_encrypted": false, "default_value": "", "label": "负责人", "is_need_convert": false, "api_name": "owner", "is_index_field": false, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "锁定状态", "index_name": "s_1", "is_index": true, "is_active": true, "create_time": 1705549113329, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "field_num": 2, "is_need_convert": false, "api_name": "lock_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "package": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113417, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "handler_brush__c", "is_index": true, "create_time": 1705549113417, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "create_time": {"describe_api_name": "handler_brush__c", "is_index": true, "create_time": 1705549113417, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "brush_result__c": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "t_0", "max_length": 2000, "is_index": true, "is_active": true, "create_time": 1705549113372, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "刷库结果", "field_num": 4, "api_name": "brush_result__c", "is_index_field": false, "help_text": "失败环境：纷享云（灰度、全网），钉钉云（灰度）", "status": "new"}, "life_status": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "生命状态", "index_name": "s_2", "is_index": true, "is_active": true, "create_time": 1705549113368, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "field_num": 3, "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "system_type__c": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "全网", "value": "100"}, {"label": "灰度", "value": "101"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "s_3", "is_index": true, "is_active": true, "create_time": 1705549113369, "is_encrypted": false, "default_value": "100", "label": "系统库类型", "field_num": 5, "api_name": "system_type__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "brush_env__c": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_many", "default_to_zero": false, "is_required": false, "options": [{"label": "纷享云", "value": "1"}, {"not_usable": false, "label": "双胞胎", "value": "2"}, {"not_usable": false, "label": "紫光云生产", "value": "3"}, {"not_usable": false, "label": "金山云", "value": "4"}, {"not_usable": false, "label": "华为云", "value": "5"}, {"not_usable": false, "label": "紫光云测试", "value": "6"}, {"not_usable": false, "label": "阿里云", "value": "8"}, {"not_usable": false, "label": "法兰克福云（亚马逊）", "value": "9"}, {"not_usable": false, "label": "模板云", "value": "10"}, {"not_usable": false, "label": "海信云", "value": "11"}, {"not_usable": false, "label": "许继云", "value": "12"}, {"not_usable": false, "label": "模板复制云", "value": "13"}, {"not_usable": false, "label": "铁塔云", "value": "14"}, {"not_usable": false, "label": "蒙牛云", "value": "15"}, {"not_usable": false, "label": "何氏眼科", "value": "16"}, {"not_usable": false, "label": "扬农化工", "value": "17"}, {"not_usable": false, "label": "伍子醉", "value": "18"}, {"not_usable": false, "label": "海克斯康", "value": "19"}, {"not_usable": false, "label": "科大讯飞", "value": "20"}], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "system_type__c", "is_extend": false, "index_name": "a_0", "is_index": true, "is_active": true, "create_time": 1705549113370, "is_encrypted": false, "option_type": "general", "default_value": [], "label": "刷库环境", "field_num": 6, "option_api_name": "option_rJoE8__c", "api_name": "brush_env__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "handler_brush__c", "is_index": true, "is_active": true, "create_time": 1705549113417, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released", "description": ""}, "out_tenant_id": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113417, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "CNY - 人民币", "value": "CNY"}, {"not_usable": true, "label": "USD - 美元", "value": "USD"}, {"label": "JPY - 日元", "value": "JPY"}, {"label": "GBP - 英镑", "value": "GBP"}, {"label": "EUR - 欧元", "value": "EUR"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "币种", "index_name": "s_4", "is_index": true, "is_active": true, "create_time": 1705549113376, "is_encrypted": false, "default_value": "", "label": "币种", "field_num": 8, "api_name": "mc_currency", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "version": {"describe_api_name": "handler_brush__c", "is_index": false, "create_time": 1705549113417, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "handler_brush__c", "is_index": true, "is_active": true, "create_time": 1705549113417, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released", "description": ""}, "record_type": {"describe_api_name": "handler_brush__c", "is_index": true, "is_active": true, "create_time": 1705549113367, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "relevant_team": {"describe_api_name": "handler_brush__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1705549113375, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "data_own_department": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "package", "is_single": true, "label_r": "归属部门", "index_name": "data_owner_dept_id", "is_index": true, "is_active": true, "create_time": 1705549113362, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "is_index_field": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "prefix": "{yyyy}-{mm}-", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "create_time": 1705549113479, "is_encrypted": false, "serial_number": 4, "default_value": "{yyyy}-{mm}-0001", "label": "编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "support_object_api_name__c": {"describe_api_name": "handler_brush__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_2", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705549113373, "is_encrypted": false, "default_value": "", "label": "对象apiName", "field_num": 12, "api_name": "support_object_api_name__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "mc_exchange_rate_version": {"describe_api_name": "handler_brush__c", "is_index": false, "is_active": true, "create_time": 1705549113379, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "label": "汇率版本", "type": "text", "field_num": 13, "is_required": false, "api_name": "mc_exchange_rate_version", "define_type": "package", "is_single": false, "is_extend": false, "label_r": "汇率版本", "is_index_field": false, "index_name": "t_3", "max_length": 256, "help_text": "", "status": "new"}}, "release_version": "6.4", "actions": {}}