{"tenant_id": "78057", "package": "CRM", "is_active": true, "last_modified_time": 1704427072456, "create_time": 1704427072456, "visible_scope": "social", "description": "", "last_modified_by": "1000", "display_name": "协同自定义对象-01", "created_by": "1000", "version": 1, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "object_PT2Wq__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "gkD", "fields": {"tenant_id": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072456, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072338, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_0", "status": "new"}, "data_own_organization": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "is_active": true, "create_time": 1704427072464, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "归属组织", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_organization", "define_type": "package", "is_single": true, "label_r": "归属组织", "is_index_field": false, "index_name": "a_0", "status": "released"}, "origin_source": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072456, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "index_name": "s_os", "config": {"display": 0}, "status": "released"}, "lock_user": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072338, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 4, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_1", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "object_PT2Wq__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_0", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1704427072461, "is_encrypted": false, "length": 10, "label": "汇率", "field_num": 6, "api_name": "mc_exchange_rate", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "create_time": 1704427072456, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072338, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 7, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "label_r": "作废前生命状态", "is_index_field": false, "index_name": "t_0", "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072456, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "object_PT2Wq__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1704427072451, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "is_active": true, "create_time": 1704427072456, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released"}, "mc_functional_currency": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072462, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "本位币", "type": "select_one", "field_num": 9, "is_required": false, "api_name": "mc_functional_currency", "options": [{"not_usable": false, "label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "本位币", "is_index_field": false, "index_name": "s_4", "config": {}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "object_PT2Wq__c", "default_is_expression": false, "is_index": true, "is_active": true, "create_time": 1704427072450, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "label_r": "负责人", "is_index_field": false, "index_name": "owner", "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "is_active": true, "create_time": 1704427072338, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "field_num": 2, "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "is_index_field": false, "index_name": "s_1", "config": {}, "status": "new"}, "package": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072456, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "create_time": 1704427072456, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "create_time": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "create_time": 1704427072456, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "life_status": {"describe_api_name": "object_PT2Wq__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "index_name": "s_2", "is_index": true, "is_active": true, "create_time": 1704427072458, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "field_num": 3, "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "is_active": true, "create_time": 1704427072456, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "out_tenant_id": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072456, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "is_active": true, "create_time": 1704427072460, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "币种", "type": "select_one", "field_num": 5, "is_required": false, "api_name": "mc_currency", "options": [{"not_usable": true, "label": "Japanese-cus-日元", "value": "JPYA"}, {"not_usable": false, "label": "ces", "value": "obj"}, {"not_usable": false, "label": "英文环境", "value": "CUMA"}, {"not_usable": false, "label": "bb", "value": "bb"}, {"not_usable": false, "label": "aa", "value": "aa"}, {"not_usable": false, "label": "ui-只同步翻译工作台", "value": "ui"}, {"not_usable": false, "label": "custom-Testing02", "value": "uIOI"}, {"not_usable": false, "label": "en-no type", "value": "notype"}, {"not_usable": false, "label": "111", "value": "zxf"}, {"not_usable": false, "label": "COP - Colombian Peso", "value": "COP"}, {"not_usable": false, "label": "CAD - Canadian Dollar", "value": "CAD"}, {"not_usable": false, "label": "ANG - 荷属安地列斯盾", "value": "ANG"}, {"not_usable": false, "label": "AMD - 亚美尼亚打兰", "value": "AMD"}, {"not_usable": false, "label": "CUP - 古巴比索", "value": "CUP"}, {"not_usable": false, "label": "AFN - Afghanistan Afghani (New)", "value": "AFN"}, {"not_usable": false, "label": "BBD - 巴巴多斯元", "value": "BBD"}, {"not_usable": false, "label": "BGN - Bulgarian Lev", "value": "BGN"}, {"not_usable": false, "label": "BYN - Belarusian Ruble", "value": "BYN"}, {"not_usable": false, "label": "EUR - 欧元", "value": "EUR"}, {"not_usable": false, "label": "AUD - Australian Dollar", "value": "AUD"}, {"not_usable": false, "label": "ALL - 阿尔巴尼亚列克", "value": "ALL"}, {"not_usable": false, "label": "BOV - Bolivia Mvdol", "value": "BOV"}, {"not_usable": false, "label": "BRB - <PERSON> <PERSON><PERSON> (old)", "value": "BRB"}, {"not_usable": false, "label": "BSD - Bahamian Dollar", "value": "BSD"}, {"not_usable": false, "label": "BWP - Botswana Pula", "value": "BWP"}, {"not_usable": false, "label": "BOB - Bolivian <PERSON>no", "value": "BOB"}, {"not_usable": false, "label": "BIF - Burundi Franc", "value": "BIF"}, {"not_usable": false, "label": "BTN - Bhutan Ngultrum", "value": "BTN"}, {"not_usable": false, "label": "BMD - Bermuda Dollar", "value": "BMD"}, {"not_usable": false, "label": "BND - Brunei Dollar", "value": "BND"}, {"not_usable": false, "label": "BRL - Brazilian Real", "value": "BRL"}, {"not_usable": false, "label": "BDT - 孟加拉国塔卡", "value": "BDT"}, {"not_usable": false, "label": "BAM - 自由兑换马克", "value": "BAM"}, {"not_usable": false, "label": "CDF - <PERSON><PERSON><PERSON>", "value": "CDF"}, {"not_usable": false, "label": "CLP - Chilean Peso", "value": "CLP"}, {"not_usable": false, "label": "BZD - Belize Dollar", "value": "BZD"}, {"not_usable": false, "label": "HKD - Hong Kong Dollar", "value": "HKD"}, {"not_usable": false, "label": "BYR - Belarusian Ruble", "value": "BYR"}, {"not_usable": false, "label": "CLF - Unidades de fomento", "value": "CLF"}, {"not_usable": false, "label": "CHF - Swiss Franc", "value": "CHF"}, {"not_usable": false, "label": "custom-Testing01", "value": "testing"}, {"not_usable": false, "label": "AWG - 阿鲁巴岛弗罗林", "value": "AWG"}, {"not_usable": false, "label": "BHD - <PERSON><PERSON>", "value": "BHD"}, {"not_usable": false, "label": "AZN - 阿塞拜疆马纳特", "value": "AZN"}, {"not_usable": false, "label": "ARS - 阿根廷比索", "value": "ARS"}, {"not_usable": false, "label": "AED - UAE Dirham", "value": "AED"}, {"not_usable": false, "label": "AOA - 安哥拉宽扎", "value": "AOA"}, {"not_usable": false, "label": "KRW - 韩元", "value": "KRW"}, {"not_usable": false, "label": "JPY - 日元", "value": "JPY"}, {"not_usable": false, "label": "GBP - 英镑", "value": "GBP"}, {"not_usable": false, "label": "USD - U.S. Dollar", "value": "USD"}, {"not_usable": false, "label": "MKD - Macedonian Denar", "value": "MKD"}, {"not_usable": false, "label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "币种", "is_index_field": false, "index_name": "s_3", "config": {}, "help_text": "", "status": "new"}, "version": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "create_time": 1704427072456, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "is_active": true, "create_time": 1704427072456, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "relevant_team": {"describe_api_name": "object_PT2Wq__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1704427072459, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "object_PT2Wq__c", "is_index": true, "is_active": true, "create_time": 1704427072457, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "object_PT2Wq__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "package", "is_single": true, "label_r": "归属部门", "index_name": "data_owner_dept_id", "is_index": true, "is_active": true, "create_time": 1704427072452, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "is_index_field": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "object_PT2Wq__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1704427072511, "is_encrypted": false, "default_value": "", "label": "主属性", "api_name": "name", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "create_time": 1704427072456, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "mc_exchange_rate_version": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072463, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "label": "汇率版本", "type": "text", "field_num": 8, "is_required": false, "api_name": "mc_exchange_rate_version", "define_type": "package", "is_single": false, "label_r": "汇率版本", "is_index_field": false, "index_name": "t_1", "max_length": 256, "help_text": "", "status": "new"}, "_id": {"describe_api_name": "object_PT2Wq__c", "is_index": false, "is_active": true, "create_time": 1704427072456, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}}, "release_version": "6.4", "actions": {}}