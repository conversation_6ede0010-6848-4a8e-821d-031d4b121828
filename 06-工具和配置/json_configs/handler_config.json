{"tenant_id": "771080", "store_table_name": "handler_config__c", "package": "CRM", "is_active": true, "last_modified_time": 1705888806889, "create_time": 1705651132755, "description": "", "last_modified_by": "8786", "display_name": "handler配置", "created_by": "8786", "version": 2, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "handler_config__c", "icon_path": "", "is_udef": true, "define_type": "custom", "fields": {"tenant_id": {"describe_api_name": "handler_config__c", "is_index": false, "is_active": true, "create_time": 1705651132755, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "handler_config__c", "is_index": false, "is_active": true, "create_time": 1705651132645, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "is_extend": false, "label_r": "锁定规则", "is_index_field": false, "status": "new"}, "handler_bursh_code__c": {"describe_api_name": "handler_config__c", "auto_adapt_places": false, "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "custom", "is_single": false, "is_extend": false, "is_index": true, "is_active": true, "create_time": 1705888806112, "is_encrypted": false, "label": "刷库编号", "target_api_name": "handler_brush__c", "show_detail_button": false, "target_related_list_name": "target_related_list_Nzjsa__c", "target_related_list_label": "handler配置", "api_name": "handler_bursh_code__c", "is_create_when_master_create": true, "is_index_field": false, "is_required_when_master_create": false, "help_text": "", "status": "new", "description": ""}, "provider_type__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "平台预置（system）", "value": "system"}, {"label": "领域预置（package）", "value": "package"}, {"label": "行业预置（industry）", "value": "industry"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index": true, "is_active": true, "create_time": 1705651132757, "is_encrypted": false, "default_value": "", "label": "定义类型", "api_name": "provider_type__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "active__c": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132767, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": true, "label": "启用", "type": "true_or_false", "is_required": false, "api_name": "active__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "handler_config__c", "is_index": false, "is_active": true, "create_time": 1705651132645, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "is_extend": false, "label_r": "加锁人", "is_index_field": false, "status": "new"}, "continue_after_approval_success__c": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132766, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "审批成功后是否继续执行", "type": "true_or_false", "is_required": false, "api_name": "continue_after_approval_success__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "is_extend": false, "label_r": "汇率", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1705651132771, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "汇率", "api_name": "mc_exchange_rate", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "handler_config__c", "is_index": false, "create_time": 1705651132755, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released"}, "handler_type__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "after", "value": "after"}, {"label": "finally", "value": "finally"}, {"label": "service", "value": "service"}, {"label": "act", "value": "act"}, {"label": "before", "value": "before"}, {"label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index": true, "is_active": true, "create_time": 1705651132756, "is_encrypted": false, "default_value": "", "label": "业务链处理器类型", "api_name": "handler_type__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "handler_config__c", "is_index": false, "is_active": true, "create_time": 1705651132645, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "is_extend": false, "label_r": "作废前生命状态", "is_index_field": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "handler_config__c", "is_index": false, "is_active": true, "create_time": 1705651132755, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "interface_code__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132755, "is_encrypted": false, "default_value": "", "label": "接口方法", "api_name": "interface_code__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "enable_gray_config__c": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132765, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "否支持灰度配", "type": "true_or_false", "is_required": false, "api_name": "enable_gray_config__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "enable_distributed_transaction__c": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132763, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "是否支持分布式事务", "type": "true_or_false", "is_required": false, "api_name": "enable_distributed_transaction__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132744, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "rest_api_url__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132760, "is_encrypted": false, "default_value": "", "label": "远程调用接口配置权限配置", "api_name": "rest_api_url__c", "is_index_field": false, "is_show_mask": false, "help_text": "Handler 远程调用接口配置信息的key，对应fs-paas-appframework-rest配置文件中的key，当本地找不到该handler的实例时，会通过 Rest 远程调用执行", "status": "new"}, "out_owner": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132755, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "config": {"display": 1}, "status": "released", "description": ""}, "industry_code__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132759, "is_encrypted": false, "default_value": "", "label": "行业Code", "api_name": "industry_code__c", "is_index_field": false, "is_show_mask": false, "help_text": "fmcg：快消\nmanu：制造\ntech：高服", "status": "new"}, "mc_functional_currency": {"describe_api_name": "handler_config__c", "is_index": false, "is_active": true, "create_time": 1705651132772, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "本位币", "type": "select_one", "is_required": false, "api_name": "mc_functional_currency", "options": [{"label": "CNY - 人民币", "value": "CNY"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "本位币", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "privilege_config__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132761, "is_encrypted": false, "default_value": "", "label": "权限配置", "api_name": "privilege_config__c", "is_index_field": false, "is_show_mask": false, "help_text": "Handler 适用的权限配置，常用的比如bizConf 开关，如果为空，则标识不受任何权限限制。如：\t{\"bizConfigs\":[{ \"key\": \"multi-unit-status\", \"value\": \"1\" }]}", "status": "new"}, "support_async__c": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132764, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "否支持异步执行（暂不支持）", "type": "true_or_false", "is_required": false, "api_name": "support_async__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "employee", "is_required": true, "wheres": [], "define_type": "package", "is_single": true, "label_r": "负责人", "is_index": true, "is_active": true, "create_time": 1705651132743, "is_encrypted": false, "default_value": "", "label": "负责人", "is_need_convert": false, "api_name": "owner", "is_index_field": false, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "锁定状态", "is_index": true, "is_active": true, "create_time": 1705651132645, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "is_need_convert": false, "api_name": "lock_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "last_modified_time": {"describe_api_name": "handler_config__c", "is_index": true, "create_time": 1705651132755, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "handler_api_name__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132752, "is_encrypted": false, "default_value": "", "label": "业务链apiName", "api_name": "handler_api_name__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "order__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "max_length": 14, "is_index": true, "is_active": true, "create_time": 1705651132768, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "排序", "api_name": "order__c", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "create_time": {"describe_api_name": "handler_config__c", "is_index": true, "create_time": 1705651132755, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "生命状态", "is_index": true, "is_active": true, "create_time": 1705651132751, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132755, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released", "description": ""}, "description__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "max_length": 2000, "is_index": true, "is_active": true, "create_time": 1705651132758, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "描述介绍", "api_name": "description__c", "is_index_field": false, "help_text": "", "status": "new"}, "management_config__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132762, "is_encrypted": false, "default_value": "", "label": "后台管理配置", "api_name": "management_config__c", "is_index_field": false, "is_show_mask": false, "help_text": "Handler的后台管理配置，比如hidden=true表示在管理界面隐藏该Handler，示例：{\"hidden\":true}", "status": "new"}, "out_tenant_id": {"describe_api_name": "handler_config__c", "is_index": false, "is_active": true, "create_time": 1705651132755, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "config": {"display": 0}, "max_length": 200, "status": "released"}, "created_by": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132755, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released", "description": ""}, "record_type": {"describe_api_name": "handler_config__c", "is_index": true, "is_active": true, "create_time": 1705651132750, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "config": {}, "help_text": "", "status": "released"}, "name": {"describe_api_name": "handler_config__c", "default_is_expression": false, "prefix": "{yyyy}-{mm}-", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "label_r": "主属性", "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "create_time": 1705651132970, "is_encrypted": false, "serial_number": 4, "default_value": "{yyyy}-{mm}-0001", "label": "编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "support_object_api_name__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132754, "is_encrypted": false, "default_value": "", "label": "支持对象的apiName", "api_name": "support_object_api_name__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "handler_label__c": {"describe_api_name": "handler_config__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705651132753, "is_encrypted": false, "default_value": "", "label": "业务链处理器名称", "api_name": "handler_label__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}}, "release_version": "6.4", "actions": {}}