{"buttons": [], "components": [{"field_section": [], "buttons": [], "api_name": "sale_log", "related_list_name": "", "is_hidden": false, "header": "跟进动态", "nameI18nKey": "paas.udobj.follow_up_dynamic", "type": "related_record", "order": 1, "_id": "sale_log"}, {"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "is_hidden": false, "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "is_show_avatar": true, "type": "user_list", "order": 3, "_id": "relevant_team_component"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "is_hidden": false, "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "fields": {"operation_time": {"render_type": "date_time", "field_name": "operation_time"}, "message": {"render_type": "text", "field_name": "log_msg"}, "user": {"render_type": "employee", "field_name": "user_id"}}, "order": 6, "_id": "operation_log"}, {"components": [["BPM_related_list", "tabs_1592559766384"], ["Approval_related_list"], ["operation_log"], ["payment_recordrelated_list_generate_by_UDObjectServer__c"], ["chart_component_nUuP4__c"], ["MarketingEventToolCreateObj_parent_id_related_list"], ["CampaignMembersObj_marketing_event_id_related_list"], ["object_8hJgM__c_field_Fv4wf__c_related_list"], ["marketing_event_path"], ["SalesOrderProductObj_field_6f8Wm__c_related_list"], ["ActiveRecordObj_field_Yqri1__c_related_list"], ["object_n4zWv__c_field_BbFwU__c_related_list"], ["JournalObj_field_5gVP2__c_related_list"], ["object_OrlD9__c_field_eyCy2__c_related_list"], ["object_836n2__c_field_nrcwR__c_related_list"], ["object_zl_whatlist__c_field_0P1HF__c_related_list"]], "buttons": [], "api_name": "container_default_MarketingEventToolCreateObj_layout_generate_by_UDObjectServer__c", "tabs": [{"api_name": "tab_BPM_related_list", "header": "流程列表"}, {"api_name": "tab_Approval_related_list", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "tab_operation_log", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "tab_payment_recordrelated_list_generate_by_UDObjectServer__c", "header": "收款记录"}, {"api_name": "container_default_MarketingEventToolCreateObj_layout_generate_by_UDObjectServer__c_N3167__c", "header": "大客户Top10"}, {"api_name": "tab_MarketingEventToolCreateObj_parent_id_related_list", "header": "子级市场活动1", "nameI18nKey": "MarketingEventToolCreateObj.field.parent_id.reference_label"}, {"api_name": "tab_CampaignMembersObj_marketing_event_id_related_list", "header": "活动成员", "nameI18nKey": "CampaignMembersObj.field.marketing_event_id.reference_label"}, {"api_name": "tab_object_8hJgM__c_field_Fv4wf__c_related_list", "header": "自定义对象(示例)", "nameI18nKey": "object_8hJgM__c.field.field_Fv4wf__c.reference_label"}, {"api_name": "marketing_event_path_1621944601370", "header": "市场活动层级"}, {"api_name": "tab_SalesOrderProductObj_field_6f8Wm__c_related_list", "header": "订单产品", "nameI18nKey": "SalesOrderProductObj.field.field_6f8Wm__c.reference_label"}, {"api_name": "tab_ActiveRecordObj_field_Yqri1__c_related_list", "header": "销售记录", "nameI18nKey": "ActiveRecordObj.field.field_Yqri1__c.reference_label"}, {"api_name": "tab_object_n4zWv__c_field_BbFwU__c_related_list", "header": "wj-多选", "nameI18nKey": "object_n4zWv__c.field.field_BbFwU__c.reference_label"}, {"api_name": "tab_JournalObj_field_5gVP2__c_related_list", "header": "日志", "nameI18nKey": "JournalObj.field.field_5gVP2__c.reference_label"}, {"api_name": "tab_object_OrlD9__c_field_eyCy2__c_related_list", "header": "mj-主", "nameI18nKey": "object_OrlD9__c.field.field_eyCy2__c.reference_label"}, {"api_name": "tab_object_836n2__c_field_nrcwR__c_related_list", "header": "自定义what对象", "nameI18nKey": "object_836n2__c.field.field_nrcwR__c.reference_label"}, {"api_name": "tab_object_zl_whatlist__c_field_0P1HF__c_related_list", "header": "zl-whatlist", "nameI18nKey": "object_zl_whatlist__c.field.field_0P1HF__c.reference_label"}], "header": "页签容器", "type": "tabs", "_id": "container_default_MarketingEventToolCreateObj_layout_generate_by_UDObjectServer__c"}, {"field_section": [], "buttons": [], "api_name": "chart_component_nUuP4__c", "related_list_name": "", "Title": "大客户Top10", "header": "大客户Top10", "style": {"height": "200px"}, "CardID": "BI_5bac4e852b90179f4028b09d", "type": "chart", "_id": "chart_component_nUuP4__c"}, {"components": [["object_hG7Es__c_md_group_component"], ["form_component"], ["LeadsObj_marketing_event_id_related_list"]], "buttons": [], "api_name": "tabs_1592559766384", "tabs": [{"api_name": "tab_object_hG7Es__c_md_group_component", "header": "jjy字段类型测试", "nameI18nKey": "object_hG7Es__c.field.field_m3eRw__c.reference_label"}, {"api_name": "tab_form_component", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "LeadsObj_marketing_event_id_related_list_1592559779129", "header": "销售线索", "nameI18nKey": "LeadsObj.field.marketing_event_id.reference_label"}], "header": "页签容器", "type": "tabs", "_id": "tabs_1592559766384"}, {"relationType": 0, "api_name": "marketing_event_path", "related_list_name": "marketing_event_list", "ref_object_api_name": "MarketingEventToolCreateObj", "header": "市场活动层级", "type": "marketing_event_path", "_id": "marketing_event_path"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "begin_time"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "end_time"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "actual_income"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "actual_cost"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "description"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "effect"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "expected_income"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "summary"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_w2liu__c"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "out_owner"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "lock_status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "marketing_plan"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "location"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "execution_desc"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "expected_cost"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "biz_status"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "event_type"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "mc_currency"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "mc_exchange_rate"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_21llU__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_1oF9b__c"}, {"is_readonly": false, "is_required": false, "render_type": "department_many", "field_name": "field_ve61W__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_oq6L1__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_rA1ck__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_d5vIE__c"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_many", "field_name": "field_v2Egf__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_xJ1rN__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_xp2q5__c"}, {"is_readonly": false, "is_required": false, "render_type": "image", "field_name": "field_9qv6n__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "field_62oeS__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "其他信息", "is_show": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "is_hidden": false, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "order": 1, "_id": "form_component"}, {"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangeStates_button_default", "label": "状态变更"}, {"action_type": "default", "api_name": "AddTeamMember_button_default", "label": "添加相关团队成员"}, {"action_type": "default", "api_name": "EditTeamMember_button_default", "label": "编辑相关团队成员"}, {"action_type": "default", "api_name": "DeleteTeamMember_button_default", "label": "移除相关团队成员"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "custom", "api_name": "button_R3mbL__c", "label": "按钮名称(示例)233"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "action": "StartStagePropellor", "label": "发起阶段推进器"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "_id": "head_info"}, {"field_section": [], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info", "nameI18nKey": "paas.udobj.summary_info", "_id": "top_info"}, {"relationType": 2, "buttons": [], "api_name": "payment_recordrelated_list_generate_by_UDObjectServer__c", "related_list_name": "payment_record_LIST", "ref_object_api_name": "payment_record", "header": "收款记录s", "nameI18nKey": "paas.udobj.receipt", "type": "relatedlist", "_id": "payment_recordrelated_list_generate_by_UDObjectServer__c"}, {"isCategory": 2, "field_section": [], "buttons": [], "chart_type": "bar", "api_name": "chart_g1Jot__c", "related_list_name": "", "Title": "部门销量排行", "header": "部门销量排行", "style": {"height": "500px"}, "CardID": "BI_623043397bdad60001d949e4", "type": "chart", "_id": "chart_g1Jot__c"}, {"buttons": [], "child_components": [], "type": "multi_table", "api_name": "object_hG7Es__c_md_group_component", "header": "jjy字段类型测试", "ref_object_api_name": "object_hG7Es__c", "related_list_name": "target_related_list_CP6r8__c", "field_api_name": "field_m3eRw__c", "nameI18nKey": "object_hG7Es__c.field.field_m3eRw__c.reference_label", "limit": 1, "button_info": [{"hidden": [], "render_type": "list_normal", "order": []}], "relationType": 0, "_id": "object_hG7Es__c_md_group_component"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "LeadsObj_marketing_event_id_related_list", "header": "销售线索", "ref_object_api_name": "LeadsObj", "related_list_name": "marketing_event_leads_list", "field_api_name": "marketing_event_id", "nameI18nKey": "LeadsObj.field.marketing_event_id.reference_label", "limit": 1, "_id": "LeadsObj_marketing_event_id_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "CampaignMembersObj_marketing_event_id_related_list", "header": "活动成员", "ref_object_api_name": "CampaignMembersObj", "related_list_name": "marketing_event_campaignmembers_list", "field_api_name": "marketing_event_id", "nameI18nKey": "CampaignMembersObj.field.marketing_event_id.reference_label", "limit": 1, "_id": "CampaignMembersObj_marketing_event_id_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "MarketingEventToolCreateObj_parent_id_related_list", "header": "子级市场活动1", "ref_object_api_name": "MarketingEventToolCreateObj", "related_list_name": "marketing_event_list", "field_api_name": "parent_id", "nameI18nKey": "MarketingEventToolCreateObj.field.parent_id.reference_label", "limit": 1, "_id": "MarketingEventToolCreateObj_parent_id_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_8hJgM__c_field_Fv4wf__c_related_list", "header": "自定义对象(示例)", "ref_object_api_name": "object_8hJgM__c", "related_list_name": "target_related_list_63ntA__c", "field_api_name": "field_Fv4wf__c", "nameI18nKey": "object_8hJgM__c.field.field_Fv4wf__c.reference_label", "limit": 1, "_id": "object_8hJgM__c_field_Fv4wf__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_n4zWv__c_field_BbFwU__c_related_list", "header": "wj-多选", "ref_object_api_name": "object_n4zWv__c", "related_list_name": "target_related_list_TGN9d__c", "field_api_name": "field_BbFwU__c", "nameI18nKey": "object_n4zWv__c.field.field_BbFwU__c.reference_label", "limit": 1, "_id": "object_n4zWv__c_field_BbFwU__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "SalesOrderProductObj_field_6f8Wm__c_related_list", "header": "订单产品", "ref_object_api_name": "SalesOrderProductObj", "related_list_name": "target_related_list_WzdDA__c", "field_api_name": "field_6f8Wm__c", "nameI18nKey": "SalesOrderProductObj.field.field_6f8Wm__c.reference_label", "limit": 1, "_id": "SalesOrderProductObj_field_6f8Wm__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_OrlD9__c_field_eyCy2__c_related_list", "header": "mj-主", "ref_object_api_name": "object_OrlD9__c", "related_list_name": "target_related_list_1ny5E__c", "field_api_name": "field_eyCy2__c", "nameI18nKey": "object_OrlD9__c.field.field_eyCy2__c.reference_label", "limit": 1, "_id": "object_OrlD9__c_field_eyCy2__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_836n2__c_field_nrcwR__c_related_list", "header": "自定义what对象", "ref_object_api_name": "object_836n2__c", "related_list_name": "target_related_list_1uC4W__c", "field_api_name": "field_nrcwR__c", "nameI18nKey": "object_836n2__c.field.field_nrcwR__c.reference_label", "limit": 1, "_id": "object_836n2__c_field_nrcwR__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "JournalObj_field_5gVP2__c_related_list", "header": "日志", "ref_object_api_name": "JournalObj", "related_list_name": "target_related_list_8D4em__c", "field_api_name": "field_5gVP2__c", "nameI18nKey": "JournalObj.field.field_5gVP2__c.reference_label", "limit": 1, "_id": "JournalObj_field_5gVP2__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "ActiveRecordObj_field_Yqri1__c_related_list", "header": "销售记录", "ref_object_api_name": "ActiveRecordObj", "related_list_name": "target_related_list_rV41z__c", "field_api_name": "field_Yqri1__c", "nameI18nKey": "ActiveRecordObj.field.field_Yqri1__c.reference_label", "limit": 1, "_id": "ActiveRecordObj_field_Yqri1__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_zl_whatlist__c_field_0P1HF__c_related_list", "header": "zl-whatlist", "ref_object_api_name": "object_zl_whatlist__c", "related_list_name": "target_related_list_6yzFG__c", "field_api_name": "field_0P1HF__c", "nameI18nKey": "object_zl_whatlist__c.field.field_0P1HF__c.reference_label", "limit": 1, "_id": "object_zl_whatlist__c_field_0P1HF__c_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "BPM_related_list", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "ref_object_api_name": "BPM", "related_list_name": "", "limit": 1, "field_section": [], "_id": "BPM_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "Approval_related_list", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "ref_object_api_name": "Approval", "related_list_name": "", "limit": 1, "field_section": [], "_id": "Approval_related_list"}, {"type": "relatedlist", "buttons": [], "api_name": "attach_component", "header": "附件", "nameI18nKey": "paas.udobj.annex", "define_type": "business", "ref_object_api_name": "AttachObj", "related_list_name": "AttachObj", "limit": 1, "_id": "attach_component"}], "last_modified_time": 1663831192187, "is_deleted": false, "version": 32, "create_time": 1534326312859, "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "MarketingEventToolCreateObj_layout_generate_by_UDObjectServer__c", "what_api_name": null, "default_component": "form_component", "config": {"edit": 1, "remove": 0}, "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["chart_g1Jot__c", "top_info", "container_default_MarketingEventToolCreateObj_layout_generate_by_UDObjectServer__c"], ["relevant_team_component", "sale_log"]], "columns": [{"width": "70%"}, {"width": "30%", "retractable": true}]}]}, "created_by": "-1000", "display_name": "默认布局", "is_default": true, "last_modified_by": "1000", "layout_type": "detail", "package": "CRM", "ref_object_api_name": "MarketingEventToolCreateObj", "tenant_id": "74255", "ui_event_ids": [], "hidden_buttons": ["ChangePartner_button_default", "DeletePartner_button_default", "ChangePartnerOwner_button_default"], "hidden_components": ["component_4fEPz__c", "component_SDWSM__c", "component_pJEzE__c", "related_what_list"], "namespace": null, "enable_mobile_layout": false, "events": []}