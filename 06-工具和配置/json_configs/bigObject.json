{"describe": {"tenant_id": "78057", "package": "CRM", "is_active": true, "visible_scope": "big", "description": "", "last_modified_by": {"describe_api_name": "object_2u71w__c", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "display_name": "大对象(示例)2", "created_by": {"describe_api_name": "object_2u71w__c", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "version": {"describe_api_name": "object_2u71w__c", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": {"describe_api_name": "object_2u71w__c", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "api_name": "object_2u71w__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "Fyw", "_id": {"describe_api_name": "object_2u71w__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}, "fields": {"owner": {"describe_api_name": "object_2u71w__c", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "_id": "6538c7eed2d5940001b4024d", "is_single": true, "label_r": "负责人", "is_index_field": false, "index_name": "owner", "help_text": "", "status": "new"}, "tenant_id": {"describe_api_name": "object_2u71w__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "lock_status": {"describe_api_name": "object_2u71w__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "_id": "6538c7fad2d5940001b40b2a", "is_single": false, "label_r": "锁定状态", "is_index_field": false, "index_name": "s_0", "config": {}, "status": "new"}, "package": {"describe_api_name": "object_2u71w__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "object_2u71w__c", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "describe_api_name": "object_2u71w__c", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "origin_source": {"describe_api_name": "object_2u71w__c", "is_index": true, "is_active": true, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "index_name": "s_os", "config": {"display": 0}, "status": "released"}, "lock_user": {"describe_api_name": "object_2u71w__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 2, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "_id": "6538c7fad2d5940001b40b2b", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_0", "status": "new"}, "record_type": {"describe_api_name": "object_2u71w__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "_id": "6538c7eed2d5940001b40254", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "object_2u71w__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 3, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "_id": "6538c7fad2d5940001b40b2c", "is_single": false, "label_r": "作废前生命状态", "is_index_field": false, "index_name": "t_0", "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "object_2u71w__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "name": {"describe_api_name": "object_2u71w__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "主属性", "api_name": "name", "_id": "6538c7eed2d5940001b40250", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "object_2u71w__c", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "owner_department": {"describe_api_name": "object_2u71w__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "_id": "6538c7eed2d5940001b40251", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}}, "release_version": "6.4", "actions": {}}