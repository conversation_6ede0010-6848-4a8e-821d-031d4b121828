{"buttons": [{"action_type": "default", "api_name": "Add_button_default", "action": "Add", "label": "新建"}, {"action_type": "default", "api_name": "DuplicateCheckObj_button_default", "action": "DuplicateCheckObj", "label": "查重工具"}, {"tenant_id": "74255", "describe_api_name": "MarketingEventToolCreateObj", "is_active": true, "last_modified_time": 1594797914802, "create_time": 1594797914802, "action_type": "custom", "button_type": "redirect", "description": "", "param_form": [], "label": "按钮名称(示例)", "last_modified_by": "1000", "version": 1, "created_by": "1000", "jump_url": "", "is_deleted": false, "wheres": [], "api_name": "button_UbB85__c", "lock_data_show_button": false, "action": "button_UbB85__c", "define_type": "custom", "use_pages": ["list_normal"], "actions": [], "redirect_type": ""}], "components": [{"field_section": [{"form_fields": [{"is_readonly": false, "is_required": true, "api_name": "name", "render_type": "text", "field_name": "name", "is_show_label": true}, {"is_readonly": false, "is_required": false, "api_name": "begin_time", "render_type": "date", "field_name": "begin_time", "is_show_label": true}, {"is_readonly": false, "is_required": false, "api_name": "end_time", "render_type": "date", "field_name": "end_time", "is_show_label": true}, {"is_readonly": false, "is_required": false, "api_name": "event_type", "render_type": "select_one", "field_name": "event_type", "is_show_label": true}, {"is_readonly": false, "is_required": false, "api_name": "base_expected_income", "render_type": "formula", "field_name": "base_expected_income", "is_show_label": true}, {"is_readonly": false, "is_required": false, "api_name": "mc_exchange_rate", "render_type": "number", "field_name": "mc_exchange_rate", "is_show_label": true}], "api_name": "layout_MarketingEventToolCreateObj_mobile_left", "column": 0}, {"form_fields": [{"is_readonly": false, "is_required": false, "api_name": "biz_status", "render_type": "select_one", "field_name": "biz_status", "is_show_label": false}], "api_name": "layout_MarketingEventToolCreateObj_mobile_right", "column": 1}], "buttons": [], "api_name": "table_component", "ref_object_api_name": "MarketingEventToolCreateObj", "include_fields": [{"field_name": "name", "api_name": "name", "render_type": "text", "label": "市场活动名称"}], "type": "table", "is_show_tag": false, "show_image": null}], "last_modified_time": 1654567693109, "is_deleted": false, "version": 2, "create_time": 1566986640608, "agent_type": "agent_type_mobile", "is_show_fieldname": true, "layout_description": null, "api_name": "layout_MarketingEventToolCreateObj_mobile", "what_api_name": null, "default_component": null, "created_by": null, "display_name": "市场活动列表布局", "is_default": true, "last_modified_by": "1000", "layout_type": "list", "package": "CRM", "ref_object_api_name": "MarketingEventToolCreateObj", "tenant_id": "74255", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": [], "namespace": null, "enable_mobile_layout": null, "layout_structure": {"layout": [{"columns": [{"width": "100%"}], "components": [["table_component"]]}]}}