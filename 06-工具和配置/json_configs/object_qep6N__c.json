{"tenant_id": "74255", "package": "CRM", "is_active": true, "last_modified_time": 1678964227847, "create_time": 1631173218514, "description": "", "last_modified_by": "1000", "display_name": "fj-UI事件主", "created_by": "1000", "version": 591, "is_open_display_name": false, "index_version": 200, "icon_index": 0, "is_deleted": false, "api_name": "object_qep6N__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "hGD", "fields": {"lock_rule": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1645013474137, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_10", "help_text": "", "status": "new"}, "field_bcI2r__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "function", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 9, "operator": "IN", "field_name": "id", "field_values": ["Budget_account_filtter__c"]}]}], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_38", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "查找科目", "target_api_name": "TPMBudgetBusinessSubjectObj", "target_related_list_name": "target_related_list_Gtsch__c", "field_num": 60, "target_related_list_label": "fj-UI事件主", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_bcI2r__c", "is_index_field": true, "help_text": "", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_4", "max_length": 16, "field_api_name": "mc_exchange_rate", "is_index": true, "is_active": true, "create_time": 1631173218519, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "汇率", "field_num": 6, "api_name": "mc_exchange_rate", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_Z24o4__c": {"return_type": "number", "describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "object_0wK5p__c", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "index_name": "d_7", "field_api_name": "field_7mnBR__c", "is_index": true, "default_result": "d_null", "is_active": true, "create_time": 1657781945722, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "field_QSs89__c", "label": "钱合计", "field_num": 54, "count_to_zero": false, "api_name": "field_Z24o4__c", "count_field_type": "number", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_zt7pl__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "used_in": "component", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_9", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1663241630653, "is_encrypted": false, "length": 12, "default_value": "", "label": "收款金额", "currency_unit": "￥", "field_num": 63, "api_name": "field_zt7pl__c", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "label_r": "作废前生命状态", "index_name": "t_4", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1645013474136, "is_encrypted": false, "default_value": "", "label": "作废前生命状态", "field_num": 7, "is_need_convert": false, "api_name": "life_status_before_invalid", "is_index_field": false, "help_text": "", "status": "new"}, "field_uQkxD__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "html_rich_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "s_37", "max_length": 131072, "is_index": false, "is_active": true, "create_time": 1671766567034, "is_encrypted": false, "default_value": "", "label": "总结", "field_num": 59, "api_name": "field_uQkxD__c", "is_index_field": false, "help_text": "", "status": "new"}, "field_EgkrH__c": {"describe_api_name": "object_qep6N__c", "auto_location": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "location", "used_in": "component", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "t_1", "is_index": true, "is_active": true, "create_time": 1637918930368, "is_encrypted": false, "label": "定位", "is_geo_index": false, "field_num": 37, "geo_field_num": 1, "api_name": "field_EgkrH__c", "range_limit": false, "radius_range": 100, "is_index_field": false, "help_text": "", "status": "new"}, "field_hekam__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1670382461571, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "text", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用详细地址", "type": "quote", "quote_field": "field_0us5a__c__r.field_9v21c__c", "is_required": false, "api_name": "field_hekam__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "t_14", "is_show_mask": false, "help_text": "", "status": "new"}, "field_cvb2S__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 12, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_2", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1636423885048, "is_encrypted": false, "length": 2, "default_value": "-1", "label": "金额", "currency_unit": "￥", "field_num": 24, "api_name": "field_cvb2S__c", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_Ef1Sz__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_many", "is_required": false, "options": [{"font_color": "#2a304d", "label": "更新主从", "value": "option1"}, {"font_color": "#2a304d", "label": "新增从1", "value": "1527SA41R"}, {"font_color": "#2a304d", "label": "新增从2", "value": "BWC802kid"}, {"font_color": "#2a304d", "label": "删除从1", "value": "U8VY2pcjy"}, {"font_color": "#2a304d", "label": "删除从2", "value": "dZ4aYk02S"}, {"font_color": "#2a304d", "label": "单独编辑从1", "value": "6vEq3d12c"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "index_name": "a_5", "field_api_name": "field_Ef1Sz__c", "is_index": true, "is_active": true, "create_time": 1631174851077, "is_encrypted": false, "option_type": "field", "default_value": ["option1"], "label": "函数操作", "field_num": 10, "option_api_name": "", "api_name": "field_Ef1Sz__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_p1Re8__c": {"return_type": "number", "describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "count", "decimal_places": 0, "sub_object_describe_apiname": "object_7wQM3__c", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "IN", "field_name": "field_5ir20__c", "field_values": ["2"]}]}], "define_type": "custom", "is_single": false, "index_name": "d_5", "field_api_name": "field_i3yKp__c", "is_index": true, "default_result": "d_null", "is_active": true, "create_time": 1640250698323, "is_encrypted": false, "count_type": "count", "count_field_api_name": "", "label": "统计fj-UI事件从1=法师", "field_num": 44, "count_to_zero": true, "api_name": "field_p1Re8__c", "count_field_type": "", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_9I230__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"not_usable": false, "label": "数学-14", "value": "58B8PKOHY"}, {"not_usable": false, "label": "英语", "value": "722TQyaS1"}, {"not_usable": false, "label": "语文I", "value": "631hxe27Z"}, {"not_usable": false, "label": "物理-50", "value": "1217tuHE1"}, {"not_usable": false, "label": "化学123", "value": "Kc5612mTu"}, {"not_usable": false, "label": "生物", "value": "KsT8300ms"}, {"not_usable": false, "label": "政治I", "value": "km7t2K5FN"}, {"not_usable": false, "label": "历史", "value": "2NL5s2kKb"}, {"not_usable": false, "label": "地理", "value": "V951l1km3"}], "define_type": "custom", "is_single": false, "index_name": "s_42", "is_index": true, "is_active": true, "create_time": 1678867259104, "is_encrypted": false, "option_type": "general", "default_value": "", "label": "单选科目", "field_num": 74, "option_api_name": "option_28vrP__c", "api_name": "field_9I230__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_q4b93__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1667200585820, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "select_one", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用手机", "type": "quote", "quote_field": "field_0us5a__c__r.field_epQal__c", "is_required": false, "api_name": "field_q4b93__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "s_33", "is_show_mask": false, "help_text": "", "status": "new"}, "version": {"describe_api_name": "object_qep6N__c", "is_index": false, "create_time": 1631173218514, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "field_7Vbk3__c": {"return_type": "number", "describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "count", "decimal_places": 0, "sub_object_describe_apiname": "object_0wK5p__c", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "index_name": "d_6", "field_api_name": "field_7mnBR__c", "is_index": true, "default_result": "d_null", "is_active": true, "create_time": 1649666554209, "is_encrypted": false, "count_type": "count", "count_field_api_name": "", "label": "统计字段", "field_num": 31, "count_to_zero": true, "api_name": "field_7Vbk3__c", "count_field_type": "", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_3w4DI__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_39", "is_index": true, "is_active": true, "create_time": 1675164607352, "is_encrypted": false, "label": "查找关联", "target_api_name": "OrderPaymentObj", "target_related_list_name": "target_related_list_5812Q__c", "field_num": 62, "target_related_list_label": "fj-UI事件主", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_3w4DI__c", "is_index_field": true, "help_text": "", "status": "new"}, "field_11Fsw__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_15", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1677144620583, "is_encrypted": false, "default_value": "", "label": "表单组件中的单行文本", "field_num": 71, "api_name": "field_11Fsw__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_rUwSP__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "function", "type": "object_reference_many", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 9, "operator": "IN", "field_name": "id", "field_values": ["func_Lt0vS__c"]}]}], "define_type": "custom", "is_single": false, "index_name": "a_1", "is_index": true, "is_active": true, "create_time": 1631283701736, "is_encrypted": false, "label": "fj-主新建无函数", "target_api_name": "object_p0I5m__c", "target_related_list_name": "target_related_list_Fwcjj__c", "field_num": 16, "target_related_list_label": "fj-UI事件主无函数", "action_on_target_delete": "set_null", "api_name": "field_rUwSP__c", "is_index_field": false, "help_text": "", "status": "new"}, "field_g90wV__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1646190109117, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "部门1", "type": "department", "field_num": 30, "is_required": false, "wheres": [], "api_name": "field_g90wV__c", "define_type": "custom", "is_single": true, "is_index_field": false, "index_name": "a_9", "help_text": "", "status": "new"}, "field_6bDGT__c": {"return_type": "number", "describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "count", "decimal_places": 0, "sub_object_describe_apiname": "object_7wQM3__c", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "index_name": "d_1", "field_api_name": "field_i3yKp__c", "is_index": true, "default_result": "d_null", "is_active": true, "create_time": 1635151029869, "is_encrypted": false, "count_type": "count", "count_field_api_name": "", "label": "统计fj-UI事件从1", "field_num": 23, "count_to_zero": true, "api_name": "field_6bDGT__c", "count_field_type": "", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_426G7__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date_time", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "l_4", "is_index": true, "is_active": true, "create_time": 1663241630811, "is_encrypted": false, "not_use_multitime_zone": false, "default_value": "", "label": "收款时间", "time_zone": "GMT+8", "field_num": 66, "api_name": "field_426G7__c", "date_format": "yyyy-MM-dd HH:mm:ss", "is_index_field": false, "help_text": "", "status": "new"}, "field_yb9iB__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "used_in": "component", "is_required": false, "options": [{"label": "未收款", "value": "incomplete"}, {"label": "已收款", "value": "complete"}], "define_type": "custom", "is_single": false, "index_name": "s_29", "field_api_name": "field_yb9iB__c", "is_index": true, "is_active": true, "create_time": 1663241630811, "is_encrypted": false, "default_value": "", "label": "收款状态", "field_num": 65, "api_name": "field_yb9iB__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "tenant_id": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1631173218514, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "field_1Hq4P__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1663241630479, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "group_type": "payment", "label": "支付(收款)组件1", "type": "group", "amount_is_readonly": false, "is_required": false, "api_name": "field_1Hq4P__c", "define_type": "custom", "is_single": false, "amount_input_type": "manual_input", "is_index_field": false, "fields": {"pay_time_field": "field_426G7__c", "pay_status_field": "field_yb9iB__c", "pay_type_field": "field_js17w__c", "pay_amount_field": "field_zt7pl__c"}, "index_name": "s_30", "help_text": "", "status": "new"}, "field_y2k46__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_5", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1631239561464, "is_encrypted": false, "default_value": "", "label": "非必填文本", "field_num": 12, "api_name": "field_y2k46__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_2p5Ck__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1664348713842, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "人员", "type": "employee", "field_num": 67, "is_required": false, "wheres": [], "api_name": "field_2p5Ck__c", "define_type": "custom", "is_single": true, "is_index_field": false, "index_name": "a_16", "help_text": "", "status": "new"}, "field_P1maQ__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_many", "is_required": false, "options": [{"not_usable": false, "label": "必修I", "value": "TAnQ3YS1h"}, {"not_usable": false, "label": "必修II2222", "value": "0m7Fp4Y7t"}, {"not_usable": false, "label": "必修III", "value": "vTvr4wXOd"}, {"not_usable": false, "label": "必修IV", "value": "6y37Kk5N1"}, {"not_usable": false, "label": "必修V", "value": "assu3f7ri"}, {"not_usable": false, "label": "选修3-1", "value": "5C2qXod43"}, {"not_usable": false, "label": "选修3-2", "value": "nhz8q9rm1"}, {"not_usable": false, "label": "选修3-3", "value": "pQ1ZptAxC"}, {"not_usable": false, "label": "选修3-4", "value": "8by4gL22c"}, {"not_usable": false, "label": "选修3-5", "value": "0N25Ks9ot"}, {"not_usable": false, "label": "必修VIII", "value": "vb71n110Y"}], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "field_9I230__c", "index_name": "a_20", "is_index": true, "is_active": true, "create_time": 1678867295412, "is_encrypted": false, "option_type": "general", "default_value": [], "label": "多选科目", "field_num": 75, "option_api_name": "option_hDgaC__c", "api_name": "field_P1maQ__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_0us5a__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_2", "is_index": true, "is_active": true, "create_time": 1631178335373, "is_encrypted": false, "default_value": "", "label": "关联fj-主新建", "target_api_name": "object_p0I5m__c", "target_related_list_name": "target_related_list_329P1__c", "field_num": 11, "target_related_list_label": "关联fj-主新建", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_0us5a__c", "is_index_field": true, "help_text": "", "status": "new"}, "data_own_organization": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1647329933395, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属组织", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_organization", "define_type": "package", "is_single": true, "label_r": "归属组织", "is_index_field": false, "index_name": "a_10", "help_text": "", "status": "released"}, "field_js17w__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_13", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1663241630653, "is_encrypted": false, "default_value": "", "label": "收款方式", "field_num": 64, "api_name": "field_js17w__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_RF8w9__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "time", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "l_2", "is_index": true, "is_active": true, "create_time": 1637763269396, "is_encrypted": false, "default_value": "", "label": "时间", "time_zone": "GMT+8", "field_num": 18, "api_name": "field_RF8w9__c", "date_format": "HH:mm", "is_index_field": false, "help_text": "", "status": "new"}, "import_dep__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1675937731267, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "部门", "type": "department", "field_num": 48, "is_required": false, "wheres": [], "api_name": "import_dep__c", "define_type": "custom", "is_single": true, "is_index_field": false, "index_name": "a_18", "help_text": "", "status": "new"}, "field_j2727__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "index_name": "a_17", "is_index": true, "is_active": true, "create_time": 1672309055792, "is_encrypted": false, "label": "查找关联(多选)-渠道", "target_api_name": "ChannelObj", "target_related_list_name": "target_related_list_HGFcy__c", "field_num": 61, "target_related_list_label": "fj-UI事件主", "action_on_target_delete": "set_null", "api_name": "field_j2727__c", "is_index_field": false, "help_text": "", "status": "new"}, "field_le23l__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_many", "is_required": true, "options": [{"font_color": "#2a304d", "label": "唐山", "value": "KtuG2wfOk"}, {"font_color": "#2a304d", "label": "石家庄", "value": "20lzvA0zI"}, {"font_color": "#2a304d", "label": "秦皇岛", "value": "option1"}, {"font_color": "#2a304d", "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "index_name": "a_6", "field_api_name": "field_le23l__c", "is_index": true, "is_active": true, "create_time": 1631240601394, "is_encrypted": false, "option_type": "field", "default_value": [], "label": "必输多选", "field_num": 14, "option_api_name": "", "api_name": "field_le23l__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_2m6mJ__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "橘子子", "value": "xvOK4a72a"}, {"label": "苹果子", "value": "option1"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "field_N89H7__c", "index_name": "s_19", "field_api_name": "field_2m6mJ__c", "is_index": true, "is_active": true, "create_time": 1640226698680, "is_encrypted": false, "option_type": "field", "default_value": "", "label": "单选子", "field_num": 43, "option_api_name": "", "api_name": "field_2m6mJ__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_jt9F4__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_6", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1631239573879, "is_encrypted": false, "default_value": "", "label": "必填文本", "field_num": 13, "api_name": "field_jt9F4__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "last_modified_time": {"describe_api_name": "object_qep6N__c", "is_index": true, "create_time": 1631173218514, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "life_status": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"font_color": "#2a304d", "label": "未生效", "value": "ineffective"}, {"font_color": "#2a304d", "label": "审核中", "value": "under_review"}, {"font_color": "#2a304d", "label": "正常", "value": "normal"}, {"font_color": "#2a304d", "label": "变更中", "value": "in_change"}, {"font_color": "#2a304d", "label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "index_name": "s_16", "field_api_name": "life_status", "is_index": true, "is_active": true, "create_time": 1631173218516, "is_encrypted": false, "option_type": "field", "default_value": "normal", "label": "生命状态", "field_num": 3, "option_api_name": "", "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_w5j83__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "function", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 9, "operator": "IN", "field_name": "id", "field_values": ["func_Lt0vS__c"]}]}], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_3", "is_index": true, "is_active": true, "create_time": 1638693225234, "is_encrypted": false, "label": "whatlist查找关联", "target_api_name": "object_zl_whatlist__c", "target_related_list_name": "target_related_list_wWeeu__c", "field_num": 40, "target_related_list_label": "whatlist查找关联", "lookup_roles": ["0_1_r"], "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_w5j83__c", "is_index_field": true, "help_text": "", "status": "new"}, "out_tenant_id": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1631173218514, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "field_pcsz7__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "file_amount_limit": 1, "is_active": true, "create_time": 1644313841932, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "label": "附件2", "type": "file_attachment", "field_num": 25, "file_size_limit": 104857600, "is_required": false, "api_name": "field_pcsz7__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "a_7", "support_file_types": [], "help_text": "单个文件不得超过100M", "status": "new"}, "field_Sec0Z__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1667291335996, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "object_reference", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用关联渠道", "type": "quote", "quote_field": "field_0us5a__c__r.field_U3vPb__c", "is_required": false, "api_name": "field_Sec0Z__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "s_34", "is_show_mask": false, "help_text": "", "status": "new"}, "country__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1637918930367, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "国家", "type": "country", "field_num": 32, "used_in": "component", "is_required": false, "api_name": "country__c", "options": [], "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "s_7", "config": {}, "help_text": "", "status": "new"}, "field_3bzRz__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "index_name": "a_19", "is_index": true, "is_active": true, "create_time": 1677205262703, "is_encrypted": false, "label": "查找关联(多选)", "target_api_name": "object_31Eox__c", "target_related_list_name": "target_related_list_ooc0j__c", "field_num": 72, "target_related_list_label": "fj-UI事件主", "action_on_target_delete": "set_null", "api_name": "field_3bzRz__c", "is_index_field": false, "help_text": "", "status": "new"}, "field_N89H7__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"font_color": "#2a304d", "child_options": [{"field_2m6mJ__c": ["xvOK4a72a"]}], "label": "橙子", "value": "85ogUr7w1"}, {"font_color": "#2a304d", "child_options": [{"field_2m6mJ__c": ["option1"]}], "label": "苹果", "value": "mBUsdvaC1"}, {"font_color": "#2a304d", "child_options": [{"field_2m6mJ__c": ["xvOK4a72a", "option1"]}], "label": "香蕉", "value": "option1"}, {"font_color": "#2a304d", "child_options": [{"field_2m6mJ__c": ["xvOK4a72a", "option1"]}], "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "index_name": "s_18", "is_used_by_stage": true, "field_api_name": "field_N89H7__c", "is_index": true, "is_active": true, "create_time": 1631672391911, "is_encrypted": false, "option_type": "field", "default_value": "", "label": "种植水果单选", "field_num": 19, "option_api_name": "", "api_name": "field_N89H7__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_cjbdf__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1632291358190, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "select_many", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用爱好", "type": "quote", "quote_field": "field_0us5a__c__r.field_ki9p0__c", "is_required": false, "api_name": "field_cjbdf__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "a_3", "is_show_mask": false, "help_text": "", "status": "new"}, "field_19l9I__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_26", "is_index": true, "is_active": true, "create_time": 1658806602170, "is_encrypted": false, "label": "测试关闭显示字段对象", "target_api_name": "object_ls211__c", "target_related_list_name": "target_related_list_qKta1__c", "field_num": 56, "target_related_list_label": "测试关闭显示字段对象", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_19l9I__c", "is_index_field": true, "help_text": "", "status": "new"}, "field_OVvd2__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "file_amount_limit": 1, "is_active": true, "create_time": 1660807470591, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "label": "大附件", "type": "big_file_attachment", "field_num": 58, "file_size_limit": 10485760, "is_required": false, "api_name": "field_OVvd2__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "a_15", "support_file_types": [], "help_text": "", "status": "new"}, "field_2z74y__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "outer_owner", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_31", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "关联客户", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_oac0I__c", "field_num": 69, "target_related_list_label": "fj-UI事件主", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_2z74y__c", "is_index_field": true, "help_text": "", "status": "new"}, "field_333w2__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "select_one", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用最爱吃的蔬菜", "type": "quote", "quote_field": "field_0us5a__c__r.field_0qdI8__c", "field_num": 47, "is_required": false, "api_name": "field_333w2__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "s_12", "is_show_mask": false, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1631173218509, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_67Gan__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_many", "is_required": false, "options": [{"font_color": "#2a304d", "label": "中国", "value": "r761zJTNW"}, {"font_color": "#2a304d", "label": "越南", "value": "Fnl2C2p8c"}, {"font_color": "#2a304d", "label": "老挝", "value": "option1"}, {"font_color": "#2a304d", "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "index_name": "a_4", "field_api_name": "field_67Gan__c", "is_index": true, "is_active": true, "create_time": 1631240680001, "is_encrypted": false, "option_type": "field", "default_value": [], "label": "多选", "field_num": 15, "option_api_name": "", "api_name": "field_67Gan__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "district__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1637918930367, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "区", "type": "district", "field_num": 35, "used_in": "component", "is_required": false, "api_name": "district__c", "options": [], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "city__c", "is_index_field": false, "index_name": "s_8", "config": {}, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"font_color": "#2a304d", "label": "未锁定", "value": "0"}, {"font_color": "#2a304d", "label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "index_name": "s_13", "field_api_name": "lock_status", "is_index": true, "is_active": true, "create_time": 1631173218457, "is_encrypted": false, "option_type": "field", "default_value": "0", "label": "锁定状态", "field_num": 2, "option_api_name": "", "is_need_convert": false, "api_name": "lock_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "package": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1631173218514, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "create_time": {"describe_api_name": "object_qep6N__c", "is_index": true, "create_time": 1631173218514, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "field_9y2wU__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "N", "field_name": "field_C2atD__c", "field_values": ["true"]}]}], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_25", "is_index": true, "is_active": true, "create_time": 1657807310858, "is_encrypted": false, "label": "关联渠道", "target_api_name": "ChannelObj", "target_related_list_name": "target_related_list_m16zz__c", "field_num": 55, "target_related_list_label": "关联渠道", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_9y2wU__c", "is_index_field": true, "help_text": "", "status": "new"}, "field_4u04f__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1655985112220, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": [], "label": "企业人数", "type": "select_many", "field_num": 53, "is_required": false, "api_name": "field_4u04f__c", "options": [{"label": "小于100", "value": "3fU210hlL"}, {"label": "大于100小于500", "value": "25zTq7z06"}, {"label": "大于500", "value": "option1"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "a_13", "config": {}, "field_api_name": "field_4u04f__c", "help_text": "", "status": "new"}, "province__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "province", "used_in": "component", "is_required": false, "options": [], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "country__c", "index_name": "s_11", "is_index": true, "is_active": true, "create_time": 1637918930367, "is_encrypted": false, "default_value": "", "label": "省", "field_num": 33, "api_name": "province__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "created_by": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1631173218514, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released", "description": ""}, "relevant_team": {"describe_api_name": "object_qep6N__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1645013474188, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "data_own_department": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1631173218514, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_department", "define_type": "package", "is_single": true, "label_r": "归属部门", "is_index_field": false, "index_name": "data_owner_dept_id", "help_text": "", "status": "released"}, "field_Ke821__c": {"expression_type": "js", "return_type": "text", "describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "t_11", "is_index": true, "is_active": true, "expression": "$created_by__r.qq_account$", "create_time": *************, "is_encrypted": false, "label": "计算字段1", "field_num": 50, "api_name": "field_Ke821__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_aorbk__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "测试英雄", "value": "C3mG8ou09"}, {"label": "妖姬", "value": "35frh44C1"}, {"label": "奶妈", "value": "1FX8kBvb1"}, {"label": "机器人", "value": "2om18ACSv"}, {"label": "女警", "value": "26e1wzry1"}, {"label": "阿木木", "value": "A3fx10XQ4"}, {"label": "死歌", "value": "d6md415kI"}, {"label": "大虫子", "value": "gDvjyMSxq"}, {"label": "奥巴马", "value": "60Uwb52e9"}, {"label": "劫", "value": "14w6q0dsb"}, {"label": "诺手", "value": "8lOQS7ynx"}, {"label": "盲僧", "value": "Aw8L4Hzfw"}, {"label": "奇亚娜", "value": "d1Pk2jFWa"}, {"label": "莫甘娜", "value": "option1"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "field_16Yl4__c", "index_name": "s_22", "field_api_name": "field_aorbk__c", "is_index": true, "is_active": true, "create_time": 1644918178936, "is_encrypted": false, "default_value": "", "label": "英雄", "field_num": 27, "api_name": "field_aorbk__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_n4D12__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1634805444473, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "text", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "单行文本引用", "type": "quote", "quote_field": "field_0us5a__c__r.field_t0891__c", "is_required": false, "api_name": "field_n4D12__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "t_3", "is_show_mask": false, "help_text": "", "status": "new"}, "field_5I5Ru__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_1", "is_index": true, "is_active": true, "create_time": 1634183910075, "is_encrypted": false, "default_value": "", "label": "关联fj-测试负责人", "target_api_name": "object_L612u__c", "target_related_list_name": "target_related_list_z3RFA__c", "field_num": 21, "target_related_list_label": "关联fj-测试负责人", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_5I5Ru__c", "is_index_field": true, "help_text": "", "status": "new"}, "field_9xG4P__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": true, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "t_2", "max_length": 2000, "is_index": true, "is_active": true, "create_time": 1638794001867, "is_encrypted": false, "min_length": 0, "default_value": "$field_y2k46__c$", "label": "多行文本", "field_num": 41, "api_name": "field_9xG4P__c", "is_index_field": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1631173218607, "is_encrypted": false, "default_value": "", "label": "主属性", "api_name": "name", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "_id": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1631173218514, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}, "field_1uR3x__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "5", "value": "8W9bG226K"}, {"label": "4", "value": "ae40LeKU2"}, {"label": "3", "value": "E4fa88jz2"}, {"label": "2", "value": "w32iV0hc2"}, {"label": "1", "value": "option1"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "field_16Yl4__c", "index_name": "s_23", "field_api_name": "field_1uR3x__c", "is_index": true, "is_active": true, "create_time": 1644918219605, "is_encrypted": false, "default_value": "", "label": "排位所在位置", "field_num": 28, "api_name": "field_1uR3x__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_6lubk__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1670382431178, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "district", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用区", "type": "quote", "quote_field": "field_0us5a__c__r.field_IN0Jz__c", "is_required": false, "api_name": "field_6lubk__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "s_36", "is_show_mask": false, "help_text": "", "status": "new"}, "field_YzEm3__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_9", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1641544227864, "is_encrypted": false, "default_value": "", "label": "地区定位赋值UI事件触发字段", "field_num": 45, "api_name": "field_YzEm3__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "town__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1641544515165, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "乡镇", "type": "town", "field_num": 46, "used_in": "component", "is_required": false, "api_name": "town__c", "options": [], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "district__c", "is_index_field": false, "index_name": "s_20", "config": {}, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1645013474136, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "where_type": "field", "label": "加锁人", "type": "employee", "field_num": 4, "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_2", "help_text": "", "status": "new"}, "field_1k25V__c": {"describe_api_name": "object_qep6N__c", "prefix": "", "auto_adapt_places": false, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "is_required": false, "define_type": "custom", "postfix": "", "is_single": false, "index_name": "s_32", "is_index": true, "is_active": true, "auto_number_type": "function", "create_time": 1667187100571, "is_encrypted": false, "serial_number": 2, "default_value": "01", "label": "编号V1", "field_num": 70, "condition": "NONE", "api_name": "field_1k25V__c", "func_api_name": "AutoNum__c", "is_index_field": false, "help_text": "", "status": "new"}, "partner_id": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "合作伙伴", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "outer_owner", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "index_name": "s_4", "is_index": true, "is_active": true, "create_time": 1638244461388, "is_encrypted": false, "label": "合作伙伴", "target_api_name": "PartnerObj", "target_related_list_name": "partner_object_qep6n__c_list", "field_num": 38, "target_related_list_label": "合作伙伴", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "partner_id", "is_index_field": true, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "object_qep6N__c", "is_index": false, "create_time": 1631173218514, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "field_5LUfm__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_40", "is_index": true, "is_active": true, "create_time": 1676969023597, "is_encrypted": false, "label": "查找关联-自己", "target_api_name": "object_qep6N__c", "target_related_list_name": "target_related_list_lq1w2__c", "field_num": 68, "target_related_list_label": "fj-UI事件主", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_5LUfm__c", "is_index_field": true, "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1631173218514, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "out_owner": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1631173218514, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released", "description": ""}, "field_16Yl4__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1644917997210, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "位置选择", "type": "select_one", "field_num": 26, "is_required": false, "api_name": "field_16Yl4__c", "options": [{"child_options": [{"field_aorbk__c": [], "field_1uR3x__c": ["8W9bG226K", "ae40LeKU2"]}], "label": "辅助", "value": "vbQKTQ169"}, {"child_options": [{"field_aorbk__c": ["A3fx10XQ4", "d6md415kI", "gDvjyMSxq", "Aw8L4Hzfw"], "field_1uR3x__c": ["E4fa88jz2", "w32iV0hc2"]}], "label": "打野", "value": "U1Ms5aKM4"}, {"child_options": [{"field_aorbk__c": ["26e1wzry1", "60Uwb52e9"], "field_1uR3x__c": ["ae40LeKU2", "E4fa88jz2"]}], "label": "射手", "value": "276tebcDS"}, {"child_options": [{"field_aorbk__c": ["14w6q0dsb", "d6md415kI", "35frh44C1"], "field_1uR3x__c": ["w32iV0hc2", "E4fa88jz2", "option1"]}], "label": "中路", "value": "C3mG8ou09"}, {"child_options": [{"field_aorbk__c": ["gDvjyMSxq", "60Uwb52e9", "8lOQS7ynx"], "field_1uR3x__c": ["option1", "w32iV0hc2"]}], "label": "上路", "value": "option1"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "s_21", "config": {}, "field_api_name": "field_16Yl4__c", "help_text": "", "status": "new"}, "out_resources": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "外部来源", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "代理通", "value": "partner"}], "define_type": "package", "is_single": false, "cascade_parent_api_name": "field_N89H7__c", "index_name": "s_14", "field_api_name": "out_resources", "is_index": false, "is_active": true, "create_time": 1638244461425, "is_encrypted": false, "option_type": "field", "default_value": "", "label": "外部来源", "field_num": 39, "option_api_name": "", "is_need_convert": false, "api_name": "out_resources", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_uVre4__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1654656482006, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "default_value": "", "label": "部门(多选)", "type": "department_many", "field_num": 51, "is_required": false, "wheres": [], "api_name": "field_uVre4__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "a_11", "help_text": "", "status": "new"}, "owner": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1631173218508, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "label_r": "负责人", "is_index_field": false, "index_name": "owner", "help_text": "", "status": "new"}, "address__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_7", "max_length": 300, "is_index": true, "is_active": true, "create_time": 1637918930367, "is_encrypted": false, "default_value": "", "label": "详细地址", "field_num": 36, "api_name": "address__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_BzE8k__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_41", "is_index": true, "is_active": true, "create_time": 1677205262704, "is_encrypted": false, "label": "查找关联1", "target_api_name": "object_l835k__c", "target_related_list_name": "target_related_list_g46F2__c", "field_num": 73, "target_related_list_label": "fj-UI事件主", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_BzE8k__c", "is_index_field": true, "help_text": "", "status": "new"}, "city__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "city", "used_in": "component", "is_required": false, "options": [], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "province__c", "index_name": "s_6", "is_index": true, "is_active": true, "create_time": 1637918930367, "is_encrypted": false, "default_value": "", "label": "市", "field_num": 34, "api_name": "city__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_0uowd__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1670382369282, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "country", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用国家", "type": "quote", "quote_field": "field_0us5a__c__r.field_ob8e1__c", "is_required": false, "api_name": "field_0uowd__c", "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "s_35", "is_show_mask": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1631173218514, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released", "description": ""}, "field_twIIy__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1655985016721, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": [], "label": "客户级别", "type": "select_many", "field_num": 52, "is_required": false, "api_name": "field_twIIy__c", "options": [{"label": "普通客户", "value": "6D2eb2Wa5"}, {"label": "vip客户", "value": "bjS0e03em"}, {"label": "重要客户", "value": "option1"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "a_12", "config": {}, "field_api_name": "field_twIIy__c", "help_text": "", "status": "new"}, "mc_currency": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "BHD - <PERSON><PERSON>", "value": "BHD"}, {"not_usable": true, "label": "BDT - 孟加拉国塔卡", "value": "BDT"}, {"not_usable": true, "label": "BGN - Bulgarian Lev", "value": "BGN"}, {"label": "AWG - 阿鲁巴岛弗罗林", "value": "AWG"}, {"label": "USD - U.S. Dollar", "value": "USD"}, {"label": "AED - UAE Dirham", "value": "AED"}, {"label": "ALL - 阿尔巴尼亚列克", "value": "ALL"}, {"label": "AOA - 安哥拉宽扎", "value": "AOA"}, {"label": "BAM - 自由兑换马克", "value": "BAM"}, {"label": "ARS - 阿根廷比索", "value": "ARS"}, {"label": "AMD - 亚美尼亚打兰", "value": "AMD"}, {"label": "AUD - Australian Dollar", "value": "AUD"}, {"label": "ANG - 荷属安地列斯盾", "value": "ANG"}, {"label": "BBD - 巴巴多斯元", "value": "BBD"}, {"label": "AFN - Afghanistan Afghani (New)", "value": "AFN"}, {"label": "AZN - 阿塞拜疆马纳特", "value": "AZN"}, {"label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "币种", "index_name": "s_17", "field_api_name": "mc_currency", "is_index": true, "is_active": true, "create_time": 1631173218518, "is_encrypted": false, "option_type": "field", "default_value": "", "label": "币种", "field_num": 5, "option_api_name": "", "api_name": "mc_currency", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_oo82r__c": {"describe_api_name": "object_qep6N__c", "is_index": false, "is_active": true, "create_time": 1637918930214, "is_encrypted": false, "is_support_town": true, "auto_adapt_places": false, "description": "", "is_unique": false, "group_type": "area", "label": "地区定位", "type": "group", "is_required": false, "api_name": "field_oo82r__c", "define_type": "custom", "is_single": false, "is_index_field": false, "fields": {"area_country": "country__c", "area_location": "field_EgkrH__c", "area_town": "town__c", "area_detail_address": "address__c", "area_city": "city__c", "area_province": "province__c", "area_district": "district__c"}, "index_name": "s_9", "help_text": "", "status": "new"}, "record_type": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1631173218515, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "font_color": "#2a304d", "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型", "value": "default__c"}, {"is_active": true, "api_name": "record_4Oreg__c", "label": "树形视图验证", "value": "record_4Oreg__c"}, {"is_active": true, "api_name": "record_fHdQx__c", "label": "UI事件业务类型", "value": "record_fHdQx__c"}, {"is_active": true, "api_name": "record_7ExSf__c", "label": "地位字段", "value": "record_7ExSf__c"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "field_l193L__c": {"describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "type": "signature", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "a_14", "support_file_types": [], "is_index": true, "file_amount_limit": 10, "is_active": true, "create_time": 1660794520613, "is_encrypted": false, "label": "签名字段", "is_watermark": false, "field_num": 57, "file_size_limit": 10485760, "api_name": "field_l193L__c", "is_index_field": false, "help_text": "", "status": "new"}, "field_7pw11__c": {"describe_api_name": "object_qep6N__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "l_1", "is_index": true, "is_active": true, "create_time": 1637562305756, "is_encrypted": false, "default_value": "", "label": "发钱日期", "time_zone": "GMT+8", "field_num": 17, "api_name": "field_7pw11__c", "date_format": "yyyy-MM-dd", "is_index_field": false, "help_text": "", "status": "new"}, "field_I2WHc__c": {"expression_type": "js", "return_type": "text", "describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "t_10", "is_index": false, "is_active": true, "expression": "$currentDate__g$", "create_time": 1654074140990, "is_encrypted": false, "label": "计算字段", "field_num": 49, "api_name": "field_I2WHc__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_3xo1m__c": {"expression_type": "js", "return_type": "number", "describe_api_name": "object_qep6N__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_3", "is_index": true, "is_active": true, "expression": "IF($field_N89H7__c._label$ == '苹果', 10, 20)", "create_time": 1639992649126, "is_encrypted": false, "label": "计算字段-使用单选", "field_num": 42, "api_name": "field_3xo1m__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "field_e3z2j__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1646122048687, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": [], "label": "多选111", "type": "select_many", "field_num": 29, "is_required": false, "api_name": "field_e3z2j__c", "options": [{"label": "示例选项", "value": "option1"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "a_8", "config": {}, "field_api_name": "field_e3z2j__c", "help_text": "", "status": "new"}, "field_2Cn77__c": {"describe_api_name": "object_qep6N__c", "is_index": true, "is_active": true, "create_time": 1632446892943, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": true, "label": "布尔值", "type": "true_or_false", "field_num": 20, "is_required": false, "api_name": "field_2Cn77__c", "options": [{"font_color": "#2a304d", "label": "是", "value": true}, {"font_color": "#2a304d", "label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_index_field": false, "index_name": "b_1", "help_text": "", "status": "new"}}, "release_version": "6.4", "actions": {}}