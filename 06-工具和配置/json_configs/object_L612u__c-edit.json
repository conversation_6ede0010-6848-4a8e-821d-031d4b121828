{"buttons": [], "components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_3M32q__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_TlXOr__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "表单组件", "nameI18nKey": "paas.udobj.form_component", "type": "form", "order": 4}, {"field_section": [], "buttons": [], "api_name": "head_info", "button_info": [{"hidden": [], "page_type": "create", "render_type": "normal", "order": ["Add_Save_button_default", "Add_Save_Continue_button_default", "Add_Save_Draft_button_default"]}, {"hidden": [], "page_type": "edit", "render_type": "normal", "order": ["Edit_Save_button_default"]}], "limit": 1, "header": "标题和按钮", "define_type": "general", "nameI18nKey": "paas.udobj.head_info", "type": "simple", "order": 3}], "last_modified_time": 1678345273264, "is_deleted": false, "version": 3, "create_time": 1634545126655, "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "edit_layout_Bpz2z__c", "what_api_name": null, "default_component": null, "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["form_component"]], "columns": [{"width": "100%"}]}]}, "created_by": "1000", "display_name": "默认布局(新建/编辑页)", "is_default": true, "last_modified_by": "1000", "layout_type": "edit", "package": "CRM", "ref_object_api_name": "object_L612u__c", "tenant_id": "74255", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": [], "namespace": null, "enable_mobile_layout": false, "events": []}