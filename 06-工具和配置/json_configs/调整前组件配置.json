[{"api_name": "relevant_team_component", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list"}, {"api_name": "operation_log", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record"}, {"components": [["form_component"], ["object_brd33__c_md_group_component"], ["operation_log"], ["BPM_related_list"], ["Approval_related_list"], ["SalesOrderObj_field_iUubu__c_related_list"], ["object_13qL1__c_field_irtoB__c_related_list"], ["object_8P92A__c_field_DtnzG__c_related_list"], ["object_d1bXP__c_field_ymbz3__c_related_list"], ["object_75fv7__c_field_bc7QN__c_related_list"], ["object_r8250__c_field_hsh6b__c_related_list"], ["object_71VIM__c_field_582RA__c_related_list"], ["object_JvFf4__c_field_GOjZv__c_related_list"], ["object_ACp4Y__c_field_rP4Ya__c_related_list"], ["object_iF3d2__c_field_9w9n0__c_related_list"], ["object_1c47g__c_field_4Xp1s__c_related_list"], ["object_h2ubH__c_field_5iv2O__c_related_list"], ["object_iF3d2__c_field_oso6A__c_related_list"], ["object_c2Ia2__c_field_Ng8Vz__c_related_list"], ["object_iF3d2__c_field_x4Bb9__c_related_list"]], "api_name": "tabs_7hISm__c", "tabs": [{"api_name": "form_component_qgkhd__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "tab_object_brd33__c_md_group_component", "header": "合同明细", "nameI18nKey": "object_brd33__c.field.field_oZ5Wk__c.reference_label"}, {"api_name": "operation_log_4796u__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "BPM_related_list_w4uo8__c", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_tMjH2__c", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "tab_SalesOrderObj_field_iUubu__c_related_list", "header": "订单排产申请", "nameI18nKey": "SalesOrderObj.field.field_iUubu__c.reference_label"}, {"api_name": "tab_object_13qL1__c_field_irtoB__c_related_list", "header": "合同变更申请", "nameI18nKey": "object_13qL1__c.field.field_irtoB__c.reference_label"}, {"api_name": "tab_object_8P92A__c_field_DtnzG__c_related_list", "header": "履约保证金", "nameI18nKey": "object_8P92A__c.field.field_DtnzG__c.reference_label"}, {"api_name": "tab_object_d1bXP__c_field_ymbz3__c_related_list", "header": "开口合同执行子合同", "nameI18nKey": "object_d1bXP__c.field.field_ymbz3__c.reference_label"}, {"api_name": "tab_object_75fv7__c_field_bc7QN__c_related_list", "header": "开票申请", "nameI18nKey": "object_75fv7__c.field.field_bc7QN__c.reference_label"}, {"api_name": "tab_object_r8250__c_field_hsh6b__c_related_list", "header": "质量保函", "nameI18nKey": "object_r8250__c.field.field_hsh6b__c.reference_label"}, {"api_name": "tab_object_71VIM__c_field_582RA__c_related_list", "header": "调整账期申请", "nameI18nKey": "object_71VIM__c.field.field_582RA__c.reference_label"}, {"api_name": "tab_object_JvFf4__c_field_GOjZv__c_related_list", "header": "发货单（NC）", "nameI18nKey": "object_JvFf4__c.field.field_GOjZv__c.reference_label"}, {"api_name": "tab_object_ACp4Y__c_field_rP4Ya__c_related_list", "header": "票据收取", "nameI18nKey": "object_ACp4Y__c.field.field_rP4Ya__c.reference_label"}, {"api_name": "tab_object_iF3d2__c_field_9w9n0__c_related_list", "header": "发货申请", "nameI18nKey": "object_iF3d2__c.field.field_9w9n0__c.reference_label"}, {"api_name": "tab_object_1c47g__c_field_4Xp1s__c_related_list", "header": "业务结算价格审批表", "nameI18nKey": "object_1c47g__c.field.field_4Xp1s__c.reference_label"}, {"api_name": "tab_object_h2ubH__c_field_5iv2O__c_related_list", "header": "结算明细清单", "nameI18nKey": "object_h2ubH__c.field.field_5iv2O__c.reference_label"}, {"api_name": "tab_object_iF3d2__c_field_oso6A__c_related_list", "header": "发货申请（多选）", "nameI18nKey": "object_iF3d2__c.field.field_oso6A__c.reference_label"}, {"api_name": "tab_object_c2Ia2__c_field_Ng8Vz__c_related_list", "header": "套保申请", "nameI18nKey": "object_c2Ia2__c.field.field_Ng8Vz__c.reference_label"}, {"api_name": "tab_object_iF3d2__c_field_x4Bb9__c_related_list", "header": "合同", "nameI18nKey": "object_iF3d2__c.field.field_x4Bb9__c.reference_label"}], "header": "页签容器", "type": "tabs"}, {"buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "custom", "api_name": "button_wRzg5__c", "label": "生成订单排产", "isActive": true}, {"action_type": "custom", "api_name": "button_pb2wr__c", "label": "合同变更申请", "isActive": true}, {"action_type": "custom", "api_name": "button_46fUQ__c", "label": "打印盖章日期", "isActive": true}, {"action_type": "custom", "api_name": "button_s2U5n__c", "label": "合同归档", "isActive": true}, {"action_type": "custom", "api_name": "button_g424V__c", "label": "合同终止", "isActive": true}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "custom", "api_name": "button_1WcoU__c", "label": "标签", "isActive": true}, {"action_type": "custom", "api_name": "button_zNeug__c", "label": "铜价", "isActive": true}, {"action_type": "custom", "api_name": "button_6xYX1__c", "label": "付款方式", "isActive": true}, {"action_type": "custom", "api_name": "button_1Tp0g__c", "label": "优惠幅度", "isActive": true}, {"action_type": "custom", "api_name": "button_iisgp__c", "label": "导入更新审批", "isActive": true}, {"action_type": "custom", "api_name": "button_cdpun__c", "label": "报价员修改", "isActive": true}, {"action_type": "custom", "api_name": "button_CNH07__c", "label": "合同状态", "isActive": true}, {"action_type": "default", "api_name": "ChangePartner_button_default", "label": "更换合作伙伴"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "DeletePartner_button_default", "label": "移除合作伙伴"}, {"action_type": "custom", "api_name": "button_os1RS__c", "label": "合同付款方式", "isActive": true}, {"action_type": "custom", "api_name": "button_95Fo6__c", "label": "发货地址", "isActive": true}, {"action_type": "custom", "api_name": "button_6IDog__c", "label": "特价申请关联", "isActive": true}, {"action_type": "custom", "api_name": "button_yuerw__c", "label": "项目名称", "isActive": true}], "api_name": "head_info", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 4, "type": "simple"}, {"field_section": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "record_type", "field_name": "record_type"}, {"render_type": "object_reference", "field_name": "field_bZzzh__c"}, {"render_type": "object_reference", "field_name": "field_45wCG__c"}, {"render_type": "object_reference", "field_name": "field_415M8__c"}, {"render_type": "count", "field_name": "field_amount__c"}, {"render_type": "count", "field_name": "field_ae63d__c"}], "api_name": "top_info", "header": "摘要信息", "type": "top_info"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "field_c1TS5__c"}, {"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "field_45wCG__c"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "field_2K6SP__c"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "field_21Hi9__c"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "field_bZzzh__c"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "field_7dvw8__c"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_rlaew__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_quote_no__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_x22eD__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_415M8__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_xo1j5__c"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "field_o6d1S__c"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "field_uzYax__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_StK1X__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_u78g5__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_quY1y__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_326Ah__c"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "field_v4oLm__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "long_text", "field_name": "field_Q10W4__c"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "field_2Tar4__c"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "field_4zj88__c"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "field_0475K__c"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "field_lmoAU__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_gd4e1__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "long_text", "field_name": "field_619eh__c"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "field_cd52d__c"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_many", "field_name": "field_Rkczn__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "field_I2myv__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_amount__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_ae63d__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_l8kcV__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_QhU4m__c"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "field_ms3al__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_trf2z__c"}, {"is_readonly": true, "is_required": false, "render_type": "percentile", "field_name": "field_HehvQ__c"}, {"is_readonly": false, "is_required": false, "render_type": "percentile", "field_name": "field_S1h40__c"}, {"is_readonly": false, "is_required": true, "render_type": "percentile", "field_name": "field_4Vv1O__c"}, {"is_readonly": false, "is_required": true, "render_type": "file_attachment", "field_name": "field_R41xc__c"}, {"is_readonly": false, "is_required": true, "render_type": "currency", "field_name": "field_Om26I__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_2c7zf__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_c9kt9__c"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "field_Doc2l__c"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "field_81Jdd__c"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "field_rM324__c"}, {"is_readonly": false, "is_required": true, "render_type": "phone_number", "field_name": "field_J92vn__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_72HJn__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_c2aJD__c"}, {"is_readonly": false, "is_required": false, "render_type": "phone_number", "field_name": "field_5d2hB__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_m11aZ__c"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "field_LPtkz__c"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "field_6tlFb__c"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "field_v2jN1__c"}, {"is_readonly": false, "is_required": false, "render_type": "file_attachment", "field_name": "field_VwA47__c"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "field_qGpbG__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_y3e2i__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_e2eN1__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "partner_id"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "out_resources"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "field_j6kVV__c"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "field_r17tm__c"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "field_u57Pf__c"}, {"is_readonly": false, "is_required": false, "render_type": "percentile", "field_name": "field_3iMyk__c"}, {"is_readonly": false, "is_required": false, "render_type": "percentile", "field_name": "field_A2oC9__c"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "field_Mh3X5__c"}, {"is_readonly": false, "is_required": false, "render_type": "file_attachment", "field_name": "field_0dKd9__c"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "field_mEwWl__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_V6fE2__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "field_xGENR__c"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "field_evVSf__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_P86yZ__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_Lp270__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_xta50__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_u23Ak__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "country", "field_name": "field_luwTL__c"}, {"is_readonly": false, "is_required": true, "render_type": "province", "field_name": "field_mw1Ay__c"}, {"is_readonly": false, "is_required": true, "render_type": "city", "field_name": "field_nh52i__c"}, {"is_readonly": false, "is_required": true, "render_type": "district", "field_name": "field_10cut__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_ZRL2l__c"}, {"is_readonly": false, "is_required": false, "render_type": "location", "field_name": "field_idpMk__c"}], "api_name": "field_2xvbe__c", "tab_index": "ltr", "column": 2, "header": "地区定位"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "field_v2K7A__c"}, {"is_readonly": false, "is_required": false, "render_type": "percentile", "field_name": "field_DJ1dS__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_L8tS1__c"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "field_RUf3A__c"}], "api_name": "group_zv5j3__c", "tab_index": "ltr", "column": 2, "header": "合同执行情况"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_organization"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}], "api_name": "group_5982R__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "_id": "form_component", "type": "form", "grayLimit": 1}, {"api_name": "object_brd33__c_md_group_component", "related_list_name": "target_related_list_37Y18__c", "button_info": [{"hidden": [], "render_type": "list_normal", "order": []}], "ref_object_api_name": "object_brd33__c", "header": "合同明细", "nameI18nKey": "object_brd33__c.field.field_oZ5Wk__c.reference_label", "type": "multi_table", "display_rule": false, "field_api_name": "field_oZ5Wk__c"}, {"relationType": 2, "api_name": "object_13qL1__c_field_irtoB__c_related_list", "related_list_name": "target_related_list_6x411__c", "ref_object_api_name": "object_13qL1__c", "header": "合同变更申请", "nameI18nKey": "object_13qL1__c.field.field_irtoB__c.reference_label", "type": "relatedlist", "field_api_name": "field_irtoB__c"}, {"relationType": 2, "api_name": "SalesOrderObj_field_iUubu__c_related_list", "related_list_name": "target_related_list_r9Ac9__c", "ref_object_api_name": "SalesOrderObj", "header": "订单排产申请", "nameI18nKey": "SalesOrderObj.field.field_iUubu__c.reference_label", "type": "relatedlist", "field_api_name": "field_iUubu__c"}, {"relationType": 2, "api_name": "object_8P92A__c_field_DtnzG__c_related_list", "related_list_name": "target_related_list_c2984__c", "ref_object_api_name": "object_8P92A__c", "header": "履约保证金", "nameI18nKey": "object_8P92A__c.field.field_DtnzG__c.reference_label", "type": "relatedlist", "field_api_name": "field_DtnzG__c"}, {"relationType": 2, "api_name": "object_d1bXP__c_field_ymbz3__c_related_list", "related_list_name": "target_related_list_a7462__c", "ref_object_api_name": "object_d1bXP__c", "header": "开口合同执行子合同", "nameI18nKey": "object_d1bXP__c.field.field_ymbz3__c.reference_label", "type": "relatedlist", "field_api_name": "field_ymbz3__c"}, {"relationType": 2, "api_name": "object_75fv7__c_field_bc7QN__c_related_list", "related_list_name": "target_related_list_j72UK__c", "ref_object_api_name": "object_75fv7__c", "header": "开票申请", "nameI18nKey": "object_75fv7__c.field.field_bc7QN__c.reference_label", "type": "relatedlist", "field_api_name": "field_bc7QN__c"}, {"api_name": "object_r8250__c_field_hsh6b__c_related_list", "related_list_name": "target_related_list_q293O__c", "ref_object_api_name": "object_r8250__c", "header": "质量保函", "nameI18nKey": "object_r8250__c.field.field_hsh6b__c.reference_label", "field_api_name": "field_hsh6b__c"}, {"api_name": "object_71VIM__c_field_582RA__c_related_list", "related_list_name": "target_related_list_T8PhB__c", "ref_object_api_name": "object_71VIM__c", "header": "调整账期申请", "nameI18nKey": "object_71VIM__c.field.field_582RA__c.reference_label", "field_api_name": "field_582RA__c"}, {"api_name": "object_ACp4Y__c_field_rP4Ya__c_related_list", "related_list_name": "target_related_list_yl24b__c", "ref_object_api_name": "object_ACp4Y__c", "header": "票据收取", "nameI18nKey": "object_ACp4Y__c.field.field_rP4Ya__c.reference_label", "field_api_name": "field_rP4Ya__c"}, {"api_name": "object_JvFf4__c_field_GOjZv__c_related_list", "related_list_name": "target_related_list_orXRn__c", "ref_object_api_name": "object_JvFf4__c", "header": "发货单（NC）", "nameI18nKey": "object_JvFf4__c.field.field_GOjZv__c.reference_label", "field_api_name": "field_GOjZv__c"}, {"api_name": "object_iF3d2__c_field_9w9n0__c_related_list", "related_list_name": "target_related_list_8V8LP__c", "ref_object_api_name": "object_iF3d2__c", "header": "发货申请", "nameI18nKey": "object_iF3d2__c.field.field_9w9n0__c.reference_label", "field_api_name": "field_9w9n0__c"}, {"api_name": "object_1c47g__c_field_4Xp1s__c_related_list", "related_list_name": "target_related_list_YpMv1__c", "ref_object_api_name": "object_1c47g__c", "header": "业务结算价格审批表", "nameI18nKey": "object_1c47g__c.field.field_4Xp1s__c.reference_label", "field_api_name": "field_4Xp1s__c"}, {"api_name": "object_h2ubH__c_field_5iv2O__c_related_list", "related_list_name": "target_related_list_Joqss__c", "ref_object_api_name": "object_h2ubH__c", "header": "结算明细清单", "nameI18nKey": "object_h2ubH__c.field.field_5iv2O__c.reference_label", "field_api_name": "field_5iv2O__c"}, {"api_name": "object_iF3d2__c_field_oso6A__c_related_list", "related_list_name": "target_related_list_922qh__c", "ref_object_api_name": "object_iF3d2__c", "header": "发货申请（多选）", "nameI18nKey": "object_iF3d2__c.field.field_oso6A__c.reference_label", "field_api_name": "field_oso6A__c"}, {"api_name": "object_c2Ia2__c_field_Ng8Vz__c_related_list", "related_list_name": "target_related_list_gu2tD__c", "ref_object_api_name": "object_c2Ia2__c", "header": "套保申请", "nameI18nKey": "object_c2Ia2__c.field.field_Ng8Vz__c.reference_label", "field_api_name": "field_Ng8Vz__c"}, {"api_name": "object_iF3d2__c_field_x4Bb9__c_related_list", "related_list_name": "target_related_list_ZUkHj__c", "header": "合同", "nameI18nKey": "object_iF3d2__c.field.field_x4Bb9__c.reference_label", "field_api_name": "field_x4Bb9__c"}, {"api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "grayLimit": 1}, {"api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "Approval", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "grayLimit": 1}, {"related_list_name": "", "Title": "合同执行", "CardID": "BI_62d06386f1f5c60001c22eae", "type": "chart", "isCategory": 2, "chart_type": "card", "incoming_main_attribute": true, "api_name": "chart_W82u4__c", "header": "合同执行", "style": {"height": "216px"}, "grayLimit": 1}]