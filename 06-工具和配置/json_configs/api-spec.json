{"openapi": "3.0.0", "info": {"title": "CRM API", "version": "v1", "description": "CRM系统API接口定义"}, "servers": [{"url": "https://crm.ceshi112.com/FHH/EM1HNCRM/API/v1", "description": "CRM API服务器"}], "paths": {"/object/assistant_and_app_consider__c/controller/ValidRecordType": {"get": {"summary": "验证记录类型", "description": "验证assistant_and_app_consider__c对象的记录类型", "operationId": "validateRecordType", "parameters": [{"name": "_fs_token", "in": "query", "required": true, "schema": {"type": "string"}, "description": "认证令牌"}, {"name": "traceId", "in": "query", "required": true, "schema": {"type": "string"}, "description": "追踪ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"describeApiName": {"type": "string", "description": "API名称", "example": "assistant_and_app_consider__c"}, "is_only_active": {"type": "boolean", "description": "是否仅活动状态", "example": true}, "seriesId": {"type": "string", "description": "系列ID", "example": "879f2e2859bc455e820a48c7e33bb7b7"}}, "required": ["describeApiName", "is_only_active", "seriesId"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "message": {"type": "string"}}}}}}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}}}, "components": {"securitySchemes": {"cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "FSAuthX"}, "fsToken": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "_fs_token"}}}, "security": [{"cookieAuth": [], "fsToken": []}]}