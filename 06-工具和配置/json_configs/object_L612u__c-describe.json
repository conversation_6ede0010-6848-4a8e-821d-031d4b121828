{"tenant_id": "74255", "package": "CRM", "is_active": true, "last_modified_time": 1664172259146, "create_time": 1633946020242, "description": "", "last_modified_by": "1000", "display_name": "fj-测试负责人", "created_by": "1000", "version": 10, "is_open_display_name": false, "index_version": 200, "icon_index": 0, "is_deleted": false, "api_name": "object_L612u__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "oeI", "fields": {"tenant_id": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020242, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020154, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_1", "status": "new"}, "data_own_organization": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1647329879305, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "归属组织", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_organization", "define_type": "package", "is_single": true, "label_r": "归属组织", "is_index_field": false, "index_name": "a_2", "status": "released", "description": ""}, "lock_user": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020205, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 4, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_1", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "object_L612u__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_1", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1633946020244, "is_encrypted": false, "length": 10, "label": "汇率", "field_num": 6, "api_name": "mc_exchange_rate", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "object_L612u__c", "is_index": false, "create_time": 1633946020242, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020206, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 7, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "label_r": "作废前生命状态", "is_index_field": false, "index_name": "t_1", "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020242, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "object_L612u__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1633946020234, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "out_owner": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020242, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released", "description": ""}, "mc_functional_currency": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020245, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "本位币", "type": "select_one", "field_num": 9, "is_required": false, "api_name": "mc_functional_currency", "options": [{"label": "CNY - 人民币", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "本位币", "is_index_field": false, "index_name": "s_4", "config": {}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020233, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "label_r": "负责人", "is_index_field": false, "index_name": "owner", "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020205, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "field_num": 2, "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "is_index_field": false, "index_name": "s_2", "config": {}, "status": "new"}, "package": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020242, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "object_L612u__c", "is_index": true, "create_time": 1633946020242, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "create_time": {"describe_api_name": "object_L612u__c", "is_index": true, "create_time": 1633946020242, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "life_status": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020241, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "field_num": 3, "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "is_index_field": false, "index_name": "s_3", "config": {}, "help_text": "", "status": "new", "description": ""}, "last_modified_by": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020242, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released", "description": ""}, "out_tenant_id": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020242, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "out_tenant_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020243, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "币种", "type": "select_one", "field_num": 5, "is_required": false, "api_name": "mc_currency", "options": [{"label": "BHD - <PERSON><PERSON>", "value": "BHD"}, {"not_usable": true, "label": "BDT - 孟加拉国塔卡", "value": "BDT"}, {"not_usable": true, "label": "BGN - Bulgarian Lev", "value": "BGN"}, {"label": "AWG - 阿鲁巴岛弗罗林", "value": "AWG"}, {"label": "USD - U.S. Dollar", "value": "USD"}, {"label": "AED - UAE Dirham", "value": "AED"}, {"label": "ALL - 阿尔巴尼亚列克", "value": "ALL"}, {"label": "AOA - 安哥拉宽扎", "value": "AOA"}, {"label": "BAM - 自由兑换马克", "value": "BAM"}, {"label": "ARS - 阿根廷比索", "value": "ARS"}, {"label": "AMD - 亚美尼亚打兰", "value": "AMD"}, {"label": "AUD - Australian Dollar", "value": "AUD"}, {"label": "ANG - 荷属安地列斯盾", "value": "ANG"}, {"label": "BBD - 巴巴多斯元", "value": "BBD"}, {"label": "AFN - Afghanistan Afghani (New)", "value": "AFN"}, {"label": "AZN - 阿塞拜疆马纳特", "value": "AZN"}, {"label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "币种", "is_index_field": false, "index_name": "s_5", "config": {}, "help_text": "", "status": "new"}, "version": {"describe_api_name": "object_L612u__c", "is_index": false, "create_time": 1633946020242, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020242, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released", "description": ""}, "relevant_team": {"describe_api_name": "object_L612u__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1633946020242, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new", "description": ""}, "record_type": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020240, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}, {"is_active": true, "api_name": "record_9dgjK__c", "label": "测试"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "object_L612u__c", "is_index": true, "is_active": true, "create_time": 1633946020242, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "index_name": "data_owner_dept_id", "status": "released", "description": ""}, "name": {"describe_api_name": "object_L612u__c", "default_is_expression": false, "prefix": "", "auto_adapt_places": false, "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "create_time": 1633946020284, "is_encrypted": false, "serial_number": 2, "default_value": "01", "label": "主属性", "condition": "NONE", "api_name": "name", "func_api_name": "", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "order_by": {"describe_api_name": "object_L612u__c", "is_index": false, "create_time": 1633946020242, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "mc_exchange_rate_version": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020246, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "label": "汇率版本", "type": "text", "field_num": 8, "is_required": false, "api_name": "mc_exchange_rate_version", "define_type": "package", "is_single": false, "label_r": "汇率版本", "is_index_field": false, "index_name": "t_2", "max_length": 256, "help_text": "", "status": "new"}, "_id": {"describe_api_name": "object_L612u__c", "is_index": false, "is_active": true, "create_time": 1633946020242, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}}, "release_version": "6.4", "actions": {}}