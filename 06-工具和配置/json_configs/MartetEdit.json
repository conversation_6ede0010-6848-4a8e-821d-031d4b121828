{"buttons": [], "components": [{"components": [["object_hG7Es__c_md_group_component"]], "buttons": [], "api_name": "tabs_component", "tabs": [{"api_name": "tab_object_hG7Es__c_md_group_component", "header": "jjy字段类型测试", "nameI18nKey": "object_hG7Es__c.field.field_m3eRw__c.reference_label"}], "header": "页签容器", "type": "tabs", "order": 5}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "begin_time"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "end_time"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "actual_income"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "actual_cost"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "description"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "effect"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "expected_income"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "summary"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_w2liu__c"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "out_owner"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "lock_status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "marketing_plan"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "location"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "execution_desc"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "expected_cost"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "biz_status"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "event_type"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "mc_currency"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "mc_exchange_rate"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_21llU__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_1oF9b__c"}, {"is_readonly": false, "is_required": false, "render_type": "department_many", "field_name": "field_ve61W__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference_many", "field_name": "field_oq6L1__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_rA1ck__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_d5vIE__c"}, {"is_readonly": false, "is_required": false, "is_tiled": false, "render_type": "select_many", "field_name": "field_v2Egf__c"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "field_xJ1rN__c"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "field_xp2q5__c"}, {"is_readonly": false, "is_required": false, "render_type": "image", "field_name": "field_9qv6n__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "field_62oeS__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "其他信息", "is_show": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "is_hidden": false, "header": "表单组件", "nameI18nKey": "paas.udobj.form_component", "type": "form", "order": 4}, {"field_section": [], "buttons": [], "api_name": "head_info", "related_list_name": "", "button_info": [{"hidden": [], "page_type": "create", "render_type": "normal", "order": ["Add_Save_button_default", "Add_Save_Continue_button_default", "Add_Save_Draft_button_default"]}, {"hidden": [], "page_type": "edit", "render_type": "normal", "order": ["Edit_Save_button_default"]}], "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "type": "simple", "order": 3}, {"buttons": [], "child_components": [], "type": "multi_table", "api_name": "object_hG7Es__c_md_group_component", "header": "jjy字段类型测试", "ref_object_api_name": "object_hG7Es__c", "related_list_name": "target_related_list_CP6r8__c", "field_api_name": "field_m3eRw__c", "nameI18nKey": "object_hG7Es__c.field.field_m3eRw__c.reference_label", "limit": 1, "button_info": [{"hidden": [], "render_type": "list_normal", "order": ["Single_Add_button_default", "Batch_Lookup_Add_button_field_kIp22__c"]}, {"hidden": [], "render_type": "list_batch", "order": ["Batch_Edit_button_default", "Delete_button_default", "Clone_button_default"]}, {"hidden": [], "render_type": "list_single", "order": ["Delete_button_default", "Clone_button_default", "Tile_button_default", "Insert_button_default"]}], "relationType": 0, "render_type": "card", "order": 6, "display_rule": false}], "last_modified_time": 1652966372092, "is_deleted": false, "version": 21, "create_time": 1629118933185, "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "edit_MarketingEventToolCreateObj_layout_generate_by_UDObjectServer__c", "what_api_name": null, "default_component": "form_component", "config": {"edit": 1, "remove": 0}, "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["form_component", "tabs_component"]], "columns": [{"width": "100%"}]}]}, "created_by": "1000", "display_name": "默认布局(新建/编辑页)", "is_default": true, "last_modified_by": "1000", "layout_type": "edit", "package": "CRM", "ref_object_api_name": "MarketingEventToolCreateObj", "tenant_id": "74255", "ui_event_ids": ["628643e3910d7c000146f43f"], "hidden_buttons": [], "hidden_components": ["related_list_form"], "namespace": null, "enable_mobile_layout": false, "events": [{"tenant_id": "74255", "describe_api_name": "MarketingEventToolCreateObj", "func_describe": "23432432", "last_modified_time": 1652966371927, "create_time": 1652966371927, "last_modified_by": "1000", "triggers": [1], "type": 1, "created_by": "1000", "trigger_field_api_names": ["description"], "is_deleted": false, "func_api_name": "func_x32C7__c", "trigger_describe_api_name": "MarketingEventToolCreateObj", "func_name": "func_x32C7"}]}