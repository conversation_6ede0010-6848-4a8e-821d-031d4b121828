{"tenant_id": "785451", "components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "full_line": false, "is_required": true, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "text", "field_name": "customer_description__c"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "text", "field_name": "sales_order_id__c"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "text", "field_name": "delivery_order_number__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "defaultValue": "分组名称Wj8", "column": 2, "header": "基本信息", "collapse": false}], "buttons": [], "api_name": "form_component", "related_list_name": "", "i18nInfoList": [{"customKey": "layout.shipping_application_proce__c.edit.edit_layout_T7h81__c.group.base_field_section__c.header", "apiName": "base_field_section__c", "defaultValue": "基本信息", "preKey": "paas.metadata.layout.base_info", "languageInfo": {"de": "Grundlegende Informationen", "pt": "Informações Básicas", "sw-TZ": "Makundi ya Makundi", "zh-TW": "基本資訊", "ko-KR": "기본 정보", "pt-BR": "Informações básicas", "en": "Basic Information", "es-ES": "Información básica", "kk-KZ": "Басқару құралдары", "zh-CN": "基本信息", "ur-PK": "بیچارہ معلومات", "it-IT": "Informazioni di base", "ar": "البيانات الأساسية", "pl-PL": "Podstawowe informacje", "ru-RU": "Основная информация", "nl-NL": "Basisinformatie", "id-ID": "Informasi <PERSON>", "tr-TR": "<PERSON><PERSON> bilgiler", "fr-FR": "Informations de base", "vi-VN": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "ja-JP": "基本情報", "th-TH": "ข้อมูลพื้นฐาน"}, "value": "基本信息"}], "column": 2, "header": "表单组件", "nameI18nKey": "paas.udobj.form_component", "type": "form", "isSticky": false, "grayLimit": 1, "order": 4}, {"field_section": [], "buttons": [], "api_name": "head_info", "related_list_name": "", "button_info": [{"hidden": [], "page_type": "create", "render_type": "normal", "order": ["Add_Save_button_default", "Add_Save_Continue_button_default", "Add_Save_Draft_button_default"]}, {"hidden": [], "page_type": "edit", "render_type": "normal", "order": ["Edit_Save_button_default"]}], "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "type": "simple", "isSticky": false, "grayLimit": 1, "order": 3}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "full_line": false, "is_required": true, "render_type": "employee", "field_name": "final_reviewer__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "direct_dealer__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "text", "field_name": "receiving_unit__c"}], "api_name": "group_0u2vX__c", "tab_index": "ltr", "defaultValue": "分组名称eZ2", "column": 2, "header": "需求信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "full_line": false, "is_required": false, "render_type": "text", "field_name": "single_line_text__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "text", "field_name": "contact_person__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "text", "field_name": "phone_number__c"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "text", "field_name": "delivery_city_text__c"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "remark__c"}], "api_name": "group_24Qrn__c", "tab_index": "ltr", "defaultValue": "分组名称U1h", "column": 2, "header": "联系信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "full_line": false, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "shipping_status__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "text", "field_name": "funding_arrangement_status__c"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "file_attachment", "field_name": "recovery_info__c"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "object_reference", "field_name": "payment_terms__c"}], "api_name": "group_7dD94__c", "tab_index": "ltr", "defaultValue": "分组名称0yH", "column": 2, "header": "内勤审核信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "full_line": false, "is_required": false, "render_type": "text", "field_name": "carrier__c"}, {"is_readonly": false, "full_line": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "transport_method__c"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "object_reference", "field_name": "related_to__c"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "date", "field_name": "scheduled_arrival_time__c"}, {"is_readonly": true, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "note_shipment_request__c"}], "api_name": "group_2SyId__c", "tab_index": "ltr", "defaultValue": "分组名称jB4", "column": 2, "header": "成品储运信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "full_line": false, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "full_line": false, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "full_line": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "full_line": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}], "api_name": "group_t2a3b__c", "tab_index": "ltr", "defaultValue": "分组名称22l", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component_REAPINAME_17399274472615457", "related_list_name": "", "i18nInfoList": [{"customKey": "layout.shipping_application_proce__c.edit.edit_layout_T7h81__c.group.group_0u2vX__c.header", "apiName": "group_0u2vX__c", "defaultValue": "需求信息", "value": "需求信息"}, {"customKey": "layout.shipping_application_proce__c.edit.edit_layout_T7h81__c.group.delivery_address__c.header", "apiName": "delivery_address__c", "defaultValue": "收货地址", "value": "收货地址"}, {"customKey": "layout.shipping_application_proce__c.edit.edit_layout_T7h81__c.group.group_24Qrn__c.header", "apiName": "group_24Qrn__c", "defaultValue": "联系信息", "value": "联系信息"}, {"customKey": "layout.shipping_application_proce__c.edit.edit_layout_T7h81__c.group.group_7dD94__c.header", "apiName": "group_7dD94__c", "defaultValue": "内勤审核信息", "value": "内勤审核信息"}, {"customKey": "layout.shipping_application_proce__c.edit.edit_layout_T7h81__c.group.group_2SyId__c.header", "apiName": "group_2SyId__c", "defaultValue": "成品储运信息", "value": "成品储运信息"}, {"customKey": "layout.shipping_application_proce__c.edit.edit_layout_T7h81__c.group.group_t2a3b__c.header", "apiName": "group_t2a3b__c", "defaultValue": "系统信息", "value": "系统信息"}], "column": 2, "header": "表单组件", "nameI18nKey": "paas.udobj.form_component", "type": "form", "isSticky": false, "grayLimit": 1, "order": 5}, {"buttons": [], "related_list_name": "target_related_list_qiprG__c", "button_info": [{"hidden": [], "render_type": "list_normal", "order": ["Import_Excel_button_default", "Single_Add_button_default"]}, {"hidden": [], "render_type": "list_batch", "order": ["Batch_Edit_button_default", "Delete_button_default", "Clone_button_default"]}, {"hidden": [], "render_type": "list_single", "order": ["Delete_button_default", "Clone_button_default", "Tile_button_default", "Insert_button_default"]}], "ref_object_api_name": "object_n9QHo__c", "nameI18nKey": "object_n9QHo__c.field.field_8xT16__c.reference_label", "type": "multi_table", "isSticky": false, "api_name": "object_n9QHo__c_md_group_component", "child_components": [], "header": "发货申请明细", "render_type": "card", "order": 7, "field_api_name": "field_8xT16__c"}, {"components": [["object_n9QHo__c_md_group_component"]], "buttons": [], "api_name": "tabs_component", "tabs": [{"api_name": "tab_object_n9QHo__c_md_group_component", "header": "发货申请明细", "nameI18nKey": "object_n9QHo__c.field.field_8xT16__c.reference_label"}], "header": "页签容器", "type": "tabs", "isSticky": false, "order": 6}], "buttons": [], "last_modified_time": 1741311017929, "package": "CRM", "create_time": 1739926346600, "i18nInfoList": [{"apiName": "display_name"}], "ref_object_api_name": "shipping_application_proce__c", "layout_type": "edit", "hidden_buttons": ["AddTeamMember_button_default", "EditTeamMember_button_default", "DeleteTeamMember_button_default", "SaleRecord_button_default"], "enable_mobile_layout": false, "last_modified_by": "1000", "ui_event_ids": [], "display_name": "默认布局(新建/编辑页)", "is_default": true, "hidden_components": ["richTextWidget"], "version": 9, "created_by": "1000", "is_deleted": false, "api_name": "edit_layout_T7h81__c", "_id": "67b52b4ab9a53d000171126e", "layout_description": "", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["form_component", "tabs_component", "form_component_REAPINAME_17399274472615457"]], "columns": [{"width": "100%"}]}]}}