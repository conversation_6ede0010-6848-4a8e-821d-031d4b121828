{
    {
        "supportVersionList": [
            "dingtalk_standard_edition",
            "manufacture_dealer_edition",
            "dealer_edition",
            "wechat_standard_edition",
            "dingtalk_strengthen_edition",
            "wechat_standardpro_edition",
            "standardpro_edition",
            "kis_edition",
            "wechat_strengthen_edition",
            "strengthen_edition",
            "promotion_sales_edition",
            "yunzhijia_weixin",
            "agent_edition",
            "enterprise_edition",
            "dingtalk_standardpro_edition",
            "kdweibo_edition",
            "jdy_edition",
            "basic_edition"
        ],
        "functionCode": "crmmanage/=/module-sysobject",
        "functionName": "预设对象管理",
        "editable": true,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "dingtalk_standard_edition",
            "manufacture_dealer_edition",
            "dealer_edition",
            "wechat_standard_edition",
            "dingtalk_strengthen_edition",
            "wechat_standardpro_edition",
            "standardpro_edition",
            "kis_edition",
            "wechat_strengthen_edition",
            "strengthen_edition",
            "promotion_sales_edition",
            "yunzhijia_weixin",
            "agent_edition",
            "enterprise_edition",
            "dingtalk_standardpro_edition",
            "kdweibo_edition",
            "jdy_edition",
            "basic_edition"
        ],
        "functionCode": "crmmanage/=/module-myobject",
        "functionName": "自定义对象管理",
        "editable": true,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "wechat_strengthen_edition",
            "strengthen_edition",
            "fmcg_basic_edition",
            "feishu_strengthen_edition",
            "enterprise_edition"
        ],
        "functionCode": "crmmanage/=/module-bigobject",
        "isEditable": true,
        "functionName": "大对象管理",
        "supportEnterpriseAccounts": "${variables_gray_conf_fktest.ea_list_format_comma},${variables_fs_gray_product_comma.Large_objects_ea}",
        "appId": "facishare-system",
        "isShow": true
    },
    {
        "supportVersionList": [],
        "functionCode": "crmmanage/=/module-mytags",
        "functionName": "对象名称设置",
        "supportModuleList": [
            "multi_language_app"
        ],
        "editable": false,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "dingtalk_standard_edition",
            "manufacture_dealer_edition",
            "dealer_edition",
            "wechat_standard_edition",
            "dingtalk_strengthen_edition",
            "wechat_standardpro_edition",
            "standardpro_edition",
            "kis_edition",
            "wechat_strengthen_edition",
            "strengthen_edition",
            "promotion_sales_edition",
            "yunzhijia_weixin",
            "agent_edition",
            "enterprise_edition",
            "dingtalk_standardpro_edition",
            "kdweibo_edition",
            "jdy_edition",
            "basic_edition"
        ],
        "functionCode": "crmmanage/=/module-templatemanage",
        "functionName": "对象模板管理",
        "editable": true,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "manufacture_dealer_edition",
            "wechat_strengthen_edition",
            "dealer_edition",
            "strengthen_edition",
            "promotion_sales_edition",
            "agent_edition",
            "dingtalk_strengthen_edition",
            "wechat_standardpro_edition",
            "enterprise_edition",
            "dingtalk_standardpro_edition",
            "standardpro_edition"
        ],
        "functionCode": "crmmanage/=/module-smartforms",
        "functionName": "智能表单",
        "editable": true,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "*"
        ],
        "functionCode": "crmmanage/=/module-labelmanage",
        "functionName": "标签管理",
        "supportModuleList": [
            "*"
        ],
        "editable": false,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "*"
        ],
        "functionCode": "crmmanage/=/module-generaloptionset",
        "functionName": "通用选项集",
        "supportEnterpriseIds": "*",
        "editable": false,
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "*"
        ],
        "functionCode": "crmmanage/=/module-businessplugin",
        "isEditable": true,
        "functionName": "对象插件管理",
        "supportEnterpriseIds": "${variables_udobj_gray_product.gray_product_830_comma_ei},${variables_udobj_gray_product.domain_plugin_management_ei},${variables_fs_gray_product_comma.domain_plugin_management_ei}",
        "appId": "facishare-system",
        "isShow": true
    },
    {
        "supportVersionList": [],
        "supportModuleList": [
            "object_change_order_app"
        ],
        "functionCode": "crmmanage/=/module-changeorder",
        "functionName": "变更单",
        "supportEnterpriseIds": "*",
        "editable": false,
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [],
        "supportModuleList": [
            "push_pull_order_app"
        ],
        "functionCode": "crmmanage/=/module-convertrule",
        "isEditable": true,
        "functionName": "转换规则",
        "supportEnterpriseAccounts": "${variables_gray_conf_870.ea_list_format_comma},${variables_fs_gray_product_comma.push_pull_order_app_ea},fktest1335,fktest1119,fktest4560,fktest1118,fktest2481,fktest3791",
        "appId": "facishare-system",
        "isShow": true
    },
    {
        "supportVersionList": [
            "dingtalk_standard_edition",
            "manufacture_dealer_edition",
            "dealer_edition",
            "wechat_standard_edition",
            "dingtalk_strengthen_edition",
            "wechat_standardpro_edition",
            "standardpro_edition",
            "kis_edition",
            "wechat_strengthen_edition",
            "strengthen_edition",
            "promotion_sales_edition",
            "yunzhijia_weixin",
            "agent_edition",
            "enterprise_edition",
            "dingtalk_standardpro_edition",
            "kdweibo_edition",
            "jdy_edition",
            "basic_edition"
        ],
        "functionCode": "crmmanage/=/module-globalvar",
        "functionName": "全局变量",
        "editable": true,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "dingtalk_standard_edition",
            "manufacture_dealer_edition",
            "dealer_edition",
            "wechat_standard_edition",
            "dingtalk_strengthen_edition",
            "wechat_standardpro_edition",
            "standardpro_edition",
            "kis_edition",
            "wechat_strengthen_edition",
            "strengthen_edition",
            "promotion_sales_edition",
            "yunzhijia_weixin",
            "agent_edition",
            "enterprise_edition",
            "dingtalk_standardpro_edition",
            "kdweibo_edition",
            "jdy_edition",
            "basic_edition"
        ],
        "functionCode": "crmmanage/=/module-globalvar",
        "functionName": "全局变量",
        "editable": true,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "manufacture_dealer_edition",
            "wechat_strengthen_edition",
            "dealer_edition",
            "strengthen_edition",
            "promotion_sales_edition",
            "yunzhijia_weixin",
            "agent_edition",
            "dingtalk_strengthen_edition",
            "wechat_standardpro_edition",
            "enterprise_edition",
            "dingtalk_standardpro_edition",
            "standardpro_edition"
        ],
        "functionCode": "crmmanage/=/module-dafenqi",
        "functionName": "打分器规则设置",
        "editable": true,
        "supportEnterpriseIds": "*",
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [],
        "functionCode": "crmmanage/=/module-multicurrency",
        "functionName": "多币种设置",
        "supportModuleList": [
            "multi_currency_app"
        ],
        "supportEnterpriseAccounts": "*",
        "editable": false,
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [],
        "functionCode": "enterprise/multiorg",
        "functionName": "多组织设置",
        "supportModuleList": [
            "multi_business_unit_app"
        ],
        "supportEnterpriseAccounts": "*",
        "editable": false,
        "appId": "facishare-system",
        "show": true
    },
    {
        "supportVersionList": [
            "*"
        ],
        "functionCode": "crmmanage/=/module-globalfieldalign",
        "functionName": "全局字段布局配置",
        "supportEnterpriseIds": "********,${variables_udobj_gray_product.gray_product_810_comma_ei},730173,********,********,158833,712632,713386,731824,766856,575252,758300,771754,771442,722160,773433,767166,773803",
        "editable": false,
        "appId": "facishare-system",
        "show": true
    }
}