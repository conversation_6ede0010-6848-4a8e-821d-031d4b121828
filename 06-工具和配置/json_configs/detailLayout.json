{"_id": "", "api_name": "layout_test01__c", "display_name": "默认布局", "layout_description": "", "ref_object_api_name": "object_test__c", "is_default": true, "is_deleted": false, "layout_structure": {"layout": [{"columns": [{"width": "100%"}], "components": [["head_info"]]}, {"columns": [{"width": "auto"}, {"width": "500px", "retractable": true}], "components": [["top_info", "tabs_2Eyh9__c"], ["relevant_team_component"]]}]}, "package": "CRM", "version": 1, "layout_type": "detail", "buttons": [], "is_deal_ui": true, "components": [{"field_section": [], "buttons": [{"api_name": "Edit_button_default", "action_type": "default"}, {"api_name": "SaleRecord_button_default", "action_type": "default"}, {"api_name": "Dial_button_default", "action_type": "default"}, {"api_name": "ChangeOwner_button_default", "action_type": "default"}, {"api_name": "StartBPM_button_default", "action_type": "default"}, {"api_name": "Abolish_button_default", "action_type": "default"}, {"api_name": "StartStagePropellor_button_default", "action_type": "default"}, {"api_name": "Lock_button_default", "action_type": "default"}, {"api_name": "Unlock_button_default", "action_type": "default"}, {"api_name": "Clone_button_default", "action_type": "default"}, {"api_name": "ChangePartner_button_default", "action_type": "default"}, {"api_name": "ChangePartnerOwner_button_default", "action_type": "default"}, {"api_name": "DeletePartner_button_default", "action_type": "default"}, {"api_name": "SendMail_button_default", "action_type": "default"}, {"api_name": "Discuss_button_default", "action_type": "default"}, {"api_name": "Remind_button_default", "action_type": "default"}, {"api_name": "Schedule_button_default", "action_type": "default"}, {"api_name": "Print_button_default", "action_type": "default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "type": "simple", "grayLimit": 1, "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3}, {"field_section": [{"field_name": "owner", "render_type": "employee"}, {"field_name": "owner_department", "render_type": "text"}, {"field_name": "last_modified_time", "render_type": "date_time"}, {"field_name": "record_type", "render_type": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info", "grayLimit": 1}, {"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "type": "user_list", "grayLimit": 1, "nameI18nKey": "paas.udobj.constant.relevant_team"}, {"field_section": [{"api_name": "base_field_section__c", "header": "基本信息", "show_header": true, "collapse": false, "column": 2, "tab_index": "ltr", "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}]}, {"api_name": "group_4j3Y7__c", "header": "系统信息", "show_header": true, "collapse": true, "column": 2, "tab_index": "ltr", "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}]}], "buttons": [], "api_name": "form_component", "related_list_name": "", "header": "详细信息", "type": "form", "grayLimit": 1, "nameI18nKey": "paas.udobj.detail_info", "column": 2}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "type": "related_record", "grayLimit": 1, "nameI18nKey": "paas.udobj.modify_log"}, {"field_section": [], "buttons": [], "api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "header": "流程列表", "type": "relatedlist", "grayLimit": 1, "nameI18nKey": "paas.udobj.process_list"}, {"field_section": [], "buttons": [], "api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "header": "审批流程", "type": "relatedlist", "grayLimit": 1, "nameI18nKey": "paas.udobj.approvalflow"}, {"components": [["form_component"], ["operation_log"], ["BPM_related_list"], ["Approval_related_list"]], "buttons": [], "api_name": "tabs_2Eyh9__c", "tabs": [{"header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "api_name": "form_component_e4RLj__c"}, {"header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "api_name": "operation_log_t80Ne__c"}, {"header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "api_name": "BPM_related_list_r5bbn__c"}, {"header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "api_name": "Approval_related_list_J71g0__c"}], "header": "页签容器", "type": "tabs"}], "hidden_components": ["component_4fEPz__c", "component_SDWSM__c", "component_pJEzE__c", "test_mobile__c", "sale_log", "related_what_list", "biDashboardCom"], "hidden_buttons": [], "events": []}