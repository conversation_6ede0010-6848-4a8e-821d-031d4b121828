{"buttons": [], "components": [{"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list", "_id": "relevant_team_component"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "_id": "operation_log"}, {"components": [["form_component"], ["operation_log"], ["BPM_related_list"], ["Approval_related_list"], ["payment_recordrelated_list_generate_by_UDObjectServer__c"], ["object_qep6N__c_field_5I5Ru__c_related_list"]], "buttons": [], "api_name": "tabs_zu1jq__c", "tabs": [{"api_name": "form_component_29Xa4__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_Upe4o__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "BPM_related_list_bR6Di__c", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_1f6Am__c", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "tab_payment_recordrelated_list_generate_by_UDObjectServer__c", "header": "收款记录s", "nameI18nKey": "paas.udobj.receipt"}, {"api_name": "tab_object_qep6N__c_field_5I5Ru__c_related_list", "header": "关联fj-测试负责人", "nameI18nKey": "object_qep6N__c.field.field_5I5Ru__c.reference_label"}], "header": "页签容器", "type": "tabs", "_id": "tabs_zu1jq__c"}, {"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartner_button_default", "label": "更换合作伙伴"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "DeletePartner_button_default", "label": "移除合作伙伴"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "_id": "head_info"}, {"field_section": [{"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info", "nameI18nKey": "paas.udobj.summary_info", "_id": "top_info"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_TlXOr__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "_id": "form_component", "type": "form"}, {"type": "relatedlist", "buttons": [], "define_type": "business", "relationType": 2, "api_name": "payment_recordrelated_list_generate_by_UDObjectServer__c", "header": "收款记录s", "nameI18nKey": "paas.udobj.receipt", "ref_object_api_name": "payment_record", "related_list_name": "payment_record_LIST", "limit": 1, "_id": "payment_recordrelated_list_generate_by_UDObjectServer__c"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_qep6N__c_field_5I5Ru__c_related_list", "header": "关联fj-测试负责人", "ref_object_api_name": "object_qep6N__c", "related_list_name": "target_related_list_z3RFA__c", "field_api_name": "field_5I5Ru__c", "nameI18nKey": "object_qep6N__c.field.field_5I5Ru__c.reference_label", "limit": 1, "_id": "object_qep6N__c_field_5I5Ru__c_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "BPM_related_list", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "ref_object_api_name": "BPM", "related_list_name": "", "limit": 1, "field_section": [], "_id": "BPM_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "Approval_related_list", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "ref_object_api_name": "Approval", "related_list_name": "", "limit": 1, "field_section": [], "_id": "Approval_related_list"}], "last_modified_time": 1664172259393, "is_deleted": false, "version": 2, "create_time": 1633946020977, "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "layout_Bpz2z__c", "what_api_name": null, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "tabs_zu1jq__c"], ["relevant_team_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "created_by": "1000", "display_name": "默认布局", "is_default": true, "last_modified_by": "1000", "layout_type": "detail", "package": "CRM", "ref_object_api_name": "object_L612u__c", "tenant_id": "74255", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": ["component_4fEPz__c", "component_SDWSM__c", "component_pJEzE__c", "sale_log"], "namespace": null, "enable_mobile_layout": null, "events": []}