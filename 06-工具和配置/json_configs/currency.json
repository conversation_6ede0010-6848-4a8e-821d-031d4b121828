{"currency_unit": {"describe_api_name": "MtCurrencyObj", "is_index": true, "is_active": true, "description": "币种单位", "is_unique": false, "label": "币种单位", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "currency_unit", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "currency_symbol": {"describe_api_name": "MtCurrencyObj", "is_index": true, "is_active": true, "description": "币种符号", "is_unique": false, "label": "币种符号", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "currency_symbol", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}, "currency_prefix": {"describe_api_name": "MtCurrencyObj", "type": "select_one", "define_type": "package", "is_index": false, "is_active": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "币种前缀", "api_name": "currency_prefix", "description": "币种前缀", "options": [{"label": "空", "value": "empty"}, {"label": "币种代码", "value": "currency_code"}, {"label": "币种单位", "value": "currency_unit"}, {"label": "币种符号", "value": "currency_symbol"}], "status": "released"}, "currency_suffix": {"describe_api_name": "MtCurrencyObj", "type": "select_one", "define_type": "package", "is_index": false, "is_active": true, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "币种后缀", "api_name": "currency_suffix", "description": "币种后缀", "options": [{"label": "空", "value": "empty"}, {"label": "币种代码", "value": "currency_code"}, {"label": "币种单位", "value": "currency_unit"}, {"label": "币种符号", "value": "currency_symbol"}], "status": "released"}, "currency_type": {"describe_api_name": "MtCurrencyObj", "is_index": true, "is_active": true, "description": "币种类型", "is_unique": false, "label": "币种类型", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "currency_type", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 50, "status": "released"}}