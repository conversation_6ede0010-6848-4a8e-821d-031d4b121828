{"tenant_id": "640907", "components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "Choose_button_default", "label": "领取"}, {"action_type": "default", "api_name": "Transfer_button_default", "label": "转换"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "FollowUp_button_default", "label": "跟进中"}, {"action_type": "default", "api_name": "Allocate_button_default", "label": "分配"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "Delete_button_default", "label": "删除"}, {"action_type": "default", "api_name": "Recover_button_default", "label": "恢复"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起流程"}, {"action_type": "default", "api_name": "Return_button_default", "label": "退回"}, {"action_type": "default", "api_name": "Move_button_default", "label": "转移"}, {"action_type": "default", "api_name": "TakeBack_button_default", "label": "收回"}, {"action_type": "default", "api_name": "Close_button_default", "label": "无效"}, {"action_type": "default", "api_name": "ViewFeedCard_button_default", "label": "查看销售记录"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "default", "api_name": "AddTeamMember_button_default", "label": "添加相关团队成员"}, {"action_type": "default", "api_name": "EditTeamMember_button_default", "label": "编辑相关团队成员"}, {"action_type": "default", "api_name": "DeleteTeamMember_button_default", "label": "移除相关团队成员"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ExtendExpireTime_button_default", "label": "申请延期"}, {"action_type": "custom", "api_name": "button_2K1xA__c", "label": "分配负责人", "isActive": true}], "api_name": "head_info", "header": "标题和按钮", "define_type": "general", "_id": "head_info", "type": "simple", "is_multiple": false}, {"buttons": [], "api_name": "sale_log", "is_hidden": false, "header": "跟进动态", "_id": "sale_log", "type": "related_record", "is_multiple": false, "order": 1}, {"field_section": [{"render_type": "true_or_false", "field_name": "is_overtime"}, {"render_type": "employee", "field_name": "owner"}], "buttons": [], "api_name": "top_info", "header": "摘要信息", "define_type": "general", "_id": "top_info", "type": "top_info", "is_multiple": false, "order": 3}, {"buttons": [], "api_name": "container_default_LeadsObj_layout_generate_by_UDObjectServer__c", "header": "页签容器", "components": [], "define_type": "container", "_id": "container_default_LeadsObj_layout_generate_by_UDObjectServer__c", "type": "tabs", "is_multiple": true, "order": 4}, {"field_section": [{"form_fields": [{"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_Zfi2a__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_7w540__c"}, {"is_readonly": true, "is_required": false, "render_type": "select_one", "field_name": "field_Oz0BC__c"}, {"is_readonly": true, "is_required": false, "render_type": "select_one", "field_name": "field_15fE6__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_M8eIT__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_cKBan__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_k8LHC__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "field_bCXne__c"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "field_8o3Ru__c"}, {"is_readonly": false, "is_required": false, "render_type": "html_rich_text", "field_name": "field_5318c__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": false, "render_type": "country", "field_name": "field_sh4bA__c"}, {"is_readonly": false, "is_required": false, "render_type": "province", "field_name": "field_l1yMg__c"}, {"is_readonly": false, "is_required": false, "render_type": "city", "field_name": "field_PFbta__c"}, {"is_readonly": false, "is_required": false, "render_type": "district", "field_name": "field_iTtyl__c"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "field_Zl2mQ__c"}, {"is_readonly": false, "is_required": false, "render_type": "location", "field_name": "field_kxE1w__c"}], "api_name": "field_s8g4v__c", "tab_index": "ltr", "column": 2, "header": "地区定位", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_ce5bg__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_YqIr7__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_p7s3c__c"}], "api_name": "group_3fT92__c", "tab_index": "ltr", "column": 2, "header": "财务信息", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_Iyde4__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_qgzh3__c"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "field_Aw56H__c"}], "api_name": "group_rA2o5__c", "tab_index": "ltr", "column": 2, "header": "三证信息", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_xW8o8__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_ptmDL__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_h81Wr__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_6jWrf__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_4ml6v__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_HI3Gd__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_i26fj__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_1jl0r__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_4quxM__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "field_Uq9Xl__c"}, {"is_readonly": false, "is_required": false, "render_type": "formula", "field_name": "field_FrMKD__c"}, {"is_readonly": false, "is_required": false, "render_type": "formula", "field_name": "field_x6r4i__c"}], "api_name": "group_colDg__c", "tab_index": "ltr", "column": 2, "header": "经销商级别评估", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": false, "render_type": "select_many", "field_name": "field_HKygG__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_2eh1t__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_H9I18__c"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "field_Kk9Nh__c"}], "api_name": "group_w1rH4__c", "tab_index": "ltr", "column": 2, "header": "仓库管理标准", "is_show": true}, {"form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "leads_status"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": false, "render_type": "true_or_false", "field_name": "is_overtime"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "sysinfo_section__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "reference_field_config": "edit_layout_6q5mA__c", "api_name": "form_component", "is_hidden": false, "header": "详细信息", "_id": "form_component", "type": "form", "is_multiple": false, "order": 5}, {"buttons": [], "api_name": "operation_log", "is_hidden": false, "header": "修改记录", "_id": "operation_log", "type": "related_record", "fields": {"operation_time": {"render_type": "date_time", "field_name": "operation_time"}, "message": {"render_type": "text", "field_name": "log_msg"}, "user": {"render_type": "employee", "field_name": "user_id"}}, "is_multiple": false, "order": 6}, {"relationType": 2, "buttons": [], "api_name": "LeadsTransferLogObj_leads_id_related_list", "related_list_name": "leads_leads_transfer_log_list", "ref_object_api_name": "LeadsTransferLogObj", "is_hidden": false, "header": "线索转换记录", "_id": "LeadsTransferLogObj_leads_id_related_list", "type": "relatedlist", "is_multiple": false, "order": 7, "field_api_name": "leads_id"}, {"relationType": 2, "buttons": [], "api_name": "CheckinsObj_field_s0sNS__c_related_list", "related_list_name": "target_related_list_8xxZ1__c", "ref_object_api_name": "CheckinsObj", "is_hidden": false, "header": "高级外勤对象", "_id": "CheckinsObj_field_s0sNS__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 8, "field_api_name": "field_s0sNS__c"}, {"relationType": 2, "buttons": [], "api_name": "LeadsFlowRecordObj_leads_id_related_list", "related_list_name": "leads_leads_flow_record_list", "ref_object_api_name": "LeadsFlowRecordObj", "is_hidden": false, "header": "线索跟进过程记录", "_id": "LeadsFlowRecordObj_leads_id_related_list", "type": "relatedlist", "is_multiple": false, "order": 9, "field_api_name": "leads_id"}, {"buttons": [], "api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "Approval", "header": "审批流程", "define_type": "general", "_id": "Approval_related_list", "type": "relatedlist", "is_multiple": false, "order": 10}, {"buttons": [], "api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "header": "流程列表", "define_type": "general", "_id": "BPM_related_list", "type": "relatedlist", "is_multiple": false, "order": 11}, {"relationType": 2, "buttons": [], "api_name": "object_qaiF1__c_field_4w57W__c_related_list", "related_list_name": "target_related_list_iC4r2__c", "ref_object_api_name": "object_qaiF1__c", "is_hidden": false, "header": "经销商合同", "_id": "object_qaiF1__c_field_4w57W__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 12, "field_api_name": "field_4w57W__c"}, {"relationType": 2, "buttons": [], "api_name": "object_HpLed__c_field_44A1b__c_related_list", "related_list_name": "target_related_list_beTf7__c", "ref_object_api_name": "object_HpLed__c", "is_hidden": false, "header": "经销商报价单", "_id": "object_HpLed__c_field_44A1b__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 13, "field_api_name": "field_44A1b__c"}, {"relationType": 2, "buttons": [], "api_name": "object_8f7J1__c_field_6HP1l__c_related_list", "related_list_name": "target_related_list_bkH2e__c", "ref_object_api_name": "object_8f7J1__c", "is_hidden": false, "header": "线索联系人", "_id": "object_8f7J1__c_field_6HP1l__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 14, "field_api_name": "field_6HP1l__c"}, {"relationType": 2, "buttons": [], "api_name": "object_cOcP5__c_field_Xjwch__c_related_list", "related_list_name": "target_related_list_bY1pd__c", "ref_object_api_name": "object_cOcP5__c", "is_hidden": false, "header": "送样SKU", "_id": "object_cOcP5__c_field_Xjwch__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 15, "field_api_name": "field_Xjwch__c"}, {"relationType": 2, "buttons": [], "api_name": "object_82epp__c_field_0hZKw__c_related_list", "related_list_name": "target_related_list_Ci91u__c", "ref_object_api_name": "object_82epp__c", "is_hidden": false, "header": "送样申请", "_id": "object_82epp__c_field_0hZKw__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 16, "field_api_name": "field_0hZKw__c"}, {"relationType": 2, "buttons": [], "api_name": "object_2WvN6__c_field_i81HJ__c_related_list", "related_list_name": "target_related_list_ddZgG__c", "ref_object_api_name": "object_2WvN6__c", "is_hidden": false, "header": "经销商分销网络", "_id": "object_2WvN6__c_field_i81HJ__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 17, "field_api_name": "field_i81HJ__c"}, {"relationType": 2, "buttons": [], "api_name": "object_8Pl0B__c_field_9zqAr__c_related_list", "related_list_name": "target_related_list_01jBw__c", "ref_object_api_name": "object_8Pl0B__c", "is_hidden": false, "header": "官方资料", "_id": "object_8Pl0B__c_field_9zqAr__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 18, "field_api_name": "field_9zqAr__c"}, {"relationType": 2, "buttons": [], "api_name": "object_IoF6m__c_field_26718__c_related_list", "related_list_name": "target_related_list_81vL2__c", "ref_object_api_name": "object_IoF6m__c", "is_hidden": false, "header": "覆盖终端明细", "_id": "object_IoF6m__c_field_26718__c_related_list", "type": "relatedlist", "is_multiple": false, "order": 19, "field_api_name": "field_26718__c"}], "buttons": [], "last_modified_time": 1735113198675, "package": "CRM", "create_time": 1589532597646, "ref_object_api_name": "LeadsObj", "layout_type": "detail", "hidden_buttons": ["ChangePartner_button_default", "ChangePartnerOwner_button_default", "DeletePartner_button_default"], "last_modified_by": "1174", "ui_event_ids": [], "display_name": "经销商-销售布局", "is_default": false, "hidden_components": ["relevant_team_component", "CRMEmail_related_list", "payment_recordrelated_list_generate_by_UDObjectServer__c", "frame_component", "tabs_component"], "version": 38, "created_by": "1001", "is_deleted": false, "api_name": "layout_6q5mA__c", "_id": "5ebe57b53ab8dd0001dde95d", "layout_description": "销售线索布局", "default_component": "form_component", "layout_structure": {"head": [{"api_name": "head_info"}], "body": {"left": [{"api_name": "top_info"}, {"children": [{"api_name": "form_component"}, {"api_name": "operation_log"}, {"api_name": "LeadsTransferLogObj_leads_id_related_list"}, {"api_name": "CheckinsObj_field_s0sNS__c_related_list"}, {"api_name": "LeadsFlowRecordObj_leads_id_related_list"}, {"children": [], "api_name": "Approval_related_list"}, {"children": [], "api_name": "BPM_related_list"}, {"api_name": "object_qaiF1__c_field_4w57W__c_related_list"}, {"api_name": "object_HpLed__c_field_44A1b__c_related_list"}, {"api_name": "object_8f7J1__c_field_6HP1l__c_related_list"}, {"api_name": "object_cOcP5__c_field_Xjwch__c_related_list"}, {"api_name": "object_82epp__c_field_0hZKw__c_related_list"}, {"api_name": "object_2WvN6__c_field_i81HJ__c_related_list"}, {"api_name": "object_8Pl0B__c_field_9zqAr__c_related_list"}, {"api_name": "object_IoF6m__c_field_26718__c_related_list"}], "api_name": "container_default_LeadsObj_layout_generate_by_UDObjectServer__c"}], "right": [{"api_name": "sale_log"}]}}}