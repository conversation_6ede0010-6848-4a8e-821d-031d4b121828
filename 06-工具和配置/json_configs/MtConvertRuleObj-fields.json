{"scene_type": {"is_index": false, "length": 2, "description": "规则场景", "is_unique": false, "label": "规则场景", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "scene_type", "define_type": "package", "index_name": "version", "round_mode": 4, "status": "released"}, "strategy": {"is_index": false, "length": 2, "description": "规则策略", "is_unique": false, "label": "规则策略", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "strategy", "define_type": "package", "index_name": "version", "round_mode": 4, "status": "released"}, "combined_by_fields": {"is_index": true, "is_active": true, "description": "合单依据字段", "is_unique": false, "label": "合单依据字段", "type": "long_text", "expression_type": "json", "is_need_convert": false, "is_required": false, "api_name": "combined_by_fields", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 10000, "status": "released"}}