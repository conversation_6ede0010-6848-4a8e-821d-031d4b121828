{"buttons": [], "components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartner_button_default", "label": "更换合作伙伴"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "DeletePartner_button_default", "label": "移除合作伙伴"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "system", "api_name": "Unfollow_button_default", "action": "Unfollow", "label": "取消关注"}, {"action_type": "system", "api_name": "Follow_button_default", "action": "Follow", "label": "关注"}, {"action_type": "system", "api_name": "Transform_button_default", "action": "Transform", "label": "转换"}, {"action_type": "default", "api_name": "EnterAccount_button_default", "action": "EnterAccount", "label": "入账"}, {"action_type": "default", "api_name": "CancelEntry_button_default", "action": "CancelEntry", "label": "取消入账"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "isSticky": false, "grayLimit": 1, "_id": "head_info"}, {"field_section": [{"field_name": "owner"}, {"field_name": "owner_department"}, {"field_name": "last_modified_time"}, {"field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "type": "top_info", "isSticky": false, "grayLimit": 1, "_id": "top_info"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "Field1__c"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "Field2__c"}, {"is_readonly": false, "is_required": false, "render_type": "html_rich_text", "field_name": "Field3__c"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_many", "field_name": "Field5__c"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "Field6__c"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "Field7__c"}, {"is_readonly": false, "is_required": false, "render_type": "date", "field_name": "Field8__c"}, {"is_readonly": false, "is_required": false, "render_type": "time", "field_name": "Field9__c"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "Field10__c"}, {"is_readonly": false, "is_required": false, "render_type": "phone_number", "field_name": "Field11__c"}, {"is_readonly": false, "is_required": false, "render_type": "image", "field_name": "Field12__c"}, {"is_readonly": false, "is_required": false, "render_type": "file_attachment", "field_name": "Field13__c"}, {"is_readonly": false, "is_required": false, "render_type": "email", "field_name": "Field14__c"}, {"is_readonly": false, "is_required": false, "render_type": "true_or_false", "field_name": "Field15__c"}, {"is_readonly": false, "is_required": false, "render_type": "percentile", "field_name": "Field17__c"}, {"is_readonly": false, "is_required": false, "render_type": "url", "field_name": "Field18__c"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "Field20__c"}, {"is_readonly": false, "is_required": false, "render_type": "department_many", "field_name": "Field21__c"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "Field22__c"}, {"is_readonly": false, "is_required": false, "render_type": "employee_many", "field_name": "Field23__c"}, {"is_readonly": false, "is_required": false, "render_type": "location", "field_name": "Field24__c"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "Field4__c"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_04Oam__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "_id": "form_component", "type": "form", "isSticky": false, "grayLimit": 1, "i18nInfoList": [{"apiName": "base_field_section__c", "customKey": "layout.object_sourceM__c.detail.layout_wx11P__c.group.base_field_section__c.header", "defaultValue": "基本信息", "languageInfo": {"de": "Grundlegende Informationen", "pt": "Informações Básicas", "sw-TZ": "Makundi ya Makundi", "zh-TW": "基本資訊", "ko-KR": "기본 정보", "pt-BR": "Informações básicas", "en": "Basic Information", "es-ES": "Información básica", "kk-KZ": "Басқару құралдары", "zh-CN": "基本信息", "ur-PK": "بیچارہ معلومات", "it-IT": "Informazioni di base", "ru-RU": "Основная информация", "pl-PL": "Podstawowe informacje", "ar": "البيانات الأساسية", "nl-NL": "Basisinformatie", "id-ID": "Informasi <PERSON>", "tr-TR": "<PERSON><PERSON> bilgiler", "fr-FR": "Informations de base", "vi-VN": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "ja-JP": "基本情報", "th-TH": "ข้อมูลพื้นฐาน"}, "preKey": "paas.metadata.layout.base_info", "value": "基本信息"}, {"apiName": "group_04Oam__c", "customKey": "layout.object_sourceM__c.detail.layout_wx11P__c.group.group_04Oam__c.header", "defaultValue": "系统信息", "value": "系统信息"}]}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "grayLimit": 1, "_id": "operation_log"}, {"field_section": [], "buttons": [], "api_name": "bpm_component", "related_list_name": "", "header": "业务流组件", "nameI18nKey": "paas.udobj.bpm_component", "type": "bpm_component", "isSticky": false, "grayLimit": 1, "_id": "bpm_component"}, {"field_section": [], "buttons": [], "api_name": "stage_component", "related_list_name": "", "header": "阶段推进器组件", "nameI18nKey": "paas.udobj.stage_component", "type": "stage_component", "isSticky": false, "grayLimit": 1, "_id": "stage_component"}, {"field_section": [], "buttons": [], "api_name": "approval_component", "related_list_name": "", "header": "审批流组件", "nameI18nKey": "paas.udobj.approval_component", "type": "approval_component", "isSticky": false, "grayLimit": 1, "_id": "approval_component"}, {"components": [["form_component"], ["operation_log"], ["BPM_related_list"], ["Approval_related_list"], ["object_sourceD1__c_md_group_component"], ["object_sourceD2__c_md_group_component"], ["object_targetM__c_Field16__c_related_list"], ["object_targetM__c_field_9UkXS__c_related_list"]], "buttons": [], "api_name": "tabs_hnSo9__c", "tabs": [{"api_name": "form_component_2a3r0__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_JWoe5__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "BPM_related_list_n0Qv4__c", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_zo9Fc__c", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "tab_object_sourceD1__c_md_group_component", "header": "Api_转换规则_源从1", "nameI18nKey": "object_sourceD1__c.field.Field3__c.reference_label"}, {"api_name": "tab_object_sourceD2__c_md_group_component", "header": "Api_转换规则_源从2", "nameI18nKey": "object_sourceD2__c.field.Field3__c.reference_label"}, {"api_name": "tab_object_targetM__c_Field16__c_related_list", "header": "Api_转换规则_目标主", "nameI18nKey": "object_targetM__c.field.Field16__c.reference_label"}, {"api_name": "tab_object_targetM__c_field_9UkXS__c_related_list", "header": "Api_转换规则_目标主（多选）", "nameI18nKey": "object_targetM__c.field.field_9UkXS__c.reference_label"}], "header": "页签容器", "type": "tabs", "isSticky": false, "_id": "tabs_hnSo9__c"}, {"buttons": [], "child_components": [], "type": "multi_table", "api_name": "object_sourceD1__c_md_group_component", "header": "Api_转换规则_源从1", "ref_object_api_name": "object_sourceD1__c", "related_list_name": "target_related_list_1tWEA__c", "field_api_name": "Field3__c", "nameI18nKey": "object_sourceD1__c.field.Field3__c.reference_label", "limit": 1, "button_info": [], "_id": "object_sourceD1__c_md_group_component"}, {"buttons": [], "child_components": [], "type": "multi_table", "api_name": "object_sourceD2__c_md_group_component", "header": "Api_转换规则_源从2", "ref_object_api_name": "object_sourceD2__c", "related_list_name": "target_related_list_257Ck__c", "field_api_name": "Field3__c", "nameI18nKey": "object_sourceD2__c.field.Field3__c.reference_label", "limit": 1, "button_info": [], "_id": "object_sourceD2__c_md_group_component"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_targetM__c_Field16__c_related_list", "header": "Api_转换规则_目标主", "ref_object_api_name": "object_targetM__c", "related_list_name": "target_related_list_u08KI__c", "field_api_name": "Field16__c", "nameI18nKey": "object_targetM__c.field.Field16__c.reference_label", "limit": 1, "_id": "object_targetM__c_Field16__c_related_list"}, {"type": "relatedlist", "buttons": [], "relationType": 2, "api_name": "object_targetM__c_field_9UkXS__c_related_list", "header": "Api_转换规则_目标主（多选）", "ref_object_api_name": "object_targetM__c", "related_list_name": "target_related_list_749vr__c", "field_api_name": "field_9UkXS__c", "nameI18nKey": "object_targetM__c.field.field_9UkXS__c.reference_label", "limit": 1, "_id": "object_targetM__c_field_9UkXS__c_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "BPM_related_list", "header": "业务流程", "nameI18nKey": "paas.udobj.process_list", "ref_object_api_name": "BPM", "related_list_name": "", "limit": 1, "field_section": [], "grayLimit": 1, "_id": "BPM_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "Approval_related_list", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "ref_object_api_name": "Approval", "related_list_name": "", "limit": 1, "field_section": [], "grayLimit": 1, "_id": "Approval_related_list"}], "last_modified_time": 1696757578025, "is_deleted": false, "version": 3, "create_time": 1692778518864, "_id": "64e5c016cf04e70001e2805d", "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "layout_wx11P__c", "what_api_name": null, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["stage_component", "bpm_component", "top_info", "tabs_hnSo9__c"], ["approval_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "created_by": "1001", "display_name": "默认布局", "is_default": true, "last_modified_by": "1001", "layout_type": "detail", "package": "CRM", "ref_object_api_name": "object_sourceM__c", "tenant_id": "700609", "ui_event_ids": [], "hidden_buttons": ["AddTeamMember_button_default", "EditTeamMember_button_default", "DeleteTeamMember_button_default", "SaleRecord_button_default"], "hidden_components": ["test__c", "sale_log", "biDashboardCom", "relevant_team_component"], "namespace": null, "enable_mobile_layout": null, "enable_sidebar_layout": null, "i18nInfoList": [{"apiName": "display_name", "languageInfo": {"de": "Standardlayout", "pt": "disposição padrão", "sw-TZ": "Layout <PERSON>", "zh-TW": "預設布局", "ko-KR": "기본 배치", "pt-BR": "disposição padrão", "en": "Default Layout", "es-ES": "disposición predeterminada", "kk-KZ": "Стандартті түсінік", "zh-CN": "默认布局", "ur-PK": "میںزیکی طور پر ترتیب دیا جاتا ہے", "it-IT": "Disposizione predefinita", "ru-RU": "Стандартный макет", "pl-PL": "Domyślny układ", "ar": "تخطيط افتراضي", "nl-NL": "Standaard layout", "id-ID": "Layout Default", "tr-TR": "Varsayılan düzen", "fr-FR": "Disposition par défaut", "vi-VN": "<PERSON><PERSON><PERSON>n mặc định", "ja-JP": "デフォルトのレイアウト", "th-TH": "การตั้งค่าเริ่มต้น"}}], "events": []}