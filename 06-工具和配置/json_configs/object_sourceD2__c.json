{"tenant_id": "158833", "package": "CRM", "is_active": true, "last_modified_time": 1743461838076, "create_time": 1692778875889, "description": "", "last_modified_by": "1012", "display_name": "Api_转换规则_源从2", "created_by": "1012", "version": 1465, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "object_sourceD2__c", "icon_path": "", "is_udef": true, "define_type": "custom", "_id": "65a0f2586b8fc300019f277a", "fields": {"lock_rule": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1705046646239, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 26, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "_id": "65a0f2760cf59d0001bc4666", "is_single": false, "is_extend": true, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_4", "status": "new", "description": ""}, "Field3__c": {"describe_api_name": "object_sourceD2__c", "auto_adapt_places": false, "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "s_1", "is_index": true, "is_active": true, "create_time": 1705046627539, "is_encrypted": false, "label": "主从字段", "target_api_name": "object_sourceM__c", "show_detail_button": false, "target_related_list_name": "target_related_list_257Ck__c", "field_num": 3, "target_related_list_label": "Api_转换规则_源从2", "api_name": "Field3__c", "is_create_when_master_create": true, "_id": "65a0f2630cf59d0001bc2cac", "is_index_field": true, "is_required_when_master_create": false, "help_text": "", "status": "new", "description": ""}, "Field7__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "d_0", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1705046636799, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额", "currency_unit": "￥", "field_num": 16, "api_name": "Field7__c", "_id": "65a0f26c0cf59d0001bc3993", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new", "description": ""}, "Field13__c": {"describe_api_name": "object_sourceD2__c", "is_index": true, "file_amount_limit": 1, "is_active": true, "create_time": 1705046627353, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "file_source": ["local", "net"], "label": "附件字段", "type": "file_attachment", "field_num": 2, "file_size_limit": 104857600, "is_required": false, "api_name": "Field13__c", "define_type": "custom", "_id": "65a0f2630cf59d0001bc2c51", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "a_0", "support_file_types": [], "help_text": "", "status": "new", "description": ""}, "Field17__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": true, "is_unique": false, "type": "percentile", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "d_1", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1705046640584, "is_encrypted": false, "length": 12, "default_value": "", "label": "百分数字段", "field_num": 21, "api_name": "Field17__c", "_id": "65a0f2700cf59d0001bc3e1b", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "mc_exchange_rate": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_3", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1705046653752, "is_encrypted": false, "length": 10, "label": "汇率", "api_name": "mc_exchange_rate", "_id": "65a0f27dce58ae0001902135", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "new"}, "Field23__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "is_index": true, "is_active": true, "create_time": 1705046639058, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "人员多选字段", "type": "employee_many", "field_num": 19, "is_required": false, "wheres": [], "api_name": "Field23__c", "define_type": "custom", "_id": "65a0f26f0cf59d0001bc3bd3", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "a_5", "help_text": "", "status": "new", "description": ""}, "life_status_before_invalid": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1705046631558, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 6, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "_id": "65a0f2670cf59d0001bc3219", "is_single": false, "is_extend": true, "label_r": "作废前生命状态", "is_index_field": false, "index_name": "t_0", "max_length": 256, "status": "new", "description": ""}, "owner_department": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705046633758, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "_id": "65a0f2690cf59d0001bc34e0", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "description": ""}, "Field2__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "t_1", "max_length": 2000, "is_index": true, "is_active": true, "create_time": 1705046632882, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "多行文本", "field_num": 8, "api_name": "Field2__c", "_id": "65a0f2680cf59d0001bc3406", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "lock_status": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046636340, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "field_num": 15, "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "_id": "65a0f26c0cf59d0001bc389d", "is_single": false, "is_extend": true, "label_r": "锁定状态", "is_index_field": false, "index_name": "s_3", "config": {}, "status": "new", "description": ""}, "package": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1692778875889, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "create_time": {"describe_api_name": "object_sourceD2__c", "is_index": true, "create_time": 1692778875889, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "Field12__c": {"describe_api_name": "object_sourceD2__c", "auto_adapt_places": false, "is_unique": false, "file_source": [], "type": "image", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "a_4", "support_file_types": ["jpg", "gif", "jpeg", "png"], "is_index": true, "file_amount_limit": 1, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "create_time": 1705046637964, "is_encrypted": false, "label": "图片字段", "is_watermark": false, "field_num": 18, "file_size_limit": 20971520, "is_ocr_recognition": false, "api_name": "Field12__c", "is_need_cdn": false, "_id": "65a0f26d0cf59d0001bc3a80", "is_index_field": false, "identify_type": "", "help_text": "", "status": "new", "description": ""}, "close__c": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046637486, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": true, "label": "布尔值-关闭", "type": "true_or_false", "field_num": 17, "is_required": false, "api_name": "close__c", "options": [{"label": "关闭", "value": true}, {"label": "不关闭", "value": false}], "define_type": "custom", "_id": "65a0f26d0cf59d0001bc39ec", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "b_1", "help_text": "", "status": "new", "description": ""}, "Field6__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "d_2", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1705046643666, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 12, "default_value": "", "label": "数字", "field_num": 23, "api_name": "Field6__c", "_id": "65a0f2730cf59d0001bc4260", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new", "description": ""}, "version": {"describe_api_name": "object_sourceD2__c", "is_index": false, "create_time": 1692778875889, "length": 8, "is_unique": false, "description": "version", "label": "版本", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1692778875889, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released", "description": ""}, "relevant_team": {"describe_api_name": "object_sourceD2__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1705046626081, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "65a0f2620cf59d0001bc2ade", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new", "description": ""}, "Field22__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "is_index": true, "is_active": true, "create_time": 1705046635480, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "人员字段", "type": "employee", "field_num": 11, "is_required": false, "wheres": [], "api_name": "Field22__c", "define_type": "custom", "_id": "65a0f26b0cf59d0001bc381b", "is_single": true, "is_extend": true, "is_index_field": false, "index_name": "a_2", "help_text": "", "status": "new", "description": ""}, "data_own_department": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1692778875889, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "index_name": "data_owner_dept_id", "status": "released", "description": ""}, "name": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705046632692, "is_encrypted": false, "default_value": "", "label": "主属性", "api_name": "name", "_id": "65a0f2680cf59d0001bc33a8", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "description": ""}, "_id": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1692778875889, "pattern": "", "is_unique": false, "description": "_id", "label": "ID", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}, "tenant_id": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1692778875889, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "租户ID", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "data_own_organization": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1720257038071, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "归属组织", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_organization", "define_type": "package", "_id": "66890a0e05cf980001b10e6a", "is_single": true, "label_r": "归属组织", "is_index_field": false, "index_name": "a_8", "status": "released", "description": ""}, "Field11__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "^[0-9+-;,]{0,100}$", "remove_mask_roles": {}, "is_unique": false, "type": "phone_number", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "p_0", "verification": false, "is_index": true, "is_active": true, "create_time": 1705046632315, "is_encrypted": false, "default_value": "", "label": "手机字段", "field_num": 7, "api_name": "Field11__c", "_id": "65a0f2680cf59d0001bc333d", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "description": ""}, "Field5__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_many", "default_to_zero": false, "is_required": false, "options": [{"label": "选项1", "value": "GzySa4U7z"}, {"label": "选项2", "value": "5KpWyWHL1"}, {"label": "选项3", "value": "IfSvC3lb4"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "a_7", "is_index": true, "is_active": true, "create_time": 1705046643424, "is_encrypted": false, "default_value": [], "label": "多选", "field_num": 22, "api_name": "Field5__c", "_id": "65a0f2730cf59d0001bc41fa", "is_index_field": false, "config": {}, "help_text": "", "status": "new", "description": ""}, "Field1__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": true, "index_name": "t_3", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1705046644578, "is_encrypted": false, "default_value": "", "label": "单行文本", "field_num": 25, "api_name": "Field1__c", "_id": "65a0f2740cf59d0001bc43fe", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new", "description": ""}, "Field9__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "time", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "l_0", "is_index": true, "is_active": true, "create_time": 1705046644182, "is_encrypted": false, "default_value": "", "label": "时间字段", "time_zone": "GMT+8", "field_num": 24, "api_name": "Field9__c", "date_format": "HH:mm", "_id": "65a0f2740cf59d0001bc4359", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "Field15__c": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046635198, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": true, "label": "布尔值字段", "type": "true_or_false", "field_num": 9, "is_required": false, "api_name": "Field15__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "_id": "65a0f26b0cf59d0001bc3786", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "b_0", "help_text": "", "status": "new", "description": ""}, "origin_source": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1692778875889, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "index_name": "s_os", "config": {"display": 0}, "status": "released", "description": ""}, "lock_user": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1705046639399, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 20, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "_id": "65a0f26f0cf59d0001bc3c36", "is_single": true, "is_extend": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_6", "status": "new", "description": ""}, "Field21__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "department_many", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "a_1", "is_index": true, "is_active": true, "create_time": 1705046628990, "is_encrypted": false, "default_value": "", "label": "部门多选字段", "field_num": 4, "api_name": "Field21__c", "_id": "65a0f2650cf59d0001bc2f53", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "is_deleted": {"describe_api_name": "object_sourceD2__c", "is_index": false, "create_time": 1692778875889, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "删除状态", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "object_describe_api_name": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1692778875889, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "out_owner": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1692778875889, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released", "description": ""}, "mc_functional_currency": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1705046653791, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "本位币", "type": "select_one", "is_required": false, "api_name": "mc_functional_currency", "options": [{"label": "CNY - 人民币", "value": "CNY"}], "define_type": "package", "_id": "65a0f27dce58ae0001902136", "is_single": false, "label_r": "本位币", "is_index_field": false, "index_name": "s_5", "config": {}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "is_index": true, "is_active": true, "create_time": 1705046643548, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "_id": "65a0f2730cf59d0001bc4238", "is_single": true, "label_r": "负责人", "is_index_field": false, "index_name": "owner", "help_text": "", "status": "new", "description": ""}, "last_modified_time": {"describe_api_name": "object_sourceD2__c", "is_index": true, "create_time": 1692778875889, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "Field4__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "enable_clone": true, "options": [{"label": "选项1", "value": "option1"}, {"label": "选项2", "value": "Y3LY13evV"}, {"label": "选项3", "value": "12Ka7xjlc"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "s_0", "is_index": true, "is_active": true, "create_time": 1705046626819, "is_encrypted": false, "default_value": "", "label": "单选", "field_num": 1, "api_name": "Field4__c", "_id": "65a0f2620cf59d0001bc2bfd", "is_index_field": false, "config": {}, "help_text": "", "status": "new", "description": ""}, "Field10__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "date_time", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "l_2", "is_index": true, "is_active": true, "create_time": 1705046646881, "is_encrypted": false, "not_use_multitime_zone": false, "default_value": "", "label": "日期时间字段", "time_zone": "GMT+8", "field_num": 28, "api_name": "Field10__c", "date_format": "yyyy-MM-dd HH:mm", "_id": "65a0f2760cf59d0001bc4831", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "life_status": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "is_extend": true, "label_r": "生命状态", "index_name": "s_2", "is_index": true, "is_active": true, "create_time": 1705046630151, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "field_num": 5, "is_need_convert": false, "api_name": "life_status", "_id": "65a0f2660cf59d0001bc30bc", "is_index_field": false, "config": {}, "help_text": "", "status": "new", "description": ""}, "Field14__c": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046636261, "is_encrypted": false, "auto_adapt_places": false, "pattern": "\\w+((-w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+", "remove_mask_roles": {}, "is_unique": false, "default_value": "", "label": "邮箱字段", "type": "email", "field_num": 14, "is_required": false, "api_name": "Field14__c", "define_type": "custom", "_id": "65a0f26c0cf59d0001bc387c", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "w_1", "is_show_mask": false, "help_text": "", "status": "new", "description": ""}, "Field8__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": true, "index_name": "l_1", "is_index": true, "is_active": true, "create_time": 1705046646776, "is_encrypted": false, "default_value": "", "label": "日期", "time_zone": "GMT+8", "field_num": 27, "api_name": "Field8__c", "date_format": "yyyy-MM-dd", "_id": "65a0f2760cf59d0001bc47ba", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "last_modified_by": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1692778875889, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released", "description": ""}, "out_tenant_id": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1692778875889, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046653752, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "币种", "type": "select_one", "is_required": false, "api_name": "mc_currency", "options": [{"label": "CNY - 人民币", "value": "CNY"}, {"label": "AUD - 澳元", "value": "AUD"}, {"label": "AED - 阿联酋迪拉姆", "value": "AED"}, {"label": "snm", "value": "snm"}], "define_type": "package", "_id": "65a0f27dce58ae0001902134", "is_single": false, "label_r": "币种", "is_index_field": false, "index_name": "s_6", "config": {}, "help_text": "", "status": "new"}, "record_type": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046627744, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "_id": "65a0f2630cf59d0001bc2ce4", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released", "description": ""}, "Field20__c": {"describe_api_name": "object_sourceD2__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "custom", "is_single": true, "is_extend": true, "index_name": "a_3", "is_index": true, "is_active": true, "create_time": 1705046636063, "is_encrypted": false, "default_value": "", "label": "部门字段", "field_num": 13, "api_name": "Field20__c", "_id": "65a0f26c0cf59d0001bc3864", "is_index_field": false, "help_text": "", "status": "new", "description": ""}, "Field18__c": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046635392, "is_encrypted": false, "auto_adapt_places": false, "pattern": "^(((http[s]?|ftp):\\/\\/|www\\.)[a-z0-9\\.\\-]+\\.([a-z]{2,4})|((http[s]?|ftp):\\/\\/)?(([01]?[\\d]{1,2})|(2[0-4][\\d])|(25[0-5]))(\\.(([01]?[\\d]{1,2})|(2[0-4][\\d])|(25[0-5]))){3})(:\\d+)?(\\/[a-z0-9\\$\\^\\*\\+\\?\\(\\)\\{\\}\\.\\-_~!@#%&:;\\/=<>]*)?", "is_unique": false, "default_value": "", "label": "网址字段", "type": "url", "field_num": 10, "is_required": false, "api_name": "Field18__c", "define_type": "custom", "_id": "65a0f26b0cf59d0001bc37d9", "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "w_0", "help_text": "", "status": "new", "description": ""}, "mc_exchange_rate_version": {"describe_api_name": "object_sourceD2__c", "is_index": false, "is_active": true, "create_time": 1705046653791, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "label": "汇率版本", "type": "text", "is_required": false, "api_name": "mc_exchange_rate_version", "define_type": "package", "_id": "65a0f27dce58ae0001902137", "is_single": false, "label_r": "汇率版本", "is_index_field": false, "index_name": "t_4", "max_length": 256, "help_text": "", "status": "new"}, "Field24__c": {"describe_api_name": "object_sourceD2__c", "is_index": true, "is_active": true, "create_time": 1705046635761, "is_encrypted": false, "auto_location": false, "auto_adapt_places": false, "is_unique": false, "label": "定位字段", "is_geo_index": false, "type": "location", "field_num": 12, "is_required": false, "api_name": "Field24__c", "range_limit": false, "define_type": "custom", "_id": "65a0f26b0cf59d0001bc3839", "radius_range": 100, "is_single": false, "is_extend": true, "is_index_field": false, "index_name": "t_2", "help_text": "", "status": "new", "description": ""}}, "release_version": "6.4", "actions": {}}