{"date_range__c": {"type": "group", "define_type": "custom", "api_name": "date_range__c", "label": "日期范围", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": false, "status": "new", "group_type": "date_time_range", "show_time": false, "fields": {"start_time_field": "start_time__c", "end_time_field": "end_time__c"}}, "start_time__c": {"type": "date_time", "define_type": "custom", "api_name": "start_time__c", "label": "开始时间", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "new", "default_value": "", "default_is_expression": false, "default_to_zero": false, "not_use_multitime_zone": false, "used_in": "component", "time_zone": "GMT+8", "date_format": "yyyy-MM-dd HH:mm"}, "end_time__c": {"type": "date_time", "define_type": "custom", "api_name": "end_time__c", "label": "结束时间", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "status": "new", "default_value": "", "default_is_expression": false, "default_to_zero": false, "not_use_multitime_zone": false, "used_in": "component", "time_zone": "GMT+8", "date_format": "yyyy-MM-dd HH:mm"}}