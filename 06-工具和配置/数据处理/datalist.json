[{"describe_api_name": "NONE", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1751212818621, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6861630965b3bc3f02a1a09f", "task_describe_params_i18n_key": ["【CCRM】销售订单号拉取-整点", "PlnTask_SalesOrderNoPull__c"], "task_describe_default_value": null, "completed_num": "0", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1751212809016, "task_estimated_execution_time": 1751212818621, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["be_queuing", "68616312aba35900010336b6"], "error_message": null, "task_status": "be_queuing", "last_modified_time": 1751212818670, "create_time": 1751212818670, "biz_api_name": "【CCRM】销售订单号拉取-整点", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "1", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 0, "task_describe": "【CCRM】销售订单号拉取-整点 执行函数 PlnTask_SalesOrderNoPull__c", "task_total_num": "0", "_id": "68616312aba35900010336b6", "biz_id": "674ed7e57bc56303ab6945b6"}, {"describe_api_name": "NONE", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1751439611440, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6864d8f6aa00d91c0a0fcb5c", "task_describe_params_i18n_key": ["【CCRM】销售订单号拉取-整点", "PlnTask_SalesOrderNoPull__c"], "task_describe_default_value": null, "completed_num": "0", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1751439606703, "task_estimated_execution_time": 1751439611440, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["be_queuing", "6864d8fb8c6e01000183d00d"], "error_message": null, "task_status": "be_queuing", "last_modified_time": 1751439611501, "create_time": 1751439611501, "biz_api_name": "【CCRM】销售订单号拉取-整点", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "1", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 0, "task_describe": "【CCRM】销售订单号拉取-整点 执行函数 PlnTask_SalesOrderNoPull__c", "task_total_num": "0", "_id": "6864d8fb8c6e01000183d00d", "biz_id": "674ed7e57bc56303ab6945b6"}, {"describe_api_name": "NONE", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1751119237726, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "685ff56e65b3bc3f02a11735", "task_describe_params_i18n_key": ["【CCRM】销售订单号拉取-整点", "PlnTask_SalesOrderNoPull__c"], "task_describe_default_value": null, "completed_num": "0", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1751119214546, "task_estimated_execution_time": 1751119237726, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["be_queuing", "685ff5856d06ef00018a9aed"], "error_message": null, "task_status": "be_queuing", "last_modified_time": 1751119237792, "create_time": 1751119237792, "biz_api_name": "【CCRM】销售订单号拉取-整点", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "1", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 0, "task_describe": "【CCRM】销售订单号拉取-整点 执行函数 PlnTask_SalesOrderNoPull__c", "task_total_num": "0", "_id": "685ff5856d06ef00018a9aed", "biz_id": "674ed7e57bc56303ab6945b6"}, {"describe_api_name": "NONE", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1749859212868, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "684cbb8565b3bc3f02985f6d", "task_describe_params_i18n_key": ["【CCRM】销售订单号拉取-整点", "PlnTask_SalesOrderNoPull__c"], "task_describe_default_value": null, "completed_num": "0", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749859205657, "task_estimated_execution_time": 1749859212868, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["be_queuing", "684cbb8cb8c86d000180c104"], "error_message": null, "task_status": "be_queuing", "last_modified_time": 1749859212914, "create_time": 1749859212914, "biz_api_name": "【CCRM】销售订单号拉取-整点", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "1", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 0, "task_describe": "【CCRM】销售订单号拉取-整点 执行函数 PlnTask_SalesOrderNoPull__c", "task_total_num": "0", "_id": "684cbb8cb8c86d000180c104", "biz_id": "674ed7e57bc56303ab6945b6"}, {"describe_api_name": "NONE", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1749056422552, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "68407b9d65b3bc3f0292984f", "task_describe_params_i18n_key": ["【CCRM】销售订单号拉取-整点", "PlnTask_SalesOrderNoPull__c"], "task_describe_default_value": null, "completed_num": "0", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749056413068, "task_estimated_execution_time": 1749056422552, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["be_queuing", "68407ba6101b410001a8f834"], "error_message": null, "task_status": "be_queuing", "last_modified_time": 1749056422575, "create_time": 1749056422575, "biz_api_name": "【CCRM】销售订单号拉取-整点", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "1", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 0, "task_describe": "【CCRM】销售订单号拉取-整点 执行函数 PlnTask_SalesOrderNoPull__c", "task_total_num": "0", "_id": "68407ba6101b410001a8f834", "biz_id": "674ed7e57bc56303ab6945b6"}, {"describe_api_name": "NONE", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1751043635560, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "685ece1c65b3bc3f02a09b22", "task_describe_params_i18n_key": ["【CCRM】销售订单号拉取-整点", "PlnTask_SalesOrderNoPull__c"], "task_describe_default_value": null, "completed_num": "0", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1751043612033, "task_estimated_execution_time": 1751043635560, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "685ece326d06ef00018ea4dc"], "error_message": null, "task_status": "in_process", "last_modified_time": 1751043635610, "create_time": 1751043634919, "biz_api_name": "【CCRM】销售订单号拉取-整点", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 0, "task_describe": "【CCRM】销售订单号拉取-整点 执行函数 PlnTask_SalesOrderNoPull__c", "task_total_num": "0", "_id": "685ece326d06ef00018ea4dc", "biz_id": "674ed7e57bc56303ab6945b6"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1749317402514, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6844771965b3bc3f029454c3", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749317401080, "task_estimated_execution_time": 1749317402514, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "684477196d06ef000103cd1f"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1749317403087, "create_time": 1749317401576, "biz_api_name": "定时查询合同签署状态", "end_time": 1749317402983, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "684477196d06ef000103cd1f", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1749324602433, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6844933865b3bc3f02945b88", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749324600780, "task_estimated_execution_time": 1749324602433, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "684493398c6e010001772bdf"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1749324602693, "create_time": 1749324601121, "biz_api_name": "定时查询合同签署状态", "end_time": 1749324602627, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "684493398c6e010001772bdf", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1750465802385, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6855fd08aa00d91c0a089733", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1750465800797, "task_estimated_execution_time": 1750465802385, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "6855fd098c6e0100012b2dce"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1750465802618, "create_time": 1750465801109, "biz_api_name": "定时查询合同签署状态", "end_time": 1750465802529, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "6855fd098c6e0100012b2dce", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1749090603457, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "68410129aa00d91c0afef8dd", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749090601230, "task_estimated_execution_time": 1749090603457, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "684101296d06ef0001a4d9a0"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1749090603856, "create_time": 1749090601985, "biz_api_name": "定时查询合同签署状态", "end_time": 1749090603790, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "684101296d06ef0001a4d9a0", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "log_c_end_order_pull__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1750306281724, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "68538d98aa00d91c0a077d99", "task_describe_params_i18n_key": ["【CCRM】销售订单业绩保存-整点", "PlnTask_salesOrderPerformanceSave__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1750306200770, "task_estimated_execution_time": 1750306281724, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "68538d99b8c86d00019765b1"], "error_message": null, "describe_api_name__r": "日志-C端订单拉取", "task_status": "in_process", "last_modified_time": 1750306286540, "create_time": 1750306201676, "biz_api_name": "【CCRM】销售订单业绩保存-整点", "end_time": 1750306282273, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "【CCRM】销售订单业绩保存-整点 执行函数 PlnTask_salesOrderPerformanceSave__c", "task_total_num": "6", "biz_api_name__r": "【CCRM】销售订单业绩保存-整点", "_id": "68538d99b8c86d00019765b1", "biz_id": "674ed7e57bc56303ab6945b9"}, {"describe_api_name": "object_1lFfW__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1751043612444, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "685ece18aa00d91c0a0cb842", "task_describe_params_i18n_key": ["每日更新交付状态（线下）", "PlnTask_updateOfflineStatus__c"], "task_describe_default_value": null, "completed_num": "13", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1751043608549, "task_estimated_execution_time": 1751043612444, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "685ece2a8c6e010001fb5964"], "error_message": null, "describe_api_name__r": "政府项目线下产品明细", "task_status": "in_process", "last_modified_time": 1751043630737, "create_time": 1751043626762, "biz_api_name": "每日更新交付状态（线下）", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "每日更新交付状态（线下） 执行函数 PlnTask_updateOfflineStatus__c", "task_total_num": "13", "biz_api_name__r": "每日更新交付状态（线下）", "_id": "685ece2a8c6e010001fb5964", "biz_id": "65968208e70ec3609a5717af"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1748885403860, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "683ddf9965b3bc3f02913a90", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1748885401262, "task_estimated_execution_time": 1748885403860, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "683ddf99aba3590001869a96"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1748885404437, "create_time": 1748885401451, "biz_api_name": "定时查询合同签署状态", "end_time": 1748885404374, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "683ddf99aba3590001869a96", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1749155402355, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6841fe4865b3bc3f02935032", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749155400780, "task_estimated_execution_time": 1749155402355, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "6841fe49b8c86d0001ce3e17"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1749155402552, "create_time": 1749155401301, "biz_api_name": "定时查询合同签署状态", "end_time": 1749155402483, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "6841fe49b8c86d0001ce3e17", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "log_c_end_order_pull__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1749921007462, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "684dace865b3bc3f0298b83c", "task_describe_params_i18n_key": ["【CCRM】销售订单业绩保存-整点", "PlnTask_salesOrderPerformanceSave__c"], "task_describe_default_value": null, "completed_num": "3", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749921000982, "task_estimated_execution_time": 1749921007462, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "684dadda101b410001a90814"], "error_message": null, "describe_api_name__r": "日志-C端订单拉取", "task_status": "in_process", "last_modified_time": 1749921247017, "create_time": 1749921242900, "biz_api_name": "【CCRM】销售订单业绩保存-整点", "end_time": null, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "【CCRM】销售订单业绩保存-整点 执行函数 PlnTask_salesOrderPerformanceSave__c", "task_total_num": "3", "biz_api_name__r": "【CCRM】销售订单业绩保存-整点", "_id": "684dadda101b410001a90814", "biz_id": "674ed7e57bc56303ab6945b9"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1749015002582, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "683fd9d965b3bc3f02923340", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749015001154, "task_estimated_execution_time": 1749015002582, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "683fd9d98c6e0100014a75cd"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1749015002863, "create_time": 1749015001897, "biz_api_name": "定时查询合同签署状态", "end_time": 1749015002787, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "683fd9d98c6e0100014a75cd", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1749079802372, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6840d6f8aa00d91c0afeda65", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749079800665, "task_estimated_execution_time": 1749079802372, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "6840d6f87670a700011b024a"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1749079802699, "create_time": 1749079800903, "biz_api_name": "定时查询合同签署状态", "end_time": 1749079802609, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "6840d6f87670a700011b024a", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "contract__c", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": 1749083402645, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6840e50865b3bc3f0292bcdf", "task_describe_params_i18n_key": ["定时查询合同签署状态", "func_searchContractStatus__c"], "task_describe_default_value": null, "completed_num": "6", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1749083400908, "task_estimated_execution_time": 1749083402645, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["in_process", "6840e509101b410001cd9e8f"], "error_message": null, "describe_api_name__r": "业务用章申请", "task_status": "in_process", "last_modified_time": 1749083403111, "create_time": 1749083401738, "biz_api_name": "定时查询合同签署状态", "end_time": 1749083403023, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "定时查询合同签署状态 执行函数 func_searchContractStatus__c", "task_total_num": "6", "biz_api_name__r": "定时查询合同签署状态", "_id": "6840e509101b410001cd9e8f", "biz_id": "649ab06f33cd9059adca42ca"}, {"describe_api_name": "SalesOrderProductObj", "tenant_id": "736684", "submitted_by__l": [{"id": "2256", "tenantId": "736684", "name": "张永胜", "picAddr": "", "email": "<EMAIL>", "nickname": "张永胜", "supervisorId": "1006", "phone": "", "description": "", "status": 0, "createTime": 1651226397057, "modifyTime": 1709623473818, "dept": "1575", "post": "资深服务端研发（PHP)工程师", "empNum": ""}], "task_execution_time": 1751180046401, "submitted_by__r": {"picAddr": "", "mobile": null, "description": "", "dept": "1575", "supervisorId": "1006", "title": null, "empNum": "", "modifyTime": 1709623473818, "post": "资深服务端研发（PHP)工程师", "createTime": 1651226397057, "phone": "", "name": "张永胜", "nickname": "张永胜", "tenantId": "736684", "id": "2256", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6860e30865b3bc3f02a17043", "task_describe_params_i18n_key": ["【数据初始化】订单产品初始化订单成交价", "DataInitializationOrderProductInit__c"], "task_describe_default_value": null, "completed_num": "255600", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": 1751180040521, "task_estimated_execution_time": 1751180046401, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["canceled", "6860e3087670a70001424b36"], "error_message": null, "describe_api_name__r": "订单产品", "task_status": "canceled", "last_modified_time": 1751252933665, "create_time": 1751180040548, "biz_api_name": "【数据初始化】订单产品初始化订单成交价", "end_time": 1751252933593, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2256"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "3", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 28, "task_describe": "【数据初始化】订单产品初始化订单成交价 执行函数 DataInitializationOrderProductInit__c", "task_total_num": "881930", "biz_api_name__r": "【数据初始化】订单产品初始化订单成交价", "_id": "6860e3087670a70001424b36", "biz_id": "685e73f0ba76376bc9bfbe4b"}, {"describe_api_name": "NONE", "tenant_id": "736684", "submitted_by__l": [{"id": "2269", "tenantId": "736684", "name": "殷军杰", "picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "email": "<EMAIL>", "nickname": "殷军杰", "supervisorId": "2256", "phone": "", "description": "", "status": 0, "createTime": *************, "modifyTime": *************, "dept": "1575", "post": "工程师", "empNum": "118891"}], "task_execution_time": *************, "submitted_by__r": {"picAddr": "https://wework.qpic.cn/wwpic/788034_9tf5gthVS46iEFJ_1659574095/0", "mobile": null, "description": "", "dept": "1575", "supervisorId": "2256", "title": null, "empNum": "118891", "modifyTime": *************, "post": "工程师", "createTime": *************, "phone": "", "name": "殷军杰", "nickname": "殷军杰", "tenantId": "736684", "id": "2269", "position": null, "enterpriseName": null, "email": "<EMAIL>", "status": 0}, "task_id": "6846e8ef65b3bc3f02955e86", "task_describe_params_i18n_key": ["客户清洗-标记（开启）", "func_clearaccountsearch__c"], "task_describe_default_value": null, "completed_num": "0", "is_deleted": false, "created_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "task_create_time": *************, "task_estimated_execution_time": *************, "task_describe_i18n_key": "scheduler.task.monitor.execute_function", "object_describe_api_name": "AsyncTaskMonitorObj", "total_num": 3065, "searchAfterId": ["completed", "6846e8fa8c6e0100010f3cf5"], "error_message": null, "task_status": "completed", "last_modified_time": *************, "create_time": *************, "biz_api_name": "客户清洗-标记（开启）", "end_time": *************, "last_modified_by__l": [{"id": "-10000", "name": "系统"}], "submitted_by": ["2269"], "created_by__l": [{"id": "-10000", "name": "系统"}], "task_describe_default_params_value": null, "last_modified_by": ["-10000"], "created_by": ["-10000"], "version": "2", "record_type": "scheduler_task", "last_modified_by__r": {"picAddr": null, "mobile": null, "description": null, "dept": null, "supervisorId": null, "title": null, "empNum": null, "modifyTime": null, "post": null, "createTime": null, "phone": null, "name": "系统", "nickname": null, "tenantId": null, "id": "-10000", "position": null, "enterpriseName": null, "email": null, "status": null}, "schedule": 100, "task_describe": "客户清洗-标记（开启） 执行函数 func_clearaccountsearch__c", "task_total_num": "0", "_id": "6846e8fa8c6e0100010f3cf5", "biz_id": "6780ec0cb4ec502f2dce9b87"}]