# 消息结构
```json
{"body":[{"data":{"taskId":"6840e50865b3bc3f0292bcdf","taskStatus":"completed","tenantId":"736684"}}],"op":"update_schedule","reconsumeTimes":0,"tenantId":"736684"}

{"body":[{"data":{"taskId":"6865dd19aa00d91c0a104695","taskStatus":"completed","tenantId":"736684"}}],"op":"complete","reconsumeTimes":0,"tenantId":"736684"}

{"body":[{"data":{"taskId":"68698b4865b3bc3f02a5da33","taskStatus":"completed","tenantId":"736684"}}],"op":"complete","reconsumeTimes":0,"tenantId":"736684"}

{"body":[{"data":{"taskId":"683fd9d965b3bc3f02923340","taskStatus":"completed","tenantId":"736684"}}],"op":"complete","reconsumeTimes":0,"tenantId":"736684"}

{"body":[{"data":{"taskId":"684a71cc65b3bc3f02974941","taskStatus":"canceled","tenantId":"795149"}}],"op":"canceled","reconsumeTimes":0,"tenantId":"795149"}

{"body":[{"data":{"taskId":"68469d0daa00d91c0a0153e3","taskStatus":"abort","tenantId":"795149"}}],"op":"abort","reconsumeTimes":0,"tenantId":"795149"}

```

# 接口更新
```shell
curl --request POST \
  --url http://localhost/API/v1/rest/object/AsyncTaskMonitorObj/action/IncrementUpdate \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'X-fs-Employee-Id: -10000' \
  --header 'X-fs-Enterprise-Id: 795149' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 795149' \
  --header 'x-fs-userInfo: -10000' \
  --data '{"data":{"task_status":"canceled","_id":"684a71ccb8c86d0001cd5c14"},"optionInfo":{"isDuplicateSearch":true,"skipFuncValidate":false}}'
```