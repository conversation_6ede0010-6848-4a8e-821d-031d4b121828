import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SmartFormRegex {
    // public static void main(String[] args) {
    // String regex =
    // "<smartform\\b[^>]*>(.*?)<\\/smartform\\b[^>]*>|<smartform[^>]*\\/>";
    // String input = "你好<smartform><p>Some text</p></smartform><smartform/>test";

    // Pattern pattern = Pattern.compile(regex);
    // Matcher matcher = pattern.matcher(input);

    // while (matcher.find()) {
    // System.out.println("Match found: " + matcher.group());
    // }
    // }
    // public static void main(String[] args) {
    // String input = "你好\n <smartform> \n<p>Some text</p>\n</smartform><smartform/>
    // \n test";
    // String regex =
    // "<smartform\\b[^>]*>(.*?)<\\/smartform\\b[^>]*>|<smartform[^>]*\\/>";
    // String result = input.replaceAll(regex, "");
    // System.out.println(result);
    // }
    public static void main(String[] args) {
        // String input = "<html>\n <head></head>\n <body>\n  <smartform>\n   <p style=\"margin: 0px;\"><a href=\"https://www.fqixin.cn/fsh5/smart-form/index.html?cardId=6442078937089676564a3428&amp;targetObjectDataId=${object_0FK83__c._id}\" target=\"_blank\" data-formlink=\"1\" style=\"white-space: normal;\">我的智能表单</a><a href=\"https://www.fqixin.cn/fsh5/smart-form/index.html?cardId=64426973520b2d3a97475758&amp;targetObjectDataId=${object_0FK83__c._id}\" target=\"_blank\" data-formlink=\"1\">https://www.fqixin.cn/fsh5/smart-form/index.html?cardId=64426973520b2d3a97475758&amp;targetObjectDataId=${object_0FK83__c._id}</a></p> \n  </smartform>\n  <p style=\"margin: 0px;\"><br></p>\n  <p style=\"margin: 0px;\"><br></p>\n  <p style=\"margin: 0px;\">普通图片字段</p>\n  <p style=\"margin: 0px;\"><img class=\"img-field\" data-original=\"object_0FK83__c.field_tx9K2__c\" title=\"图片\" data-img-url=\"${object_0FK83__c.field_tx9K2__c}\" src=\"https://a9.fspage.com/FSR/frontend/html/paas/template/assets/images/image-74a8b3bfbd.png\" width=\"386\" height=\"246\" load_id=\"v9601swb9jr\"></p>\n  <p style=\"margin: 0px;\"><br></p>\n  <p style=\"margin: 0px;\">引用图片字段</p>\n  <p style=\"margin: 0px;\"><img class=\"img-field\" data-original=\"object_0FK83__c.field_PPr0D__c\" title=\"引用字段图片\" data-img-url=\"${object_0FK83__c.field_PPr0D__c}\" src=\"https://a9.fspage.com/FSR/frontend/html/paas/template/assets/images/image-74a8b3bfbd.png\" width=\"386\" height=\"246\" load_id=\"pg786tgmzug\"></p>\n  <p style=\"margin: 0px;\"><br></p>\n  <p style=\"margin: 0px;\">动态表格里的图片</p>\n  <table class=\"paas-template-dtable editor adaptbywindow\" data-autoincrement=\"\" data-summarytr=\"\" data-type=\"dtable\" data-datasourcetype=\"ref\" data-datasource=\"object_wFdDq__c_____target_related_list_1kryo__c\" data-recordtype=\"\">\n   <thead>\n    <tr class=\"firstRow\">\n     <th width=\"239\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><span class=\"variable-label\">图片</span></th>\n     <th width=\"26\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></th>\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></th>\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></th>\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></th>\n    </tr>\n   </thead>\n   <tbody><!--#for(item:object_wFdDq__c_____target_related_list_1kryo__c_list)-->\n    <tr>\n     <td width=\"109\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><img class=\"img-field\" data-original=\"object_wFdDq__c_____target_related_list_1kryo__c.field_pgmZf__c\" title=\"图片\" data-img-url=\"${item.field_pgmZf__c}\" src=\"https://a9.fspage.com/FSR/frontend/html/paas/template/assets/images/image-74a8b3bfbd.png\" width=\"386\" height=\"246\" load_id=\"a07z1ahjeiu\"></td>\n     <td width=\"5\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></td>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></td>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></td>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></td>\n    </tr><!--#end-->\n   </tbody>\n   <tfoot>\n    <tr>\n     <td colspan=\"5\" align=\"center\" contenteditable=\"false\" style=\"border:1px solid #EEEEEE\">表格行数会根据实际数据量自动加载</td>\n    </tr>\n   </tfoot>\n  </table>\n  <p style=\"margin: 0px;\"><br></p>\n  <table width=\"100%\" class=\"adaptbywindow table-layout-v1\" style=\"width: 756px;\">\n   <tbody>\n    <tr class=\"firstRow\">\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><span class=\"variable-wrapper\" contenteditable=\"false\" title=\"当前时间\" data-original=\"globalVars.currentTime__g\">${globalVars.currentTime__g}</span><br></td>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n    </tr>\n    <tr>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n    </tr>\n    <tr>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n     <td valign=\"top\" style=\"border-color: rgb(118, 146, 60);\" width=\"231\"><br></td>\n    </tr>\n   </tbody>\n  </table>\n  <table width=\"100%\" class=\"paas-template-dtable editor adaptbywindow\" data-autoincrement=\"\" data-summarytr=\"\" data-type=\"dtable\" data-datasourcetype=\"ref\" data-datasource=\"object_g6x4x__c\" data-recordtype=\"\">\n   <thead>\n    <tr class=\"firstRow\">\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><span class=\"variable-label\">主属性</span></th>\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><span class=\"variable-label\">数字</span></th>\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><span class=\"variable-label\">人员</span></th>\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></th>\n     <th width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></th>\n    </tr>\n   </thead>\n   <tbody><!--#for(item:object_g6x4x__c_list)-->\n    <tr>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><span class=\"variable-wrapper\" contenteditable=\"false\" title=\"主属性\" data-original=\"object_g6x4x__c.name\">${item.name}</span></td>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><span class=\"variable-wrapper\" contenteditable=\"false\" title=\"数字\" data-original=\"object_g6x4x__c.field_sy42S__c\">${item.field_sy42S__c}</span></td>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><span class=\"variable-wrapper\" contenteditable=\"false\" title=\"人员\" data-original=\"object_g6x4x__c.field_tcyzo__c\">${item.field_tcyzo__c}</span></td>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></td>\n     <td width=\"\" align=\"\" style=\"padding:3px 10px;border:1px solid #EEEEEE;background-color:#FFFFFF\"><br></td>\n    </tr><!--#end-->\n   </tbody>\n   <tfoot>\n    <tr>\n     <td colspan=\"5\" align=\"center\" contenteditable=\"false\" style=\"border:1px solid #EEEEEE\">表格行数会根据实际数据量自动加载</td>\n    </tr>\n   </tfoot>\n  </table>\n  <p><br></p>\n </body>\n</html>";
        // String regex = "<smartform\\b[^>]*>([\\s\\S]*?)<\\/smartform\\b[^>]*>";
        // String result = input.replaceAll(regex, "");
        // System.out.println(result);
        // Bubble sort
        


    }
}
