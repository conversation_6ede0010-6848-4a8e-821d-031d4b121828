curl -i --request POST \
  --url http://localhost/API/v1/rest/object/validate_rule/service/cacheValidate \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 442289' \
  --header 'x-fs-userInfo: 1037' \
  --data '{"describeVersion":59,"detailCacheValid":[{"describeVersion":43,"detailLayoutList":[{"layout_api_name":"layout_1it2o__c","layout_last_modified_time":1696642887526,"layout_version":31,"recordType":"default__c"}],"objectApiName":"object_8FFks__c"},{"describeVersion":3,"detailLayoutList":[],"objectApiName":"object_x6C9t__c"}],"layout_api_name":"layout_pL12I__c","layout_last_modified_time":1735797336227,"layout_type":"add","layout_version":44,"objectApiName":"object_MYEoK__c","recordType":"default__c"}'


  watch com.facishare.paas.appframework.metadata.DescribeLogicService filterDescribesWithActionCode '{params,returnObj,throwExp}' 'params[0].getTenantId() == "442289"'  -n 5  -x 4 


  watch com.facishare.paas.appframework.metadata.LayoutLogicService findObjectLayoutWithType '{params,returnObj,throwExp}' ''  -n 5  -x 3 