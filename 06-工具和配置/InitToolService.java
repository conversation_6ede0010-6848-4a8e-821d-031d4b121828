package com.facishare.paas.appframework.metadata.tools;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.RuntimeUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.tools.BrushDescribe;
import com.facishare.paas.appframework.metadata.dto.tools.BrushLayouts;
import com.facishare.paas.appframework.metadata.dto.tools.InitTools;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseIdsByEnvironment;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service("initToolService")
public class InitToolServiceImpl implements InitToolService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private MetaDataActionService metaDataActionService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private RedisDao redisDao;
    private volatile boolean finished = false;

    @Override
    public List<Integer> getEnterpriseList(Integer currentEnv, List<Integer> runStatusList) {
        GetEnterpriseIdsByEnvironment.Arg envArg = new GetEnterpriseIdsByEnvironment.Arg();
        envArg.setEnv(currentEnv);
        envArg.setRunStatusList(CollectionUtils.empty(runStatusList) ? Lists.newArrayList(2) : runStatusList);
        GetEnterpriseIdsByEnvironment.Result ret = enterpriseEditionService.getEnterpriseIdsByEnvironment(envArg);
        return ret.getEnterpriseIds();
    }

    @Override
    public void execute(InitTools.Arg arg) {
        List<String> enterpriseIds = arg.getEnterpriseIds();
        if (CollectionUtils.empty(enterpriseIds)) {
            return;
        }

        String taskRecordType = arg.getTaskRecordType();
        String taskDataId = arg.getTaskDataId();
        if (!redisDao.exists(InitToolService.getKey(taskRecordType, taskDataId))) {
            redisDao.set(InitToolService.getKey(taskRecordType, taskDataId), "RUNNING", 60 * 60 * 12);
        }
        redisDao.incrBy(InitToolService.getKey(taskRecordType, taskDataId, "TOTAL"), enterpriseIds.size());
        ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
        scheduledExecutorService.scheduleAtFixedRate(() -> {
            if (!finished) {
                // 执行定时任务逻辑
                updateTaskProgress(arg.getTaskTenantId(), arg.getTaskDescribeApiName(), arg.getTaskDataId(), arg.getTaskRecordType());
            } else {
                scheduledExecutorService.shutdown();  // 完成定时任务,关闭executor
            }

        }, 1, 1, TimeUnit.MINUTES);
        if (enterpriseIds.size() < 10) {
            enterpriseIds.forEach(enterpriseId -> execute(arg, enterpriseId));
        } else {
            ParallelUtils.ParallelTask initToolsTask = ParallelUtils.createInitToolsTask();
            enterpriseIds.parallelStream().forEach(enterpriseId ->
                    initToolsTask.submit(() -> execute(arg, enterpriseId))
            );
        }
    }

    private void execute(InitTools.Arg arg, String enterpriseId) {
        if (!redisDao.exists(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId()))) {
            redisDao.sadd(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), "failed"), enterpriseId);
        } else {
            if (arg instanceof BrushDescribe.Arg) {
                executeBrushDescribe((BrushDescribe.Arg) arg, enterpriseId);
            } else if (arg instanceof BrushLayouts.Arg) {
                executeBrushLayout((BrushLayouts.Arg) arg, enterpriseId);
            }
        }
    }

    private void updateTaskProgress(String taskTenantId, String taskDescribeApiName, String taskDataId, String taskRecordType) {
        User user = User.systemUser(taskTenantId);
        IObjectData objectData = metaDataFindService.findObjectData(user, taskDataId, taskDescribeApiName);
        Long succeed = redisDao.lpush(InitToolService.getKey(taskRecordType, taskDataId, "succeed"));
        Long failed = redisDao.lpush(InitToolService.getKey(taskRecordType, taskDataId, "failed"));
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("success_tenant_num__c", String.valueOf(succeed));
        fieldMap.put("failure_tenant_num__c", String.valueOf(failed));
        Long total = redisDao.llen(InitToolService.getKey(taskRecordType, taskDataId, "TOTAL"));
        long processCount = succeed + failed;
        if (processCount == total) {
            BigDecimal completedProgress = new BigDecimal(String.valueOf(processCount))
                    .divide(new BigDecimal(String.valueOf(total)), 4, RoundingMode.HALF_UP);
            fieldMap.put("completed_progress__c", completedProgress.toPlainString());
            fieldMap.put("execution_status__c", "COMPLETED");
        }
        metaDataActionService.updateWithMap(user, objectData, fieldMap);
    }


    private void executeBrushDescribe(BrushDescribe.Arg arg, String enterpriseId) {
        String objectApiName = arg.getDescribeApiName();
        try {
            Map<String, Object> describeJson = arg.getDescribeJson();
            IObjectDescribe describeDraft;
            boolean existObj = false;
            if (CollectionUtils.notEmpty(describeJson)) {
                describeDraft = new ObjectDescribe(describeJson);
                describeDraft.setTenantId(enterpriseId);
                if (StringUtils.isNotEmpty(objectApiName)) {
                    describeDraft.setApiName(objectApiName);
                }
            } else {
                describeDraft = describeLogicService.findObject(enterpriseId, objectApiName);
                existObj = true;
            }

            Map<String, Object> describeAttribute = arg.getDescribeAttribute();
            if (CollectionUtils.notEmpty(describeAttribute)) {
                describeAttribute.keySet().forEach(attr -> describeDraft.set(attr, describeAttribute.get(attr)));
            }
            Map<String, Map<String, Object>> fields = arg.getFields();
            if (CollectionUtils.notEmpty(fields)) {
                fields.keySet().forEach(fieldApiName -> {
                    IFieldDescribe fieldDescribeInDB = describeDraft.getFieldDescribe(fieldApiName);
                    IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fields.get(fieldApiName));
                    if (Objects.isNull(fieldDescribeInDB)) {
                        describeDraft.addFieldDescribe(fieldDescribe);
                    } else {
                        describeDraft.updateFieldDescribe(fieldDescribe);
                    }
                });
            }
            if (existObj) {
                describeLogicService.update(describeDraft);
            } else {
                describeLogicService.create(false, describeDraft);
            }
            redisDao.sadd(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), "succeed"), enterpriseId);
        } catch (Exception e) {
            log.warn("createOrUpdateDescribe error, tenantId:{}, objectApiName:{}", enterpriseId, objectApiName, e);
            redisDao.sadd(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), "failed"), enterpriseId);
            // 记录日志方便排查问题
            sendAuditLog(arg, enterpriseId, e);
        }
    }

    private static void sendAuditLog(InitTools.Arg arg, String enterpriseId, Exception e) {
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(RuntimeUtils.getAppName())
                .serverIp(RuntimeUtils.getServerIp())
                .profile(RuntimeUtils.getProfile())
                .action("initTools")
                .tenantId(enterpriseId)
                .userId(User.SUPPER_ADMIN_USER_ID)
                .objectApiNames(arg.getDescribeApiName())
                .extra(arg.getTaskRecordType())
                .num(0)
                .message(e.getMessage())
                .traceId(TraceContext.get().getTraceId())
                .build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    private void executeBrushLayout(BrushLayouts.Arg arg, String enterpriseId) {
        try {
            layoutLogicService.createOrUpdateLayoutForDevTools(enterpriseId, arg.getDescribeApiName(), arg.getLayouts());
            redisDao.sadd(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), "succeed"), enterpriseId);
        } catch (Exception e) {
            redisDao.sadd(InitToolService.getKey(arg.getTaskRecordType(), arg.getTaskDataId(), "failed"), enterpriseId);
            log.warn("createOrUpdateLayoutForDevTools error", e);
            sendAuditLog(arg, enterpriseId, e);
        }
    }
}
