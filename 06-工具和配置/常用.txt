watch com.facishare.paas.appframework.metadata.LayoutLogicServiceImpl findObjectLayoutWithType '{params,returnObj.getComponents().{#this}}'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.controller.BaseListHeaderController findDefaultLayout '{params,returnObj.getComponents().{^ #this.getName() == "form_component"}.getString()}'  -n 5  -x 4 

watch com.facishare.paas.appframework.metadata.LayoutLogicServiceImpl _findObjectLayoutByType '{params,returnObj.getComponents().{^ #this.getName() == "form_component"}.getString()}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.LayoutLogicServiceImpl findObjectLayoutWithTypeIncludeAllComponent '{params,returnObj.getComponents().{^ #this.getName() == "form_component"}.getString()}'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController getLayout  '{params,returnObj.getComponents().{^ #this.getName() == "form_component"}.getString()}'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController handleFieldAlign '{params[0].getComponents().{^ #this.getName() == "form_component"}.getString()}'  -n 5  -x 3 
watch com.facishare.paas.appframework.metadata.LayoutLogicService findObjectLayoutWithType '{params,returnObj,throwExp}'  -n 5  -x 3 


returnObj.getComponents().{^ #this.getName() == "form_component"}.getFieldSections()


watch com.facishare.paas.appframework.metadata.LayoutLogicService findObjectLayoutWithType '{params,returnObj.getComponents().{^ #this.getName() == "form_component"}.getString()}'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController asyncFillFieldInfo '{params[1]}' 'params[0].getTenantId()=="1"'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillDepartmentInfo '{@com.alibaba.fastjson.JSON@toJSONString(params[2]),returnObj}' 'params[0].getTenantId()=="1"&&params[0].getApiName()=="SalesOrderObj"'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.action.AbstractStandardEditAction classifyDetailData '{this.detailsToUpdate}'  -b -s -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl findDetailObjectDataList '{params[1],returnObj.keySet()}'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.ButtonLogicServiceImpl getButtonByComponentActions '{params,returnObj.{#this.getName()}}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.layout.LayoutRender getDetailPageButtons '{params,returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.ButtonLogicServiceImpl getButtonByComponentActions '{params,returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.layout.ButtonFilter filterByDataPrivilege '{params,returnObj.{#this.getButtons()}}'  -f -s -n 5  -x 3

watch com.facishare.paas.appframework.metadata.layout.ButtonFilter filterByObjectDescribe '{params,returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl checkOutUserDataPrivilege '{params,returnObj}'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController findObjectDataWithChangeOrder '{params,returnObj}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.LayoutLogicService findLayoutByApiName '{params,returnObj.getComponents().{^ #this.getName() == "tabs_7hISm__c"}.{#this.get("components")}}' 'params[1] == "layout_TNr9d__c"' -n 5  -x 3 

watch com.facishare.paas.metadata.api.service.ILayoutService findByNameAndObjectDescribeApiNameAndTenantId '{params,returnObj.getComponents().{^ #this.getName() == "tabs_7hISm__c"}.{#this.get("components")}}' 'params[2] == "layout_TNr9d__c"' -n 5  -x 3 
watch com.facishare.paas.metadata.dao.pg.mapper.metadata.UiComponentMapper find '{params,returnObj.getComponents()}'  'params[0] == "layout_TNr9d__c"'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillRefObjNameToData '{params[2],params[2].containsKey("object_i1EHR__c_658be3271dd1330001d8bb73"),params[2]["object_i1EHR__c_658be3271dd1330001d8bb73"]}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl createNameCacheKey '{params}' 'params[1] == "658be3271dd1330001d8bb73"'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillRefObjNameToData '{params[0].{ #{"id":#this.get("field_l5IaO__c"), "label":#this.get("field_l5IaO__c__r")}}'  -n 5  -x 3 -b -s

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillRefObjNameToData '{params[0].{ #this.get("field_l5IaO__c")}, params[0].{ #this.get("field_l5IaO__c__r")}}'  -n 5  -x 3 -b -s

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillRefObjNameToData '{params[0].{ #this.get("field_l5IaO__c")}, params[0].{ #this.get("field_l5IaO__c__r")},  params[2].containsKey("object_i1EHR__c_658be3271dd1330001d8bb73"),   params[2].get("object_i1EHR__c_658be3271dd1330001d8bb73")}'  -n 5  -x 3 -b -s


watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillRefObjNameToData '{params[1].{^ #this.getApiName() == "field_l5IaO__c"}.{ #this}}'  -n 5  -x 3 -b -s

watch com.facishare.paas.appframework.metadata.FieldDescribeExt isWhatListField '{returnObj}' 'this.getApiName() == "field_l5IaO__c"'  -n 5  -x 3 
watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillRefObjNameToData '{params[1].{^ #this.getApiName() == "field_l5IaO__c"}.{ #this}.size(), #target = target, params[1].size(),params[1].{^ #this.getApiName() == "field_l5IaO__c"}.{ #target.getRefObjTargetApiName(#this, params[0].get(0))}}'  -n 5  -x 4


watch com.facishare.paas.appframework.core.predef.action.StandardCloneAction fillDataInfo '{params[0].getFieldDescribes().{#this.getType() == "object_reference"},{params[0].getFieldDescribes().{#this.getApiName() == "field_l5IaO__c"}.get(0)}' 'params[0].getApiName() == "object_o0ie1__c"'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.action.StandardCloneAction fillDataInfo '{params[0].getFieldDescribes().{ #this.getType() == "object_reference" ? #this.getApiName() : ""}}' 'params[0].getApiName() == "object_o0ie1__c"'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillObjectDataWithRefObject '{params[5].{ #this.getApiName() == "field_l5IaO__c" ? #this : null}}, ' 'params[0].getApiName() == "object_o0ie1__c"'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillObjectDataWithRefObject '{params[0].{ #this.getApiName() == "field_l5IaO__c" ? #this : null}}, ' 'params[0].getApiName() == "object_o0ie1__c"'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindService findDetailObjectDataList '{params[0],returnObj.keySet()}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindService findDetailObjectDataList '{params[0].{ #this.getApiName()} ,returnObj}' 'params.length== 3 && params[0] instanceof java.util.List'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl processFilterSupportDisplayName '{params[2].getFilters()}' 'params[0]==1'   -b -s -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl getSearchTemplateQueryWithIgnoreSceneFilter '{params[2].getFilters()}' 'params[0].getTenantId()==1'  -n 5  -x 4 

watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl mergeFilter '{params[3]}.getFilters()'  'params[0].getTenantId()==1 && params[0].getUserId() == 7719'   -n 5  -x 4

watch com.facishare.paas.appframework.metadata.MetaDataFindService getSearchTemplateQueryWithIgnoreSceneFilter '{params[2].getFilters()}'  'params[0].getTenantId()==1'  -n 20  -x 4

watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl findSearchQuery '{@com.alibaba.fastjson.JSON@toJSONString(returnObj.getSearchQuery().get().getSearchQueryContainer())}'  'params[0].getTenantId()==1 && params[0].getUserId() == 7719'  -n 5  -x 4

watch com.facishare.paas.appframework.core.predef.action.AbstractConvertAction filterDetailObjectData '{params[1].{ #this.get("product_id") != null ? #this.get("product_id") : null}}' 'params[0].getApiName() == "SaleContractLineObj"'  -n 5  -x 3 
watch com.facishare.paas.appframework.core.predef.action.AbstractConvertAction filterDetailObjectData '{params[1]}' 'params[0].getApiName() == "SaleContractLineObj"'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl findBySearchQuery '{@com.alibaba.fastjson.JSON@toJSONString(params[2]),returnObj.getData().{ #this.get("product_id") != null ? #this.get("product_id") : null}}'  'params[1] == "SaleContractLineObj"'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl buildDetailSearchTemplateQuery '{params[1], @com.alibaba.fastjson.JSON@toJSONString(returnObj)}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindService buildDetailSearchTemplateQuery '{ @com.alibaba.fastjson.JSON@toJSONString(returnObj)}'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.ObjectMappingService mappingDataByConvertRule '{params[1].getDetails().get("SaleContractLineObj").{ #this.get("product_id") != null ? #this.get("product_id") : null},returnObj.getDetails().get("SalesOrderProductObj").{ #this.get("product_id") != null ? #this.get("product_id") : null}}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.filter.ObjectDataFilter doFilter '{params[0].{ #this.get("product_id") != null ? #this.get("product_id") : null},returnObj.{ #this.get("product_id") != null ? #this.get("product_id") : null}}'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.action.AbstractConvertAction filterDetailObjectData '{params[1].{ #this.get("product_id") != null ? #this.get("product_id") : null}, target.sourceDetailObjectData.get("SaleContractLineObj") != null ? target.sourceDetailObjectData.get("SaleContractLineObj").{ #this.get("product_id") != null ? #this.get("product_id") : null} : null}' 'params[0].getApiName() == "SaleContractLineObj"'  -n 5  -x 3 -b -s


watch com.facishare.paas.appframework.metadata.filter.ObjectDataFilter doFilter '{params[0].{ #this.get("product_id") != null ? #this.get("product_id") : null},returnObj.{ #this.get("product_id") != null ? #this.get("product_id") : null}, target.queryExt.toJsonString()' '{params[0].get(0).get("object_describe_api_name") == "SaleContractLineObj"}'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.filter.ObjectDataFilter doFilter '{params[0].{ #this.get("product_id") != null ? #this.get("product_id") : null},returnObj.{ #this.get("product_id") != null ? #this.get("product_id") : null}, target.queryExt.toJsonString(), target.describeExt.getApiName(), target.filterLabel}'   -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.filter.ObjectDataFilter filterWithWheres '{params[0].{ #this.get("product_id") != null ? #this.get("product_id") : null},returnObj.{ #this.get("product_id") != null ? #this.get("product_id") : null}, target.queryExt.toJsonString(), target.describeExt.getApiName(), target.filterLabel}'   -n 5  -x 3 

watch com.facishare.paas.metadata.api.service.IObjectDescribeService findByExample '{params,returnObj.{ #this.getApiName() != null ? #this.getApiName() : null} }'  -n 5  -x 3 

watch com.facishare.paas.metadata.api.service.IObjectDescribeService findByExample '{params,returnObj.{ #this.getApiName() == "FlowAutoTaskObj" ? #this.getApiName() : null} }' 'params[0] == "71557"'  -n 5  -x 3 

watch com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl getSystemDescribesWithMapOptimize '{params,returnObj.{ #this.getDescribeApiName() == "SparePartsDeliveryObj" ? #this.getDescribeApiName() : null},throwExp}' 'params[0] == "776739"'  -n 5  -x 3


watch com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl handleObjectsWith '{params[2].{ #this.getApiName() != null ? #this.getApiName() : null} }'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl findObjectsByTenantId '{returnObj.{ #this.getApiName() != null ? #this.getApiName() : null}}'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.tools.InitToolServiceImpl updateFieldByMap '{ returnObj.{ #this.getApiName() != null ? #this.toJsonString() : null}}'  -n 5  -x 3 


watch com.facishare.webpage.customer.metadata.WebMainChannelMetaDataServiceImpl filterWebMainChannelMetaDataList '{params[1].{ #this.getAppId() != null ? #this.getAppId() : null},returnObj.{ #this.getAppId() != null ? #this.getAppId() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.metadata.InnerApplicationServiceImpl getManAppDataList '{params,returnObj.{#this.getAppId() != null ? #this.getAppId() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.metadata.ConfigWebAppService getMenuList '{params,returnObj.{#this.getId() != null ? #this.getId() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.metadata.ConfigWebAppService filterCrossPermissionToTenantMenus '{params[1].{#this.getId() != null ? #this.getId() : null},returnObj}'  -n 5  -x 3 

watch com.facishare.webpage.customer.service.impl.TenantMenuServiceImpl findTenantMenuById '{returnObj.getMetaMenuDataList().{#this.getApiName() != null ? #this.getApiName() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.controller.impl.TenantMenuActionImpl getTenantMenuItemVO '{returnObj.{#this.getApiName() == "FMCGSerialNumberStatusObj" ? #this.getApiName() : null}}'  -n 5  -x 3



watch com.facishare.webpage.customer.metadata.MetaMenuServiceImpl getMetaMenuList '{returnObj.{#this.getApiName() != null  ? #this.getApiName() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.metadata.MetaMenuServiceImpl filterMetaMenuByGray '{params[2].{#this.getApiName() == "FMCGSerialNumberStatusObj"  ? #this.getApiName() : null},returnObj.{#this.getApiName() == "FMCGSerialNumberStatusObj"  ? #this.getApiName() : null}}'  -n 5  -x 3



watch com.facishare.webpage.customer.metadata.ObjectMetaDataService getObjectMenus '{returnObj.{#this.getApiName() != null  ? #this.getApiName() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.util.MenuObjUtil buildSimpObjectByObjDescribes '{params[1].{#this.getDescribeApiName() == "FMCGSerialNumberStatusObj"  ? #this.getDescribeApiName() : null}}'  -n 5  -x 3 


watch com.facishare.webpage.customer.remote.impl.ObjectServiceImpl getObjectMenus '{returnObj.{#this.getApiName() == "FMCGSerialNumberStatusObj"  ? #this.getApiName() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.metadata.ObjectMetaDataService getObjectMenus '{returnObj.{#this.getApiName() == "FMCGSerialNumberStatusObj"  ? #this.getApiName() : null}}'  -n 5  -x 3 

watch com.facishare.webpage.customer.metadata.ObjectMetaDataService queryMetaData '{params,returnObj.getMetaMenuDataList().{#this.getApiName() == "FMCGSerialNumberStatusObj"  ? #this.getApiName() : null}}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl batchConvertRichTextFieldFromMetadata '{params[0].{#this != null ? #this.toJsonString() : null},returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.metadata.dispatcher.ObjectDataProxy findBySearchQuery '{params,returnObj.getData().{#this != null ? #this.toJsonString() : null},throwExp}' 'params[1] == "object_t26g1__c"' -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.QuoteValueServiceImpl findObjectData '{params[1],returnObj.{#this != null ? #this.toJsonString() : null},throwExp}' 'params[0].getTenantId()=="40200097"'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.QuoteValueServiceImpl fillUdObjData '{params[1].getDataList().{#this != null ? #this.toJsonString() : null},throwExp}' 'params[0].getTenantId()=="40200097"' -n 5  -x 3 -b -s 


logger --name com.facishare.paas.appframework.metadata.QuoteValueServiceImpl --level debug

watch com.facishare.paas.appframework.metadata.QuoteValueServiceImpl getIdList '{params,returnObj,throwExp}'  -n 10  -x 3 

watch com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl findDescribeByPrivilegeAndModule '{params,returnObj.{#this.getDisplayName() != null  ? #this.getDisplayName() :  #this.getApiName()},throwExp}'  -n 5  -x 3

watch com.facishare.webpage.customer.config.PresetLinkAppConfig getLinkAppVOList '{params,returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindService findSearchQuery '{params[2],@com.alibaba.fastjson.JSON@toJSONString(returnObj.toSearchTemplateQuery()),throwExp}'  -n 5  -x 3

watch com.facishare.paas.appframework.metadata.MetaDataFindService getSearchTemplateQueryWithIgnoreSceneFilter '{params[4],@com.alibaba.fastjson.JSON@toJSONString(returnObj),throwExp}'  -n 5  -x 3

watch com.facishare.paas.appframework.metadata.treeview.TreeViewService findTreeViewObjDataListByQuery '{@com.alibaba.fastjson.JSON@toJSONString(params[4]),returnObj.size(),throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.controller.StandardTreeListController filterOutPermissionDeniedData '{@com.alibaba.fastjson.JSON@toJSONString(params[1]),returnObj}'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateServiceImpl computeCalculateFieldsForEditData '{@com.alibaba.fastjson.JSON@toJSONString(params[0].getMasterData()),@com.alibaba.fastjson.JSON@toJSONString(params[0].getOldMasterData()),throwExp}'  -n 5  -x 3 


watch com.facishare.webpage.customer.service.impl.PaaSAppServiceImpl getLinkAppVOList '{params,returnObj,throwExp}' 'params[0].getTenantId()==40164270'  -n 5  -x 3 
watch com.facishare.webpage.customer.dao.PaaSAppDaoImpl getLinkAppVOListQuery '{params[0].getTenantId(),params[0].getLinkAppIds(),returnObj,throwExp}'  'params[0].getTenantId()==40164270'  -n 5  -x 3 

watch com.facishare.webpage.customer.dao.PaaSAppDaoImpl getLinkAppRoleList '{params,returnObj,throwExp}' 'params[0].getTenantId()==40164270'   -n 5  -x 3 

watch com.facishare.webpage.customer.rest.SynlinkDataRestServiceImpl listUptreamLinkAppByPage '{params,returnObj.getData(),throwExp}'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.controller.StandardDetailListController findAndMergeSnapshot '{@com.alibaba.fastjson.JSON@toJSONString(params[0])}'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.facade.ChangeOrderHandlerLogicService findAndMergeObjectDataWithOriginalData '{@com.alibaba.fastjson.JSON@toJSONString(params[2])}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataMiscService fillMaskFieldValue '{@com.alibaba.fastjson.JSON@toJSONString(params[1])}'  -b -s -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.mask.MaskFieldLogicServiceImpl maskFieldRoleFilter '{params,returnObj,throwExp}'  -n 5  -x 3 
watch com.facishare.paas.appframework.metadata.mask.MaskFieldLogicServiceImpl maskFieldRoleFilter '{params[1].{#this.getApiName()}, returnObj.{#this.getApiName()}}' -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl fillMaskFieldValue '{params,returnObj,throwExp}'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.controller.BaseListController asyncFillFieldInfo '{params[1].{#this.get("dealer_transaction_price_tax__c")}}' 'params[0] == "order_product__c"'-b -s -n 5  -x 3


watch com.facishare.paas.appframework.core.predef.controller.BaseListController asyncFillFieldInfo '{params[0].getApiName(),params[1].{#this.get("dealer_transaction_price_tax__c")}}' -b -s -n 5  -x 3

watch com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicServiceImpl findAndMergeObjectDataWithOriginalData '{params[2].{#this.get("dealer_transaction_price_tax__c")},returnObj.{#this.get("dealer_transaction_price_tax__c")},throwExp}'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.controller.BaseListController getQueryResult '{returnObj.getData().{#this.get("dealer_transaction_price_tax__c")},throwExp}'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.controller.StandardDetailListController buildResult '{params[2].getData().{#this.get("dealer_transaction_price_tax__c")},returnObj.getDataList().{#this.get("dealer_transaction_price_tax__c")},throwExp}'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.LayoutRuleLogicServiceImpl findValidLayoutRuleByLayout '{params[2],returnObj.{#this.getApiName() + ":" + #this.getType()}, returnObj.{#this.getApiName() + ":" + #this.getLastModifiedTime()},throwExp}' 'params[1] == "object_80bM8__c"' -n 5  -x 4  

watch com.facishare.paas.appframework.metadata.LayoutRuleLogicServiceImpl findValidLayoutRuleByLayout '{params,returnObj, returnObj.{#this.getApiName() + ":" + #this.getLastModifiedTime()},throwExp}' -n 5  -x 4  

watch com.facishare.paas.appframework.metadata.LayoutRuleLogicServiceImpl validateLayoutRuleLastModifiedTime '{params[1],params[2],params[3].getName(),returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl findObjectsWithoutCopy '{params,returnObj == null ? null : @com.alibaba.fastjson.JSON@toJSONString(returnObj),throwExp}' 'params[0]=="74255"'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.DescribeLogicService findObjectsWithoutCopy '{params,returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.DescribeLogicService findObjectsWithoutCopy '{params,returnObj.get("object_solitude__c") == null ? null : returnObj.get("object_solitude__c").toJsonString(),throwExp}' 'params[0]=="74255"'  -n 5  -x 3 
watch com.facishare.paas.appframework.metadata.FieldDescribeExt of '{params,returnObj.toJsonString(),throwExp}'  -n 5  -x 3 


  
  
watch com.facishare.webpage.customer.dao.LinkAppObjectAssociationDao listByTypeAndUpstreamAndAppId '{params[2],returnObj,throwExp}' 'params[2] == "FSAID_127a3981"'  -n 5  -x 3 


watch com.facishare.paas.score.core.logic.RuleEngineLogicService batchCalculate '{params[0],returnObj,throwExp}' 'params[0].getObjectApiName() == "object_j4t83__c"'  -n 5  -x 3 


watch com.facishare.paas.score.core.rest.api.RuleEngineRest batchCalculate '{params,@com.alibaba.fastjson.JSON@toJSONString(returnObj),throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataFindService findDetailObjectDataList '{params,@com.alibaba.fastjson.JSON@toJSONString(returnObj),throwExp}'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl calculateCountFields '{params[1], @com.alibaba.fastjson.JSON@toJSONString(params[2]), params[3].toJsonString(),throwExp}'  -n 20 -x 3 -b -s

watch com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl getCountResultWithContext '{params[1], @com.alibaba.fastjson.JSON@toJSONString(params[2]), params[3].toJsonString(),throwExp}'  -n 20 -x 3 -b -s


watch com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl _batchCalculate '{@com.alibaba.fastjson.JSON@toJSONString(params[0]), @com.alibaba.fastjson.JSON@toJSONString(params[1]), params[2].getApiName(),throwExp}' 'params[2].getApiName() == "object_sourceD1__c"'  -n 10  -x 3 -b -s


watch com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl getCountResultWithContext '{params[1],params[2].getDescribeApiName(), @com.alibaba.fastjson.JSON@toJSONString(params[4]),returnObj==null ? null : returnObj.longValue(),throwExp}' 'params[2].getDescribeApiName() == "object_sourceD1__c"&&params[2].getApiName() == "field_dfuwX__c"'  -n 30 -x 3


watch com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl getCountResultWithContext '{params[2].getDescribeApiName(), params[3].toJsonString(), @com.alibaba.fastjson.JSON@toJSONString(params[4]),returnObj==null ? null : returnObj.longValue(),throwExp}'  -n 10 -x 3


watch com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl statCanQueryInEs '{params,returnObj,throwExp}'  'params[0] == "700609"&&params[1] == "object_targetD1__c"&&params[3]=="field_dfuwX__c"'  -n 30  -x 3 



watch com.fxiaoke.paas.auth.factory.FieldClient userFieldPermission '{@com.alibaba.fastjson.JSON@toJSONString(params[0]),returnObj,throwExp}'  -n 5  -x 3 


watch com.fxiaoke.paas.auth.factory.FieldClient userFieldPermission '{params[1].toString(),returnObj,throwExp}'  -n 5  -x 3 

watch com.fxiaoke.paas.auth.factory.FieldClient userFieldPermission '{params,returnObj,throwExp}'  -n 5  -x 3 



watch com.facishare.paas.metadata.dispatcher.ObjectDataProxy findBySearchQuery '{params,returnObj,throwExp}'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.action.BaseImportDataAction callPreProcessingFunction '{@com.alibaba.fastjson.JSON@toJSONString(params[1]),throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.core.predef.action.BaseImportDataAction getRefDefObjDataByAggregateSearchQuery '{params,returnObj,throwExp}'  -n 5  -x 3 
watch com.facishare.paas.appframework.core.predef.action.BaseImportDataAction getRefDefObjDataByAggregateSearchQuery '{params,returnObj,throwExp}' 'params[1] == "ProductObj"'  -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.ImportServiceImpl findImportReferenceMapping '{params,returnObj,throwExp}'  'params[0].getTenantId() == "814947"'  -n 10  -x 3 

watch com.facishare.paas.appframework.core.predef.action.BaseImportDataAction batchHandleObjectReference '{params,returnObj,throwExp}'  -n 10  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataActionServiceImpl batchUpdate '{params,returnObj,throwExp}' 'params[2].get == "object_sourceD1__c"' -n 5  -x 3 

watch com.facishare.webpage.customer.core.service.I18nService getTransValueIncludePreKey '{params,returnObj,throwExp}' 'params[0] == 158833' -n 5  -x 3 


watch com.facishare.paas.appframework.metadata.MetaDataActionService batchUpdate '{@com.alibaba.fastjson.JSON@toJSONString(params[1]),returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.MetaDataActionServiceImpl batchUpdate '{@com.alibaba.fastjson.JSON@toJSONString(params[1]),returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.webpage.customer.api.service.UserWebMainChannelService queryUserWebMainChannelVO '{params,returnObj,throwExp}' 'params[0] == "811342"'  -n 5  -x 3 


watch com.facishare.paas.appframework.core.predef.action.BaseImportDataAction getRefDefObjDataByAggregateSearchQuery '{params,returnObj,throwExp}' 'params[1] == "AccountObj"'  -n 5  -x 3 

watch com.fxiaoke.enterpriserelation2.service.PublicEmployeeObjService batchGetOutUserInfoByName '{@com.alibaba.fastjson.JSON@toJSONString(params[0]), @com.alibaba.fastjson.JSON@toJSONString(params[1]),returnObj,throwExp}'  -n 5  -x 3 

watch com.facishare.paas.appframework.metadata.importobject.dataconvert.SelectOneImportFieldDataConverter convertFieldData '{params[0],@com.alibaba.fastjson.JSON@toJSONString(params[1]),returnObj,throwExp}'  -n 5  -x 3 


  watch com.facishare.paas.appframework.metadata.LayoutLogicService findObjectLayoutWithType '{params,@com.alibaba.fastjson.JSON@toJSONString(returnObj),throwExp}' 'params[0].getTenantId() == "442289"'  -n 5  -x 3 

  watch com.facishare.paas.appframework.metadata.MetaDataFindService findBySearchQuery '{params,returnObj,throwExp}' 'params[0].getTenantId() == "442289"'  -n 5  -x 3 

  watch com.facishare.paas.appframework.core.predef.action.AbstractConvertAction filterDetailObjectData '{@com.alibaba.fastjson.JSON@toJSONString(params[0]),@com.alibaba.fastjson.JSON@toJSONString(params[1]), @com.alibaba.fastjson.JSON@toJSONString(params[2]), @com.alibaba.fastjson.JSON@toJSONString(params[3]), @com.alibaba.fastjson.JSON@toJSONString(params[4]),returnObj,throwExp}'  -n 5  -x 3 

  watch com.facishare.paas.appframework.metadata.ConvertRuleLogicServiceImpl buildQueryConditionContainer '{params,@com.alibaba.fastjson.JSON@toJSONString(returnObj),throwExp}'   -n 5  -x 3 