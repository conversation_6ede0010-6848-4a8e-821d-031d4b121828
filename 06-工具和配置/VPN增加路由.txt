route delete 10.21.0.0/16 10.190.101.1 -p
route delete 10.22.0.0/16 10.190.101.1 -p
route delete 10.28.0.0/16 10.190.101.1 -p
route delete **********/16 10.190.101.1 -p
route delete 172.26.0.0/16 10.190.101.1 -p
route delete 172.27.0.0/16 10.190.101.1 -p
route delete **********/16 10.190.101.1 -p
route delete **********/16 10.190.101.1 -p
route delete **********/16 10.190.101.1 -p
route delete 10.112.0.0/12 10.190.101.1 -p



route add 10.21.0.0/16 10.190.101.7 -p
route add 10.22.0.0/16 10.190.101.7 -p
route add 10.28.0.0/16 10.190.101.7 -p
route add **********/16 10.190.101.7 -p
route add 172.26.0.0/16 10.190.101.7 -p
route add 172.27.0.0/16 10.190.101.7 -p
route add **********/16 10.190.101.7 -p
route add **********/16 10.190.101.7 -p
route add **********/16 10.190.101.7 -p
route add 10.112.0.0/12 10.190.101.7 -p


route add 10.21.0.0/16 10.190.101.6 -p
route add 10.22.0.0/16 10.190.101.6 -p
route add 10.28.0.0/16 10.190.101.6 -p
route add **********/16 10.190.101.6 -p
route add 172.26.0.0/16 10.190.101.6 -p
route add 172.27.0.0/16 10.190.101.6 -p
route add **********/16 10.190.101.6 -p
route add **********/16 10.190.101.6 -p
route add **********/16 10.190.101.6 -p
route add 10.112.0.0/12 10.190.101.6 -p


route add 10.21.0.0/16 10.190.101.88 -p
route add 10.22.0.0/16 10.190.101.88 -p
route add 10.28.0.0/16 10.190.101.88 -p
route add **********/16 10.190.101.88 -p
route add 172.26.0.0/16 10.190.101.88 -p
route add 172.27.0.0/16 10.190.101.88 -p
route add **********/16 10.190.101.88 -p
route add **********/16 10.190.101.88 -p
route add **********/16 10.190.101.88 -p
route add 10.112.0.0/12 10.190.101.88 -p





route add 10.21.0.0/16 10.190.101.5 -p
route add 10.22.0.0/16 10.190.101.5 -p
route add 10.28.0.0/16 10.190.101.5 -p
route add **********/16 10.190.101.5 -p
route add 172.26.0.0/16 10.190.101.5 -p
route add 172.27.0.0/16 10.190.101.5 -p
route add **********/16 10.190.101.5 -p
route add **********/16 10.190.101.5 -p
route add **********/16 10.190.101.5 -p
route add 10.112.0.0/12 10.190.101.5 -p

route add 10.21.0.0/16 10.190.101.10 -p
route add 10.22.0.0/16 10.190.101.10 -p
route add 10.28.0.0/16 10.190.101.10 -p
route add **********/16 10.190.101.10 -p
route add 172.26.0.0/16 10.190.101.10 -p
route add 172.27.0.0/16 10.190.101.10 -p
route add **********/16 10.190.101.10 -p
route add **********/16 10.190.101.10 -p
route add **********/16 10.190.101.10 -p
route add 10.112.0.0/12 10.190.101.10 -p

route add 10.21.0.0/16 10.190.101.19 -p
route add 10.22.0.0/16 10.190.101.19 -p
route add 10.28.0.0/16 10.190.101.19 -p
route add **********/16 10.190.101.19 -p
route add 172.26.0.0/16 10.190.101.19 -p
route add 172.27.0.0/16 10.190.101.19 -p
route add **********/16 10.190.101.19 -p
route add **********/16 10.190.101.19 -p
route add **********/16 10.190.101.19 -p
route add 10.112.0.0/12 10.190.101.19 -p

route add 10.21.0.0/16 10.190.101.135 -p
route add 10.22.0.0/16 10.190.101.135 -p
route add 10.28.0.0/16 10.190.101.135 -p
route add **********/16 10.190.101.135 -p
route add 172.26.0.0/16 10.190.101.135 -p
route add 172.27.0.0/16 10.190.101.135 -p
route add **********/16 10.190.101.135 -p
route add **********/16 10.190.101.135 -p
route add **********/16 10.190.101.135 -p
route add 10.112.0.0/12 10.190.101.135 -p


networksetup -setadditionalroutes STB ********** *********** 192.168.1.38 ********** *********** 192.168.1.38


networksetup -getadditionalroutes STB 

networksetup -setadditionalroutes STB


networksetup -setadditionalroutes STB ********** *********** 192.168.1.38 ********** *********** 192.168.1.38



networksetup -setadditionalroutes STB ********** *********** 192.168.31.213 ********** *********** 192.168.31.213 ********** *********** 192.168.31.213 ********** *********** 192.168.31.213 ********** *********** 192.168.31.213 ************ *************** 192.168.31.213


networksetup -setadditionalroutes STB ********** *********** 192.168.43.45 ********** *********** 192.168.43.45 ********** *********** 192.168.43.45 ********** *********** 192.168.43.45 ********** *********** 192.168.43.45 ************ *************** 192.168.43.45


networksetup -setadditionalroutes STB ********** *********** *************** ********** *********** *************** ********** *********** *************** ********** *********** *************** ********** *********** *************** ************ *************** ***************


ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print \$2}'


ifconfig en0 | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}'










#!/bin/bash

# 获取当前Wi-Fi的名称
echo "Getting Wi-Fi service name..."
WIFI_SERVICE=$(networksetup -listallhardwareports | awk '/Hardware Port: Wi-Fi/{print $3}')
echo "Wi-Fi service name: $WIFI_SERVICE"

# 获取当前Wi-Fi的DNS地址
echo "Getting DNS IP..."
DNS_IP=$(networksetup -getdnsservers "$WIFI_SERVICE")

# 如果获取DNS失败或返回无效结果，使用scutil --dns作为备用方案
if [[ -z "$DNS_IP" || "$DNS_IP" == *"There aren't any DNS Servers set"* ]]; then
  echo "Failed to get DNS using networksetup, trying scutil --dns..."
  DNS_IP=$(scutil --dns | awk '
    /resolver #/ { resolver = $2 }
    /nameserver\[0\]/ { dns_ip = $3 }
    /if_index : [0-9]+ \(en0\)/ && resolver == "#1" { print dns_ip; exit }
  ')
fi

# 如果仍然无法获取DNS，使用默认的DNS地址
if [ -z "$DNS_IP" ]; then
  echo "Failed to get DNS using scutil --dns, using default DNS *******..."
  DNS_IP="*******"
fi

echo "DNS IP: $DNS_IP"

# 获取当前IP地址
echo "Getting current IP..."
CURRENT_IP=$(ifconfig | grep "inet " | grep -Fv 127.0.0.1 | awk '{print $2}')
echo "Current IP: $CURRENT_IP"

# 修改/etc/resolver/stbvpn.firstshare.cn文件中的nameserver地址
echo "Updating /etc/resolver/stbvpn.firstshare.cn..."
echo "nameserver $DNS_IP" | sudo tee /etc/resolver/stbvpn.firstshare.cn > /dev/null
echo "Updated /etc/resolver/stbvpn.firstshare.cn"

# 设置名字为STB的DNS地址为此Wi-Fi提取的DNS的IP地址
echo "Setting DNS servers for STB..."
sudo networksetup -setdnsservers STB $DNS_IP
echo "Set DNS servers for STB"

# 设置路由地址
echo "Setting additional routes for STB..."
sudo networksetup -setadditionalroutes STB ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ********** *********** $CURRENT_IP ************ *************** $CURRENT_IP
echo "Set additional routes for STB"

# 输出成功
echo "Success"