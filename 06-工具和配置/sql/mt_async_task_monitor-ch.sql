 CREATE TABLE IF NOT EXISTS mt_async_task_monitor ON CLUSTER '{cluster}' (
   "id" String ,
   "tenant_id" String ,
   "created_by" LowCardinality(String) ,
   "create_time" Nullable(Int64),
   "object_describe_api_name" LowCardinality(String) ,
   "owner" LowCardinality(String) ,
   "describe_api_name" String, 
   "error_message" String, 
   "task_status" LowCardinality(String), 
   "last_modified_time" Nullable(Int64), 
   "sys_modified_time" Int64 NOT NULL DEFAULT last_modified_time * 1000 ,
   "biz_api_name" String, 
   "task_estimated_execution_time" Nullable(Int64), 
   "task_execution_time" Nullable(Int64), 
   "end_time" Nullable(Int64), 
   "submitted_by" LowCardinality(String), 
   "task_id" String, 
   "task_describe_default_params_value" String, 
   "task_describe_params_i18n_key" String, 
   "last_modified_by" LowCardinality(String), 
   "record_type" String, 
   "completed_num" Nullable(Int64), 
   "task_create_time" Nullable(Int64), 
   "task_describe_i18n_key" String, 
   "task_total_num" Nullable(Int64), 
   "biz_id" String, 
   "version" Nullable(Int32) ,
   "is_deleted" Int16 ,
   "bi_sys_flag" Int8 ,
   "bi_sys_batch_id" Int64 ,
   "bi_sys_is_deleted" UInt8 ,
   "bi_sys_version" DateTime DEFAULT now() ,
   "bi_sys_ods_part" String DEFAULT 's' ,
   INDEX idx_owner owner TYPE set(1000) GRANULARITY 1 ,
) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version, bi_sys_is_deleted) 
PARTITION BY bi_sys_ods_part 
ORDER BY ( tenant_id,bi_sys_flag,id) 
TTL bi_sys_version + INTERVAL 1 MONTH DELETE WHERE bi_sys_ods_part = 's' AND (bi_sys_flag = 0 OR is_deleted in (-1,-2)),
bi_sys_version + INTERVAL 1 WEEK DELETE WHERE bi_sys_ods_part = 'i';

