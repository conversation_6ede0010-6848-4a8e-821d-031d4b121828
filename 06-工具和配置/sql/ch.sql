 CREATE TABLE IF NOT EXISTS mt_convert_tracker ON CLUSTER '{cluster}' (
   "id" String ,
   "tenant_id" String ,
   "created_by" LowCardinality(String) ,
   "create_time" Int64,
   "last_modified_by" LowCardinality(String), 
   "object_describe_api_name" LowCardinality(String) ,
   "owner" LowCardinality(String) ,
   "target_id" String, 
   "target_api_name" String, 
   "target_master_id" String, 
   "source_master_id" String, 
   "event_id" String, 
   "rule_api_name" String, 
   "source_api_name" String, 
   "source_id" String, 
   "source_master_api_name" String, 
   "target_master_api_name" String, 
   "version" Nullable(Int32) ,
   "last_modified_time" Int64 ,
   "sys_modified_time" Int64 NOT NULL DEFAULT last_modified_time * 1000 ,
   "is_deleted" Int16 ,
   "bi_sys_flag" Int8 ,
   "bi_sys_batch_id" Int64 ,
   "bi_sys_is_deleted" UInt8 ,
   "bi_sys_version" DateTime DEFAULT now() ,
   INDEX idx_owner owner TYPE set(1000) GRANULARITY 1 ,
   PROJECTION prj_batch_id ( SELECT * ORDER BY tenant_id, bi_sys_batch_id, is_deleted ) 
) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version, bi_sys_is_deleted) 
ORDER BY ( tenant_id,bi_sys_flag,id);