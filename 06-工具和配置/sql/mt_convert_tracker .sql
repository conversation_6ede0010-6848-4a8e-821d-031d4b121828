create table if not exists mt_convert_tracker
(
    id                       varchar(32) not null,
    tenant_id                varchar(20) not null,
    object_describe_api_name varchar(50) not null,
    create_time              bigint      not null,
    created_by               var<PERSON>r(50) not null,
    last_modified_time       bigint      not null,
    last_modified_by         varchar(50) not null,
    sys_modified_time        bigint      not null,
    version                  integer     not null,
    is_deleted               smallint    not null,
    event_id                 varchar(32) not null,
    source_api_name          varchar(64) not null,
    target_api_name          varchar(64) not null,
    source_master_api_name   varchar(64),
    target_master_api_name   varchar(64),
    source_id                varchar(32) not null,
    source_master_id         varchar(32),
    target_id                varchar(32) not null,
    target_master_id         varchar(32),
    rule_api_name            varchar(64) not null,
    constraint mt_convert_tracker_pkey primary key (id, tenant_id)
);
 
create index if not exists mt_convert_tracker_index_event_id on mt_convert_tracker (event_id) where (is_deleted = 0);
create index if not exists mt_convert_tracker_index_san_sid on mt_convert_tracker (source_api_name, source_id) where (is_deleted = 0);
create index if not exists mt_convert_tracker_index_tan_tid on mt_convert_tracker (target_api_name, target_id) where (is_deleted = 0);
create index if not exists mt_convert_tracker_index_sman_smid on mt_convert_tracker (source_master_api_name, source_master_id) where (is_deleted = 0);
create index if not exists mt_convert_tracker_index_tman_tmid on mt_convert_tracker (target_master_api_name, target_master_id) where (is_deleted = 0);
create index if not exists mt_convert_tracker_index_rule_api_name on mt_convert_tracker (rule_api_name) where (is_deleted = 0);
 
 
DROP TRIGGER IF EXISTS x_audit_changes ON mt_convert_tracker;
DROP TRIGGER IF EXISTS x_system_changes ON mt_convert_tracker;
CREATE TRIGGER x_system_changes
    BEFORE INSERT OR UPDATE
    ON mt_convert_tracker
    FOR EACH ROW
EXECUTE PROCEDURE f_system_change();
CREATE TRIGGER x_audit_changes
    AFTER INSERT OR UPDATE OR DELETE
    ON mt_convert_tracker
    FOR EACH ROW
EXECUTE PROCEDURE f_change_detail('id', 'tenant_id', 'object_describe_api_name');


CREATE INDEX CONCURRENTLY IF NOT EXISTS indexname on object_q522h__c