create table if not exists mt_async_task_monitor
(
    id                                 varchar(32)  not null,
    tenant_id                          varchar(10)  not null,
    object_describe_api_name           varchar(200) not null,
    create_time                        bigint       not null,
    created_by                         varchar(50)  not null,
    last_modified_time                 bigint       not null,
    last_modified_by                   varchar(50)  not null,
    sys_modified_time                  bigint       not null,
    version                            integer      not null,
    is_deleted                         smallint     not null,
    task_id                            varchar(100),
    biz_id                             varchar(100),
    describe_api_name                  varchar(100),
    task_describe_default_value        varchar(2000),
    task_describe_i18n_key             varchar(100),
    task_describe_params_i18n_key      jsonb,
    task_describe_default_params_value jsonb,
    error_message                      varchar(10000),
    record_type                        varchar(100),
    task_status                        varchar(100),
    biz_api_name                       varchar(100),
    task_create_time                   bigint,
    task_estimated_execution_time      bigint,
    task_execution_time                bigint,
    task_total_num                     bigint,
    completed_num                      bigint,
    end_time                           bigint,
    submitted_by                       varchar(50),
    primary key (id, tenant_id)
);

CREATE UNIQUE INDEX IF NOT EXISTS async_task_monitor_tt_idx ON mt_async_task_monitor (task_id, tenant_id) where (is_deleted = 0);
CREATE INDEX IF NOT EXISTS async_task_monitor_record_type_idx ON mt_async_task_monitor USING btree (tenant_id, record_type) where (is_deleted = 0);
CREATE INDEX IF NOT EXISTS async_task_monitor_queuing_time_idx ON mt_async_task_monitor USING btree (tenant_id, task_create_time) where (is_deleted = 0);
CREATE INDEX IF NOT EXISTS async_task_monitor_start_time_idx ON mt_async_task_monitor USING btree (tenant_id, task_execution_time) where (is_deleted = 0);
CREATE INDEX IF NOT EXISTS async_task_monitor_describe_api_name_idx ON mt_async_task_monitor USING btree (tenant_id, describe_api_name) where (is_deleted = 0);


DROP TRIGGER IF EXISTS x_audit_changes ON mt_async_task_monitor;
DROP TRIGGER IF EXISTS x_system_changes ON mt_async_task_monitor;
CREATE TRIGGER x_system_changes
    BEFORE INSERT OR UPDATE
    ON mt_async_task_monitor
    FOR EACH ROW
EXECUTE PROCEDURE f_system_change();
create trigger x_audit_changes
    after insert or update or delete
    on mt_async_task_monitor
    for each row
execute procedure f_change_detail('id', 'tenant_id', 'object_describe_api_name');
