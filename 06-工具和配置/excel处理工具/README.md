# Excel数据合并工具

## 功能说明
这个工具用于合并Excel文件中的Sheet1和Sheet2数据。根据Sheet1中的`大附件path`字段，在Sheet2的`大附件字段`中查找匹配项，将匹配的数据合并到一个新的Excel文件中。

## 使用方法
1. 确保Python环境已安装以下依赖：
   - pandas
   - openpyxl

2. 将要处理的Excel文件放在上级目录中，默认文件名为`cdnlight2021导出.xlsx`
   - Sheet1需包含字段：企业账号、大附件path、文件名、上传者id、文件大小（单位byte）
   - Sheet2需包含字段：数据id、主属性、对象apiName、大附件字段

3. 运行脚本：
   ```bash
   cd 06-工具和配置/excel处理工具
   python3 merge_excel.py
   ```

4. 处理结果会保存在一个新的Excel文件中，只包含一个Sheet1，原始文件保持不变

## 执行过程
- 脚本会实时显示处理进度
- 显示预计剩余处理时间
- 处理完成后会显示匹配记录数和处理用时

## 注意事项
- 源文件不会被修改
- 处理结果自动保存为新文件，命名格式为：`原文件名_合并结果_时间戳.xlsx` 