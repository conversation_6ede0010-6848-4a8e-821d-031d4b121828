import pandas as pd

# 读取Excel文件
excel_path = '06-工具和配置/cdnlight2021导出.xlsx'

# 读取两个工作表
df1 = pd.read_excel(excel_path, sheet_name='Sheet1')
df2 = pd.read_excel(excel_path, sheet_name='Sheet2')

# 输出列名
print("Sheet1的列名:", df1.columns.tolist())
print("\nSheet2的列名:", df2.columns.tolist())

# 打印数据类型
print("\nSheet1的数据类型:")
print(df1.dtypes)

print("\nSheet2的数据类型:")
print(df2.dtypes)

# 显示Sheet2的前2行数据的实际样式
print("\nSheet2的前2行完整数据:")
for i in range(min(2, len(df2))):
    print(f"\n第{i+1}行:")
    for col in df2.columns:
        value = df2.iloc[i][col]
        print(f"  {col}: {value} ({type(value).__name__})") 