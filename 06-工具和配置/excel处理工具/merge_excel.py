import pandas as pd
import json
import re
import time
import sys
import os

# 读取Excel文件
print("开始读取Excel文件...")
excel_path = '../cdnlight2021导出.xlsx'
df1 = pd.read_excel(excel_path, sheet_name='Sheet1')
df2 = pd.read_excel(excel_path, sheet_name='Sheet2')
print(f"读取完成! Sheet1: {len(df1)}行数据, Sheet2: {len(df2)}行数据")

# 创建一个新的DataFrame用于存储合并后的数据
merged_data = []

# 用于显示进度的函数
def show_progress(current, total, prefix='处理进度', suffix='完成', length=50):
    percent = float(current) * 100 / total
    filled_length = int(length * current // total)
    bar = '█' * filled_length + '-' * (length - filled_length)
    sys.stdout.write(f'\r{prefix}: |{bar}| {percent:.1f}% {suffix} ({current}/{total})')
    sys.stdout.flush()
    if current == total:
        print()

# 记录开始时间
start_time = time.time()
total_rows = len(df1)
matched_count = 0

print(f"\n开始处理数据合并，共需处理 {total_rows} 条记录...")

# 预处理Sheet2的数据以提高搜索效率
attachment_paths = {}
print("正在预处理Sheet2数据以提高匹配效率...")
for idx, row in df2.iterrows():
    if idx % 100 == 0:
        sys.stdout.write(f"\r预处理Sheet2数据: {idx+1}/{len(df2)}")
        sys.stdout.flush()
    
    if pd.notna(row['大附件字段']):
        attachment_field = str(row['大附件字段'])
        # 提取所有ALIOSS路径
        paths = re.findall(r'"path":"(ALIOSS_[^"]+)"', attachment_field)
        for path in paths:
            if path not in attachment_paths:
                attachment_paths[path] = []
            attachment_paths[path].append(idx)

print(f"\n预处理完成，共提取到 {len(attachment_paths)} 个不同的附件路径")

# 遍历sheet1中的每一行
for idx, row1 in df1.iterrows():
    path = row1['大附件path']
    matched = False
    
    # 每5条记录显示一次进度
    if idx % 5 == 0 or idx == total_rows - 1:
        show_progress(idx + 1, total_rows)
        # 每100条记录刷新一次处理时间
        if idx % 100 == 0 and idx > 0:
            elapsed = time.time() - start_time
            estimated_total = (elapsed / idx) * total_rows
            remaining = estimated_total - elapsed
            min_r, sec_r = divmod(remaining, 60)
            print(f"\n已处理 {idx}/{total_rows}，预计剩余时间: {int(min_r)}分{int(sec_r)}秒")
    
    # 使用预处理的数据进行快速查找
    if path in attachment_paths:
        # 只检查可能包含该路径的行
        for sheet2_idx in attachment_paths[path]:
            row2 = df2.iloc[sheet2_idx]
            # 合并数据
            merged_row = {
                '企业账号': row1['企业账号'],
                '大附件path': row1['大附件path'],
                '文件名': row1['文件名'],
                '上传者id': row1['上传者id'],
                '文件大小（单位byte）': row1['文件大小（单位byte）'],
                '数据id': row2['数据id'],
                '主属性': row2['主属性'],
                '对象apiName': row2['对象apiName']
            }
            merged_data.append(merged_row)
            matched = True
            matched_count += 1
            break
    
    # 如果在sheet2中找不到匹配项，仍然添加sheet1的数据
    if not matched:
        merged_row = {
            '企业账号': row1['企业账号'],
            '大附件path': row1['大附件path'],
            '文件名': row1['文件名'],
            '上传者id': row1['上传者id'],
            '文件大小（单位byte）': row1['文件大小（单位byte）'],
            '数据id': None,
            '主属性': None,
            '对象apiName': None
        }
        merged_data.append(merged_row)

# 计算处理时长
elapsed_time = time.time() - start_time
minutes, seconds = divmod(elapsed_time, 60)

# 创建合并后的DataFrame
df_merged = pd.DataFrame(merged_data)
print(f"\n数据处理完成! 共匹配到 {matched_count} 条记录，处理用时: {int(minutes)}分{int(seconds)}秒")

# 生成新的Excel文件路径
output_dir = os.path.dirname(os.path.abspath(excel_path))
file_name = os.path.basename(excel_path)
name_without_ext = os.path.splitext(file_name)[0]
output_path = os.path.join(output_dir, f"{name_without_ext}_合并结果_{int(time.time())}.xlsx")

print(f"正在将结果写入新Excel文件: {output_path}")
# 将结果写入新的Excel文件，只包含一个Sheet1
df_merged.to_excel(output_path, sheet_name='Sheet1', index=False)

print(f"合并完成! 共处理 {len(df1)} 条数据，合并后 {len(df_merged)} 条数据写入新文件 {output_path}")
print(f"源文件 {excel_path} 保持不变") 