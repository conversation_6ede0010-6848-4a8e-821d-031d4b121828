/**
 * <AUTHOR>
 * @codeName 新增城市仓签收入库明细复制是否在源单据
 * @description 新增城市仓签收入库明细复制是否在源单据
 * @createTime 2023-07-12
 */

Map dataMap = context.data as Map

String field_d8xZb__c = dataMap.field_d8xZb__c //收货原因 总仓发货1 城市仓发货2 样品归还3
String field_1iqi2__c = dataMap.field_1iqi2__c //wms发货单
String field_n2CX5__c = dataMap.field_n2CX5__c //城市仓发货
log.info('收货原因:' + field_d8xZb__c)

List<Map> detailsList = []
if (field_1iqi2__c) { //总仓发货
  //一次返回closure中的数据最多为2W条，多于2W的场合需要利用返回值的lastId循环执行find函数
  def closure  = {
         List<Map> list ->//循环聚合所有数据 100条为一批
    list.each { item->
      detailsList.add(item)
    }
  }
  def (Boolean error, PageQueryData data, String errorMessage) = Fx.object.find('object_1Q392__c', [['field_Q21c4__c':field_1iqi2__c]], closure)
  if (error) {
    log.info(errorMessage)
    } else {
    log.info(data)
  }
}
else { //城市仓发
  //一次返回closure中的数据最多为2W条，多于2W的场合需要利用返回值的lastId循环执行find函数
  def closure  = {
       List<Map> list ->//循环聚合所有数据 100条为一批
    list.each { item->
      detailsList.add(item)
    }
  }
  def (Boolean error, PageQueryData data, String errorMessage) = Fx.object.find('object_KeBE4__c', [['field_hd29T__c':field_n2CX5__c]], closure)
  if (error) {
    log.info(errorMessage)
  } else {
    log.info(data)
  }
}

UIEvent event = UIEvent.build(context) {
}

// //获取当前操作的从对象数据
// Map currentData = event.getCurrentDetail()
// log.info("添加行："+event.getCurrentAddDetail())
// if( currentData ){

//       String field_Q3uxd__c = currentData.field_Q3uxd__c //产品编码
//       String field_jTc02__c = currentData.field_jTc02__c //序列号
//       log.info("序列号:"+field_jTc02__c)
//       log.info("产品编码:"+field_Q3uxd__c)

//       Map map = [:]

//       if( field_d8xZb__c=="1" ){ //总仓发货
//           if( field_jTc02__c ){
//             map = detailsList.find{x ->(x.field_DGJOC__c as String)==field_jTc02__c}
//           }
//           else{
//             map = detailsList.find{x ->(x.field_cU0wH__c as String)==field_Q3uxd__c}
//           }
//       }
//       else{
//           if( field_jTc02__c ){
//             map = detailsList.find{x ->(x.field_DOxDc__c as String)==field_jTc02__c}
//           }
//           else{
//             map = detailsList.find{x ->(x.field_o615r__c as String)==field_Q3uxd__c}
//           }
//       }

//       log.info("查询匹配到的wms回传发货单或城市仓发货明细："+map)
//       if( !map ){
//         currentData.put("field_6jEhG__c","0") //是否在wms回传发货单明细或城市仓发货明细中 是：1 否：0
//       }
//       else{
//         currentData.put("field_6jEhG__c","1") //是否在wms回传发货单明细或城市仓发货明细中 是：1 否：0
//         if( map.containsKey("field_7z2QH__c") ){ //WMS回传发货明细
//           currentData.put("field_7z2QH__c",map._id)
//         }
//         else if( map.containsKey("field_d5cIl__c") ){ //城市仓发货明细
//           currentData.put("field_d5cIl__c",map._id)
//         }
//       }
// }
// else{
  //获取当前新增的从对象数据
  // List<Map> currentAddData = event.getCurrentAddDetail()
  // log.info("添加行："+currentAddData)

  List<Map> currentAddData = event.details.object_2mBCv__c as List
  currentAddData.each { currentData_item->
    String field_Q3uxd__c = currentData_item.field_Q3uxd__c //产品编码
    String field_jTc02__c = currentData_item.field_jTc02__c //序列号
    log.info('序列号:' + field_jTc02__c)
    log.info('产品编码:' + field_Q3uxd__c)

    Map map = [:]
    if (field_1iqi2__c) { //总仓发货
    if (field_jTc02__c) {
      map = detailsList.find { x ->(x.field_DGJOC__c as String) == field_jTc02__c }
    }
        else {
      map = detailsList.find { x ->(x.field_cU0wH__c as String) == field_Q3uxd__c }
        }
    }
    else {
    if (field_jTc02__c) {
      map = detailsList.find { x ->(x.field_DOxDc__c as String) == field_jTc02__c }
    }
        else {
      map = detailsList.find { x ->(x.field_o615r__c as String) == field_Q3uxd__c }
        }
    }

    log.info('查询匹配到的wms回传发货单或城市仓发货明细：' + map)
    if (!map) {
    currentData_item.put('field_6jEhG__c','0') //是否在wms回传发货单明细或城市仓发货明细中 是：1 否：0
    }
    else {
    currentData_item.put('field_6jEhG__c','1') //是否在wms回传发货单明细或城市仓发货明细中 是：1 否：0
    if (field_1iqi2__c) { //总仓发货
      currentData_item.put('field_7z2QH__c', map._id)
    }
        else {
      currentData_item.put('field_d5cIl__c', map._id)
        }
    }
  }

// }

return event
