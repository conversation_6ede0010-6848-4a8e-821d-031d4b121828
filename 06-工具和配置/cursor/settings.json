{"editor.fontFamily": "Consolas, 'JetBrains Mono', monospace", "aicontext.personalContext": "", "redhat.telemetry.enabled": true, "java.configuration.maven.userSettings": "/Users/<USER>/.m2/settings.xml", "java.configuration.maven.lifecycleMappings": "/Users/<USER>/.m2/repository", "java.jdt.ls.java.home": "/Library/Java/JavaVirtualMachines/zulu-11.jdk/Contents/Home", "maven.executable.path": "mvn", "maven.terminal.favorites": [{"alias": "跳过测试并打包", "command": "clean install -DskipTests"}, {"alias": "运行单元测试", "command": "test"}, {"alias": "清理并编译", "command": "clean compile"}], "java.configuration.runtimes": [{"name": "JavaSE-17", "path": "/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home", "default": true}, {"name": "JavaSE-1.8", "path": "/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home"}, {"name": "JavaSE-11", "path": "/Library/Java/JavaVirtualMachines/zulu-11.jdk/Contents/Home"}], "groovyLint.java.executable": "/Library/Java/JavaVirtualMachines/zulu-11.jdk/Contents/Home/bin/java", "aws.telemetry": false, "markdown.extension.extraLangIds": [], "markdown-preview-enhanced.plantumlJarPath": "/opt/homebrew/Cellar/plantuml/1.2025.2/libexec/plantuml.jar", "terminal.integrated.fontFamily": "MesloLGS NF", "security.promptForLocalFileProtocolHandling": false, "editor.fontSize": 14, "java.test.config": {"testSourcePath": ["src/test/groovy", "src/test/java"]}, "rsp-ui.enableStartServerOnActivation": [{"id": "redhat.vscode-community-server-connector", "name": "Community Server Connector", "startOnActivation": true}], "workbench.settings.applyToAllProfiles": ["terminal.integrated.commandsToSkipShell"], "rsp-ui.rsp.java.home": "/Library/Java/JavaVirtualMachines/zulu-11.jdk/Contents/Home", "java.debug.logLevel": "info", "terminal.integrated.profiles.linux": {"bash": {"path": "bash", "icon": "terminal-bash"}, "zsh": {"path": "zsh"}, "fish": {"path": "fish"}, "tmux": {"path": "tmux", "icon": "terminal-tmux"}, "pwsh": {"path": "pwsh", "icon": "terminal-powershell"}}, "terminal.integrated.defaultProfile.osx": "zsh", "java.debug.settings.jdwp.requestTimeout": 300000, "ai-commit.AI_COMMIT_LANGUAGE": "Simplified Chinese", "git.enableSmartCommit": true, "diffEditor.maxComputationTime": 0, "update.mode": "manual", "cursor.cpp.disabledLanguages": [], "workbench.editor.editorActionsLocation": "titleBar", "github.copilot.selectedCompletionModel": "", "github.copilot.enable": {"*": false, "plaintext": false, "markdown": false, "scminput": false}, "workbench.editor.enablePreview": false, "workbench.editorAssociations": {"*.copilotmd": "vscode.markdown.preview.editor", "*.mdc": "workbench.editor.mdc"}, "update.releaseTrack": "prerelease", "cursor.diffs.useCharacterLevelDiffs": true, "cursor.cmdk.useThemedDiffBackground": false, "cursor.composer.shouldAllowCustomModes": true, "workbench.colorTheme": "Solarized Dark", "switch2idea.ideaPath": "/Applications/IntelliJ IDEA.app", "java.import.maven.disableTestClasspathFlag": true, "maven.settingsFile": "", "augment.chat.userGuidelines": "1. 使用中文交流。 2. 如下场景使用英文：日志记录、错误提示、异常信息、断言信息。 3. 使用mvn命令运行单元测试。 4. 使用2个空格做代码缩进。 5. 使用文本块代替字符串拼接。 6. 尽可能使用局部变量类型推断，避免显式指定类型。 7. 使用增强switch表达式。 8. 使用instanceof模式匹配。 9. 使用junit5生成测试用例。 10. 尽量使用record类型。 11. 使用lombok的注解。 12. 尽量自主运行，减少询问用户操作指令。", "window.nativeTabs": true, "workbench.activityBar.orientation": "vertical"}