{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "I18nCommentAdderFromFile",
            "request": "launch",
            "mainClass": "com.facishare.paas.I18nCommentAdderFromFile",
            "projectName": "fs-paas-app-web"
        },
        {
            "type": "java",
            "name": "I18nCommentAdderFromFile",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.core.I18nCommentAdderFromFile",
            "projectName": "fs-paas-app-api"
        },
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "FileSizeConverter",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.core.util.FileSizeConverter",
            "projectName": "fs-paas-app-api"
        },
        {
            "type": "java",
            "name": "HtmlExt",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.common.util.HtmlExt",
            "projectName": "fs-paas-app-common"
        },
        {
            "type": "java",
            "name": "LocalDateUtils",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.common.util.LocalDateUtils",
            "projectName": "fs-paas-app-common"
        },
        {
            "type": "java",
            "name": "SolveProblem",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.log.dto.SolveProblem",
            "projectName": "fs-paas-app-log"
        },
        {
            "type": "java",
            "name": "ObjectRelateMapping",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.metadata.ObjectRelateMapping",
            "projectName": "fs-paas-app-metadata-util"
        },
        {
            "type": "java",
            "name": "PredefineObjectRelateLists",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.metadata.PredefineObjectRelateLists",
            "projectName": "fs-paas-app-metadata-util"
        },
        {
            "type": "java",
            "name": "RelatedObjectOrder",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.metadata.RelatedObjectOrder",
            "projectName": "fs-paas-app-metadata-util"
        },
        {
            "type": "java",
            "name": "ResourceLoader",
            "request": "launch",
            "mainClass": "com.facishare.paas.appframework.metadata.ResourceLoader",
            "projectName": "fs-paas-app-metadata-util"
        }
    ]
}