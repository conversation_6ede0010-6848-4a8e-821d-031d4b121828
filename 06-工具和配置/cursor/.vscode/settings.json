{"maven.view": "hierarchical", "launch": {}, "java.debug.settings.onBuildFailureProceed": true, "java.configuration.updateBuildConfiguration": "interactive", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable", "terminal.integrated.autoReplies": {}, "window.density.editorTabHeight": "default", "workbench.activityBar.orientation": "vertical"}