{"tenant_id": "771080", "store_table_name": "object_init_tools__c", "package": "CRM", "is_active": true, "last_modified_time": 1701680391913, "description": "", "display_name": "初始化工具", "created_by": "8786", "is_open_display_name": false, "is_deleted": false, "api_name": "object_init_tools__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "ujk", "release_version": "6.4", "fields": {"lock_rule": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "is_extend": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_9", "status": "new"}, "add_edit_layout__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_5", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "新建编辑页布局json", "file_size_limit": 104857600, "api_name": "add_edit_layout__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "del_func_privilege_code__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_2", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "删除的功能权限编码", "api_name": "del_func_privilege_code__c", "is_index_field": false, "is_show_mask": false, "help_text": "如：button_0I99D__c,Add,Print", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_0", "max_length": 16, "is_index": true, "is_active": true, "is_encrypted": false, "length": 10, "label": "汇率", "api_name": "mc_exchange_rate", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "new"}, "failure_tenant_num__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "d_2", "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "失败企业数", "api_name": "failure_tenant_num__c", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "failure_tenant_list__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_12", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "失败企业id列表", "file_size_limit": 104857600, "api_name": "failure_tenant_list__c", "is_index_field": false, "help_text": "执行过程中失败的企业id集合，用“,”分割", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "is_extend": false, "label_r": "作废前生命状态", "is_index_field": false, "index_name": "t_3", "max_length": 256, "status": "new"}, "req_bus_side_url__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_1", "max_length": 100, "is_index": true, "is_active": false, "is_encrypted": false, "default_value": "", "label": "请求业务侧接口", "api_name": "req_bus_side_url__c", "is_index_field": false, "is_show_mask": false, "help_text": "如：/fs-paas-auth/addFunc  注意：需要配合『服务端口』字段使用。", "status": "new"}, "owner_department": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "success_tenant_num__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "d_1", "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "成功的企业数", "api_name": "success_tenant_num__c", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "completed_progress__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": true, "description": "", "is_unique": false, "type": "percentile", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "d_3", "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "", "label": "完成进度", "api_name": "completed_progress__c", "is_index_field": false, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "锁定状态", "index_name": "s_7", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "is_need_convert": false, "api_name": "lock_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "package": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "create_time": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "success_tenant_list__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_1", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "成功的企业id列表", "file_size_limit": 104857600, "api_name": "success_tenant_list__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "list_layout__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_9", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "列表页布局json", "file_size_limit": 104857600, "api_name": "list_layout__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "field_env__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_many", "default_to_zero": false, "is_required": false, "options": [{"label": "纷享云", "value": "1"}, {"label": "双胞胎", "value": "2"}, {"label": "紫光云生产", "value": "3"}, {"label": "金山云", "value": "4"}, {"label": "华为云", "value": "5"}, {"label": "紫光云测试", "value": "6"}, {"label": "阿里云", "value": "8"}, {"label": "法兰克福云（亚马逊）", "value": "9"}, {"label": "模板云", "value": "10"}, {"label": "海信云", "value": "11"}, {"label": "许继云", "value": "12"}, {"label": "模板复制云", "value": "13"}, {"label": "铁塔云", "value": "14"}, {"label": "蒙牛云", "value": "15"}, {"label": "何氏眼科", "value": "16"}, {"not_usable": true, "label": "自定义企业", "value": "custom"}, {"is_required": false, "not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "cascade_parent_api_name": "update_tenant_ids__c", "is_extend": false, "index_name": "a_3", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": ["2", "3", "4", "5", "6", "8", "9", "10", "11", "12", "13", "14", "15", "16"], "label": "对应环境的企业", "api_name": "field_env__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "version": {"describe_api_name": "object_init_tools__c", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released", "description": ""}, "relevant_team": {"describe_api_name": "object_init_tools__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "update_tenant_ids__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "直接填写", "value": "0"}, {"label": "文件上传", "value": "1"}, {"not_usable": true, "label": "纷享云所有企业", "value": "2"}, {"child_options": [{"field_env__c": ["1", "2", "3", "4", "5", "6", "8", "9", "10", "11", "12", "13", "14", "15", "16"]}], "label": "刷租户库（全网刷）", "value": "3"}, {"child_options": [{"field_env__c": ["1", "2", "3", "4", "5", "6", "8", "9", "10", "11", "12", "13", "14", "15", "16"]}], "label": "刷系统库", "value": "4"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "s_4", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "0", "label": "企业ids获取方式", "api_name": "update_tenant_ids__c", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "add_func_privilege_code__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_10", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "添加的功能权限编码", "api_name": "add_func_privilege_code__c", "is_index_field": false, "is_show_mask": false, "help_text": "如：button_0I99D__c,Add,Print", "status": "new"}, "data_own_department": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_department", "define_type": "package", "is_single": true, "label_r": "归属部门", "is_index_field": false, "index_name": "data_owner_dept_id", "help_text": "", "status": "released"}, "name": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "任务名称", "api_name": "name", "is_index_field": false, "is_show_mask": false, "help_text": "对象apiName-[描述/布局/功能权限]-创建/更新", "status": "new"}, "tenant_id": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "store_table_name__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_7", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "store_table_name", "api_name": "store_table_name__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "origin_source": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "index_name": "s_os", "config": {"display": 0}, "status": "released", "description": ""}, "lock_user": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "is_extend": false, "label_r": "加锁人", "is_index_field": false, "index_name": "a_2", "status": "new"}, "tenant_ids__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "t_8", "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "企业EI", "api_name": "tenant_ids__c", "is_index_field": false, "help_text": "74255,78058", "status": "new"}, "is_mobile_digest_layout__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "移动摘要布局", "type": "true_or_false", "is_required": false, "api_name": "is_mobile_digest_layout__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "b_1", "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "describe_init_option__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "1", "label": "描述初始化选项", "type": "select_one", "is_required": false, "api_name": "describe_init_option__c", "options": [{"label": "新建对象描述", "value": "1"}, {"label": "新增或更新字段描述", "value": "2"}, {"label": "更新描述属性", "value": "3"}, {"not_usable": true, "label": "跳过描述处理", "value": "skipDescribe"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "s_2", "config": {}, "help_text": "", "status": "new"}, "describe_attribute__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_5", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "描述属性", "api_name": "describe_attribute__c", "is_index_field": false, "is_show_mask": false, "help_text": "{\"config\":{}}", "status": "new"}, "tenant_data__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_11", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "企业Ids附件", "file_size_limit": 104857600, "api_name": "tenant_data__c", "is_index_field": false, "help_text": "『通过文件上传企业名单』字段为是时生效，上传企业id时，格式如下：78057,74255", "status": "new"}, "object_describe_api_name": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "out_owner": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released", "description": ""}, "mc_functional_currency": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "本位币", "type": "select_one", "is_required": false, "api_name": "mc_functional_currency", "options": [{"label": "CNY - 人民币", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "本位币", "is_index_field": false, "index_name": "s_0", "config": {}, "help_text": "", "status": "new"}, "is_list_layout__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": true, "label": "列表页布局", "type": "true_or_false", "is_required": false, "api_name": "is_list_layout__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "b_4", "help_text": "", "status": "new"}, "execution_status__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "执行状态", "type": "select_one", "is_required": false, "api_name": "execution_status__c", "options": [{"label": "执行成功", "value": "success"}, {"label": "执行中", "value": "running"}, {"label": "部分执行失败", "value": "failure"}, {"label": "终止任务", "value": "terminate"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "s_5", "config": {}, "help_text": "", "status": "new"}, "role_code__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_9", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "角色码", "api_name": "role_code__c", "is_index_field": false, "is_show_mask": false, "help_text": "如：00000000000000000000000000000009", "status": "new"}, "owner": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "is_extend": false, "label_r": "负责人", "is_index_field": false, "index_name": "owner", "help_text": "", "status": "new"}, "tenant_black__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_7", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "企业黑名单", "file_size_limit": 104857600, "api_name": "tenant_black__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "last_modified_time": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "life_status": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "is_extend": false, "label_r": "生命状态", "index_name": "s_1", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "is_need_convert": false, "api_name": "life_status", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "describe_json__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_15", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "对象或字段描述json", "file_size_limit": 104857600, "api_name": "describe_json__c", "is_index_field": false, "help_text": "1、对象描述格式：{\"tenant_id\":\"74255\",\"api_name\":\"object_L612u__c\",\"fields\":{\"tenant_id\":{},\"_id\":{}},\"release_version\":\"6.4\",\"actions\":{}}\n2、添加字段描述格式：{ \"field_test__c\": {},\"field_test2__c\": {}}", "status": "new"}, "service_port__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_6", "max_length": 100, "is_index": true, "is_active": false, "is_encrypted": false, "default_value": "", "label": "服务端口", "api_name": "service_port__c", "is_index_field": false, "is_show_mask": false, "help_text": "需要查看发布系统不同服务对象的端口不同。", "status": "new"}, "last_modified_by": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released", "description": ""}, "out_tenant_id": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "币种", "type": "select_one", "is_required": false, "api_name": "mc_currency", "options": [{"label": "CNY - 人民币", "value": "CNY"}, {"not_usable": true, "label": "USD - 美元", "value": "USD"}, {"label": "JPY - 日元", "value": "JPY"}, {"label": "GBP - 英镑", "value": "GBP"}, {"label": "EUR - 欧元", "value": "EUR"}], "define_type": "package", "is_single": false, "label_r": "币种", "is_index_field": false, "index_name": "s_3", "config": {}, "help_text": "", "status": "new"}, "record_type": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "对象描述配置", "label": "刷对象描述"}, {"is_active": true, "api_name": "record_layout__c", "description": "布局配置", "label": "刷布局"}, {"is_active": true, "api_name": "record_func_privilege__c", "description": "功能权限配置", "label": "刷功能权限"}, {"is_active": true, "api_name": "record_assign__c", "description": "业务类型角色分配配置", "label": "业务类型角色分配"}, {"is_active": true, "api_name": "record_role_layout_assign__c", "description": "业务类型角色布局分配", "label": "业务类型角色布局分配"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "related_layout__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": ["DetailLayout", "MobileList"], "label": "布局配置", "type": "select_many", "is_required": false, "api_name": "related_layout__c", "options": [{"not_usable": true, "label": "布局跳过处理", "value": "skipLayout"}, {"label": "详情页布局", "value": "DetailLayout"}, {"label": "列表页布局", "value": "ListLayout"}, {"label": "移动摘要布局", "value": "MobileList"}, {"label": "新建编辑页布局", "value": "AddEditLayout"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "a_10", "config": {}, "help_text": "", "status": "new"}, "sys_type__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": [], "label": "系统库类型", "type": "select_many", "is_required": false, "api_name": "sys_type__c", "options": [{"label": "全网", "value": "100"}, {"label": "灰度", "value": "101"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "a_16", "config": {}, "help_text": "", "status": "new"}, "is_detail_layout__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": true, "label": "详情页布局", "type": "true_or_false", "is_required": false, "api_name": "is_detail_layout__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "b_2", "help_text": "", "status": "new"}, "layout_operation__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": false, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "init", "label": "布局操作", "type": "select_one", "is_required": false, "api_name": "layout_operation__c", "options": [{"label": "新建", "value": "init"}, {"label": "更新", "value": "update"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "s_6", "config": {}, "help_text": "", "status": "new"}, "record_type_roles_json__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_13", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "业务类型和角色分配json", "file_size_limit": 104857600, "api_name": "record_type_roles_json__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "record_type_roles_layout_json__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_6", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "业务类型角色布局分配json", "file_size_limit": 104857600, "api_name": "record_type_roles_layout_json__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "init_object_api_name__c": {"describe_api_name": "object_init_tools__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "custom", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_4", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "初始化对象", "api_name": "init_object_api_name__c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "detail_layout__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_14", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "详情页布局json", "file_size_limit": 104857600, "api_name": "detail_layout__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "func_operation__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "update", "label": "功能权限操作", "type": "select_one", "is_required": false, "api_name": "func_operation__c", "options": [{"label": "创建功能项", "value": "create"}, {"label": "删除功能项", "value": "delete"}, {"label": "更新功能权限", "value": "update"}, {"label": "创建功能项并更新权限", "value": "createAndUpdate"}, {"not_usable": true, "label": "跳过处理功能权限", "value": "skipFuncPrivilege"}, {"not_usable": true, "label": "初始化功能权限", "value": "init"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "s_8", "config": {}, "help_text": "", "status": "new"}, "is_edit_layout__c": {"describe_api_name": "object_init_tools__c", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "新建编辑页布局", "type": "true_or_false", "is_required": false, "api_name": "is_edit_layout__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "is_single": false, "is_extend": false, "is_index_field": false, "index_name": "b_3", "help_text": "", "status": "new"}, "func_privilege_json__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_4", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "功能权限json", "file_size_limit": 104857600, "api_name": "func_privilege_json__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}, "mc_exchange_rate_version": {"describe_api_name": "object_init_tools__c", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "label": "汇率版本", "type": "text", "is_required": false, "api_name": "mc_exchange_rate_version", "define_type": "package", "is_single": false, "label_r": "汇率版本", "is_index_field": false, "index_name": "t_0", "max_length": 256, "help_text": "", "status": "new"}, "mobile_list_layout__c": {"describe_api_name": "object_init_tools__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "type": "file_attachment", "is_required": false, "define_type": "custom", "is_single": false, "is_extend": false, "index_name": "a_8", "support_file_types": [], "is_index": true, "file_amount_limit": 1, "is_active": true, "is_encrypted": false, "label": "移动摘要布局json", "file_size_limit": 104857600, "api_name": "mobile_list_layout__c", "is_index_field": false, "help_text": "单个文件不得超过100M", "status": "new"}}}