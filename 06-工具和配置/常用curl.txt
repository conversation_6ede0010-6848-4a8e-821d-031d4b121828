curl -i -X POST \
   -H "Content-Type:application/json" \
   -H "x-fs-ei:71574" \
   -d \
'{"tenantId":74255}' \
 'http://localhost/webPage/PaaSAppRestService/clearTenantCache'

 curl -L -X DELETE 'localhost/fs-open-app-manage/open/manage/test/removeEnterpriseViewCache/91747/FSAID_5f5e248'
curl -L -X GET 'localhost/fs-open-app-manage/open/manage/test/crmAvailable/hzzmsz2025/1025'

curl -i -L -X POST 'localhost/fs-open-app-manage/open/manage/test/newRepairCrmView/' -H 'Content-Type: application/json' -d '{"verboseResult":true,"filterStopEmployee":false,"eaList":["cm0271000000"],"verboseResult":true}'

curl -i -L -X GET 'localhost/fs-open-app-manage/open/manage/datacheck/repair/crmView/74255' -H 'Content-Type: application/json' 

curl -L -X DELETE 'localhost/fs-open-app-manage/open/manage/test/removeEnterpriseViewCache/74255/FSAID_5f5e248'


 curl -i -X POST \
   -H "Content-Type:application/json" \
   -H "x-fs-ei:71574" \
   -d \
'{"linkAppIds":["FSAID_114910e4","FSAID_11490f80","FSAID_11490d3d"],"tenantId":806542,"status":1}' \
 'http://localhost/webPage/LinkAppRestService/batchUpdateStatus'

 curl -i -X POST \
 -H "Content-Type:application/json" \
 -H "x-fs-ei:71574" \
 -d \
'{"linkAppIds":["FSAID_127a3981","FSAID_11490f80"],"tenantId":798811,"status":1}' \
'http://localhost/webPage/LinkAppRestService/batchUpdateStatus'

 curl -i -X POST \
 -H "Content-Type:application/json" \
 -H "x-fs-ei:40200537" \
 -d \
'{"linkAppIds":["FSAID_11490c83","FSAID_11490d3d","FSAID_11491078"],"tenantId":40200537,"status":1}' \
'http://localhost/webPage/LinkAppRestService/batchUpdateStatus'


curl -i -X POST \
-H "Content-Type:application/json" \
-H "x-fs-ei:802190" \
-d \
'{"linkAppIds":["FSAID_11491249"],"tenantId":802190,"status":1}' \
'http://localhost/webPage/LinkAppRestService/batchUpdateStatus'



 curl -i -X POST \
 -H "Content-Type:application/json" \
 -H "x-fs-ei:71574" \
 -d \
'{"linkAppIds":["FSAID_11490d3d"],"tenantId":799428,"status":1}' \
'http://localhost/webPage/LinkAppRestService/batchUpdateStatus'


curl -i -X POST \
-H 'Content-Type: application/json' \
-H 'x-fs-ei: 71574' \
-d \
'{"lastId": "0","limit": 500,"eas":"wpie123456","grayFlag":true}' \
'http://localhost/webPage/LinkAppRestService/listUptreamLinkAppByPage'




curl -i -X POST \
   -H "Content-Type:application/json" \
   -H "x-fs-ei:71574" \
   -d \
'{"tenantId":768645, "appIds":["FSAID_5f5e51a"]}' \
 'http://localhost/webPage/PaaSAppRestService/getPaaSAppByAppIds'


 curl -i -X POST \
   -H "Content-Type:application/json" \
   -H "x-fs-ei:71574" \
   -d \
'{"linkAppIds":["FSAID_11491084"],"tenantId":74255,"status":1}' \
 'http://localhost/webPage/LinkAppRestService/batchUpdateStatus'



  curl -i -X POST \
   -H "Content-Type:application/json" \
   -H "x-fs-ei:71574" \
   -d \
'{"appId":"FSAID_5f5e51a"}' \
 'http://localhost/webPage/LinkAppRestService/deleteByAppId'



 curl -i -X POST \
-H "Content-Type:application/json" \
-H "x-fs-ei:71574" \
-d \
'{"status":-1}' \
'http://localhost/brush/crmViewRepair'



curl -i -X POST \
'http://localhost/webPage/LinkAppRestService/listPresetLinkApp' \
-H 'Content-Type: application/json' \
-H 'x-fs-ei: 74255'\


curl -L -X GET 'localhost/fs-open-app-manage/open/manage/test/repairCrmView/' -H 'Content-Type: application/json' -d '{"eaList":["85135"],"verboseResult":true}'
curl -L -X GET 'localhost/open/manage/test/repairCrmView/' -H 'Content-Type: application/json' -d '{"eaList":["dmrjxgf"],"verboseResult":true}'

url -L -X POST 'http://localhost:28207/com.facishare.open.app.pay.api.service.QuotaService/queryQuotaRecords' -H 'x-fs-ei: 85135' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"85135","arg2":"FSAID_5f5e248"}'

curl -X POST 'localhost/open/manage/test/cache/batchClearQuotaCache' -H 'Content-Type: application/json' -d '["xuanzhi888"]'


 curl -L -X GET 'localhost/open/manage/test/crmAvailable/transfarchem001/1600'


 curl -X POST 'http://localhost/webPage/LinkAppRestService/getLinkAppRoleList' -H 'Content-Type: application/json' -H 'x-fs-ei: 40200537' -H 'Content-Type: application/json' --data '{"tenantId":40200537,"type":2,"orderByUpdateTimeDesc":false}'

// 清理CRM用户数缓存

curl -i -X POST \
   -H "Content-Type:application/json" \
   -H "x-fs-ei:808079" \
   -d \
'{"linkAppIds":["FSAID_11491068","FSAID_11490d3d","FSAID_114910e4","FSAID_127a3981","FSAID_11490f80"],"tenantId":808079,"status":1}' \
 'http://localhost/webPage/LinkAppRestService/batchUpdateStatus'


curl -L -X GET 'localhost/fs-open-app-manage/open/manage/test/crmAvailable/dtdfsj13361/1000'

curl -i -L -X POST 'localhost/fs-open-app-manage/open/manage/test/newRepairCrmView/' \
 -H 'Content-Type: application/json' -d '{"verboseResult":true,"filterStopEmployee":false,"eaList":["hsykjt"],"verboseResult":true}'

curl -L -X DELETE 'localhost/fs-open-app-manage/open/manage/test/removeEnterpriseViewCache/hzzmsz2025/FSAID_5f5e519'



curl -L -X GET 'localhost/fs-open-app-manage/open/manage/test/quotaInfo/fktest087/FSAID_5f5e519'
curl -L -X GET 'localhost/open/manage/test/quotaInfo/zjhorizon/FSAID_5f5e519'


 curl -L -X GET 'localhost/fs-open-app-manage/open/manage/test/crmAvailable/91324/1000'


  curl -L -X GET 'localhost/open/manage/test/crmAvailable/634858/1026'

curl -i -L -X POST 'localhost/open/manage/test/newRepairCrmView/' -H 'Content-Type: application/json' -d '{"verboseResult":true,"filterStopEmployee":false,"eaList":["insightcrm"],"verboseResult":true}'

curl -L -X DELETE 'localhost/open/manage/test/removeEnterpriseViewCache/szosprznkj2024/FSAID_5f5e519'

  

   curl -X POST 'http://localhost/fs-open-app-center-web/CRM/newQueryQuota' -H 'Content-Type: application/json' -H 'x-fs-ei: 74255' -H 'Content-Type: application/json' --data '{"fsEa":"74255"}'

   curl -X POST 'http://localhost/CRM/newQueryQuota' -H 'Content-Type: application/json' -H 'x-fs-ei: 590064' -H 'Content-Type: application/json' --data '{"fsEa":"803708_sandbox"}'

   curl -X POST 'http://localhost/fs-open-app-center-web/CRM/newRemoveUsers' -H 'Content-Type: application/json' -H 'x-fs-ei: 758842' -H 'Content-Type: application/json' --data '{"fsEa":"asign1025","userIds":[1000,1008,1006,1005,1003,1002,1001,1009,1013,1021,1026,1033,1032,1035,1036,1037,1038,1039,1046,1042,1044,1023,1045,1048,1049,1050,1054,1007,1055,1057]}'

   curl -X POST 'http://localhost/CRM/newRemoveUsers' -H 'Content-Type: application/json' -H 'x-fs-ei: 758842' -H 'Content-Type: application/json' --data '{"fsEa":"asign1025","userIds":[1000,1008,1006,1005,1003,1002,1001,1009,1013,1021,1026,1033,1032,1035,1036,1037,1038,1039,1046,1042,1044,1023,1045,1048,1049,1050,1054,1007,1055,1057]}'

   curl -L -X POST 'http://localhost:28207/com.facishare.open.app.center.api.service.externals.AppViewBizService/removeEmployeeView' -H 'x-fs-ei: 757990' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"FSAID_5f5e51a","arg2":"asign1025","arg3":[1047,1058,1060,1061,1064,1065,1066,1068,1069,1070,1071,1072]}'

   curl -X POST 'http://localhost/fs-open-app-center-web/CRM/newQueryQuota' -H 'Content-Type: application/json' -H 'x-fs-ei: 40040148' -H 'Content-Type: application/json' --data '{"fsEa":400401483_sandbox"}'

   curl -X POST 'http://localhost/CRM/queryQuotaRecords' -H 'Content-Type: application/json' -H 'x-fs-ei: 803708' -H 'Content-Type: application/json' --data '{"fsEa":"803708_sandbox"}'

   curl -i -X POST 'http://localhost/fs-open-app-center-web/CRM/queryQuotaRecords' -H 'Content-Type: application/json' -H 'x-fs-ei: 715240' -H 'Content-Type: application/json' --data '{"fsEa":"dknm888"}'

   curl -L -X POST 'http://localhost:28207/com.facishare.open.app.pay.api.service.QuotaService/queryQuotaRecords' -H 'x-fs-ei: 757990' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"mengniu777","arg2":"FSAID_5f5e519"}'


curl -L -X POST 'http://fs-open-app-center-all:28207/com.facishare.open.app.pay.api.service.QuotaService/queryQuotaRecords' -H 'x-fs-ei: 40040131' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"huanfa6688","arg2":"FSAID_5f5e519"}'

curl -i -L -X POST 'http://************:12765/fs-open-app-center-provider/fs-open-app-manage/open/manage/test/removeEnterpriseViewCache/74255/FSAID_5f5e519'

 curl -L -X DELETE 'localhost/fs-open-app-manage/open/manage/test/removeEnterpriseViewCache/szosprznkj2024/FSAID_5f5e519'

 curl -i -L -X DELETE 'http://************:47563/fs-open-app-manage/open/manage/test/removeEnterpriseViewCache/74255/FSAID_5f5e24a'
 curl -i -L -X DELETE 'http://localhost/open/manage/test/removeEnterpriseViewCache/74255/FSAID_5f5e24a'


 curl -L -X POST 'http://fs-open-app-center-provider:28207/com.facishare.open.app.pay.api.service.QuotaService/queryQuotaRecords' -H 'x-fs-ei: 40030133' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"803708_sandbox","arg2":"FSAID_5f5e519"}'


 curl -i -X POST 'http://fs-open-app-center-provider.foneshare:28207/com.facishare.open.app.pay.api.service.QuotaService/addQuotaRecord' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":{"appId":"FSAID_5f5e519","attribute":0,"beginTime":*************,"endTime":*************,"fsEa":"********","quota":250,"quotaType":"PURCHASE"}}'

  curl -i -X POST 'http://localhost:28207/com.facishare.open.app.pay.api.service.QuotaService/addQuotaRecord' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":{"appId":"FSAID_5f5e519","attribute":0,"beginTime":*************,"endTime":*************,"fsEa":"********","quota":250,"quotaType":"PURCHASE"}}'

curl -i -X POST 'http://fs-open-app-center-provider.foneshare:28207/com.facishare.open.app.center.api.service.OpenAppAdminService/removeAppAdminIds' -H 'x-fs-ei: 619615' -H 'Content-Type: application/json' -d '{"version":"1.3","arg1":{"enterpriseAccount":"ntxx0948","userId":-10000},"arg2":"FSAID_989ac9","arg3":[1629]}'

curl -i -X POST 'http://localhost:28207/com.facishare.open.app.center.api.service.OpenAppAdminService/removeAppAdminIds' -H 'x-fs-ei: 619615' -H 'Content-Type: application/json' -d '{"version":"1.3","arg1":{"enterpriseAccount":"ntxx0948","userId":-10000},"arg2":"FSAID_989ac9","arg3":[1629]}'


curl -L -X POST 'http://localhost:28207/com.facishare.open.app.pay.api.service.QuotaService/queryUsedQuota' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"cm0271000000","arg2":"FSAID_5f5e519"}'

curl -L -X POST 'http://fs-open-app-center-provider:28207/com.facishare.open.app.pay.api.service.QuotaService/queryUsedQuota' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"kxsw888","arg2":"FSAID_5f5e519"}'

curl -i -L -X POST 'http://fs-open-app-center-all:28207/com.facishare.open.app.center.api.service.externals.IPaasOpenAppViewService/queryPaasAppViewsForEaVo' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"huanfa6688"}'

curl -i -L -X POST 'http://localhost/com.facishare.open.app.center.api.service.externals.IPaasOpenAppViewService/queryPaasAppViewsForEaVo' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":"huanfa6688","arg2":"WEB"}'

invoke queryPaasAppViewsForEaVo("huanfa6688",null,"")

curl -L -X POST 'http://fs-open-app-center-all:28207/com.facishare.open.app.center.api.service.OpenFsUserAppViewService/canAccessComponent' -H 'Content-Type: application/json' -H 'x-fs-ei: ********' -d '{"version":"1.3","arg1":{"enterpriseAccount":"huanfa6688", "userId":1000},"arg2":"FSAID_5f5e51a"}'


curl -L -X POST 'http://localhost:28207/com.facishare.open.app.pay.api.service.QuotaService/deleteQuotaRecord' -H 'x-fs-ei: 772311' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":3565040}'


curl -L -X POST 'http://fs-enterprise-relation-biz.hexagonmi-public-prod:80/fs-enterprise-linkapp-web/linkAppService/dispatch/com.facishare.linkapp.api.service.LinkAppAssociationObjectService_sendLinkAppObjectAssociationInnerEvent' -H 'Content-Type: application/json' -H 'x-fs-ei: 768811' -d '{"argsJson":["hexagon","FSAID_127a3981","ServiceRequestObj"]}'


curl -i --request POST \
  --url http://localhost/webPage/LinkAppRestService/batchAddLinkApp \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'X-fs-Enterprise-Account: fktest087' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 590064' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --header 'x-fs-userInfo: 1000' \
  --data '{
    "linkAppList": [
        {
            "linkAppId": "FSAID_114911c2",
            "tenantId":590064,
            "status":1
        }
    ]
}'


curl --location --request GET 'localhost/API/v3/rest/scoringRule/grayV3' \
--header 'x-fs-userInfo: -10000' \
--header 'x-fs-ei: 812570' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'


curl -i --location --request GET 'localhost/API/v3/rest/scoringRule/grayV3' \
--header 'x-fs-userInfo: -10000' \
--header 'x-fs-ei: 680469' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'





curl -i --request POST \
  --url http://localhost/API/v1/object/SalesOrderObj/action/TriggerEvent \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1001' \
  --header 'x-fs-enterprise-id: 808330' \
  --data '{"event_id":"67c9069d484fa900012a5a83","layout_api_name":"edit_SalesOrderObj_layout_generate_by_UDObjectServer__c","object_data":{"object_describe_api_name":"SalesOrderObj","record_type":"default__c","created_by":["1001"],"owner":["1029"],"data_own_department":["1000"],"data_own_department__r":"CRM管理员","approval_status__c":"1","discount":"100","field_dk6td__c":"248","is_synced__c":"2","is_user_define_work_flow":false,"field_b1Vwh__c":"0","dynamic_amount":"0","order_status__c":"1","field_f2kwq__c":"0","total_shipments__c":"0","payment_amount":null,"invoice_amount":null,"returned_goods_amount":null,"refund_amount":null,"product_amount":null,"field_k4T0b__c":"0.00","plan_payment_amount":null,"field_40W72__c":"0.00","single_choice__c":"6","stamp_type__c":"1","currency__c":"CNY","ship_to_tel":"***********","customer_type__c":"经销商","remark":"","ship_to_add":"","delivery_date":*************,"field_Rr01P__c":"0.00","order_category__c":"1","recipient__c":"李丽","order_amount":"0.00","delivery_detail_address__c":"深圳市龙岗区坂田街道布龙路333号东门一楼 八号仓","no_invoice_amount":"0.00","shipping_warehouse_id__c":"789","invoice_status":"3","receivable_amount":"0.00","field_Hufj9__c":null,"order_time":*************,"account_id__r":"上海申宇宙影视科技有限公司","account_id":"678b22296cc54600019705d3","out_owner":[],"out_owner__l":[],"ship_to_id":"","ship_to_id__r":"","ns_internal_id__c":"5999288","customer_category__c__v":"14","payment_method__c__r":"先款后货","customer_category__c":"dealer | 经销商","payment_method__c":"先款后货","customer_category__c__r":"dealer | 经销商","payment_method__c__v":"1","owner__l":[{"id":"1029","tenantId":"808330","name":"陈田","picAddr":"N_202502_21_5012a8191ee548aea392149cc0c76a41","email":"","nickname":"陈田","phone":"","description":"","status":0,"createTime":*************,"modifyTime":*************,"dept":"1022","post":"","empNum":"5938742"}],"gift_shipment_warehouse__c__r":"国内虚拟仓","gift_shipment_warehouse__c":"675fed344f4f1f0001c09cf9","related_shipping_address__c__r":"上海市闵行区龙吴路4221号碳素厂内傅氏影视器材","field_8uI0O__c__r":"上海市","owner__r":{"picAddr":"N_202502_21_5012a8191ee548aea392149cc0c76a41","mobile":null,"description":"","dept":"1022","supervisorId":null,"title":null,"empNum":"5938742","modifyTime":*************,"post":"","createTime":*************,"phone":"","name":"陈田","nickname":"陈田","tenantId":"808330","id":"1029","position":null,"enterpriseName":null,"email":"","status":0},"field_2pAQz__c__r":"上海市","related_shipping_address__c":"67b299c0ce45550001fb1be7","field_8uI0O__c":"257","field_2pAQz__c":"355"},"detail_object_data":{"SalesOrderProductObj":{}},"trigger_field_api_name":["delivery_detail_address__c"],"trigger_info":{"trigger_page":"Add"},"seriesId":"63dd4cd3e2ec43ccb49f68276b3a6aa3","maskFieldApiNames":{"SalesOrderObj":[]}}'


curl https://api.deepseek.com/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-cb2e358e182149eab03ee44df8ede7a9" \
  -d '{
        "model": "deepseek-chat",
        "messages": [
          {"role": "system", "content": "You are a helpful assistant."},
          {"role": "user", "content": "Hello!"}
        ],
        "stream": false
      }'

curl --request POST \
  --url http://localhost/API/v1/rest/object/validate_rule/service/cacheValidate \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 688092' \
  --header 'x-fs-userInfo: 48305' \
  --data '{"describeVersion":44,"layout_api_name":"edit_layout_Mf98r__c","layout_last_modified_time":*************,"layout_type":"add","objectApiName":"object_Dg99m__c"}'
      


      curl --request POST \
  --url http://localhost/paasAppManager/getAppList \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'X-fs-Enterprise-Account: 812549' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 812549' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 812549' \
  --header 'x-fs-userInfo: 1000' \
  --data '{

}'


 curl -i -X POST 'http://api.fxiaokeglobal.local/fs-open-app-center-provider/com.facishare.open.app.pay.api.service.QuotaService/addQuotaRecord' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":{"appId":"FSAID_5f5e519","attribute":0,"beginTime":*************,"endTime":*************,"fsEa":"********","quota":250,"quotaType":"PURCHASE"}}'



 curl -i -X POST 'http://localhost:28207/com.facishare.open.app.pay.api.service.QuotaService/addQuotaRecord' -H 'x-fs-ei: ********' -H 'Content-Type: application/json' -d '{"version":"1.0","arg1":{"appId":"FSAID_5f5e519","attribute":0,"beginTime":*************,"endTime":*************,"fsEa":"********","quota":250,"quotaType":"PURCHASE"}}'


 curl --request POST \
  --url http://localhost/API/v1/rest/object/ServiceKnowledgeObj/controller/List \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 797634' \
  --header 'x-fs-employee-id: 1985' \
  --header 'x-fs-enterprise-id: 797634' \
  --header 'x-fs-userInfo: 1985' \
  --data '{"search_rich_text_extra":true,"search_query_info":"{\"filters\":[{\"operator\":\"IN\",\"field_name\":\"name\",\"field_values\":[\"KB-0609\"]}],\"limit\":1,\"offset\":0}","object_describe_api_name":"ServiceKnowledgeObj","include_layout":false,"include_invalid_data":false,"include_button":false,"include_describe":false,"need_return_count_num":true}'

 # 更新本位币成其他货币
  curl -i --request POST \
  --url http://localhost/API/v1/rest/object/currency/service/update_functional_currency \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'x-fs-ei: 818466' \
  --header 'x-fs-userInfo: 1000' \
  --data '{
      "functionalCurrency":"CNY",
      "skipValidate":true
  }'