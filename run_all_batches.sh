#!/bin/bash

# 分批执行curl命令脚本
# 总共4965个租户ID，分成10批执行，每批最多500个
# 使用方法: chmod +x run_all_batches.sh && ./run_all_batches.sh

echo "开始执行分批curl命令..."
echo "总共10批，每批最多500个租户ID"
echo "================================"

# 批次 1 - 包含 500 个租户ID
echo "执行批次 1/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["1","753672","679945","720904","729103","5","712719","286732","679936","770049","12","794625","712711","778245","794627","786461","786462","507925","761880","794644","761872","557075","106525","679959","434200","778282","57378","704558","786473","106535","770086","770107","598075","770110","303153","778302","712764","737330","704566","770103","712758","761930","737353","737352","753742","786507","254026","467021","647234","532549","720965","434249","794716","811102","729176","540764","786523","106582","794715","704593","778326","663636","761940","794707","589929","57441","712811","761960","409711","712801","73836","770149","794723","450665","450679","737402","753785","794750","753791","778364","565375","155770","663667","581746","794736","614539","139399","729230","770189","778380","770179","770181","147597","737433","729247","794777","589969","778386","737426","393372","630930","139417","319640","794770","639126","794771","778410","778409","73899","57515","762016","729248","671931","721081","671929","778424","737459","680115","770225","278719","639154","516283","770231","770230","680119","770228","524492","770254","311499","16593","762079","786644","762066","647381","794833","770261","336090","590057","778474","737513","704745","549102","770275","762082","745696","753888","196846","57581","729337","762110","737532","590064","794870","286975","762103","762101","581878","680181","434439","155910","712963","268","786716","794908","753946","729369","794911","590109","737554","762128","762133","778537","721192","762159","704815","663843","98600","794917","82221","704804","57649","573755","295217","794937","319795","647486","688432","303418","770356","737611","794956","770379","778571","713033","762190","778572","672077","704833","540999","713029","639323","270674","770396","794964","278872","729429","65888","688488","688489","786795","565602","778599","794977","393576","778617","762224","663921","762250","721289","786826","778637","82313","647557","82316","754075","237970","762265","811423","713119","762270","688541","614800","606615","713108","614825","778665","762280","647596","704943","778669","57767","795044","770465","778656","795042","713146","770495","795066","713138","754098","754097","754122","704974","565710","98761","721345","41419","590274","786882","721348","778692","770527","745950","680415","762333","786900","786901","762320","704983","795089","713173","762324","729578","475622","565737","762345","631274","664041","795112","680428","770530","418285","40240008","770533","680421","778747","786943","25077","303612","721393","410116","762376","729615","57862","655885","770563","672258","524803","729607","74255","279056","533020","729629","762396","754192","680469","778792","385568","778786","762400","459307","418347","320052","795196","778809","787007","721469","705085","787003","705075","770609","574026","778830","680524","778828","754252","680515","754240","582213","762459","795229","90706","238160","672348","295516","688723","713322","787055","721516","672352","729696","787042","33392","721528","705145","787063","254590","90750","770698","795277","672397","795268","705154","729730","762496","705158","721541","770692","238227","762521","787103","762515","639632","754321","705172","713386","778921","688808","582317","639660","754349","795307","688802","582306","623269","393899","778918","762555","41649","778937","754360","762557","762551","762571","778954","762570","58050","377536","762574","713421","778946","713408","705221","369354","582360","66261","787159","336601","271065","639700","623319","713429","402148","778990","377571","713447","58094","631544","705278","729852","779019","729866","729865","385793","680717","770819","680706","811776","713478","680710","705287","811804","680730","639768","795419","549653","705301","803603","795439","705325","795430","779047","713509","779067","746299","705339","787263","713534","811834","574270","500529","467760","754483","516926","787248","598839","336708","443204","475972","99143","770892","779075","787269","795463","779099","467796","795480","721746","795478","394074","795475","746346","779113","787305","705382","705383","729957","795491","779130","729976","385907","377715","779132","623474","705393","525174","279418","787315","607115","672648","713609","197508","779138","754562","754566","533382","787330","508808","721818","689051","705439","598942","689042","746391","779159","418714","738197","779157","746388","705429"]}'

echo "批次 1 完成"
sleep 3

# 批次 2 - 包含 500 个租户ID
echo "执行批次 2/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["779179","705448","738217","615340","689080","787381","607154","795575","74687","738229","779209","746440","533452","705486","705487","746435","615361","787394","771032","287703","738271","435155","754653","771027","779217","787415","197598","787409","730069","680938","779241","738280","623594","754670","599020","680940","754657","484328","582648","771066","738303","787448","771068","730099","787445","771058","705526","394234","672777","771080","705548","500737","795659","459790","730112","746496","738311","738310","680965","754718","754711","787474","721963","779304","312359","599087","779309","787499","746540","541730","91183","705573","746558","779315","754743","402491","664628","664629","795724","738379","787533","746568","787535","730191","754765","451660","746561","443469","533573","787521","795714","746586","754776","787544","787546","42071","738387","787540","722003","787541","689235","779345","631895","812114","42079","754772","730210","189547","722020","738426","681081","787577","771197","730227","738419","746608","722036","664693","795788","705675","730249","722056","787592","738447","74889","91273","689283","722052","779419","779417","746649","730269","754845","787611","574611","779414","386202","787628","74917","738478","730285","304290","787620","648357","648359","779429","386228","779448","754879","746686","779454","787641","754878","705727","156853","66744","746679","99516","738506","640203","369856","779470","754880","779463","607429","779460","738500","607451","468178","779473","779472","738512","746711","730326","722155","705768","779497","738543","34024","730338","779490","91372","672999","50413","787711","787701","99576","91387","722160","238847","787698","58622","779509","681226","533768","75013","656652","599297","402700","746769","83230","771370","83232","738602","386337","754988","746785","771366","566589","738610","58683","754999","746825","99655","648526","656717","451905","558400","705858","779591","722267","738642","771409","705879","705876","517465","755048","804200","746861","787813","746848","779621","746852","771451","632188","796026","787829","664950","648564","517511","771466","787855","476547","787850","238981","673165","746892","771468","574848","779650","517517","746885","443798","656798","542111","615838","705949","779668","779690","787885","583082","75170","746926","730540","689568","640420","411051","722362","730553","705971","705972","746932","787916","452039","730569","689609","779720","779718","746949","755163","771545","673241","787928","746973","632286","730580","706026","337383","771567","443874","738798","730605","746989","566766","214500","730595","779746","787942","738791","99825","747002","738813","321020","779762","755185","779767","722422","83455","730612","689674","787980","419335","787976","148999","239110","738828","747010","747009","673284","730628","771588","140818","583193","738841","771614","722461","353810","640528","534035","632361","615979","747048","771630","755232","722470","640550","738852","722491","468535","607801","730680","738872","386609","755263","747069","165428","706099","362046","17979","738870","665140","738868","730676","755274","83520","738890","730703","706126","747086","738894","788043","525888","730688","648793","722523","706139","788063","788058","689757","755283","788053","656982","247390","648788","648791","484966","788078","706153","779887","730735","353888","58981","444000","673389","689760","755302","747109","190061","738938","624248","714363","755320","706169","722557","755324","779888","771702","624246","616073","386693","771725","738956","788107","722560","755335","722566","738970","632477","771741","648863","788116","485021","665236","714410","706216","714409","738989","419488","738983","788131","124593","452276","755390","739006","788154","747196","738994","329401","755383","738998","738996","575159","665269","566984","714443","755401","739017","771791","714447","747213","771778","739010","739009","689882","739034","771807","739038","788181","788182","722647","689896","370407","575210","788206","280288","788203","714466","739043","739044","640742","771812","706298","739065","780024","739071","771839","263921","780019","739056","739063","739062","288506","231164","780020","780047","747279","780045","747265","739078","722715","714520","83733","747295","476944","665362","657168","780049","575252","780054","747311","59175","50985","689952","722720","771877","780090","747326","788277","452412","714544"]}'

echo "批次 2 完成"
sleep 3

# 批次 3 - 包含 500 个租户ID
echo "执行批次 3/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["706359","755531","83779","526154","739145","689993","608077","657231","747329","739141","403273","739140","722778","755545","771928","690009","771935","747357","739164","780124","583505","755541","771924","690024","485219","690031","436065","722796","739169","100204","722790","452457","51055","755578","706424","690044","771952","624498","771959","92032","755594","477062","706440","739207","739206","788355","771992","747421","714653","640912","747409","771991","739223","722838","690068","706452","477095","706474","329639","362406","67494","665505","223149","108462","772027","772026","780222","321457","788409","75708","239555","772047","83911","788426","501711","640961","714691","788418","559046","755679","534492","296912","550878","739292","632798","780252","706514","780241","739280","690134","747476","690155","788463","92137","731106","755682","231401","534501","788451","747515","788478","780281","788475","788470","477181","460795","772086","550902","780276","387078","419840","747532","788486","747520","739334","706586","387095","739357","788500","731158","747542","542743","747563","583720","755754","714792","657455","706605","780323","747554","731174","780326","690212","641062","755748","755772","747580","780348","559153","526387","731189","755765","788531","288836","690248","600139","747593","772175","788552","747585","739392","460875","747611","706651","747613","780380","706653","772179","616529","747602","747601","772177","755792","747607","747605","706644","10335","747604","387173","731243","731242","747618","714848","501869","780384","723040","706663","526459","34932","747646","706684","747645","649328","747634","747633","755829","731253","665716","452743","477315","780429","551055","501903","747654","641158","460950","583834","600220","755870","723101","469136","632979","207000","780439","772246","551061","428185","346276","788648","772271","100520","788647","231599","772261","747704","747711","772285","780466","739511","747703","788657","755894","731318","75969","624840","706763","280775","755919","780493","26822","559311","788683","706754","755906","747713","444621","788673","739526","452809","239824","288979","780499","469210","780521","542956","772333","51434","510187","207087","297193","739557","755967","780542","633072","67832","223481","788749","788750","559370","780556","788747","780547","772354","436495","788738","788764","747802","731418","755997","788756","788758","502044","723223","788752","493850","739606","780564","788780","600363","788776","67876","592172","788779","780579","772385","477484","452907","190770","788797","715064","788798","788799","665913","338225","723260","715057","756016","452923","739656","756044","788804","739651","772417","461132","731462","461129","616774","739674","330068","706907","772447","723294","747868","371026","305500","747858","584023","518501","756073","690542","780653","772451","747879","84333","624999","330090","600440","35187","739711","100727","723325","731506","354687","772471","649588","600456","772494","649612","633228","756096","690564","739739","731551","739742","772499","706962","788885","723346","739730","772498","362911","354718","256417","592300","756140","43437","772518","534970","739774","641468","739772","706992","756145","715184","706993","780724","788940","780746","723401","780749","158149","707011","747975","592325","780743","731590","731588","715224","641498","412115","551389","666077","772567","788944","756182","788945","43486","731626","707054","772589","592353","772578","461291","780774","756219","567792","690673","723447","723446","707060","477689","502264","772619","657930","723466","772618","772616","231942","780814","780813","641551","772620","780812","748039","641541","387594","772635","616984","772634","444948","633370","772632","707102","59926","780828","813590","641559","772629","731691","731689","690720","780832","723495","379433","731686","789025","707108","404022","772665","715324","592447","731699","789046","633395","805431","780855","707127","723510","707125","772681","789065","748099","731713","748097","723524","412233","59992","649811","756305","772689","707158","715351","756309","690773","592488","92773","756321","674400","780901","789116","707195","707198","789113","707199","682623","109175","707196","690802","731763","780913","625268","780917","690816","748161","584322","641666","690823","748165","780954","780959","756382","723613","174741","559775","780946","649875","682646","658069","780948","477863"]}'

echo "批次 3 完成"
sleep 3

# 批次 4 - 包含 500 个租户ID
echo "执行批次 4/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["715435","690856","731823","658095","690863","789161","436911","592547","666273","723620","682661","772794","756409","723641","756415","748222","674495","453296","789174","731824","674484","772810","715464","223937","789199","723656","756430","789194","731852","756419","674496","68299","756423","756422","780998","395976","641734","731867","35537","731865","666329","723678","789211","682717","674525","715474","715475","404189","756439","781014","609000","256738","461541","789230","756456","682729","731884","682722","510701","772833","649957","707301","789219","789246","781048","756478","813812","690928","404219","756469","748276","707338","756495","723726","781070","690957","781058","453388","781057","592647","789251","592665","707354","412438","715545","723736","781084","76566","690960","740112","543509","740116","641834","756520","756527","756526","666415","789289","740142","781100","781088","731941","510760","666426","510775","641848","68403","633659","781118","715568","641842","756533","658251","789322","633678","772940","748355","772931","748354","781121","781120","584515","772932","723801","641883","52051","691033","731992","723806","789338","748371","723795","691025","772948","723819","756587","723811","789348","682851","732001","781159","52076","248686","715620","781179","437111","469878","781177","723833","445300","732025","658303","789370","535408","772978","617328","707440","732023","576373","486266","781172","682890","723852","633731","666497","396170","732036","674714","707486","68504","740247","789394","666539","715688","84902","101289","707494","600996","789436","732089","592827","781247","781246","682940","560048","682929","781232","732107","781258","781257","781263","773069","314306","781260","756675","781252","609222","732100","789468","756699","789471","781276","707549","789467","732112","756688","781269","682965","568300","60391","732131","756707","748512","723942","601081","789500","683002","707579","748543","633854","781298","338942","560117","560118","494584","265222","789519","175110","723981","142346","248843","658433","396311","666651","461844","85010","756767","781341","666642","494622","748561","723985","461851","625684","76830","707605","691241","650282","781357","789540","216106","371758","732199","412715","691237","732219","748603","789564","322613","732216","683065","789562","756796","789556","560176","748595","748594","732208","60475","642103","543799","412740","789583","789576","781388","748611","789597","781400","420949","748639","388176","666719","707676","732240","756823","789585","76894","330853","789608","748653","658540","756844","781415","715877","388212","617592","781424","789645","453764","666764","617603","60559","789660","740510","724125","740509","601246","19610","789648","715927","363675","52383","683157","666794","773291","478372","732335","3239","650400","789669","412845","789665","470184","814267","715965","175291","773302","265413","756942","675020","756940","748743","756933","683204","715992","683224","675039","732369","732375","691413","675029","789743","404706","781551","683244","642273","609506","625892","756966","503016","511223","781563","748795","781562","756991","732413","625905","773363","773360","781558","732405","478456","781556","748810","757002","535822","724236","675075","748801","560408","732442","691480","732441","642332","748829","781587","773395","519454","757010","519453","625938","707857","691478","40030078","40030079","584982","716053","773396","757012","748820","535848","781610","757033","757032","666926","773411","748839","724263","683303","159026","773433","773432","683327","757054","757053","666930","691508","757044","781620","691509","683336","683340","757069","757068","757059","52552","789829","781632","781638","724293","462152","691525","789854","724313","732511","40030007","732508","421214","716113","732523","757097","601441","732514","789862","773472","781690","732537","331121","732535","748916","781684","593289","773515","781706","748938","732554","757128","585101","748942","773518","642435","707975","593286","535960","683416","707999","388509","773523","552337","732561","789906","44450","609707","781743","52645","85421","40030159","40030156","609703","708005","789948","40030162","40030163","40030161","789945","40030164","757170","40030169","789943","757174","708021","781771","724426","40030118","708046","40030119","675276","40030117","789956","748993","634306","40030126","683462","40030127","757188","40030130","749018","40030128","224720","691678"]}'

echo "批次 4 完成"
sleep 3

# 批次 5 - 包含 500 个租户ID
echo "执行批次 5/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["40030135","40030133","69081","773585","40030137","60891","773590","781782","40030141","40030082","789997","40030081","781807","40030084","724461","40030090","789990","40030088","757223","773606","716260","683519","667135","724477","40030101","40030106","40030107","454140","773617","40030105","790000","40030111","732661","724483","544256","683527","691716","577031","708122","642584","290326","716312","716313","781852","691731","716305","405018","781845","716308","454183","626218","60966","757286","781861","634424","273974","716350","650801","691763","781877","790066","790092","593481","716360","781897","781896","52807","773696","781892","642649","757338","724575","421459","757331","790102","470621","69212","134748","806483","626285","773740","781932","790117","716387","405101","511606","93810","757368","585340","790136","93814","700018","700022","716426","716427","773770","716424","814728","716431","577152","421518","454285","642690","773766","708251","781978","773785","642717","478866","642716","691871","790170","257691","749206","716458","585387","675500","36522","781985","724640","44716","781991","503466","642727","642744","716476","700093","782001","732849","478917","732872","683724","790219","724675","773827","790215","741056","790208","773828","708293","675546","560857","773850","708312","782035","790224","741079","773845","716522","159458","773867","773866","790253","184035","757485","634607","634595","773863","749286","642791","642812","700156","298738","691955","773873","675569","691956","773898","642824","454402","790283","69386","749319","364298","143117","503575","716570","642840","470804","790296","700188","708381","757521","716561","667414","691989","257827","790316","651054","724771","610085","692006","790305","282411","85811","757565","331581","708403","749366","159551","642871","790348","69441","757578","200513","724808","700238","692045","782151","773957","757572","782148","298836","773978","692056","773977","700249","667487","495463","773994","708456","773992","724846","626540","700258","429928","782181","733049","774009","724863","634750","307059","774012","405375","708464","782199","282489","782198","724854","601975","642935","790386","708469","634765","692108","774028","774019","782210","716675","692099","724865","85902","790429","790430","692124","61335","708498","708496","667542","53151","708501","782249","749481","675752","757679","790440","708525","462767","700322","249770","708515","790437","323503","708518","724923","405431","724920","749502","716735","749501","757692","307130","757684","749515","716750","757709","757708","724940","790469","774080","339913","733126","266186","790467","749531","651225","708574","675804","724956","651230","700370","782290","749520","716758","520153","700373","675818","53216","724971","684011","749545","684009","724961","667622","724965","733157","774117","241644","692197","724985","733176","405490","724989","757757","708595","774129","757744","724976","708593","675829","716811","774152","790534","577539","692231","684058","757784","708626","790549","462878","684049","667670","757781","790572","749610","127011","495658","667706","405558","102449","667705","790584","700479","757821","774194","159800","684104","53316","708684","684108","69703","782413","708685","725062","790593","782406","364616","651334","774235","700504","684121","700510","77910","774226","438364","381023","725073","774224","708695","774250","331879","544874","749672","684137","774253","397409","774252","700525","700514","774241","774240","659579","782463","585871","725123","782466","61580","749702","585880","37009","749720","790680","741523","45210","725136","626837","626836","790673","700564","594071","684181","774313","438432","749740","716963","422060","700577","774311","69804","757924","667835","667832","53427","790714","782525","536752","684211","684208","782512","544951","774345","790735","774350","717004","741581","667842","381135","307401","782533","782532","782555","438486","725209","479442","651486","749779","651475","774359","717015","536790","733419","774379","757994","774377","774382","757997","774381","78057","602339","757984","757990","717054","774396","758002","774411","774414","749838","667916","733452","774412","758028","774403","733441","635139","749824","774407","102668","708888","758047","684319","782622","758045","676124","774428","725267","717075","774417","774416","774422","749845","102686","774443","184610","373028","774447","700719","340269","774439","684326","782629"]}'

echo "批次 5 完成"
sleep 3

# 批次 6 - 包含 500 个租户ID
echo "执行批次 6/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["618809","20784","774463","282928","667967","708924","700720","774455","749878","782667","782664","676175","536911","651585","733507","635202","667974","782660","446793","684379","586074","340307","782675","635216","389468","725329","708951","790888","774510","782701","405856","782690","749926","659812","643431","790883","725369","471412","758136","758143","708988","561535","782705","782704","684401","299384","479611","717172","758132","479622","709000","782729","577932","774542","430464","758157","782732","340354","717188","684421","774532","733594","725400","561564","758172","717213","258459","782737","315806","725392","700822","381337","45470","405912","684459","668075","725423","635311","659874","627104","299438","430509","709030","676261","700837","733627","774587","725435","733626","750013","332210","438705","790964","684466","709043","774576","758192","700854","774602","750023","233935","791004","307668","627164","635358","758227","790999","733655","733653","774634","365025","684526","20967","733665","791008","635385","561656","684536","700921","651770","258550","594431","758259","627185","758258","782834","717296","758256","700918","782837","471544","94721","733704","610829","758286","782851","233995","635392","619011","733703","774662","782852","741914","750106","782874","782879","659998","684574","758300","758290","774672","750103","782870","750123","782891","782890","29217","397857","782892","782883","700963","676387","782882","406061","45613","676411","782906","758329","782904","791096","750141","782898","283197","676400","782897","782902","700980","782901","733748","774708","758346","184897","463427","78408","791104","528965","733786","102994","725599","684639","725598","299602","545361","766544","758358","684628","29280","479842","275053","758375","758373","774776","496242","291454","717425","537207","86659","774799","782990","750222","774797","782988","471693","701061","733850","750234","774815","750237","782994","709267","733842","135839","791213","766633","709295","676525","594592","733862","676519","684708","291499","783032","709308","709309","783036","733874","684721","684726","774836","725684","701134","791241","619212","78532","684738","709312","455372","791239","217806","684741","758468","783067","594650","774879","758495","602847","791259","422623","758482","750295","651990","684778","733929","750313","783087","684782","406242","340716","717543","774885","783077","684773","389877","774905","537339","799481","783101","332542","733941","774901","791282","422649","783117","717581","791300","684803","758529","717568","733952","488202","684807","86796","774918","635672","684827","774937","725785","750366","733982","684828","299804","471838","725783","717591","725803","488230","750377","725806","684845","45863","250660","283437","774951","750373","783141","733988","709438","774974","717631","684860","521009","701234","791349","774964","750411","758602","406341","758600","725836","389964","774976","701252","766810","4950","750428","717651","774992","463708","774998","226142","758613","774997","725845","783188","725867","381797","725866","619368","725865","725864","775023","725870","750444","652128","717665","766816","758624","725863","734054","709479","635751","717668","783204","725883","725882","758649","725881","488308","783224","586623","643966","791414","553842","783223","480123","775029","775051","783241","750479","414594","717711","553870","783244","750476","783235","783234","709504","758661","766852","725914","717721","758687","480146","725916","78744","783250","725904","758672","439197","775061","766869","791468","766891","627626","766888","791471","717740","766892","758691","218026","717735","783270","766884","734116","758713","103347","701368","734136","439230","758706","668599","734134","758708","734154","717771","701387","758728","685007","766926","758734","766924","766914","652227","766912","766919","791488","78799","758746","701403","734170","758744","439248","791514","766941","791515","758748","783324","480223","750547","431071","78810","766935","758742","775125","758741","676821","734190","775141","775163","726009","78835","783358","775166","676850","480263","652301","709647","791561","783372","734209","791559","431114","709638","783367","791555","791580","62482","709662","447504","701468","783388","709661","62487","775196","259098","783378","439325","766999","758806","332833","775214","537645","767021","734243","767010","775202","685091","783398","758842","726077"]}'

echo "批次 6 完成"
sleep 3

# 批次 7 - 包含 500 个租户ID
echo "执行批次 7/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["791604","791605","791606","767029","660533","635958","709685","734282","783432","758861","758860","775234","775239","570456","750685","619615","783443","750674","439389","709716","750676","685141","717931","676975","808041","660589","758892","636014","791659","775264","783463","676965","734331","775290","717944","537722","595066","734332","717936","758897","87167","734346","652426","791695","291980","726147","390285","726146","644227","513163","734362","791709","775321","775320","726175","767134","750748","734355","701587","636051","791703","685206","67000161","750762","701608","767151","767149","775340","767148","570543","767138","767143","67000144","767161","767166","758973","464063","398524","767153","767159","767158","537782","767157","67000127","701643","758985","750792","685257","67000122","67000121","758988","775363","791749","783553","38090","67000114","67000115","758980","750788","67000110","67000108","791775","67000106","726239","701660","308435","472272","67000105","67000102","718033","373979","67000094","67000095","644328","759017","67000092","67000093","67000090","709870","586989","791785","67000091","67000088","62695","759020","67000089","791780","67000087","67000085","67000082","67000083","750821","709861","67000081","759034","734456","67000077","67000074","775423","791803","775410","67000066","709878","554229","775413","67000065","455942","726282","67000063","783625","67000060","414981","496900","750856","783631","67000058","67000059","67000056","750861","67000057","701709","67000055","734465","775425","67000050","775430","365832","783642","783640","775453","783635","333085","701715","627989","709930","775466","750889","783656","750894","750883","767265","67000021","775456","685350","242991","734500","447796","734520","759101","357695","67000005","791856","791857","562487","644427","709961","439631","423244","701760","54603","644420","685400","750937","70995","734552","759135","742751","791898","726353","685398","726358","734550","709973","595305","79201","791916","439652","726381","775533","791910","537955","71021","791907","464244","734578","759154","701808","79226","791921","734582","685447","619911","652699","415122","685468","701843","800150","398748","701841","726423","374168","734612","791981","710056","701865","710062","652718","701858","685476","701861","791971","783803","775611","734648","570814","710077","701885","742844","685501","669107","710067","701878","13759","701877","701898","701899","792015","783816","685518","759235","759234","751042","751041","554435","13774","783835","570841","701912","783833","95699","652764","554461","742876","775632","570838","570859","710126","718316","407023","751098","587257","701951","587262","783868","570865","325117","783858","718320","742896","775664","669175","669195","701963","251393","742920","775694","800266","390669","734725","775710","734749","587294","759324","431642","792081","734741","325147","775723","742955","587309","792105","546336","792100","767522","742945","448042","595512","718395","726585","775742","595516","357938","79417","726578","652848","595507","390712","554550","726581","554572","775756","792135","480844","341576","743001","734808","341590","792152","497233","661084","792154","628305","783954","792150","759376","742992","456285","710230","456283","710231","685655","775765","570967","669294","743010","685665","751200","767584","710247","792191","54901","71286","792180","792182","439932","611954","726640","792177","726644","595592","595595","743049","702089","751240","784012","759427","579205","775813","472713","628380","497298","751260","734876","743063","300698","562839","562856","784043","792238","620202","38564","726701","38566","792230","751265","751264","636580","79535","751268","775866","759481","710328","620218","751281","792247","784053","571087","710336","440012","775873","792262","702145","743109","751301","816861","669401","448213","726744","440017","726743","767721","734959","571118","784099","603872","734945","775911","775910","612081","636656","743157","587511","440071","341763","726796","775948","784128","562948","40220021","792348","104211","40220022","784157","759580","497424","734999","653077","40220025","71452","40220024","40220026","784148","784175","759585","538403","522026","300843","792380","775994","792383","587579","456499","415536","603952","743216","55104","776010","792397","440131","710479","792394","710477","792395","382799","554822","268107","366421","636760","603992","784217","784221","358226","792400","776022","30557","792430","710505","636781","784239","726894"]}'

echo "批次 7 完成"
sleep 3

# 批次 8 - 包含 500 个租户ID
echo "执行批次 8/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["743277","735084","792420","784226","776037","776059","759673","776060","776049","792432","776079","718733","710541","718722","776066","726912","735111","374664","792450","6030","513940","710553","399260","767895","472987","620438","792488","628652","792489","784302","784288","735162","669624","276406","776121","743352","653247","6073","776114","784305","792503","473018","710580","718773","792499","776139","751561","759753","792526","776136","432066","792523","726978","784321","178121","333774","710598","612295","751556","653254","784346","653275","784344","726992","776144","776151","743380","776148","473048","628713","710634","776173","759779","341999","776161","710624","391146","743417","776185","784377","22518","751613","63478","727036","759798","767989","776180","686090","735242","751628","792587","784387","301068","784386","776199","784390","759813","563224","751642","792606","735256","751635","792597","686099","710673","432154","759828","792622","735273","710696","727080","423970","776238","636965","743463","448555","514090","727077","759845","653370","366646","776248","768062","522289","751676","653374","776243","743475","710707","391228","784432","776247","792625","776246","710708","776267","759883","464966","743503","710732","63559","514127","784450","784449","268361","784454","784453","514120","768088","686174","661596","759900","325727","456794","88159","776296","555115","546924","727151","653423","784481","481386","751739","784506","776307","710771","727153","604275","776305","604274","637042","481404","727159","424059","579701","751732","743563","776335","776334","784526","784524","653445","718980","645254","792733","481430","268439","792729","30870","751763","768145","489637","759983","784558","792746","628897","784546","743585","751777","702631","596134","743611","727227","637114","768176","727222","784564","743626","760014","743629","784578","71880","620739","735424","743623","735430","710855","792770","751833","555231","399581","424155","776405","768213","792786","620779","784616","481507","760047","760044","792811","751842","776417","276718","751845","792802","710884","784612","743675","743674","391412","776442","637179","751865","743673","727289","735480","612607","776445","637182","252155","792820","260347","661747","760049","751857","88315","743664","325881","743670","719092","776436","702733","776450","375049","792832","710919","481559","784665","424212","604443","424210","751902","710941","710930","792852","751890","768273","751895","751894","645399","776468","727339","702760","743720","702766","760111","702767","776494","497954","637219","702752","416044","735520","604452","448809","751931","645432","760121","776505","768312","735562","727375","39238","620878","579905","751936","735558","784710","792898","498005","407890","88407","792940","760170","735592","481636","743784","743791","768367","620896","604515","670048","792935","735584","555363","760186","760189","604542","334194","711026","743795","645488","481662","555378","244088","498044","792951","760182","719242","784782","760206","629132","620942","792967","768415","776604","678291","760210","80282","792976","424345","768404","678318","752047","760239","784814","719279","743853","661932","227749","678316","792996","719267","735649","743847","588196","719270","670116","784827","719289","793017","719292","752060","784828","678320","760240","768436","113086","784843","72131","752079","686540","522688","719309","768460","88521","522696","793052","719322","752090","678363","760281","530907","793048","719327","727516","776658","752105","645611","719342","793067","301548","760290","793062","719329","424426","760294","793058","793059","88561","719354","31219","776701","768508","645616","719344","760304","793073","776693","80383","793074","784884","424441","178685","80385","735754","727561","154113","776717","375308","776704","743943","752155","580121","637467","793118","743967","768541","768540","481819","793107","735787","735784","653866","662057","735791","784941","440876","555555","678439","743993","743992","793151","604733","96821","760381","793146","113209","793139","449094","596555","793166","760392","776783","760397","784973","768589","784963","416332","776769","449096","760388","744004","776794","735833","776792","768595","784979","768594","744017","784977","678486","776791","719447","64094","64097","580202","785001","719464","719465","744040","784999","744037","776805","432745","645753","785016","80500","72308"]}'

echo "批次 8 完成"
sleep 3

# 批次 9 - 包含 500 个租户ID
echo "执行批次 9/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["785011","760434","776816","735863","817778","711306","785032","703113","785038","752268","793216","744069","785028","703131","735897","539291","744095","760479","793242","637587","588434","793239","678548","744084","604841","768680","662185","735919","744110","776878","711331","711328","793254","662182","735911","744103","662183","793249","793250","735909","768677","744101","506536","31409","776889","785086","596668","793273","400048","793270","678576","735920","580276","768694","277179","793267","236226","785097","735944","776911","793289","768717","735948","793284","793285","785090","711360","719553","735936","604868","793282","776921","768734","793307","727772","326364","686802","424671","645845","449243","760533","457433","80606","326372","580329","686827","793326","744168","727784","686816","735969","473836","686817","735974","711399","39661","776954","457460","752376","572155","760572","735987","793334","678647","744206","506625","785164","432897","244490","711427","793351","744198","793346","719621","793372","793374","719641","776991","768797","793370","391967","760592","498459","785175","776982","793362","760618","776995","719651","629539","678694","760614","711463","768804","776996","621369","744250","760632","310071","678719","678716","768828","744240","301882","760650","768840","613194","711502","531276","80708","662338","785218","760641","785217","654147","670529","785222","736069","719685","670552","777048","613210","793433","793428","580432","752466","785233","785232","613202","752470","777044","793427","768875","760681","793455","736108","645998","736098","744295","293736","760677","777061","785275","785278","785277","580464","719731","490366","760690","793457","670583","752523","637832","236418","752521","162688","793481","105351","572302","785292","654209","719748","760728","768926","719773","777107","736146","621456","777106","736145","138143","654228","678805","711595","793518","736175","760751","678829","678818","80811","39850","654245","785318","678823","367530","744379","744378","7089","768959","768945","285631","105404","744374","793522","768948","777163","662474","768970","785354","785353","711630","678850","678851","760770","678848","678849","752583","703431","662469","687057","777175","719831","785364","719829","711658","736235","654313","539625","703464","793577","752622","539616","105448","777185","703462","769019","793596","539641","424949","31734","760819","498685","777207","392185","228350","785397","728052","252931","752651","39936","793612","703497","793608","777231","678927","752654","752652","711682","531457","777217","785414","793601","760838","678917","670725","711685","769051","752671","752659","654352","752657","703504","736278","760853","711701","760875","785451","539690","760872","769071","654380","162852","793643","793638","785441","785445","728101","777252","728120","728125","687165","760892","760883","597041","343100","793653","703536","392249","416824","760884","662602","760905","678984","785481","760904","793675","72777","638019","793671","638021","777285","638045","654429","629853","785501","777298","678992","711764","425049","769108","679019","777324","769120","752736","621669","785509","654438","285803","777343","679039","679036","785532","760956","777330","752753","687216","703606","752756","638070","654472","711819","785545","687246","760975","785549","744579","760963","752769","703622","654471","15503","760964","752772","646301","269457","785565","752796","744593","490653","703638","72860","638100","785556","679082","711850","793768","711852","760995","777377","277674","785594","638136","720059","548025","785592","605361","654513","638129","777395","793781","638131","744630","761035","89285","761038","711887","761037","752845","769219","523471","679107","761026","777409","769217","654530","711873","752837","769220","711898","793821","769246","195796","56535","769244","744658","752849","687318","769239","711895","457944","711893","761067","793832","785647","556269","662767","646380","752867","711910","703719","154863","580856","630009","720120","761080","752892","711933","687357","736497","793840","711927","736501","736500","777460","761076","670987","679176","572683","736520","720140","589070","785667","711936","621828","335125","785691","646424","72979","359702","736536","793881","736531","711953","531731","761129","744744","777519","679214","752941","711970","793895","711969","417066","711974","662820","711972","703802","630074","777534","777533"]}'

echo "批次 9 完成"
sleep 3

# 批次 10 - 包含 465 个租户ID
echo "执行批次 10/10..."
curl --request POST \
  --url http://localhost/API/v1/object/brush_data/service/migrate_field_compliance_setting \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Host: localhost' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --header 'content-type: application/json' \
  --header 'x-fs-employee-id: 1000' \
  --header 'x-fs-enterprise-id: 590064' \
  --data '{"tenantIds":["597296","711985","326970","793907","343354","761163","793933","712011","793930","752972","769370","785752","712030","425299","687453","728403","712018","712022","703831","744788","769387","720235","793967","32099","490850","753006","777571","712038","736614","744805","785787","712056","253296","769407","712063","785790","712060","753020","769404","540031","81275","712048","712049","785781","605558","769419","712075","712072","761231","56709","793993","712077","97672","712065","712070","769414","793985","793986","81295","720260","777627","785819","720280","785823","581021","728477","720285","597393","785810","441756","449959","654767","671149","761260","400808","753061","753081","679358","777661","769469","654782","777660","654771","564659","474556","646581","785847","777654","720331","769487","581070","40394","777664","613826","720326","761285","753093","769499","785883","24017","646619","744924","736732","343519","441820","679382","794064","769495","761301","277988","761320","646634","277984","736750","597486","720354","777698","720352","785888","728550","785893","794108","777715","769522","769524","474628","646671","728579","777731","237066","753153","605701","491018","744964","769562","802334","794136","728604","392734","794158","630315","745006","106020","532014","654881","671268","753215","97845","654911","777788","777779","794160","548405","777782","228925","794190","540234","425538","769612","753218","319050","777796","794205","794200","712285","507485","712272","794198","769617","753239","777835","794220","540265","646762","794219","777828","761444","327286","679544","777849","769657","777848","450160","704115","777845","761460","56960","548489","794254","663176","81540","769664","343691","777883","786075","712346","786074","655000","720536","761496","810655","646813","753310","433808","654992","769682","786071","654997","712342","777876","753323","401060","73378","622250","204453","736931","687782","777895","671396","769701","474793","761530","777912","630461","786109","499390","32441","786097","753328","491196","769718","777910","655048","753354","753353","736969","777933","777932","769728","401098","40652","786117","761540","753371","630491","777951","679647","384720","753373","679634","777938","761553","523996","417501","777941","777940","81632","753385","753391","556782","499438","777959","761575","753381","761594","769785","761592","777983","786173","319219","491263","777971","777970","655091","57084","409339","753398","769782","597750","794355","802572","769803","114433","794380","614159","573198","622351","614158","753420","777987","753411","777986","761601","794374","753409","777984","777991","777990","458505","777988","655131","106258","753437","786194","614160","753426","565010","753425","655122","753424","261918","712471","728875","778026","737071","720684","679724","786220","548640","753442","499502","753447","786215","458539","384810","261932","794428","425781","753470","327475","745276","753459","532274","769840","786230","606004","507719","728907","597834","655180","81732","384845","761666","769857","794439","319305","597831","565062","753477","794434","89934","720728","778073","712543","737117","794459","671581","786257","466778","450395","761684","794477","769897","794479","761710","581487","671597","294755","753506","671584","720736","597858","786272","737126","769893","761701","597881","794494","769913","565119","737139","769906","745330","769905","778103","81791","737140","720757","491399","769930","778127","597902","794502","778112","786304","491400","712602","778137","728991","688030","786334","614302","393117","679825","786327","761750","778133","761749","679829","794515","778153","720813","155562","786339","532384","507822","597923","778145","786341","417705","589753","720827","712635","786360","778175","262069","442289","778162","786359","704458","769993","704457","688078","679886","794569","761805","737228","794566","688064","769991","720836","761797","688069","606168","761817","778201","810974","434133","729054","671699","589777","778199","597975","671701","597993","737259","770025","770028","737274","786426","720891","663548","712690","581617","49148"]}'

echo "批次 10 完成"
sleep 3

