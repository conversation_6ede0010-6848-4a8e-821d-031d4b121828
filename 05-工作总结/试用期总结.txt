我叫冯津，于2021年5月24日进入公司，根据公司的需要，目前担任JAVA程序员一职，负责函数程序及自定义函数程序的开发。伴随着充实紧凑的工作生活，两个月的时间已经过去了。这一段时间里有工作上的收获，知识的丰富，经验的增长，同事也暴露出很多问题和不足。总结经验，吸取教训，现对自己的工作加以总结。
来到一个新的工作环境，最能发现自身的不足，这两个月，抱着虚心学习的态度，学习公司的开发流程，代码规范，了解公司产品及框架和主要技术，主动和同事沟通、学习经验，希望能更快的融入公司和开发团队，能够全心的投入工作。在试用期期间完成的工作还是十分有限的，简单列一些：1、熟悉公司CRM产品框架。2、熟悉配置文件提交、代码提交、做版、发布、测试流程。3、解决工作流触发函数并发高导致 pg 负载高



我叫冯津，于2021年5月24日进入公司，根据公司的需要，目前担任JAVA程序员一职，负责函数程序及自定义函数程序的开发。伴随着充实紧凑的工作生活，两个月的时间已经过去了。这一段时间里有工作上的收获，知识的丰富，经验的增长，同事也暴露出很多问题和不足。总结经验，吸取教训，现对自己的工作加以总结。
来到一个新的工作环境，最能发现自身的不足，这两个月，抱着虚心学习的态度，学习公司的开发流程，代码规范，了解公司产品及框架和主要技术，主动和同事沟通、学习经验，希望能更快的融入公司和开发团队，能够全心的投入工作。在试用期期间完成的工作还是十分有限的，简单列一些：1、熟悉公司CRM产品框架。2、熟悉配置文件提交、代码提交、做版、发布、测试流程。3、解决工作流触发函数并发高导致pg负载高 4、添加删除相关团队函数接口升级。5、新增Fx.org.getEnviorimentType函数 6、循环分页查询函数接口 7、自定义对象主属性支持显示字段。经过两个多月的工作，，在开发过程中也认识到自己的不足，在部门领导及同事的帮助和指导下，完成了一些需求的开发，也认识到自己对公司业务的不熟悉，自己对业务的了解度还不够。在后续的工作中争取多看一些历史的需求及技术文档，并梳理总结，争取早日补足业务上的短板。


1、目前对groovy语言只是初步了解可用的状态，后续需要加深对groovy的学习争取能够早入熟练掌握，并且能够在自定义函数的开发过程共熟练使用，针对后续的自定义函数提供建设性意见。
 2、对自定义对象项目appframework的业务了解不足，在之前的主属性支持可显示字段的开发过程中并不能提前了解到涉及修改的接口。因此，在后续的工作中需要看一些历史的需求及技术文档，并梳理总结，争取早日补足业务上的短板。
 3、在接下来的工作中，提高自己的沟通能力，建立良好的人际关系。




1.工作流函数异步改造；
2.对象支持显示字段
3.导出支持使用打印模版；
4.掩码字段可自定义去掩码显示角色
5.UI事件能力提升


765
1.流程后动作执行函数时增加是否异步的方式
2.内部相关团队函数接口支持团队类型
770
1.负责人可配置必填
2.对象支持显示字段
3.增加Fx.org.getEnviorimentType函数
4.增加Fx.BI.getTargetCompletion函数
5.主属性支持修改类型
6.计算公式校验增加是否落地校验结果
775
1.导出支持使用打印模版
2.函数引用关系计入更改集和沙盒
3.find函数可以返回关联关系，主从关系的主属性，单选多选值字段的value值
4.find函数可以从DB查找数据
5.如果预置对象的批量接口不支持业务触发，运行时自动转成单个接口（研发优化）
6.TriggerEvent，事件不存在时给出提示
7.函数日志优化
780
1.查询数据时增加参数重载，支持返回__r的信息
2.UI事件函数的引用关系优化
3.UI按钮支持UI事件
4.新增添加外部相关团队支持团队类型函数接口
5.导入时函数兼容支持返回相关团队数据
6.lookup字段范围函数增加新返回类型
785
1.优化按钮删除函数逻辑
2.AddUI，如果函数被删除了，给出业务提示
3.掩码字段可自定义去掩码显示角色--放开CRM管理可配置
4.Fx.object.find函数增加startWith、endWith、between
5.新增替换外外部团队成员接口（可移除外部负责人）
6.【Bug转需求】SVIP-自定义函数列表筛选查询报错
7.提供新建/更新标签函数
790
1.UI事件控制从对象业务类型的隐藏
2.UI事件支持隐藏选项值
3.UI事件可控制从对象按钮显示/隐藏
4.UI事件可支持从对象字段的只读、隐藏
5.UI事件可指定清除具体某个字段的错误提示&可指定错误提示显示位置
6.UI事件删除明细时能从context中添加被删除的明细数据的详细信息
7.对象显示字段支持whatlist关系

——————————————————————————
1.加强业务的理解
2.提高开发效率
3.提高自身的技能


1.结合业务，加强自己负责模块的代码的理解，达到独立进行相关需求的开发的要求。
2.加强熟悉代码封装的工具类，提高代码的开发效率，提高代码质量。
3.加强专业知识的查缺补漏，补足在开发过程中的短板。

——————————————————————————

1.线下问题
2.线上问题

线下问题
1.及时跟进处理日常工作中的问题

线上问题
1. 关注线上服务调用和异常，及时通知相关人员处理问题
2. 跟进排查线上出现的异常情况，重点关注所在业务线的相关业务异常，并及时修复。
3. 跟进修复线上bug，目前时间占比较重，现阶段的问题都比较难排查，耗费时间较多。  


——————————————————————————
1.配合各团队之间的开发任务

1.跟进配合其他团队的依赖需求
2.提供供其他团队使用的参考文档
3.解答其他团队提出的问题

——————————————————————————