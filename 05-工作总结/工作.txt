1、@冯津FengJin 760 相关团队支持添加部门、角色、用户组，函数操作相关团队接口也需要一并升级下  -》编辑团队成员-editTeamMember  Fx.object.editTeamMember(<String ObjectAPIName>,<String ObjectDataId>,<List<Map> TeamMemberList>)
2、自定义函数报错返回友好提示：http://wiki.firstshare.cn/pages/viewpage.action?pageId=152193313
@李涛Scott @郑磊 @冯津FengJin 对于函数调用接口失败后的错误信息，我们需要统一下，把较明确的错误信息反馈给用户。大致分为三类：

1、业务异常，相对好处理，就把业务侧错误信息返回，对于业务侧返回不合理的 case by case 处理，比方说 create/batchUpdate 接口不支持的对象，把提示信息调整为【当前对象不支持该操作，请使用 xxxx 接口】
2、系统未知异常，统一返回“系统发生未知异常，请联系纷享客服”
3、接口超时，统一返回“接口执行超时，请联系纷享客服”



1、UI事件隐藏从对象udobj和函数侧灰度配置不统一优化


 curl -X POST 'http://**************:4859/v1/function/run'   -H 'x-fs-ei:71557' -H 'x-fs-peer-display-name:%E5%87%BD%E6%95%B0%E5%BC%82%E6%AD%A5%E8%81%94%E8%B0%83' -H 'x-peer-name:flow-after' -H 'X-fs-RPC-Id:0.1' -H 'x-fs-locale:zh-CN' -H 'x-fs-userinfo:-10000' -H 'x-fs-peer-name:flow-after' -H 'X-fs-Trace-Id:fs-bpm-after-action/61433db65d865900015fdfe2' -H 'x-fs-model-name:workflow' -H 'x-fs-batch:false' -H 'rpcId:0.1' -H 'Accept-Language:zh-CN' -H 'x-peer-ip:*************' -H 'Content-Type:application/json' -H 'X-fs-Trace-Color:1'  -d '{"parameters":[],"async":true,"api_name":"func_1kq5U__c","binding_object_api_name":"object_UPfNh__c","binding_object_data_id":"60d1bd60c1cd5e00013f095e"}'