
------------------------------
分页循环批量函数
一、codereview
1、分页查询，需要按照默认按照id排序。
2、优化效率，去除skip，传上一次的id
3、Integer去除。
4、限制处理数据量。2W条
5、扫描数据量结果。处理了多少条，总共多少条。
      1.一次处理不过来，如何让用户分两次。
        如：处理3W条，一次处理了2W条，剩余1W条如何处理。---传入id
      2.如果保证两次都
6、函数的名称修改。
7、论坛改成新版批量处理。----投产后。
二、codereview
1、返回值改为对象。
2、增加判断是否已经查出全量的boolean值
3、优化searchQueryTemplate的逻辑。1）单一原则。2）只查询一次全量count
4、是否可以中断 逻辑优化。


------------------------------

1、负责人可配置非必填---灰度支持?
2、计算字段l是否落地问题？
日志：
1、WebIDE 补齐代码不全设计及评审
2、770对象可配置显示字段、主属性支持修改类型代码开发、负责人可配置冒烟测试及问题修复
3、获取BI目标完成率函数接口提测。
4、优化自定义函数报错返回。

1、770对象可配置显示字段、主属性支持修改类型代码开发、负责人可配置冒烟测试及问题修复
2、获取BI目标完成率函数接口开发及测试
3、相关团队函数添加外部团队问题修复及发布版本。
4、熟悉导出Excel接口相关逻辑。

------------------------------
6月28日-7月4日
1、What组件是干什么用的  
2、formula计算字段
3、获取BI目标完成率，冯津
4、、自定义函数报错返回友好提示：http://wiki.firstshare.cn/pages/viewpage.action?pageId=152193313
5、补充新增函数文档
8、判断当前环境是production还是sandbox
函数方法
Fx.org.getEnvior(String apiName, List searchCondition, Closure closure);
field_24Zkp__c__r.field_uT0e5__c
field_24Zkp__c
field_uT0e5__c

设计评审：
1、